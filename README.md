#### HeartMerge


##### 获取签名指纹
###### `keytool -list -v -alias heartmerge -keystore app\heartmerge.jks`

##### 生成 Facebook 密钥散列
###### `keytool -exportcert -alias heartmerge -keystore .\app\heartmerge.jks | D:\openssl-0.9.8k_X64\bin\openssl.exe sha1 -binary | D:\openssl-0.9.8k_X64\bin\openssl.exe base64`
# dev 8wLVFwBDTFEujPI76KkNKVofsl0=
# prod xxxx

[//]: # (keytool -exportcert -alias androiddebugkey -keystore %USERPROFILE%\.android\debug.keystore | openssl sha1 -binary | openssl base64)

#### summary
HeartMerge一款流行的语音聊天和视频通话程序,
在这里，你不用面对现实社交中的压力，探索不同的文化,大家因兴趣相聚, 自由地敞开心扉
您可以与其他人联系，与任何您想要的人进行视频通话和聊天，我们为您提供最好的社交体验，24/7与本地用户在线聊天。

全球社区：与世界各地的人们联系，发现多元化、富有创意的社区。 HeartMerge是形成社区的地方 - 结交新朋友的地方。
1V1: 视频聊天让您与真实用户在线交流，不同国家和文化碰撞出奇妙火花。
普通聊天：文字、语音、视频通话、图片等我们鼓励用户通过文字聊天开启聊天，加深彼此了解，建立亲密关系，确保安全的社交环境。
实时视频聊天：享受与朋友和创作者的实时视频聊天。我们的实时聊天功能让您可以互动并分享发生的时刻。
我们的大多数用户都因受到 HeartMerge 的多种功能的启发而采取了第一步，从而成为了朋友。


HeartMerge is a popular voice chat and video calling program,
Here, you don't have to face the pressure of real-life social interactions, explore different cultures, gather together for interests, and freely open up your hearts
You can contact others, make video calls and chat with anyone you want, and we provide you with the best social experience, 24/7 online chatting with local users.

Global Community: Connect with people from all over the world and discover diverse and creative communities. HeartMerge is a place to form a community - a place to make new friends.
1V1: Video chat allows you to communicate with real users online, creating wonderful sparks of collision between different countries and cultures.
Regular chat: We encourage users to initiate conversations through text, voice, video calls, pictures, etc., deepen mutual understanding, establish intimate relationships, and ensure a safe social environment.
Real time video chat: Enjoy real-time video chat with friends and creators. Our real-time chat feature allows you to interact and share moments that happen.
Most of our users were inspired by HeartMerge's various features and took the first step, becoming friends.


java -jar bundletool.jar build-apks --bundle=your_app.aab --output=output.apks --ks=your_key.jks --ks-key-alias=your_key_alias --ks-pass=pass:your_key_password

优化需求
1, 全局消息通知
2,feedback 图片上传加loading
