*.iml
.gradle
/local.properties
/.idea
.DS_Store
/build
/captures
.externalNativeBuild
.cxx
local.properties
/.kotlin

/app/debug/app-debug.apk
/app/debug/output-metadata.json
/app/release/baselineProfiles/0/app-release.dm
/app/release/baselineProfiles/1/app-release.dm
/app/release/app-release.apk
/app/release/output-metadata.json
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirRDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/BaseDownloader$actor$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/BaseDownloader$queryProgress$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/BaseDownloader.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DefaultDownloadDispatcher.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DefaultDownloadQueue$1$1$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DefaultDownloadQueue$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DefaultDownloadQueue$Companion.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DefaultDownloadQueue.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DefaultFileValidator.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DefaultHttpClientFactory.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DefaultTaskManager.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DownloadConfig.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DownloadDispatcher.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/Downloader.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DownloadParam.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DownloadQueue.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DownloadTask$getProgress$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DownloadTask$notifyFailed$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DownloadTask$notifyStarted$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DownloadTask$notifyStopped$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DownloadTask$notifySucceed$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DownloadTask$notifyWaiting$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DownloadTask$progress$1$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DownloadTask$progress$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DownloadTask$start$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DownloadTask$state$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DownloadTask$StateHolder.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DownloadTask$stop$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DownloadTask$suspendStart$$inlined$CoroutineExceptionHandler$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DownloadTask$suspendStart$2$deferred$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DownloadTask$suspendStart$2.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DownloadTask$suspendStart$errorHandler$1$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/DownloadTask.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/FileContent.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/FileHeader$Companion.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/FileHeader.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/FileValidator.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/HttpClientFactory.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/NormalDownloader$Companion.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/NormalDownloader$download$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/NormalDownloader$startDownload$2$deferred$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/NormalDownloader$startDownload$2.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/NormalDownloader.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/QueryProgress.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/Range$Companion.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/Range.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/RangeDownloader$download$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/RangeDownloader$download$3$deferred$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/RangeDownloader$download$3.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/RangeDownloader$startDownload$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/RangeDownloader$startDownload$2.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/RangeDownloader$startDownload$progressChannel$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/RangeDownloader.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/RangeTmpFile.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/core/TaskManager.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/helper/Api.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/helper/Default.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/helper/RequestKt.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/utils/FileUtilsKt.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/utils/HttpUtilKt.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/utils/LogUtilKt.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/utils/UtilKt$parallel$2$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/utils/UtilKt$parallel$2$2$1.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/utils/UtilKt$parallel$2.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/utils/UtilKt.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/BuildConfig.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/DownloadXKt.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/Progress.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/State$Downloading.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/State$Failed.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/State$None.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/State$Stopped.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/State$Succeed.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/State$Waiting.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/zlc/season/downloadx/State.dex
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/transformed/bundleLibRuntimeToDirDebug/desugar_graph.bin
/downloadx/build/.transforms/9e41b9cd2007a6417c5ffc6a8b5deaeb/results.bin
/downloadx/build/.transforms/28b56482e068a24cea987522c285d46e/transformed/classes/classes_dex/classes.dex
/downloadx/build/.transforms/28b56482e068a24cea987522c285d46e/results.bin
/downloadx/build/generated/source/buildConfig/debug/zlc/season/downloadx/BuildConfig.java
/downloadx/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml
/downloadx/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json
/downloadx/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties
/downloadx/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json
/downloadx/build/intermediates/compile_library_classes_jar/debug/bundleLibCompileToJarDebug/classes.jar
/downloadx/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar
/downloadx/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt
/downloadx/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
/downloadx/build/intermediates/incremental/debug/packageDebugResources/merger.xml
/downloadx/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
/downloadx/build/intermediates/incremental/mergeDebugShaders/merger.xml
/downloadx/build/intermediates/incremental/packageDebugAssets/merger.xml
/downloadx/build/intermediates/java_res/debug/processDebugJavaRes/out/META-INF/downloadx_debug.kotlin_module
/downloadx/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/zlc/season/downloadx/BuildConfig.class
/downloadx/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt
/downloadx/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt
/downloadx/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml
/downloadx/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json
/downloadx/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/META-INF/downloadx_debug.kotlin_module
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/BaseDownloader$actor$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/BaseDownloader$queryProgress$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/BaseDownloader.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DefaultDownloadDispatcher.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DefaultDownloadQueue$1$1$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DefaultDownloadQueue$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DefaultDownloadQueue$Companion.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DefaultDownloadQueue.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DefaultFileValidator.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DefaultHttpClientFactory.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DefaultTaskManager.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DownloadConfig.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DownloadDispatcher.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/Downloader.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DownloadParam.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DownloadQueue.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DownloadTask$getProgress$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DownloadTask$notifyFailed$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DownloadTask$notifyStarted$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DownloadTask$notifyStopped$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DownloadTask$notifySucceed$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DownloadTask$notifyWaiting$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DownloadTask$progress$1$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DownloadTask$progress$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DownloadTask$start$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DownloadTask$state$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DownloadTask$StateHolder.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DownloadTask$stop$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DownloadTask$suspendStart$$inlined$CoroutineExceptionHandler$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DownloadTask$suspendStart$2$deferred$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DownloadTask$suspendStart$2.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DownloadTask$suspendStart$errorHandler$1$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/DownloadTask.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/FileContent.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/FileHeader$Companion.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/FileHeader.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/FileValidator.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/HttpClientFactory.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/NormalDownloader$Companion.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/NormalDownloader$download$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/NormalDownloader$startDownload$2$deferred$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/NormalDownloader$startDownload$2.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/NormalDownloader.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/QueryProgress.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/Range$Companion.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/Range.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/RangeDownloader$download$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/RangeDownloader$download$3$deferred$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/RangeDownloader$download$3.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/RangeDownloader$startDownload$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/RangeDownloader$startDownload$2.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/RangeDownloader$startDownload$progressChannel$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/RangeDownloader.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/RangeTmpFile.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/core/TaskManager.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/helper/Api.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/helper/Default.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/helper/RequestKt.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/utils/FileUtilsKt.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/utils/HttpUtilKt.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/utils/LogUtilKt.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/utils/UtilKt$parallel$2$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/utils/UtilKt$parallel$2$2$1.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/utils/UtilKt$parallel$2.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/utils/UtilKt.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/BuildConfig.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/DownloadXKt.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/Progress.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/State$Downloading.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/State$Failed.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/State$None.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/State$Stopped.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/State$Succeed.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/State$Waiting.class
/downloadx/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/zlc/season/downloadx/State.class
/downloadx/build/intermediates/runtime_library_classes_jar/debug/bundleLibRuntimeToJarDebug/classes.jar
/downloadx/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.keystream
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.keystream.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.values.at
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab_i
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab_i.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.keystream
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.keystream.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.values.at
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab_i
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab_i.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/counters.tab
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab_i
/downloadx/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab_i.len
/downloadx/build/kotlin/compileDebugKotlin/cacheable/last-build.bin
/downloadx/build/kotlin/compileDebugKotlin/classpath-snapshot/shrunk-classpath-snapshot.bin
/downloadx/build/kotlin/compileDebugKotlin/local-state/build-history.bin
/downloadx/build/outputs/logs/manifest-merger-debug-report.txt
/downloadx/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin
/downloadx/build/tmp/kotlin-classes/debug/META-INF/downloadx_debug.kotlin_module
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/BaseDownloader$actor$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/BaseDownloader$queryProgress$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/BaseDownloader.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DefaultDownloadDispatcher.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DefaultDownloadQueue$1$1$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DefaultDownloadQueue$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DefaultDownloadQueue$Companion.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DefaultDownloadQueue.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DefaultFileValidator.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DefaultHttpClientFactory.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DefaultTaskManager.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DownloadConfig.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DownloadDispatcher.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/Downloader.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DownloadParam.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DownloadQueue.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DownloadTask$getProgress$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DownloadTask$notifyFailed$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DownloadTask$notifyStarted$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DownloadTask$notifyStopped$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DownloadTask$notifySucceed$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DownloadTask$notifyWaiting$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DownloadTask$progress$1$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DownloadTask$progress$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DownloadTask$start$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DownloadTask$state$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DownloadTask$StateHolder.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DownloadTask$stop$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DownloadTask$suspendStart$$inlined$CoroutineExceptionHandler$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DownloadTask$suspendStart$2$deferred$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DownloadTask$suspendStart$2.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DownloadTask$suspendStart$errorHandler$1$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/DownloadTask.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/FileContent.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/FileHeader$Companion.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/FileHeader.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/FileValidator.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/HttpClientFactory.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/NormalDownloader$Companion.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/NormalDownloader$download$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/NormalDownloader$startDownload$2$deferred$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/NormalDownloader$startDownload$2.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/NormalDownloader.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/QueryProgress.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/Range$Companion.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/Range.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/RangeDownloader$download$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/RangeDownloader$download$3$deferred$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/RangeDownloader$download$3.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/RangeDownloader$startDownload$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/RangeDownloader$startDownload$2.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/RangeDownloader$startDownload$progressChannel$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/RangeDownloader.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/RangeTmpFile.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/core/TaskManager.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/helper/Api.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/helper/Default.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/helper/RequestKt.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/utils/FileUtilsKt.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/utils/HttpUtilKt.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/utils/LogUtilKt.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/utils/UtilKt$parallel$2$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/utils/UtilKt$parallel$2$2$1.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/utils/UtilKt$parallel$2.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/utils/UtilKt.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/DownloadXKt.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/Progress.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/State$Downloading.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/State$Failed.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/State$None.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/State$Stopped.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/State$Succeed.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/State$Waiting.class
/downloadx/build/tmp/kotlin-classes/debug/zlc/season/downloadx/State.class
/app/release/baselineProfiles/0/release-1.0.0-20240918.dm
/app/release/baselineProfiles/1/release-1.0.0-20240918.dm
/app/release/release-1.0.0-20240918.apk
/app/release/
/app/debug/debug-1.0.3-20241114.apk
/app/debug/
