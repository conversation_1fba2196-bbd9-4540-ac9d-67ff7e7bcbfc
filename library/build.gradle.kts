plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.jetbrains.kotlin.android)
    `maven-publish`
}

android {
    namespace = "com.bdc.android.library"
    compileSdk = 36

    defaultConfig {
        minSdk = 24

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
    }

    buildFeatures {
        buildConfig = true
    }
}

dependencies {
    api(libs.androidx.appcompat)
    api(libs.androidx.activity)
    api(libs.androidx.activity.ktx)
    api(libs.androidx.fragment)
    api(libs.androidx.fragment.ktx)
    api(libs.androidx.core)
    api(libs.androidx.core.ktx)
    api(libs.androidx.core.runtime)

    api(libs.material)
    api(libs.flexbox)
    api(libs.swiperefreshlayout)
    api(libs.androidx.recyclerview)

    api(libs.androidx.datastore.core)
    api(libs.androidx.lifecycle.livedata)
    api(libs.androidx.lifecycle.livedata.core.ktx)
    api(libs.androidx.collection)
    api(libs.androidx.collection.ktx)
    api(libs.androidx.collection.jvm)
    api(libs.androidx.lifecycle.runtime)
    api(libs.androidx.lifecycle.runtime.ktx)
    api(libs.androidx.lifecycle.common.java8)
    api(libs.androidx.lifecycle.viewmodel.ktx)
    api(libs.androidx.lifecycle.viewmodel.savedstate)
    api(libs.androidx.lifecycle.livedata.ktx)
    api(libs.androidx.lifecycle.viewmodel.ktx)
    api(libs.kotlinx.coroutines.android)
    api(libs.kotlinx.coroutines.core)
    api(libs.kotlin.stdlib.jdk8)
    api(libs.androidx.loader)
    api(libs.androidx.viewbinding)

    /*三方组件库*/
    api(libs.okhttp)
    api(libs.retrofit)
    api(libs.converter.gson)
    api(libs.gson)
    api(libs.glide)
    api(libs.glide.transformations)
    api(libs.eventbus)
    api(libs.mmkv)
    api(libs.dsladapter)
    api(libs.dsltablayout)
    api(libs.immersionbar)
    api(libs.immersionbar.components)
    api(libs.immersionbar.ktx)
    api(libs.permissionx)
    api(libs.logger)
    api(libs.xpopup)

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}