<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="XToolbar">
        <attr name="x_action" format="reference|integer" />
        <attr name="x_action_title" format="reference|string" />
        <attr name="x_action_text_color" format="reference|color" />
        <attr name="x_action_text_background" format="reference" />
        <attr name="x_show_action" format="boolean" />
        <attr name="x_background" format="reference" />
        <attr name="x_navi" format="reference|integer" />
        <attr name="x_title" format="reference|string" />
        <attr name="x_title_color" format="reference|color" />
        <attr name="x_title_max_length" format="integer" />
        <attr name="x_show_line" format="boolean" />

    </declare-styleable>

    <declare-styleable name="CoordinatorTitleLayout">
        <!--左侧图标 一般为返回icon resID-->
        <attr name="left_icon" format="reference|integer" />
        <!--右侧图标 icon resID-->
        <attr name="right_ic" format="reference|integer" />
        <!--标题中文-->
        <attr name="title" format="reference|string" />
        <!--标题颜色 默认#333 -->
        <attr name="title_color" format="reference|color" />
        <!--标题英文-->
        <attr name="title_english" format="reference|string" />
        <!--标题英文 默认#f0f0f0 -->
        <attr name="title_english_color" format="reference|color" />
        <!--右下角图片资源 自适应大小 img resID-->
        <attr name="image" format="reference|integer" />
        <!--右下角图片资源右边距 默认16dp-->
        <attr name="image_right_margin" format="dimension" />
        <!--是否默认展开 默认true-->
        <attr name="expanded" format="boolean" />
        <!--右侧文字按钮-->
        <attr name="right_text" format="reference|string" />
        <!--右侧文字大小-->
        <attr name="right_text_size" format="dimension" />
        <!--右侧文字颜色-->
        <attr name="right_text_color" format="reference|color" />
        <!--右侧文字加粗-->
        <attr name="right_text_bold" format="boolean" />
        <!--是否需要展开时状态栏透明 收起时状态栏白色-->
        <attr name="need_statusbar_transparent" format="boolean" />
    </declare-styleable>

    <declare-styleable name="ExpandTextView">
        <attr name="collapsed" format="boolean" />
        <attr name="collapsedMaxLine" format="integer" />
        <attr name="collapsedColor" format="color" />
        <attr name="collapsedUnderLine" format="boolean" />
    </declare-styleable>

    <declare-styleable name="ShadowLayout">
        <!-- 1、关于阴影 -->
        <!--阴影：是否要显示阴影，可能有只想用shape和selector功能，不想要阴影-->
        <attr name="shadowHidden" format="boolean" />
        <!--阴影：阴影颜色-->
        <attr name="shadowColor" format="color" />
        <!--阴影：阴影的扩散范围(也可以理解为扩散程度)-->
        <attr name="shadowLimit" format="dimension" />
        <!--控件区域是否对称，默认是对称。不对称的话，那么控件区域随着阴影区域走-->
        <attr name="shadowSymmetry" format="boolean" />
        <!--阴影：x轴的偏移量-->
        <attr name="shadowOffsetX" format="dimension" />
        <!--阴影：y轴的偏移量-->
        <attr name="shadowOffsetY" format="dimension" />
        <!--阴影：左边是否隐藏阴影-->
        <attr name="shadowHiddenLeft" format="boolean" />
        <!--阴影：右边是否隐藏阴影-->
        <attr name="shadowHiddenRight" format="boolean" />
        <!--阴影：上边是否隐藏阴影-->
        <attr name="shadowHiddenTop" format="boolean" />
        <!--阴影：下面是否隐藏阴影-->
        <attr name="shadowHiddenBottom" format="boolean" />

        <!-- 2、关于圆角 -->
        <!--圆角：统一大小，其中包括了阴影，shape、背景图、stroke边框圆角-->
        <attr name="shadowCornerRadius" format="dimension" />
        <!--圆角：左上圆角。设置后会忽略hl_cornerRadius的值-->
        <attr name="shadowCornerRadius_leftTop" format="dimension" />
        <!--圆角：右上圆角。同上-->
        <attr name="shadowCornerRadius_rightTop" format="dimension" />
        <!--圆角：左下圆角。同上-->
        <attr name="shadowCornerRadius_leftBottom" format="dimension" />
        <!--圆角：右下圆角。同上-->
        <attr name="shadowCornerRadius_rightBottom" format="dimension" />

        <!-- 3、关于shape及selector -->
        <!-- shape及selector样式：pressed、sectored -->
        <attr name="shadowShapeMode">
            <enum name="shadowPressed" value="1" />
            <enum name="shadowSelected" value="2" />
            <enum name="shadowRipple" value="3" />
        </attr>

        <!-- 为false的默认背景，可单独使用-->
        <attr name="shadowLayoutBackground" format="reference|color" />
        <!-- 为true的默认背景，必须搭配hl_layoutBackground属性使用。否则报错-->
        <attr name="shadowLayoutBackgroundPressed" format="reference|color" />

        <!-- stroke样式，stroke的宽度-->
        <attr name="shadowStrokeWith" format="dimension" />
        <!-- 为false时，默认的stoke颜色值，可单独使用-->
        <attr name="shadowStrokeColor" format="color" />
        <!-- 为true时，默认的stoke颜色值，必须搭配hl_strokeColor属性使用。否则报错-->
        <attr name="shadowStrokeColor_true" format="color" />

        <!-- 这里最终舍弃了系统clickable的方案，因为系统系统主动设置setOnClickListener后，会主动设置成true,所以这里采用自定义 -->
        <attr name="clickable" format="boolean" />
        <!-- hl_clickable为false时，要展示的图片或颜色-->
        <attr name="shadowLayoutBackground_clickFalse" format="reference|color" />

        <!-- 4、关于填充的渐变色 -->
        <!-- 要知道，如果设置了渐变色。那么将以渐变色为主。hl_layoutBackground属性将无效 -->
        <!-- 渐变的起始颜色 -->
        <attr name="shadowStartColor" format="color" />
        <!-- 渐变的中间颜色 -->
        <attr name="shadowCenterColor" format="color" />
        <!-- 渐变的终止颜色 -->
        <attr name="shadowEndColor" format="color" />
        <!-- 渐变角度，默认值为0-->
        <attr name="shadowAngle" format="integer" />

        <!-- 5、绑定一个TextView，在点击按钮时，有可能有颜色变化 -->
        <attr name="shadowBindTextView" format="reference" />
        <attr name="shadowTextColor" format="color" />
        <attr name="shadowTextColor_true" format="color" />
        <attr name="shadowText" format="string" />
        <attr name="shadowText_true" format="string" />
    </declare-styleable>
    <declare-styleable name="XRefreshLayout">
        <attr name="hasRecyclerView" format="boolean"/>
        <attr name="isNestedScrollingEnabled" format="boolean"/>
    </declare-styleable>
</resources>