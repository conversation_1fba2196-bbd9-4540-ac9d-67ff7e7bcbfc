<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.AndroidCoreLibraryExample" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Customize your light theme here. -->
        <!-- <item name="colorPrimary">@color/my_light_primary</item> -->
    </style>

    <style name="Theme.AndroidCoreLibraryExample" parent="Base.Theme.AndroidCoreLibraryExample" />

    <style name="BottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomSheetStyle</item>
    </style>

    <style name="BottomSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
    </style>

    <style name="CoordinatorTitleLayoutExpanded" parent="@android:style/TextAppearance.Medium">
        <item name="android:textSize">24sp</item>
        <item name="android:textColor">#333</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.1</item>
    </style>

    <style name="CoordinatorTitleLayoutCollapsed" parent="@android:style/TextAppearance.Medium">
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">#333</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="TransparentDialog" parent="Theme.AppCompat.Dialog.Alert">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@null</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:colorBackground">@null</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
    </style>
</resources>