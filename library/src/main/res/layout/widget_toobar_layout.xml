<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.Toolbar xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="?attr/actionBarSize"
    app:contentInsetStart="0dp"
    tools:layout_height="@dimen/dp_40">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/iv_negative"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="?attr/actionBarSize"
            android:minHeight="?attr/actionBarSize"
            android:scaleType="centerInside"
            android:src="@mipmap/ic_back_white" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerHorizontal="true"
            android:layout_marginStart="80dp"
            android:layout_marginEnd="80dp"
            android:gravity="center"
            android:singleLine="true"
            android:textColor="@color/color_toolbar_text"
            android:textSize="18sp"
            android:textStyle="bold"
            tools:text="标题" />

        <LinearLayout
            android:id="@+id/layout_action"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_action"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="15dp"
                android:gravity="center"
                android:paddingHorizontal="10dp"
                android:paddingVertical="3dp"
                android:textColor="@color/color_toolbar_action_text"
                android:textSize="13sp"
                tools:text="标题" />

            <ImageView
                android:id="@+id/iv_action"
                android:layout_width="wrap_content"
                android:visibility="gone"
                android:layout_height="wrap_content"
                android:contentDescription="@null"
                android:minWidth="?attr/actionBarSize"
                android:minHeight="?attr/actionBarSize"
                android:scaleType="centerInside" />
        </LinearLayout>

        <View
            android:id="@+id/title_line"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_alignParentBottom="true"
            android:background="@color/color_EEEEEE" />
    </RelativeLayout>
</androidx.appcompat.widget.Toolbar>
