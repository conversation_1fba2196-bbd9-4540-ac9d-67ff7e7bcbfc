<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="350dp"
        android:layout_marginTop="80dp"
        android:gravity="center"
        android:orientation="vertical">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_empty"
            android:layout_width="273dp"
            android:layout_height="202dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_empty_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:text="@string/tip_no_content"
            android:textColor="#999999"
            android:textSize="14sp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_empty_action"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="16dp"
            android:gravity="center"
            android:paddingHorizontal="22dp"
            android:paddingVertical="7dp"
            android:textColor="@color/white"
            android:textSize="13sp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_empty_tips"
            tools:text="哦吼！冲鸭！" />
    </LinearLayout>

</LinearLayout>