<?xml version="1.0" encoding="utf-8"?>

<com.google.android.material.appbar.AppBarLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/appbar"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/transparent"
    app:elevation="0dp">

    <com.google.android.material.appbar.CollapsingToolbarLayout
        android:id="@+id/collapsing_toolbar_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:animateLayoutChanges="true"
        android:background="@color/transparent"
        app:collapsedTitleTextAppearance="@style/CoordinatorTitleLayoutCollapsed"
        app:contentScrim="@color/transparent"
        app:expandedTitleMarginStart="16dp"
        app:expandedTitleTextAppearance="@style/CoordinatorTitleLayoutExpanded"
        app:layout_scrollFlags="scroll|exitUntilCollapsed|enterAlwaysCollapsed">

        <View
            android:id="@+id/v_expandes_height"
            android:layout_width="match_parent"
            android:layout_height="104dp"
            android:background="@color/transparent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_english"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:layout_marginStart="18dp"
            android:textColor="#f0f0f0"
            android:textSize="24sp"
            android:textStyle="bold" />

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/transparent"
            app:contentInsetLeft="0dp"
            app:contentInsetStart="0dp"
            app:layout_collapseMode="pin" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_show_img"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginTop="?attr/actionBarSize"
            android:minHeight="60dp" />

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="wrap_content"
            android:layout_height="?attr/actionBarSize"
            android:gravity="center"
            app:layout_collapseMode="pin">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_left_button"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginStart="16dp"
                android:visibility="visible" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="wrap_content"
            android:layout_height="?attr/actionBarSize"
            android:layout_gravity="end"
            android:gravity="center"
            app:layout_collapseMode="pin">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_right_button"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:visibility="visible" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="wrap_content"
            android:layout_height="?attr/actionBarSize"
            android:layout_gravity="end"
            android:gravity="center"
            app:layout_collapseMode="pin">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_right_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:textColor="@color/color_333333"
                android:textSize="14sp" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <!--       <androidx.appcompat.widget.LinearLayoutCompat
                   android:layout_width="wrap_content"
                   android:layout_height="wrap_content"
                   android:layout_gravity="end"
                   android:layout_marginTop="?attr/actionBarSize"
                   android:gravity="center"
                   app:layout_collapseMode="pin">

                   <androidx.appcompat.widget.AppCompatTextView
                       android:id="@+id/tv_cancel"
                       android:layout_width="wrap_content"
                       android:layout_height="wrap_content"
                       android:layout_marginTop="26dp"
                       android:layout_marginEnd="16dp"
                       android:text="取消"
                       android:textColor="#6577ff"
                       android:textSize="14sp"
                       android:textStyle="bold"
                       android:visibility="gone" />
               </androidx.appcompat.widget.LinearLayoutCompat>-->

        <View
            android:id="@+id/v_underline"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="?attr/actionBarSize"
            android:background="@color/color_EEEEEE"
            android:visibility="invisible"
            app:layout_collapseMode="pin" />
    </com.google.android.material.appbar.CollapsingToolbarLayout>
</com.google.android.material.appbar.AppBarLayout>