<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_gravity="center"
        android:background="@drawable/shape_bg_loading"
        android:gravity="center"
        android:orientation="vertical">

        <ProgressBar
            android:id="@+id/progress"
            style="@android:style/Widget.ProgressBar.Small"
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:indeterminateDrawable="@drawable/shape_progress" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingLeft="12dp"
            android:paddingRight="12dp"
            android:text="loading..."
            android:textColor="#EEEEEE"
            android:textSize="11sp"
            android:visibility="gone" />
    </LinearLayout>

</FrameLayout>