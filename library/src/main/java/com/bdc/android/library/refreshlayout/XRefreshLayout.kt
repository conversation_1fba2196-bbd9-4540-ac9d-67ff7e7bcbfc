package com.bdc.android.library.refreshlayout

import android.content.Context
import android.util.AttributeSet
import android.view.ViewGroup
import androidx.core.view.children
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.DslAdapterStatusItem
import com.angcyo.dsladapter.DslLoadMoreItem.Companion.LOAD_MORE_ERROR
import com.angcyo.dsladapter.DslLoadMoreItem.Companion.LOAD_MORE_NORMAL
import com.angcyo.dsladapter.DslLoadMoreItem.Companion.LOAD_MORE_NO_MORE
import com.bdc.android.library.R
import com.google.android.material.appbar.AppBarLayout

/**
 * Copyright (C), 2019-2021, 中传互动（湖北）信息技术有限公司
 * Author: He<PERSON><PERSON>
 * Date: 2021/8/9 17:09
 * Description: 刷新控件封装
 */
class XRefreshLayout : SwipeRefreshLayout {

    var hasRecyclerView: Boolean = true
    lateinit var recyclerView: XRecyclerView
    var isRecyclerViewNestedScrollingEnabled: Boolean = true
    private var listener: OnRefreshListener? = null

    constructor(context: Context) : super(context)
    constructor(context: Context, attributeSet: AttributeSet) : super(context, attributeSet) {
        val typedArray = context.obtainStyledAttributes(attributeSet, R.styleable.XRefreshLayout)
        hasRecyclerView = typedArray.getBoolean(R.styleable.XRefreshLayout_hasRecyclerView, true)
        isRecyclerViewNestedScrollingEnabled =
            typedArray.getBoolean(R.styleable.XRefreshLayout_isNestedScrollingEnabled, true)
        typedArray.recycle()

        if (hasRecyclerView) {
            initRecyclerView()
        }
    }

    init {
        setColorSchemeResources(R.color.colorPrimary)
    }

    private fun initRecyclerView() {
        recyclerView = XRecyclerView(context)
        if (!isRecyclerViewNestedScrollingEnabled) recyclerView.isNestedScrollingEnabled = false
        addView(recyclerView)
        setOnRefreshListener(OnRefreshListener { onRefresh() })
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        setEnabled(this)
    }

    fun setEnabled(viewGroup: ViewGroup) {
        viewGroup.children.iterator().forEach {
            when (it) {
                is AppBarLayout -> {
                    it.addOnOffsetChangedListener(
                        AppBarLayout.OnOffsetChangedListener { _, verticalOffset ->
                            isEnabled = verticalOffset >= 0
                        })
                }

                is ViewGroup -> {
                    setEnabled(it)
                }

                else -> {
                }
            }
        }
    }

    fun setupPageStateItem(item: DslAdapterStatusItem) {
        if (hasRecyclerView) {
            recyclerView.dslAdapter.dslAdapterStatusItem = item
        }
    }

    fun hasItemDecoration() {
        recyclerView.hasItemDecoration()
    }

    fun onRefreshOnly() {
        post {
            isRefreshing = true
        }
    }

    fun onRefresh(hasAnimation: Boolean = true) {
        post {
            isRefreshing = hasAnimation
            if (hasRecyclerView) {
                recyclerView.onRefresh()
            } else {
                listener?.onRefresh()
            }
        }
    }

    fun onFinishRefresh(delay: Long = 400) {
        if (isRefreshing) postDelayed({ isRefreshing = false }, delay)
    }

    override fun setOnRefreshListener(l: OnRefreshListener?) {
        super.setOnRefreshListener(l)
        listener = l
    }

    fun setOnRefreshListener(l: (Boolean, Int) -> Unit) {
        if (hasRecyclerView) {
            recyclerView.setOnRefreshListener(l)
        }
    }

    fun clearItems() {
        if (hasRecyclerView) {
            recyclerView.clearItems()
        }
    }

    fun clearHeaderItems() {
        if (hasRecyclerView) {
            recyclerView.clearHeaderItems()
        }
    }

    fun clearAllItems() {
        if (hasRecyclerView) {
            recyclerView.clearAllItems()
        }
    }

    inline fun <reified T : DslAdapterItem> appendHeader(item: T) {
        if (hasRecyclerView) {
            recyclerView.appendHeader(item)
        }
    }

    inline fun <reified T : DslAdapterItem> appendHeader(index: Int, item: T) {
        if (hasRecyclerView) {
            recyclerView.appendHeader(index, item)
        }
    }

    inline fun <reified T : DslAdapterItem> append(item: T) {
        onFinishRefresh()
        if (hasRecyclerView) {
            recyclerView.append(item)
        }
    }

    inline fun <reified T : DslAdapterItem> append(index: Int, item: T) {
        if (hasRecyclerView) {
            recyclerView.append(index, item)
        }
    }

    inline fun <reified T : DslAdapterItem> append(
        index: Int = -1,
        items: List<Any>,
        noinline initItem: T.() -> Unit = {}
    ) {
        if (hasRecyclerView) {
            recyclerView.append(index, items, initItem)
        }
    }

    inline fun <reified T : DslAdapterItem> append(
        items: List<Any>?,
        crossinline initItem: T.(data: Any?) -> Unit = {}
    ) {
        append(false, items, initItem)
    }

    inline fun <reified T : DslAdapterItem> append(
        refresh: Boolean = false,
        items: List<Any>?,
        crossinline initItem: T.(data: Any?) -> Unit = {}
    ) {
        onFinishRefresh()
        if (hasRecyclerView) {
            recyclerView.append(refresh, items, initItem)
        }
    }

    inline fun <reified T : DslAdapterItem> append(
        refresh: Boolean = false,
        items: List<Any>?,
        isNoMore: Boolean,
        crossinline initItem: T.(data: Any?) -> Unit = {}
    ) {
        onFinishRefresh()
        if (hasRecyclerView) {
            recyclerView.append(refresh, items, initItem)
            if (isNoMore) {
                recyclerView.dslAdapter.setLoadMore(LOAD_MORE_NO_MORE)
            } else {
                recyclerView.dslAdapter.setLoadMore(LOAD_MORE_NORMAL)
            }
        }
    }

    fun loadFailedWithMore(){
        onFinishRefresh()
        recyclerView.dslAdapter.setLoadMore(
            LOAD_MORE_ERROR
        )
    }

    fun resetAdapterStatus() {
        recyclerView.setAdapterStatus(DslAdapterStatusItem.ADAPTER_STATUS_NONE)
    }

    fun setAdapterStatus(state: Int = DslAdapterStatusItem.ADAPTER_STATUS_EMPTY/*默认空*/) {
        recyclerView.setAdapterStatus(state)
    }
}