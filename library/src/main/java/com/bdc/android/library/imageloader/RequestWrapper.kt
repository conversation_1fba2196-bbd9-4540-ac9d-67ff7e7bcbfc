package com.bdc.android.library.imageloader

import android.graphics.drawable.Drawable
import android.net.Uri
import android.widget.ImageView
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation
import jp.wasabeef.glide.transformations.RoundedCornersTransformation

import java.io.File

/**
 * Copyright (C), 2019-2020, 中传互动（湖北）信息技术有限公司
 * Author: HeChao
 * Date: 2020/10/10 16:26
 * Description:加载配置参数包装
 */
class RequestWrapper private constructor(
    var url: String?,
    var resId: Int,
    var file: File?,
    var uri: Uri?,
    var errorId: Int,
    var errorDrawable: Drawable?,
    var placeholderId: Int,
    var placeDrawable: Drawable?,
    var crossFade: Boolean,
    var skipMemoryCache: Boolean,
    var diskCacheStrategy:DiskCacheStrategy,
    var width: Int,
    var height: Int,
    var blur: Int,
    var cornerType: RoundedCornersTransformation.CornerType = RoundedCornersTransformation.CornerType.ALL,/*图片圆角方式*/
    var radius: Int,
    var scaleType: BitmapTransformation?,
    var isCircle: Boolean,
    var isGif: Boolean,
    var isAvatar: Boolean,
    var isIcon: Boolean,
    var isCover: Boolean,
    var isRandomColor: Boolean,
    var imageView: ImageView?
) {
    companion object {
        fun obtain(
            url: String?,
            resId: Int,
            file: File?,
            uri: Uri?,
            errorId: Int,
            errorDrawable: Drawable?,
            placeholderId: Int,
            placeholderDrawable: Drawable?,
            crossFade: Boolean = true,
            skipMemoryCache: Boolean,
            diskCacheStrategy:DiskCacheStrategy,
            width: Int,
            height: Int,
            blur: Int,
            cornerType: RoundedCornersTransformation.CornerType,
            radius: Int,
            scaleType: BitmapTransformation?,
            isCircle: Boolean,
            isGif: Boolean,
            isAvatar: Boolean,
            isIcon: Boolean,
            isCover: Boolean,
            isRandomColor: Boolean,
            imageView: ImageView?
        ): RequestWrapper {
            return RequestWrapper(
                url,
                resId,
                file,
                uri,
                errorId,
                errorDrawable,
                placeholderId,
                placeholderDrawable,
                crossFade,
                skipMemoryCache,
                diskCacheStrategy,
                width,
                height,
                blur,
                cornerType,
                radius,
                scaleType,
                isCircle,
                isGif,
                isAvatar,
                isIcon,
                isCover,
                isRandomColor,
                imageView
            )
        }
    }
}
