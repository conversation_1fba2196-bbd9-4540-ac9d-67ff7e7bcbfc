package com.bdc.android.library.widget

import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.appcompat.app.AppCompatActivity
import androidx.coordinatorlayout.widget.CoordinatorLayout
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.appbar.CollapsingToolbarLayout
import com.gyf.immersionbar.ImmersionBar
import com.bdc.android.library.R
import com.bdc.android.library.extension.*
import com.bdc.android.library.utils.*
import java.lang.Math.abs

/**
 * author : jql
 * time   : 2021/11/1 15:07
 * desc   : 上滑效果居中标题layout
 * 滚动控件 如Rv Sv 在xml标签添加   app:layout_behavior="@string/appbar_scrolling_view_behavior"
 * 自定义属性详见 R.styleable.CoordinatorTitleLayout
 */
class CoordinatorTitleLayout : CoordinatorLayout {
    private var appBarLayout: AppBarLayout? = null

    //当前是否是透明状态栏
    private var isTransparentStatusBar = false

    //是否需要标题栏展开的时候设置透明状态栏 收起的时候设置白色状态栏
    private var needTransparentStatusBar = false

    //收缩监听 true  完全展开  false 正在收起
    private var onExpandedListener: ((Boolean) -> Unit) = { }

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        appBarLayout = LayoutInflater.from(context)
            .inflate(R.layout.widget_coordinator_appbar_layout, this, true) as AppBarLayout
        appBarLayout?.addOnOffsetChangedListener { _, verticalOffset ->
            /*  //渐变 暂无需求
            val toolbarHeight = appBarLayout.totalScrollRange
             mBinding.layoutTop.setBackgroundColor(
                  changeAlpha(
                      ContextCompat.getColor(requireContext(), R.color.white),
                      abs(verticalOffset * 1.0f) / toolbarHeight
                  )
              )*/
            when {
                verticalOffset == 0 -> { //展开
                    onExpandedListener(true)
                    appBarLayout?.findViewById<View>(R.id.iv_show_img)?.makeVisible()
                    appBarLayout?.findViewById<View>(R.id.tv_english)?.makeVisible()
                    if (needTransparentStatusBar && !isTransparentStatusBar) {
                        appBarLayout?.setBackgroundColor(Color.TRANSPARENT)
                        isTransparentStatusBar = true
                    }
                }

                verticalOffset < 0 -> { //收缩
                    if (needTransparentStatusBar && isTransparentStatusBar) {
                        appBarLayout?.setBackgroundColor(Color.WHITE)
                        isTransparentStatusBar = false
                    }
                    onExpandedListener(false)
                    appBarLayout?.findViewById<View>(R.id.iv_show_img)?.makeInVisible()
                    appBarLayout?.findViewById<View>(R.id.tv_english)?.makeInVisible()
                }
            }
            if (abs(verticalOffset) >= getActionBarSize()) {
                appBarLayout?.findViewById<View>(R.id.v_underline)?.makeVisible()
            } else {
                appBarLayout?.findViewById<View>(R.id.v_underline)?.makeInVisible()
            }
        }
        if (!isTransparentStatusBar) {
            ImmersionBar.with(context as AppCompatActivity).transparentStatusBar().init()
            isTransparentStatusBar = true
        }

        appBarLayout?.findViewById<CollapsingToolbarLayout>(R.id.collapsing_toolbar_layout)?.apply {
            collapsedTitleGravity = Gravity.CENTER
            expandedTitleGravity = Gravity.TOP
            expandedTitleMarginTop = getActionBarSize() + 14f.dp2px()
            setExpandedTitleTypeface(Typeface.DEFAULT_BOLD)
        }
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.CoordinatorTitleLayout)
        setLeftButtonIcon(
            typedArray.getResourceId(
                R.styleable.CoordinatorTitleLayout_left_icon, R.drawable.ic_default_back_black_arrow
            )
        )
        setTitle(typedArray.getString(R.styleable.CoordinatorTitleLayout_title))
        setEnglishTitle(typedArray.getString(R.styleable.CoordinatorTitleLayout_title_english))
        setEnglishTitleColor(
            typedArray.getColor(
                R.styleable.CoordinatorTitleLayout_title_english_color, Color.parseColor("#f0f0f0")
            )
        )
        setTitleColor(
            typedArray.getColor(
                R.styleable.CoordinatorTitleLayout_title_color, Color.parseColor("#333333")
            )
        )
        setImage(
            typedArray.getResourceId(
                R.styleable.CoordinatorTitleLayout_image, Color.TRANSPARENT
            )
        )
        setRightButtonIcon(
            typedArray.getResourceId(
                R.styleable.CoordinatorTitleLayout_right_ic, Color.TRANSPARENT
            )
        )
        setRightText(typedArray.getString(R.styleable.CoordinatorTitleLayout_right_text))
        setRightTextSize(
            typedArray.getDimensionPixelSize(
                R.styleable.CoordinatorTitleLayout_right_text_size, 14F.dp2px()
            )
        )
        setRightTextColor(
            typedArray.getColor(
                R.styleable.CoordinatorTitleLayout_right_text_color, Color.BLACK
            )
        )
        setRightTextBold(
            typedArray.getBoolean(
                R.styleable.CoordinatorTitleLayout_right_text_bold, false
            )
        )
        setImageRightMargin(
            typedArray.getDimensionPixelSize(
                R.styleable.CoordinatorTitleLayout_image_right_margin, 16f.dp2px()
            )
        )
        setExpanded(typedArray.getBoolean(R.styleable.CoordinatorTitleLayout_expanded, true))
        needTransparentStatusBar = typedArray.getBoolean(
            R.styleable.CoordinatorTitleLayout_need_statusbar_transparent, false
        )
        if (needTransparentStatusBar) {
            appBarLayout?.let {
                ImmersionBarUtil.setStatusTransparentBarFont(context, true, it)
            }

        }
        typedArray.recycle()
    }

    /**
     * 设置左侧icon
     */
    fun setLeftButtonIcon(@DrawableRes icon: Int) {
        appBarLayout?.findViewById<ImageView>(R.id.iv_left_button)?.apply {
            setImageResource(icon)
            click {
                if (context is AppCompatActivity) {
                    (context as AppCompatActivity).onBackPressed()
                }
            }
        }
    }

    /**
     * 设置右侧icon
     */
    fun setRightButtonIcon(@DrawableRes icon: Int) {
        appBarLayout?.findViewById<ImageView>(R.id.iv_right_button)?.apply {
            setImageResource(icon)
            makeVisible()
        }
    }

    /**
     * 设置右侧按钮点击事件
     */
    fun setRightIconClick(block: (v: View) -> Unit) =
        appBarLayout?.findViewById<ImageView>(R.id.iv_right_button)
            ?.setOnClickListener { block(it) }


    /**
     * 设置收缩状态 true：展开    false：收缩
     */
    fun setExpanded(expanded: Boolean) {
        appBarLayout?.setExpanded(expanded)
        if (!expanded) appBarLayout?.findViewById<View>(R.id.v_underline)?.makeVisible()
    }

    /**
     * 设置标题中文
     */
    fun setTitle(title: String?) {
        appBarLayout?.findViewById<CollapsingToolbarLayout>(R.id.collapsing_toolbar_layout)?.title =
            title
    }

    /**
     * 设置标题英文
     */
    fun setEnglishTitle(english: String?) {
        appBarLayout?.findViewById<TextView>(R.id.tv_english)?.apply {
            text = english
            layoutParams = (layoutParams as CollapsingToolbarLayout.LayoutParams).apply {
                setMargins(18f.dp2px(), getActionBarSize() + 20f.dp2px(), 0, 0)
            }
        }
    }

    /**
     * 设置标题英文颜色
     */
    fun setEnglishTitleColor(color: Int) {
        appBarLayout?.findViewById<TextView>(R.id.tv_english)?.setTextColor(color)
    }

    /**
     * 设置标题颜色
     */
    fun setTitleColor(color: Int) {
        appBarLayout?.findViewById<CollapsingToolbarLayout>(R.id.collapsing_toolbar_layout)?.apply {
            setCollapsedTitleTextColor(color)
            setExpandedTitleColor(color)
        }
    }

    fun setImage(@DrawableRes img: Int) {
        appBarLayout?.findViewById<ImageView>(R.id.iv_show_img)?.setImageResource(img)
    }

    /**
     * 设置右下角图右边距 默认16dp 不需要就设置0
     */
    fun setImageRightMargin(rightMargin: Int) {
        appBarLayout?.findViewById<ImageView>(R.id.iv_show_img)?.apply {
            layoutParams = (layoutParams as CollapsingToolbarLayout.LayoutParams).apply {
                setMargins(0, getActionBarSize(), rightMargin, 0)
            }
        }
    }

    /**
     * 设置右下角图距上面间距
     */
    fun setImageTopMargin(topMargin: Int) {
        appBarLayout?.findViewById<ImageView>(R.id.iv_show_img)?.apply {
            layoutParams = (layoutParams as CollapsingToolbarLayout.LayoutParams).apply {
                setMargins(0, topMargin, this.rightMargin, 0)
            }
        }
    }

    fun setRightText(text: String?) {
        appBarLayout?.findViewById<TextView>(R.id.tv_right_button)?.text = text
    }

    fun setRightTextSize(spSize: Int) {
        appBarLayout?.findViewById<TextView>(R.id.tv_right_button)?.textSize =
            spSize.toFloat().px2sp()
    }

    fun setRightTextColor(color: Int) {
        appBarLayout?.findViewById<TextView>(R.id.tv_right_button)?.setTextColor(color)
    }

    fun setRightTextBold(bold: Boolean) {
        appBarLayout?.findViewById<TextView>(R.id.tv_right_button)?.typeface =
            if (bold) Typeface.DEFAULT_BOLD else Typeface.DEFAULT
    }

    fun showRightText(show: Boolean) {
        appBarLayout?.findViewById<TextView>(R.id.tv_right_button)?.apply {
            if (show) makeVisible()
            else makeGone()
        }
    }

    /**
     * 设置右侧按钮点击事件
     */
    fun setRightTextClick(block: (v: View) -> Unit) =
        appBarLayout?.findViewById<TextView>(R.id.tv_right_button)?.setOnClickListener { block(it) }

    /**
     * 收缩监听 true  完全展开  false 正在收起
     */
    fun onExpandedListener(block: (Boolean) -> Unit) {
        onExpandedListener = block
    }

    fun getActionBarSize() = run {
        val typedValue = TypedValue()
        if (context.theme.resolveAttribute(
                android.R.attr.actionBarSize,
                typedValue,
                true
            )
        ) TypedValue.complexToDimensionPixelSize(typedValue.data, resources.displayMetrics)
        else 0
    }

    fun setScrollFlags(flags: Int) {
        val layoutParams =
            appBarLayout?.findViewById<CollapsingToolbarLayout>(R.id.collapsing_toolbar_layout)?.layoutParams as AppBarLayout.LayoutParams
        layoutParams.scrollFlags = flags
    }
}