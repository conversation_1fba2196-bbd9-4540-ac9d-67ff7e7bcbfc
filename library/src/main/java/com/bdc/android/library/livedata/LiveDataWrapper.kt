package com.bdc.android.library.livedata

/**
 * Copyright (C), 2020-2021, 中传互动（湖北）信息技术有限公司
 * @Author: pym
 * @Date: 2021/6/15 9:52
 * @Description:
 */
class LiveDataWrapper<T> {
    var hasException = false
    var isComplete = false
    var value: T?

    internal constructor(value: T) {
        this.value = value
    }

    internal constructor(value: T?, hasException: Boolean) {
        this.value = value
        this.hasException = hasException
    }

    constructor(isComplete: <PERSON>olean, value: T?) {
        this.isComplete = isComplete
        this.value = value
    }

}