package com.bdc.android.library.ktnet

import com.bdc.android.library.ktnet.coroutines.Await
import com.bdc.android.library.ktnet.coroutines.AwaitImpl
import com.bdc.android.library.ktnet.interception.NetWorkFailedInterceptor
import retrofit2.Call
import java.lang.reflect.Type

/**
 *
 * @Author： Lxf
 * @Time： 2022/2/17 3:48 下午
 * @description： 
 */
fun <T> Call<T>.toResponse() = toParser()

fun <T> Call<T>.toParser(): Await<T> = AwaitImpl(this)

fun isAwait(type: Type) : Boolean {
    return type == Await::class.java
}

var netWorkFailedInterceptor: NetWorkFailedInterceptor? = null