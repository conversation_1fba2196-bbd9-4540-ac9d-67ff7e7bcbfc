package com.bdc.android.library.utils

import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.widget.Toast


/**
 * @PackageName : com.bdc.android.library.utils
 * <AUTHOR> hechao
 * @Date :   2019-10-30 23:50
 */
object ToastUtil {
    @JvmStatic
    fun show(message: String?) {
//        if (TextUtils.isEmpty(message)) return
//        try {
//            // Make sure we're on the main thread
//            if (Looper.myLooper() == Looper.getMainLooper()) {
//                Toast.makeText(
//                    AppManager.getApplication(), message, Toast.LENGTH_SHORT
//                ).show()
//            } else {
//                // Post to main thread if we're not on it
//                Handler(Looper.getMainLooper()).post {
//                    Toast.makeText(
//                        AppManager.getApplication(), message, Toast.LENGTH_SHORT
//                    ).show()
//                }
//            }
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }
        ToastManager.show(message)
    }
}

object ToastManager {
    private var currentToast: Toast? = null

    @JvmStatic
    fun show(message: String?) {
        if (TextUtils.isEmpty(message)) return

        try {
            // 取消之前的Toast
            currentToast?.cancel()

            // 创建新的Toast
            currentToast = Toast.makeText(
                AppManager.getApplication(), message, Toast.LENGTH_LONG
            ).apply {
                if (Looper.myLooper() == Looper.getMainLooper()) {
                    show()
                } else {
                    Handler(Looper.getMainLooper()).post {
                        show()
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun cancelAll() {
        currentToast?.cancel()
        currentToast = null
    }
}