package com.bdc.android.library.imageloader

import android.content.Context
import android.net.Uri
import android.view.View
import androidx.annotation.CheckResult
import androidx.annotation.DrawableRes
import androidx.fragment.app.Fragment
import java.io.File

/**
 * Copyright (C), 2019-2020, 中传互动（湖北）信息技术有限公司
 * Author: He<PERSON>hao
 * Date: 2020/10/10 16:26
 * Description:加载配置类
 */
class RequestManager(private val context: Context) {

    constructor(fragment: Fragment) : this(fragment.requireContext())

    constructor(view: View) : this(view.context)

    @CheckResult
    fun load(url: String?): RequestBuilder {
        return RequestBuilder(context, getImageLoader()).url(url)
    }

    @CheckResult
    fun load(@DrawableRes resId: Int): RequestBuilder {
        return RequestBuilder(context, getImageLoader()).resId(resId)
    }

    @CheckResult
    fun load(file: File): RequestBuilder {
        return RequestBuilder(context, getImageLoader()).file(file)
    }

    @CheckResult
    fun load(uri: Uri): RequestBuilder {
        return RequestBuilder(context, getImageLoader()).uri(uri)
    }

    private fun getImageLoader(): ILoader {
        return GlideImageLoader()
    }
}