package com.bdc.android.library.utils

import java.math.RoundingMode
import java.text.DecimalFormat

/**
 * @PackageName : com.waiting.fm.utils
 * <AUTHOR> hechao
 * @Date :   2019-11-29 00:57
 */
object DecimalUtil {

    private var decimalFormat: DecimalFormat = DecimalFormat("0.00")
    private var decimalFormat1 = DecimalFormat("0")

    @JvmStatic
    fun format(value: Double): String {
        return decimalFormat.format(value)
    }

    @JvmStatic
    fun format(value: Int): String {
        return decimalFormat.format(value)
    }

    @JvmStatic
    fun formatWithDecimal(value: Double): String {
        return decimalFormat1.format(value)
    }

    @JvmStatic
    fun formatProgress(value: Float): String {
        return DecimalFormat("0.0").format(value)
    }

    @JvmStatic
    fun format(src: String): Float {
        decimalFormat1.roundingMode = RoundingMode.HALF_UP
        return decimalFormat1.format(src.toDouble()).toFloat()
    }

    @JvmStatic
    fun formatDecimal(value: Double): String {
        val d = (value * 100).toInt()
        return if (d % 100 == 0) {
            decimalFormat1.format(value)
        } else {
            decimalFormat.format(value)
        }

    }
}
