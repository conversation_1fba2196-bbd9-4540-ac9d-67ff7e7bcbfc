package com.bdc.android.library.widget

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import com.bdc.android.library.R

/**
 * Copyright (C), 2020-2021, 中传互动（湖北）信息技术有限公司
 * @Author: pym
 * @Date: 2021/10/9 16:37
 * @Description:
 */
class LoadingDialog : Dialog {
    private var mCanceledOnTouchOutside: Boolean = true
    private var mTitle: String = ""

    constructor(context: Context, canceledOnTouchOutside: Boolean, title: String) : super(
        context,
        R.style.TransparentDialog
    ) {
        mCanceledOnTouchOutside = canceledOnTouchOutside
        mTitle = title
    }

    constructor(context: Context, themeResId: Int) : super(context, themeResId)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val view: View = LayoutInflater.from(context).inflate(R.layout.dialog_loading, null)
        setContentView(view)
        clearStatusBar()
        autoSetStatusBarMode()
        view.findViewById<TextView>(R.id.tv_title)?.apply {
            this.text = mTitle
            if (mTitle.isBlank()) {
                this.visibility = View.GONE
            } else {
                this.visibility = View.VISIBLE
            }
        }
        view.setOnClickListener { if (mCanceledOnTouchOutside) dismiss() }
    }

    private fun clearStatusBar() {
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        window?.decorView?.setPadding(0, 0, 0, 0)
        window?.setFlags(
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        )
        window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        window?.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT
        )
        //设置全屏
        val option = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
        window?.decorView?.systemUiVisibility = option
        //remove status bar shadow
        setWindowFlag(
            WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS or WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION,
            false
        )
        window?.statusBarColor = Color.TRANSPARENT
        window?.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS) //尝试兼容部分手机上的状态栏空白问题
    }

    private fun setWindowFlag(bits: Int, on: Boolean) {
        window?.apply {
            val winParams = attributes
            if (on) {
                winParams.flags = winParams.flags or bits
            } else {
                winParams.flags = winParams.flags and bits.inv()
            }
            attributes = winParams
        }

    }

    /**
     * 是否是亮色调状态栏
     *
     * @return
     */
    fun autoSetStatusBarMode() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val decorView = window?.decorView
            decorView?.apply {
                var vis = decorView?.systemUiVisibility
                val isLightMode: Boolean = isActivityStatusBarLightMode()
                vis = if (isLightMode) {
                    vis?.or(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR)
                } else {
                    vis?.and(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv())
                }
                if (vis != null) {
                    decorView?.systemUiVisibility = vis
                }
            }
        }
    }

    fun isActivityStatusBarLightMode(): Boolean {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
//            val decorView = window?.decorView
//            val vis = decorView?.systemUiVisibility
//            if (vis != null) {
//                return (vis and View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR) != 0
//            }
        }
        return true
    }

}

fun createLoadingDialog(
    context: Context,
    canceledOnTouchOutside: Boolean = true,
    title: String = ""
): LoadingDialog {
    return LoadingDialog(context, canceledOnTouchOutside, title)
}