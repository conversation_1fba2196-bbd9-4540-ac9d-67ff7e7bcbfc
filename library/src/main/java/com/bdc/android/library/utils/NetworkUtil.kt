package com.bdc.android.library.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build


/**
 * @PackageName : com.bdc.android.library.utils
 * <AUTHOR> hechao
 * @Date :   2019-11-29 01:24
 */
object NetworkUtil {

    fun isNetworkConnected(context: Context?): <PERSON><PERSON><PERSON> {
        return (context?.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager)
            .let {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    it.activeNetwork != null
                } else {
                    it.activeNetworkInfo?.isConnected
                }
            } ?: false
    }

    private fun isNetworkValidated(context: Context?): Bo<PERSON>an {
        return (context?.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager).let {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                it.getNetworkCapabilities(
                    it.activeNetwork
                )?.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
            } else {
                it.activeNetworkInfo?.isAvailable
            }
        } ?: false
    }

    fun isWifiConnected(context: Context?): Boolean {
        return (context?.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager).let {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                it.getNetworkCapabilities(
                    it.activeNetwork
                )?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
            } else {
                it.activeNetworkInfo?.type == ConnectivityManager.TYPE_WIFI
            }
        } ?: false
    }

    fun isMobileConnected(context: Context?): Boolean {
        return (context?.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager).let {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                it.getNetworkCapabilities(
                    it.activeNetwork
                )?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)
            } else {
                it.activeNetworkInfo?.type == ConnectivityManager.TYPE_MOBILE
            }
        } ?: false
    }
}
