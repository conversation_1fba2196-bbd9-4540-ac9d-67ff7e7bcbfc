package com.bdc.android.library.imageloader

import android.content.Context
import com.bumptech.glide.Glide
import com.bumptech.glide.GlideBuilder
import com.bumptech.glide.Registry
import com.bumptech.glide.load.engine.cache.InternalCacheDiskCacheFactory
import com.bumptech.glide.module.AppGlideModule

/**
 * Copyright (C), 2019-2020, 中传互动（湖北）信息技术有限公司
 * Author: <PERSON><PERSON><PERSON>
 * Date: 2020/10/10 16:26
 * Description:
 */
@com.bumptech.glide.annotation.GlideModule
class GlideCacheModule : AppGlideModule() {
    override fun applyOptions(
        context: Context, builder: GlideBuilder
    ) {
        builder.setDiskCache(InternalCacheDiskCacheFactory(context, 100 * 1024 * 1024))
        super.applyOptions(context, builder)
    }

    override fun registerComponents(context: Context, glide: Glide, registry: Registry) {
    }
}