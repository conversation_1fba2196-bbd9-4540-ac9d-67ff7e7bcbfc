package com.bdc.android.library.utils;

import android.animation.ArgbEvaluator;
import android.animation.ObjectAnimator;
import android.graphics.Color;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;

public class AnimationUtil {
    /**
     * 向下移动显示
     */
    public static TranslateAnimation moveToDown() {
        TranslateAnimation mHiddenAction = new TranslateAnimation(
                Animation.RELATIVE_TO_SELF, 0.0f,
                Animation.RELATIVE_TO_SELF, 0.0f,
                Animation.RELATIVE_TO_SELF, 0.0f,
                Animation.RELATIVE_TO_SELF, 1.0f);
        mHiddenAction.setDuration(500);
        return mHiddenAction;
    }

    /**
     * 从控件的底部移动到控件所在位置
     */
    public static TranslateAnimation moveToTop() {
        TranslateAnimation mHiddenAction = new TranslateAnimation(
                Animation.RELATIVE_TO_SELF, 0.0f,
                Animation.RELATIVE_TO_SELF, 0.0f,
                Animation.RELATIVE_TO_SELF, 1.0f,
                Animation.RELATIVE_TO_SELF, 0.0f);
        mHiddenAction.setDuration(500);
        return mHiddenAction;
    }


    /**
     * 由白变透明
     */
    public static ObjectAnimator animatorOpen(View v) {
        ObjectAnimator animatorOpen = ObjectAnimator.ofInt(v, "backgroundColor", Color.WHITE, Color.TRANSPARENT);
        animatorOpen.setDuration(500);
        animatorOpen.setEvaluator(new ArgbEvaluator());
        return animatorOpen;
    }

    /**
     * 由透明变白
     */
    public static ObjectAnimator animatorClose(View v) {
        ObjectAnimator animatorClose = ObjectAnimator.ofInt(v, "backgroundColor", Color.TRANSPARENT, Color.WHITE);
        animatorClose.setDuration(500);
        animatorClose.setEvaluator(new ArgbEvaluator());
        return animatorClose;
    }
}
