package com.bdc.android.library.utils

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Paint
import android.net.Uri
import android.provider.Settings
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.widget.EditText
import android.widget.TextView
import com.angcyo.dsladapter.isListEmpty
import com.bdc.android.library.utils.Logger.i
import com.orhanobut.logger.Logger
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import java.text.DecimalFormat
import java.util.Arrays
import java.util.Locale
import java.util.regex.Pattern

/**
 * Created by lin on 2017/1/9.
 */
object StringUtil {
    /**
     * 是否是手机号
     *
     * @return
     */
    fun isMobile(charSequence: CharSequence?): Boolean {
        if (charSequence == null) return false
        //        return charSequence.toString().matches("0?(13|14|15|18)[0-9]{9}");
        val regex =
            "^((13[0-9])|(14[0-9])|(15([0-9]))|(16[0-9])|(17[0-9])|(18[0-9])|(19[0-9]))\\d{8}$"
        val p = Pattern.compile(regex)
        val m = p.matcher(charSequence.toString())
        return m.matches()
    }

    /**
     * 电话3 4 4格式(即：xxx xxxx xxxx)
     * 电话长度11位数字
     * @param view 输入框
     * @param text 文本
     */
    fun formatMobile344(view: EditText?, text: String?) {
        view?.let {
            val space = ' '
            val indexSpace1 = 3
            val indexSpace2 = 8
            val sb = java.lang.StringBuilder()
            //1.取出所有字符，去掉' '和非法字符
            text?.let {
                for (i in it.indices) {
                    //如果数字数大于11位，去掉后面的数字
                    if (sb.length >= 11) {
                        break
                    }

                    //是否合法字符(0~9)
                    val pattern = Pattern.compile("^[0-9]*$")
                    val matcher = pattern.matcher(text[i].toString())
                    if (it[i] != space && matcher.matches()) {
                        sb.append(text[i])
                    }
                }
            }
            //2.根据长度追加' '
            if (sb.length > indexSpace1) {
                sb.insert(indexSpace1, space)
            }
            if (sb.length > indexSpace2) {
                sb.insert(indexSpace2, space)
            }
            //3.设置文本和光标位置
            if (sb.toString() != text) {
                view.setText(sb.toString())
                view.setSelection(sb.length)
            }
        }
    }

    /**
     * 获得已输入的电话号，不包括空格
     * @param editText 输入控件
     * @return 电话号
     */
    fun getMobileByEditText(editText: EditText?): String {
        return editText?.let {
            val text = editText.text.toString()
            val space = ' '
            val sb = java.lang.StringBuilder()
            for (i in 0 until text.length) {
                if (text[i] != space) {
                    sb.append(text[i])
                }
            }
            sb.toString()
        } ?: ""
    }

    /**
     * 手机号中间加*
     *
     * @param phone
     * @return
     */
    fun mobileEncrypt(phone: String): String {
        return if (!TextUtils.isEmpty(phone) && phone.length == 11) {
            phone.substring(0, 3) + "****" + phone.substring(7)
        } else phone
    }

    fun formatOrderNo(orderNo: String): String {
        if (TextUtils.isEmpty(orderNo)) return orderNo
        val builder = StringBuilder()
        for (i in 0 until orderNo.length) {
            if (i % 4 == 0 && i != orderNo.length - 1) {
                builder.append(" ")
            }
            builder.append(orderNo[i])
        }
        return builder.toString()
    }

    /**
     * 中文匹配
     *
     * @param text
     * @return
     */
    fun isChinese(text: String?): Boolean {
        val p = Pattern.compile("[\u4e00-\u9fa5]+$")
        //        Pattern p = Pattern.compile("(\\d+)\u4e2a\u6587\u4ef6");
        val m = p.matcher(text ?: "")
        return m.matches()
    }

    /**
     * 验证邮箱
     */
    //    public static boolean isEmail(final CharSequence charSequence) {
    //        if (charSequence == null) return false;
    //        return charSequence.toString().matches("^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$");
    //    }
    fun isEmail(email: String?): Boolean {
        val str =
            "^([a-zA-Z0-9_\\-.]+)@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.)|(([a-zA-Z0-9\\-]+\\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(]?)$"
        val p = Pattern.compile(str)
        val m = p.matcher(email ?: "")
        return m.matches()
    }

    /**
     * 获取本地软件版本号名称
     */
    fun getLocalVersionName(context: Context): String {
        var localVersion = ""
        try {
            val packageInfo = context.applicationContext
                .packageManager
                .getPackageInfo(context.packageName, 0)
            localVersion = packageInfo.versionName ?: ""
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        return localVersion
    }

    /**
     * 获取本地软件版本号名称
     */
    fun getLocalVersionCode(context: Context): Int {
        var localVersion = 0
        try {
            val packageInfo = context.applicationContext
                .packageManager
                .getPackageInfo(context.packageName, 0)
            localVersion = packageInfo.versionCode
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        Logger.i("localVersionNo $localVersion")
        return localVersion
    }

    /**
     * 是不是数字
     */
    fun isNumeric(str: String): Boolean {
        return try {
            str.toDouble()
            true
        } catch (e: NumberFormatException) {
            false
        }
    }

    /**
     * 拨打电话（跳转到拨号界面，用户手动点击拨打）
     *
     * @param phoneNum 电话号码
     */
    fun callPhone(context: Context, phoneNum: String) {
        val intent = Intent(Intent.ACTION_DIAL)
        val data = Uri.parse("tel:$phoneNum")
        intent.data = data
        context.startActivity(intent)
    }

    fun setEditTwo(editText: EditText) {
        editText.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(edt: Editable) {
                val temp = edt.toString()
                val posDot = temp.indexOf(".")
                if (posDot <= 0) return
                if (temp.length - posDot - 1 > 2) {
                    edt.delete(posDot + 3, posDot + 4)
                }
            }

            override fun beforeTextChanged(arg0: CharSequence, arg1: Int, arg2: Int, arg3: Int) {}
            override fun onTextChanged(arg0: CharSequence, arg1: Int, arg2: Int, arg3: Int) {}
        })
    }

    /**
     * 获取本机手机序列号
     */
    fun getAndroidID(context: Context): String {
        return Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
    }

    /**
     * 使用正则表达式来判断字符串中是否包含字母
     *
     * @param str 待检验的字符串
     * @return 返回是否包含
     * true: 包含字母 ;false 不包含字母
     */
    fun isCotainZimu(str: String?): Boolean {
        val regex = ".*[a-zA-Z]+.*"
        val m = Pattern.compile(regex).matcher(str ?: "")
        return m.matches()
    }

    fun isContainNumber(company: String?): Boolean {
        val p = Pattern.compile("[0-9]")
        val m = p.matcher(company ?: "")
        return m.find()
    }

    //米转换为千米工具
    fun convertDistance(distance: Float): String {
        val decimalFormat = DecimalFormat("#################.##")
        var ds = ""
        ds = if (distance > 1000) {
            "${decimalFormat.format((distance / 1000).toDouble())}km"
        } else {
            "${distance}m"
        }
        return ds
    }

    fun getSHA1(context: Context): String? {
        try {
            val info = context.packageManager.getPackageInfo(
                context.packageName, PackageManager.GET_SIGNATURES
            )
            val cert = info?.signatures?.get(0)?.toByteArray()
            val md = MessageDigest.getInstance("SHA1")
            val publicKey = md.digest(cert)
            val hexString = StringBuilder()
            for (b in publicKey) {
                val appendString = Integer.toHexString(0xFF and b.toInt())
                    .lowercase(Locale.US)
                if (appendString.length == 1) hexString.append("0")
                hexString.append(appendString)
                hexString.append(":")
            }
            val result = hexString.toString()
            return result.substring(0, result.length - 1)
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
        }
        return null
    }

    @JvmOverloads
    fun numberToUppercase(number: Int, forWeek: Boolean = false): String {
        return when (number) {
            1 -> "一"
            2 -> "二"
            3 -> "三"
            4 -> "四"
            5 -> "五"
            6 -> "六"
            7 -> if (forWeek) "日" else "七"
            8 -> "八"
            9 -> "九"
            10 -> "十"
            else -> number.toString() + ""
        }
    }

    public fun copy(context: Context, content: String?) {
        val clipboardManager =
            context.getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager
        val clipData = ClipData.newPlainText("Label", content);
        clipboardManager?.let { it.setPrimaryClip(clipData) }
    }

    //    public static void copy(Context context, String content) {
    //        ClipboardManager cm = (ClipboardManager) context.getSystemService(Context.CLIPBOARD_SERVICE);
    //        ClipData mClipData = ClipData.newPlainText("Label", content);
    //        if (cm != null) {
    //            cm.setPrimaryClip(mClipData);
    //        }
    //    }
    //    public static void sendSMS(Context context, String phoneNumber) {
    ////        if (PhoneNumberUtils.isGlobalPhoneNumber(phoneNumber)) {
    //        Intent intent = new Intent(Intent.ACTION_SENDTO, Uri.parse("smsto:" + phoneNumber));
    //        intent.putExtra("sms_body", "");
    //        context.startActivity(intent);
    ////        }
    //    }
    fun checkSpecialChar(str: String?): Boolean {
        val limitEx = "[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#¥%……&*（）——+|{}【】‘；：”“’。，、？]"
        val pattern = Pattern.compile(limitEx)
        val m = pattern.matcher(str ?: "")
        return m.find()
    }

    /**
     * 基本功能：过滤所有以"<"开头以">"结尾的标签
     *
     * @param str
     * @return String
     */
    fun filterHtml(str: String?): String {
        if (TextUtils.isEmpty(str)) {
            return ""
        }
        val pattern = Pattern.compile("<([^>]*)>")
        val matcher = pattern.matcher(str)
        val sb = StringBuffer()
        var result1 = matcher.find()
        while (result1) {
            matcher.appendReplacement(sb, "")
            result1 = matcher.find()
        }
        matcher.appendTail(sb)
        return sb.toString()
    }

    fun regexName(str: String?): Boolean {
        if (TextUtils.isEmpty(str)) {
            return false
        }
        val limitEx = "^[\\u4E00-\\u9FA50-9a-zA-Z]{4,30}$"
        val pattern = Pattern.compile(limitEx)
        val m = pattern.matcher(str ?: "")
        return m.find()
    }

    fun getLineCount(
        text: String,
        textView: TextView,
        textViewWidthPx: Float
    ): List<String?> {
        val paint = Paint()
        paint.textSize = textView.textSize
        paint.typeface = textView.typeface
        return splitWordsIntoStringsThatFit(text.trim { it <= ' ' }, textViewWidthPx, paint)
    }

    private fun splitWordsIntoStringsThatFit(
        source: String,
        maxWidthPx: Float,
        paint: Paint
    ): List<String?> {
        val result = ArrayList<String?>()
        val currentLine = ArrayList<String?>()
        val sources = source.split("\n").toTypedArray()
        for (chunk in sources) {
            if ("" == chunk) {
                chunk.plus("\n")
            }
            if (paint.measureText(chunk) < maxWidthPx) {
                processFitChunk(maxWidthPx, paint, result, currentLine, chunk)
            } else {
                //the chunk is too big, split it.
                val splitChunk = splitIntoStringsThatFit(chunk, maxWidthPx, paint)
                for (chunkChunk in splitChunk) {
                    processFitChunk(maxWidthPx, paint, result, currentLine, chunkChunk)
                }
            }
        }
        if (!currentLine.isEmpty()) {
            result.addAll(currentLine)
        }
        return result
    }

    private fun processFitChunk(
        maxWidth: Float,
        paint: Paint,
        result: ArrayList<String?>,
        currentLine: ArrayList<String?>,
        chunk: String
    ) {
        currentLine.add(chunk)
        val currentLineStr = TextUtils.join(" ", currentLine)
        if (paint.measureText(currentLineStr) >= maxWidth || "\n" == chunk) {
            //remove chunk
            currentLine.removeAt(currentLine.size - 1)
            result.add(TextUtils.join(" ", currentLine))
            currentLine.clear()
            //ok because chunk fits
            currentLine.add(chunk)
        }
    }

    private fun splitIntoStringsThatFit(
        source: String,
        maxWidthPx: Float,
        paint: Paint
    ): List<String> {
        if (TextUtils.isEmpty(source) || paint.measureText(source) <= maxWidthPx) {
            return Arrays.asList(source)
        }
        val result = ArrayList<String>()
        var start = 0
        for (i in 1..source.length) {
            val substr = source.substring(start, i)
            if (paint.measureText(substr) >= maxWidthPx) {
                //this one doesn't fit, take the previous one which fits
                val fits = source.substring(start, i - 1)
                result.add(fits)
                start = i - 1
            }
            if (i == source.length) {
                val fits = source.substring(start, i)
                result.add(fits)
            }
        }
        return result
    }

    fun getImageUrlsFromHtml(content: String?): Array<String?>? {
        if (TextUtils.isEmpty(content)) return null
        val imageSrcList: MutableList<String?> = ArrayList()
        val p = Pattern.compile(
            "<img\\b[^>]*\\bsrc\\b\\s*=\\s*('|\")?([^'\"\n\r\\f>]+(\\.jpg|\\.bmp|\\.eps|\\.gif|\\.mif|\\.miff|\\.png|\\.tif|\\.tiff|\\.svg|\\.wmf|\\.jpe|\\.jpeg|\\.dib|\\.ico|\\.tga|\\.cut|\\.pic|\\b)\\b)[^>]*>",
            Pattern.CASE_INSENSITIVE
        )
        val m = p.matcher(content ?: "")
        var quote: String?
        var src: String? = null
        while (m.find()) {
            quote = m.group(1)
            src =
                if (quote == null || quote.trim { it <= ' ' }.isEmpty()) m.group(2).split("//s+")
                    .toTypedArray()[0] else m.group(2)
            imageSrcList.add(src)
        }
        if (imageSrcList.isListEmpty()) {
            i("详情中未匹配到图片链接！！！")
            return null
        }
        return imageSrcList.toTypedArray()
    }
}