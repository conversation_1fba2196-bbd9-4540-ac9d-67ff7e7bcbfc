package com.bdc.android.library.base.fragment

import android.content.Context
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.viewbinding.ViewBinding
import com.bdc.android.library.event.MessageEvent
import com.bdc.android.library.utils.ClassUtil
import com.gyf.immersionbar.ImmersionBar
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.impl.LoadingPopupView
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.lang.reflect.ParameterizedType

/**
 * Copyright (C), 2020-2021, 中传互动（湖北）信息技术有限公司
 * Author: He<PERSON>hao
 * Date: 2021/12/15 14:25
 * Description:
 */
abstract class BaseCoreFragment<B : ViewBinding, VM : ViewModel> : Fragment() {

    protected val TAG = this::class.java.canonicalName

    protected var mContext: Context? = null

    /**
     * rootView是否初始化标志，防止回调函数在rootView为空的时候触发
     */
    private var hasCreateView: Boolean = false

    /**
     * 当前Fragment是否处于可见状态标志，防止因ViewPager的缓存机制而导致回调函数的触发
     */
    private var isFragmentVisible: Boolean = false

    protected lateinit var mBinding: B

    protected val mViewModel: VM by lazy {
//        ViewModelProvider.AndroidViewModelFactory.getInstance(
//            AppManager.getApplication()
//        ).create(ClassUtil.create<VM>(javaClass.genericSuperclass, 1))
        ViewModelProvider(this)[ClassUtil.create<VM>(javaClass.genericSuperclass, 1)]
    }

    private val mLoadingDialog: LoadingPopupView by lazy {
        XPopup.Builder(mContext).asLoading()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        hasCreateView = false
        isFragmentVisible = false
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return bindingInflater(inflater).also {
            mBinding = it
        }.root
    }

    private fun bindingInflater(layoutInflater: LayoutInflater): B {
        val viewBindingClass = getGenericViewBindingClass()
        val inflateMethod = viewBindingClass.getMethod("inflate", LayoutInflater::class.java)
        @Suppress("UNCHECKED_CAST") return inflateMethod.invoke(null, layoutInflater) as B
    }

    private fun getGenericViewBindingClass(): Class<B> {
        var genericSuperclass = javaClass.genericSuperclass
        while (genericSuperclass !is ParameterizedType) {
            genericSuperclass = (genericSuperclass as Class<*>).genericSuperclass
        }
        return (genericSuperclass as ParameterizedType).actualTypeArguments[0] as Class<B>
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        bindListener()
        initViewStates()
        initViewEvents()
        initData()
        if (isTransparentStatusBar()) {
            ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(isDarkStatusBar())
                .init()
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        mContext = context
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        hasCreateView = true
        if (isVisibleToUser) {
            onFragmentVisibleChange(true)
            isFragmentVisible = true
            return
        }
        if (isFragmentVisible) {
            onFragmentVisibleChange(false)
            isFragmentVisible = false
        }
    }

    open fun onFragmentVisibleChange(isVisible: Boolean) {
    }

    override fun onStart() {
        super.onStart()
        if (isRegistered() && !EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    protected abstract fun getLayoutId(): Int

    open fun isTransparentStatusBar(): Boolean {
        return false
    }

    open fun isDarkStatusBar(): Boolean {
        return true
    }

    open fun initView() {}

    open fun bindListener() {}

    open fun initData() {}
    open fun initViewStates() {} //初始化数据状态监听
    open fun initViewEvents() {} //初始化数据事件监听

    open fun showLoading(message: String?) {
        if (!mLoadingDialog.isShown) {
            if (TextUtils.isEmpty(message)) {
                mLoadingDialog.show()
            } else {
                mLoadingDialog.setTitle(message).show()
            }
        }
    }

    open fun stopLoading() {
        if (mLoadingDialog.isShown) {
            mLoadingDialog.dismiss()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    open fun onMessageEvent(event: MessageEvent) {
    }

    open fun isRegistered(): Boolean = false
    override fun onDestroy() {
        super.onDestroy()
        if (isRegistered() && EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }
}