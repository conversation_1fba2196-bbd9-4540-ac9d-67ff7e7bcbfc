package com.bdc.android.library.flowlayout

import android.content.Context
import android.view.View


/**
 * Author:Lxf
 * Create on:2024/7/27
 * Description:
 */
abstract class FlowLayoutAdapter {
    private var flowLayout: FlowLayout? = null

    abstract fun createView(
        context: Context,
        flowLayout: FlowLayout,
        position: Int
    ): View

    abstract fun getItemCount(): Int

    abstract fun getItem(position: Int): Any?

    fun setFlowLayout(flowLayout: FlowLayout?) {
        this.flowLayout = flowLayout
    }

    fun notifyChange() {
        flowLayout?.requestLayout()
    }
}