package com.bdc.android.library.livedata

import androidx.lifecycle.MutableLiveData

/**
 * Copyright (C), 2020-2021, 中传互动（湖北）信息技术有限公司
 * @Author: pym
 * @Date: 2021/6/15 9:48
 * @Description:
 */
class StateLiveData<T> : MutableLiveData<LiveDataWrapper<T?>?>() {
    fun setData(value: T?) {
        setValue(LiveDataWrapper(value))
    }

    fun hasException(hasException: Boolean) {
        val liveDataWrapper: LiveDataWrapper<T?> = LiveDataWrapper(null, hasException)
        value = liveDataWrapper
    }

    fun onComplete() {
        val liveDataWrapper: LiveDataWrapper<T?> = LiveDataWrapper(true, null)
        value = liveDataWrapper
    }

    override fun setValue(value: LiveDataWrapper<T?>?) {
        super.setValue(value)
    }

}