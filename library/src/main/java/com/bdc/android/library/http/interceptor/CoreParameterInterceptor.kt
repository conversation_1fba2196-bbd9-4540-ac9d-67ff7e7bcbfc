package com.bdc.android.library.http.interceptor

import android.os.Build
import com.bdc.android.library.utils.JsonUtil
import com.bdc.android.library.utils.SystemUtil
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import okhttp3.ResponseBody.Companion.toResponseBody
import java.io.IOException
import java.net.HttpURLConnection.HTTP_BAD_REQUEST
import java.net.HttpURLConnection.HTTP_INTERNAL_ERROR
import java.net.HttpURLConnection.HTTP_UNAUTHORIZED

/**
 * Copyright (C), 2019-2021, 中传互动（湖北）信息技术有限公司
 * Author: HeChao
 * Date: 2021/6/8 16:45
 * Description: 公共参数拦截器
 */
abstract class CoreParameterInterceptor<T> : Interceptor {

    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        var request = chain.request()
        //获取当前app渠道来源
        val customRequestBuilder = getRequestBuilder(request)
        request = if (customRequestBuilder != null) {
            // 如果有自定义的 RequestBuilder，使用它来构建请求
            customRequestBuilder.apply {
                header("appos", if (SystemUtil.isHarmonyOS) "Harmony" else "Android")
                header("model", Build.MODEL)
                header("manufacturer", Build.MANUFACTURER)
            }.build()
        } else {
            // 如果没有自定义的 RequestBuilder，使用默认方式
            request.newBuilder().apply {
                header("appos", if (SystemUtil.isHarmonyOS) "Harmony" else "Android")
                header("model", Build.MODEL)
                header("manufacturer", Build.MANUFACTURER)
            }.build()
        }
        val response = chain.proceed(request)
        return try {
            val responseBody = response.body
            val bodyString = responseBody?.string()
            if (bodyString?.isNotEmpty() == true && (response.isSuccessful || response.code == HTTP_UNAUTHORIZED || response.code == HTTP_BAD_REQUEST || response.code == HTTP_INTERNAL_ERROR)) {
                val result = JsonUtil.fromJson(
                    bodyString, getResponseClass()!!::class.java
                )
                handleResponse(result)
                val contentType = responseBody.contentType()
                val body = bodyString.toString().toResponseBody(contentType)
                response.newBuilder().body(body).build()
            } else {
                response
            }
        } catch (e: Exception) {
            e.printStackTrace()
            response
        }
    }

    open fun getRequestBuilder(request: Request): Request.Builder? = null

    abstract fun getResponseClass(): T

    open fun handleResponse(data: T) {}
}