@file:Suppress("UNCHECKED_CAST")

package com.bdc.android.library.utils

import java.lang.reflect.ParameterizedType
import java.lang.reflect.Type

/**
 * @PackageName : com.bdc.android.library.utils
 * <AUTHOR> hechao
 * @Date :   2019-12-10 05:52
 * 适用于父类获取子类必须重载的泛型对象
 */
object ClassUtil {
    fun <T> create(type: Type?, index: Int = 0): Class<T> {
        // 确保 type 是一个 ParameterizedType
        if (type is ParameterizedType) {
            // 获取泛型类型的实际类型参数
            val actualTypeArguments = type.actualTypeArguments
            val targetType = actualTypeArguments.getOrNull(index)
            return if (targetType is Class<*>) {
                // 返回对应的 Class 类型
                targetType as Class<T>
            } else {
                throw IllegalArgumentException("Invalid type argument at index $index")
            }
        }
        throw IllegalArgumentException("Provided type is not a ParameterizedType")
    }

    inline fun <reified T> create(): Class<T> {
        return T::class.java
    }
}