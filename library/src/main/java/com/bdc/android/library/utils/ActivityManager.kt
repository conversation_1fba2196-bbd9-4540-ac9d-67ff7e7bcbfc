package com.bdc.android.library.utils

import android.app.Activity
import android.app.ActivityManager
import android.content.Context
import android.text.TextUtils

/**
 * Created on 2019/5/4 0004
 * Created by hechao
 * Description
 */
object ActivityManager  //将构造方法私有化，所以不能通构造方法来初始化ActivityManager
{
    /**
     * 存放Activity的map
     */
    private val mActivities: MutableList<Activity> =
        ArrayList()

    fun size(): Int {
        return mActivities.size
    }

    //添加activity
    fun addActivity(activity: Activity) {
        if (!mActivities.contains(activity)) {
            mActivities.add(activity)
        }
    }

    fun removeActivity(cls: Class<*>) {
        for (activity in mActivities) {
            if (activity.javaClass == cls) {
                removeActivity(activity)
                break
            }
        }
    }

    //关闭指定的Activity
    fun removeActivity(activity: Activity?) {
        if (activity != null) {
            if (mActivities.contains(activity)) {
                mActivities.remove(activity)
            }
            activity.finish()
        }
    }

    //将activity全部关闭掉
    fun clearAll() {
        for (activity in mActivities) {
            activity.finish()
        }
    }

    fun clearOther(clazz: Class<*>) {
        for (activity in mActivities) {
            if (activity.javaClass.simpleName == clazz.simpleName) {
                continue
            }
            activity.finish()
        }
    }

    fun clearOther(classSimpleName: String) {
        for (activity in mActivities) {
            if (activity.javaClass.simpleName == classSimpleName) {
                continue
            }
            activity.finish()
        }
    }

    fun clear(classSimpleName: String) {
        for (activity in mActivities) {
            if (activity.javaClass.simpleName == classSimpleName) {
                activity.finish()
            }

        }
    }

    fun getCurrentActivity(context: Context): String {
        val manager =
            context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val info = manager.getRunningTasks(1)[0]
        return info.topActivity?.className ?: ""
    }

    val current: Context?
        get() = if (mActivities.size > 0) {
            mActivities[mActivities.size - 1]
        } else null

    val last: Context?
        get() = if (mActivities.size >= 2) {
            mActivities[mActivities.size - 2]
        } else null

    /**
     * 判断当前栈里面是否打开某个activity
     *
     * @param classSimpleName
     * @return
     */
    fun hasTargetActivity(classSimpleName: String): Boolean {
        for (activity in mActivities) {
            if (activity.javaClass.simpleName == classSimpleName) {
                return true
            }
        }
        return false
    }

    fun finishTargetActivity(classSimpleName: String) {
        for (activity in mActivities) {
            if (activity.javaClass.simpleName == classSimpleName) {
                activity.finish()
                break
            }
        }
    }

    /**
     * 判断某个界面是否在前台
     *
     * @param activity 要判断的Activity
     * @return 是否在前台显示
     */
    fun isForeground(activity: Activity): Boolean {
        return isForeground(
            activity,
            activity.javaClass.name
        )
    }

    /**
     * 判断某个界面是否在前台
     *
     * @param context   Context
     * @param className 界面的类名
     * @return 是否在前台显示
     */
    fun isForeground(
        context: Context?,
        className: String
    ): Boolean {
        if (context == null || TextUtils.isEmpty(className)) return false
        val am =
            context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val list = am.getRunningTasks(1)
        if (list != null && list.size > 0) {
            val cpn = list[0].topActivity
            if (className == cpn!!.className) return true
        }
        return false
    }
}