package com.bdc.android.library.utils

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import androidx.annotation.ColorRes
import androidx.fragment.app.Fragment
import com.bdc.android.library.extension.dp2px
import com.gyf.immersionbar.ImmersionBar

/**
 * Copyright (C), 2020-2021, 中传互动（湖北）信息技术有限公司
 * Author: Yin
 * Date: 2021/5/10
 * Description:状态栏
 */
object ImmersionBarUtil {

    /**
     * 默认透明状态栏
     */
    fun setDefaultBar(any: Any) {
        if (any is Activity) {
            ImmersionBar.with(any).init()
        } else if (any is Fragment) {
            ImmersionBar.with(any).init()
        }
    }

    /**
     * 透明状态栏
     * color : 颜色
     * isFits : 是否解决布局重叠
     * isDark : 状态栏图标是否为深色
     */
    fun setStatusBarColorAndFont(any: Any, color: Int, isFits: Boolean, isDark: Boolean) {
        if (any is Activity) {
            ImmersionBar.with(any).statusBarColor(color).fitsSystemWindows(isFits)
                .statusBarDarkFont(isDark).init()
        } else if (any is Fragment) {
            ImmersionBar.with(any).statusBarColor(color).fitsSystemWindows(isFits)
                .statusBarDarkFont(isDark).init()
        }
    }

    /**
     * 白色状态栏, 深色图标, 状态栏和布局不重叠
     */
    fun setStatusBarNormal(any: Any) {
        if (any is Activity) {
            ImmersionBar.with(any)
                .statusBarColor("#FFFFFF")
                .fitsSystemWindows(true)
                .statusBarDarkFont(true, 0.3f)
                .keyboardEnable(true)
                .init()
        } else if (any is Fragment) {
            ImmersionBar.with(any)
                .statusBarColor("#FFFFFF")
                .fitsSystemWindows(true)
                .statusBarDarkFont(true, 0.3f)
                .keyboardEnable(true)
                .init()
        }
    }


    /**
     * 状态栏颜色   状态栏图标是否深色方法
     */
    fun setStatusBarColor(
        any: Any,
        @ColorRes color: Int,
        isFits: Boolean,
        isDark: Boolean,
        isKeyBoardEnable: Boolean
    ) {
        if (any is Activity) {
            ImmersionBar.with(any)
//                .statusBarColor(color)
                .fitsSystemWindows(isKeyBoardEnable, color) //2参解决透明状态栏下,状态栏和布局之间有间距的问题
                .statusBarDarkFont(isDark, 0.3f)
                .keyboardEnable(isFits)
                .init()
        } else if (any is Fragment) {
            ImmersionBar.with(any)
//                .statusBarColor(color)
                .fitsSystemWindows(true, color)
                .statusBarDarkFont(isDark, 0.3f)
                .keyboardEnable(true)
                .init()
        }
    }

    /**
     * 透明状态栏
     * isDark : 状态栏图标是否为深色
     */
    fun setStatusTransparentBarFont(any: Any, isDark: Boolean, statusAlpha: Float = 0.3f) {
        if (any is Activity) {
            ImmersionBar.with(any).statusBarDarkFont(isDark, statusAlpha).keyboardEnable(true)
                .init()
        } else if (any is Fragment) {
            ImmersionBar.with(any).statusBarDarkFont(isDark, statusAlpha).keyboardEnable(true)
                .init()
        }
    }

    /*透明状态栏，自动设置顶部paddingTop*/
    fun setStatusTransparentBarFont(any: Any, isDark: Boolean, view: View, paddingBottom: Int = 0) {
        if (any is Activity) {
            setStatusTransparentBarFont(any, isDark)
            val notchHeight = ImmersionBar.getNotchHeight(any)
            view.setPadding(
                0,
                notchHeight + (if (notchHeight <= 0) ImmersionBar.getStatusBarHeight(any) else 0),
                0,
                paddingBottom
            )
        } else if (any is Fragment) {
            setStatusTransparentBarFont(any, isDark)
            val notchHeight = ImmersionBar.getNotchHeight(any)
            view.setPadding(
                0,
                notchHeight + (if (notchHeight <= 0) ImmersionBar.getStatusBarHeight(any) else 0),
                0,
                paddingBottom
            )
        }
    }

    fun setStatusTransparentBarFontBackup(
        any: Any,
        isDark: Boolean,
        view: View,
        paddingBottom: Int = 0
    ) {
        if (any is Activity) {
            setStatusTransparentBarFont(any, isDark)
            val notchHeight = ImmersionBar.getNotchHeight(any)
            view.setPadding(
                0,
                notchHeight + (if (notchHeight <= 0) ImmersionBar.getStatusBarHeight(any) else 0) + 15F.dp2px(),
                0,
                paddingBottom
            )
        } else if (any is Fragment) {
            setStatusTransparentBarFont(any, isDark)
            val notchHeight = ImmersionBar.getNotchHeight(any)
            view.setPadding(
                0,
                notchHeight + (if (notchHeight <= 0) ImmersionBar.getStatusBarHeight(any) else 0) + 15F.dp2px(),
                0,
                paddingBottom
            )
        }
    }

    /**
     * 检查当前机型状态栏字体是否能变黑
     */
    fun isSupportStatusBarDarkFont(): Boolean {
        return ImmersionBar.isSupportStatusBarDarkFont()
    }

    fun getStatusBarHeight(activity: Activity): Int {
        return ImmersionBar.getStatusBarHeight(activity)
    }

    fun getActionBarHeight(activity: Activity): Int {
        return ImmersionBar.getActionBarHeight(activity)
    }

    /**
     * Gets navigation bar height.
     * 获得导航栏的高度
     *
     * @param activity the com.zchd.splash.activity
     * @return the navigation bar height
     */
    fun getNavigationBarHeight(activity: Activity): Int {
        return ImmersionBar.getNavigationBarHeight(activity)
    }

    /**
     * Has navigtion bar boolean.
     * 判断是否存在导航栏
     *
     * @param activity the com.zchd.splash.activity
     * @return the boolean
     */
    fun hasNavigationBar(activity: Activity): Boolean {
        return ImmersionBar.hasNavigationBar(activity)
    }

    fun setStatusTransparentPadding(view: View, activity: Activity) {
        val statusBarHeight = ImmersionBar.getStatusBarHeight(activity)
        val layoutParams = view.layoutParams as ViewGroup.LayoutParams
        ImmersionBar.setStatusBarView(activity, statusBarHeight + layoutParams.height, view)

    }
}