package com.bdc.android.library.utils

/**
 * Copyright (C), 2019-2020, 中传互动（湖北）信息技术有限公司
 * Author: He<PERSON>hao
 * Date: 2020/10/17 11:50
 * Description:
 */
object ConstellationUtil {

    private val dayArray = intArrayOf(
        20, 19, 21, 20, 21, 22, 23,
        23, 23, 24, 23, 22
    )
    private val constellationArray = arrayOf(
        "摩羯座", "水瓶座", "双鱼座", "白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "处女座", "天秤座",
        "天蝎座", "射手座", "摩羯座"
    )

    fun getConstellation(month: Int, day: Int): String? {
        return if (day < dayArray.get(month - 1)) constellationArray[month - 1] else constellationArray[month]
    }
}