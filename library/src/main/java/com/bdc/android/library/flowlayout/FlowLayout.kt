package com.bdc.android.library.flowlayout

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.VelocityTracker
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.widget.Scroller
import com.bdc.android.library.R
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min


/**
 * Author:Lxf
 * Create on:2024/7/27
 * Description:
 */
class FlowLayout : ViewGroup {
    private var mViewContentWidth = 0 // 内容显示宽度
    private var mViewContentHeight = 0 // 内容显示高度
    private var mViewReallyHeight = 0 // 控件实际高度(所有子控件的高度和+paddingTop+paddingBottom)
    private var mHorizontalSpacing = 0 // 水平方向间距
    private var mVerticalSpacing = 0 // 竖直方向间距

    private var mShowChildViewCount = 0 // 显示的子控件数
    private var mChildViewAllShow = true // 子控件是否已经全部显示了
    private var mTotalShowRowCount = 0 // 总显示行数
    private var mMaxRowCount = Int.MAX_VALUE // 最大显示行数
    private val mRowChildViewList: MutableList<RowChildViewInfo> = ArrayList() // 所有子控件行信息集合

    private var mMaxScrollY = 0 // 滑动时，最大滑动偏移量
    private var mScroller: Scroller? = null // 支持滑动
    private var mVelocityTracker: VelocityTracker? = null // ACTION_UP 时测速

    // 每一行的水平方向对齐方式
    private var mHorizontalGravity = HORIZONTAL_GRAVITY_LEFT

    // 适配器对象
    private var mFlowLayoutAdapter: FlowLayoutAdapter? = null

    // 子控件点击监听
    private var mOnItemClickListener: OnItemClickListener? = null

    // 子控件布局完成监听
    private var mOnChildLayoutFinishListener: OnChildLayoutFinishListener? = null

    @JvmOverloads
    constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init(context, attrs)
    }

    constructor(
        context: Context,
        attrs: AttributeSet?,
        defStyleAttr: Int,
        defStyleRes: Int
    ) : super(context, attrs, defStyleAttr, defStyleRes) {
        init(context, attrs)
    }

    private fun init(context: Context, attrs: AttributeSet?) {
        mScroller = Scroller(context)
        mVelocityTracker = VelocityTracker.obtain()

        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.FlowLayout)
        mMaxRowCount =
            typedArray.getInteger(R.styleable.FlowLayout_flow_max_row_count, Int.MAX_VALUE)
        mHorizontalGravity = typedArray.getInteger(
            R.styleable.FlowLayout_flow_horizontal_gravity,
            HORIZONTAL_GRAVITY_LEFT
        )
        mHorizontalSpacing =
            typedArray.getDimensionPixelSize(R.styleable.FlowLayout_flow_horizontal_spacing, 0)
        mVerticalSpacing =
            typedArray.getDimensionPixelSize(R.styleable.FlowLayout_flow_vertical_spacing, 0)
        typedArray.recycle()
    }

    /**
     * 设置适配器
     *
     * @param flowLayoutAdapter [FlowLayoutAdapter] 子类对象
     */
    fun setAdapter(flowLayoutAdapter: FlowLayoutAdapter) {
        this.mFlowLayoutAdapter = flowLayoutAdapter
        flowLayoutAdapter.setFlowLayout(this)
        requestLayout()
    }

    /**
     * 获取适配器，由方法 [.setAdapter] 设置的
     *
     * @return 返回设置的适配器
     */
    fun getFlowLayoutAdapter(): FlowLayoutAdapter? {
        return mFlowLayoutAdapter
    }

    /**
     * 设置子控件布局完成监听
     *
     * @param onChildLayoutFinishListener [OnChildLayoutFinishListener]
     */
    fun setOnChildLayoutFinishListener(onChildLayoutFinishListener: OnChildLayoutFinishListener?) {
        this.mOnChildLayoutFinishListener = onChildLayoutFinishListener
    }

    /**
     * 移除子控件布局完成监听
     */
    fun removeOnLayoutFinishListener() {
        this.mOnChildLayoutFinishListener = null
    }

    /**
     * 设置子控件点击监听
     *
     * @param onItemClickListener [OnItemClickListener]
     */
    fun setOnItemClickListener(onItemClickListener: OnItemClickListener?) {
        this.mOnItemClickListener = onItemClickListener
    }

    /**
     * 设置最大显示行数
     *
     * @param maxRowCount 最大显示行数  小于0表示全部显示
     */
    fun setMaxRowCount(maxRowCount: Int) {
        var maxRowCount = maxRowCount
        if ((maxRowCount == mMaxRowCount)
            || (maxRowCount < 0 && mMaxRowCount < 0)
            || (mChildViewAllShow && maxRowCount > mTotalShowRowCount)
            || (mChildViewAllShow && maxRowCount < 0)
        ) {
            return
        }

        if (maxRowCount < 0) {
            maxRowCount = Int.MAX_VALUE
        }
        this.mMaxRowCount = maxRowCount
        // 先滑动到顶部
        if (scrollY > 0) {
            scrollTo(0, 0)
        }
        requestLayout()
    }

    /**
     * 设置水平方向控件对齐方式，默认居左对齐（[.HORIZONTAL_GRAVITY_LEFT]）
     *
     * @param horizontalGravity [.HORIZONTAL_GRAVITY_LEFT]、
     * [.HORIZONTAL_GRAVITY_RIGHT]、
     * [.HORIZONTAL_GRAVITY_LEFT_RIGHT]、
     * [.HORIZONTAL_GRAVITY_CENTER]
     */
    fun setHorizontalGravity(horizontalGravity: Int) {
        if (this.mHorizontalGravity != horizontalGravity) {
            this.mHorizontalGravity = horizontalGravity
            requestLayout()
        }
    }

    /**
     * 设置子控件之间的间距
     *
     * @param horizontalSpacing 水平方向间距 dp
     * @param verticalSpacing   竖直方向间距 dp
     */
    fun setSpacing(horizontalSpacing: Int, verticalSpacing: Int) {
        var horizontalSpacing = horizontalSpacing
        var verticalSpacing = verticalSpacing
        if (horizontalSpacing < 0 || verticalSpacing < 0) return

        horizontalSpacing = dip2px(context, horizontalSpacing.toFloat())
        verticalSpacing = dip2px(context, verticalSpacing.toFloat())
        if (this.mHorizontalSpacing != horizontalSpacing || this.mVerticalSpacing != verticalSpacing) {
            this.mHorizontalSpacing = horizontalSpacing
            this.mVerticalSpacing = verticalSpacing
            requestLayout()
        }
    }

    /**
     * 滚动到顶部
     *
     * @param animation true：使用动画滚动  false：不使用动画
     */
    fun scrollToTop(animation: Boolean) {
        val scrollY = scrollY
        if (scrollY > 0) {
            if (animation) {
                smallScrollToPosition(scrollY, -scrollY)
            } else {
                scrollTo(0, 0)
            }
        }
    }

    /**
     * 滚动到底部
     *
     * @param animation true：使用动画滚动  false：不使用动画
     */
    fun scrollToBottom(animation: Boolean) {
        val scrollY = scrollY
        if (mMaxScrollY > scrollY) {
            if (animation) {
                smallScrollToPosition(scrollY, mMaxScrollY - scrollY)
            } else {
                scrollTo(0, mMaxScrollY)
            }
        }
    }

    /**
     * 滚动到指定位置
     *
     * @param animation position：需要滚动到的位置
     * @param animation true：使用动画滚动  false：不使用动画
     */
    fun scrollToPosition(position: Int, animation: Boolean) {
        if (position <= 0) {
            scrollToTop(animation)
        } else if (position >= mMaxScrollY) {
            scrollToBottom(animation)
        } else {
            if (animation) {
                val scrollY = scrollY
                smallScrollToPosition(scrollY, position - scrollY)
            } else {
                scrollTo(0, position)
            }
        }
    }

    /**
     * 滚动到指定行数
     *
     * @param animation rowNumber：需要滚动到的行数值
     * @param animation true：使用动画滚动  false：不使用动画
     */
    fun scrollToRowNumber(rowNumber: Int, animation: Boolean) {
        if (rowNumber <= 0) {
            scrollToTop(animation)
        } else if (rowNumber >= mTotalShowRowCount) {
            scrollToBottom(animation)
        } else {
            var position = 0
            // 根据行数计算 position
            for (rowChildViewInfo in mRowChildViewList) {
                position += rowChildViewInfo.rowHeight
                if (rowChildViewInfo.rowNumber == rowNumber) {
                    break
                }
            }
            scrollToPosition(position, animation)
        }
    }

    /**
     * 竖直方向平滑滚动方法
     *
     * @param startY 开始位置
     * @param dy     移动距离
     */
    private fun smallScrollToPosition(startY: Int, dy: Int) {
        mScroller!!.startScroll(
            0, startY, 0, dy,
            min(600.0, max(300.0, abs(dy.toDouble()))).toInt()
        )
        postInvalidate()
    }

    /**
     * 获取显示的行数。<br></br>
     * **重点注意：不要直接调用，而要在 [.setOnChildLayoutFinishListener]
     * 回调中调用才能保证结果的正确性。**<br></br><br></br>
     * 注意：当调用 [.setMaxRowCount] 方法设置了最大行数时，<br></br>
     * &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
     * 可能小于最大行数：当设置的最大行数 > 全部显示完成所需的行数时；<br></br>
     * &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
     * 或者等于最大行数：当设置的最大行数 <= 全部显示完成所需的行数时。
     *
     * @return 显示的行数
     */
    fun getShowRowCount(): Int {
        return mTotalShowRowCount
    }

    /**
     * 是否所有的子控件都显示了。<br></br>
     * **重点注意：不要直接调用，而要在 [.setOnChildLayoutFinishListener]
     * 回调中调用才能保证结果的正确性。**<br></br><br></br>
     * 注意：当调用 [.setMaxRowCount] 方法设置了最大行数时，可能并非所有子控件都显示完全了。
     *
     * @return true：所有子控件都显示出来了   false：还有子控件没有显示出来
     */
    fun isChildViewAllShow(): Boolean {
        return mChildViewAllShow
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        var heightMeasureSpec = heightMeasureSpec
        removeAllViews()
        mRowChildViewList.clear()
        mShowChildViewCount = 0
        mTotalShowRowCount = 0

        val widthSize = MeasureSpec.getSize(widthMeasureSpec)
        val heightMode = MeasureSpec.getMode(heightMeasureSpec)
        val heightSize = MeasureSpec.getSize(heightMeasureSpec)

        if (mFlowLayoutAdapter == null || mMaxRowCount == 0) {
            // 确定高度
            heightMeasureSpec = if (heightMode == MeasureSpec.EXACTLY) {
                MeasureSpec.makeMeasureSpec(heightSize, MeasureSpec.EXACTLY)
            } else {
                MeasureSpec.makeMeasureSpec(paddingTop + paddingBottom, MeasureSpec.EXACTLY)
            }
            super.onMeasure(widthMeasureSpec, heightMeasureSpec)
            return
        }
        // 内容显示宽度和高度
        mViewContentWidth = widthSize - paddingLeft - paddingRight
        mViewContentHeight = heightSize - paddingTop - paddingBottom

        // 所有孩子控件都完全显示需要的高度，默认加上顶部的 padding 值
        var flowLayoutReallyHeight = paddingTop
        val childCount: Int = mFlowLayoutAdapter!!.getItemCount()
        if (childCount > 0) {
            // 当前行已使用的宽度
            var currentRowWidth = 0
            // 当前子控件所需要的水平方向间距，因为间距数 = 水平方向一行子控件个数 - 1（最后一个没有间距）
            // 本来是每行除最后一个之外，每个后面有间距；这里转换一下，变成除了第一个之外，后面每个在前面加一个间距
            var currentHorizontalSpacing = 0
            // 当前行的高度，以一行中最大高度的子控件高度为行高
            var currentRowMaxHeight = 0
            // 总行数
            mTotalShowRowCount = 1
            // 显示的子控件数
            mShowChildViewCount = 0

            var mChildViewList: MutableList<ChildViewInfo> = ArrayList()
            for (i in 0 until childCount) {
                val childView: View = mFlowLayoutAdapter!!.createView(context, this, i)
                if (childView.visibility == GONE) {
                    continue
                }

                addView(childView)

                measureChild(childView, widthMeasureSpec, heightMeasureSpec)

                val marginLayoutParams = childView.layoutParams as MarginLayoutParams
                var childViewLeftMargin = 0
                var childViewTopMargin = 0
                var childViewRightMargin = 0
                var childViewBottomMargin = 0
                if (marginLayoutParams != null) {
                    childViewLeftMargin = marginLayoutParams.leftMargin
                    childViewTopMargin = marginLayoutParams.topMargin
                    childViewRightMargin = marginLayoutParams.rightMargin
                    childViewBottomMargin = marginLayoutParams.bottomMargin
                }

                val childViewWidth = childView.measuredWidth
                val childViewHeight = childView.measuredHeight
                // 计算当前行已使用的宽度
                currentRowWidth += childViewWidth + childViewLeftMargin + childViewRightMargin + currentHorizontalSpacing
                // 取一行最大高度为行高
                currentRowMaxHeight = max(
                    (childViewHeight + childViewTopMargin + childViewBottomMargin).toDouble(),
                    currentRowMaxHeight.toDouble()
                )
                    .toInt()
                // 换行
                if (currentRowWidth > mViewContentWidth) {
                    // 增加上一行高度
                    flowLayoutReallyHeight += currentRowMaxHeight
                    // 组合成新的行对象信息
                    val rowChildViewInfo = RowChildViewInfo()
                    rowChildViewInfo.rowChildViews = mChildViewList
                    rowChildViewInfo.rowNumber = mTotalShowRowCount
                    rowChildViewInfo.rowHeight = currentRowMaxHeight
                    rowChildViewInfo.currentRowUsedWidth =
                        currentRowWidth - (childViewWidth + childViewLeftMargin + childViewRightMargin + currentHorizontalSpacing)
                    mRowChildViewList.add(rowChildViewInfo)

                    // 换行时设置为0，因为间距数 = 水平方向一行子控件个数 - 1 （最后一个没有间距）
                    // 本来是每行除最后一个之外，每个后面有间距；这里转换一下，变成除了第一个之外，后面每个在前面加一个间距
                    currentHorizontalSpacing = 0
                    // 计算新行已使用宽度
                    currentRowWidth =
                        childViewWidth + childViewLeftMargin + childViewRightMargin + currentHorizontalSpacing
                    // 新行高度
                    currentRowMaxHeight =
                        childViewHeight + childViewTopMargin + childViewBottomMargin
                    // 新行子控件集合
                    mChildViewList = ArrayList()
                    // 总行数加1
                    mTotalShowRowCount += 1
                    // 显示最大行数控制
                    if (mTotalShowRowCount > mMaxRowCount) {
                        // 超过最大行数的部分，减掉
                        mTotalShowRowCount -= 1
                        currentRowMaxHeight = 0
                        break
                    }

                    // 增加竖直方向上的间距
                    flowLayoutReallyHeight += mVerticalSpacing
                }

                // 确定当前子控件所在的位置
                val childViewInfo = ChildViewInfo(childView, mTotalShowRowCount, i)
                childViewInfo.right = currentRowWidth - childViewRightMargin + paddingLeft
                childViewInfo.left = childViewInfo.right - childViewWidth
                childViewInfo.top = flowLayoutReallyHeight + childViewTopMargin
                childViewInfo.bottom = childViewInfo.top + childViewHeight
                mChildViewList.add(childViewInfo)
                mShowChildViewCount += 1
                // 除了每行的第一个，后面的子控件都在前边加上一个水平间距
                currentHorizontalSpacing = mHorizontalSpacing
            }
            // 加上最后一行高度
            flowLayoutReallyHeight += currentRowMaxHeight
            // 加上最后一行的行对象信息
            val rowChildViewInfo = RowChildViewInfo()
            rowChildViewInfo.rowChildViews = mChildViewList
            rowChildViewInfo.rowNumber = mTotalShowRowCount
            rowChildViewInfo.rowHeight = currentRowMaxHeight
            rowChildViewInfo.currentRowUsedWidth = currentRowWidth
            mRowChildViewList.add(rowChildViewInfo)
        }
        // 加上底部 padding 值
        flowLayoutReallyHeight += paddingBottom
        mViewReallyHeight = flowLayoutReallyHeight

        // 确定高度
        heightMeasureSpec = if (heightMode == MeasureSpec.EXACTLY) {
            MeasureSpec.makeMeasureSpec(heightSize, MeasureSpec.EXACTLY)
        } else {
            MeasureSpec.makeMeasureSpec(flowLayoutReallyHeight, MeasureSpec.EXACTLY)
        }

        // 滑动时，最大滑动偏移量
        mMaxScrollY = mViewReallyHeight - mViewContentHeight - paddingBottom - paddingTop
        setMeasuredDimension(widthMeasureSpec, heightMeasureSpec)
    }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        // 确定子控件是否已经全部显示了
        mChildViewAllShow =
            if (mFlowLayoutAdapter == null) true else mFlowLayoutAdapter!!.getItemCount() === mShowChildViewCount
        if (mRowChildViewList.isEmpty()) {
            if (mOnChildLayoutFinishListener != null) mOnChildLayoutFinishListener!!.onLayoutFinish(
                this, mShowChildViewCount
            )
            return
        }

        // 水平方向不同对齐方式偏移量，默认居左对齐，不偏移
        var offsetX = 0
        for (rowChildViewInfo in mRowChildViewList) {
            val rowChildViews = rowChildViewInfo.rowChildViews
            if (mHorizontalGravity == HORIZONTAL_GRAVITY_RIGHT) {
                // 居右对齐
                offsetX = mViewContentWidth - rowChildViewInfo.currentRowUsedWidth
            } else if (mHorizontalGravity == HORIZONTAL_GRAVITY_LEFT_RIGHT) {
                // 左右两端对齐
                offsetX = if (rowChildViews!!.size > 1) {
                    (mViewContentWidth - rowChildViewInfo.currentRowUsedWidth) / (rowChildViews.size - 1)
                } else {
                    0
                }
            } else if (mHorizontalGravity == HORIZONTAL_GRAVITY_CENTER) {
                // 居中对齐
                offsetX = (mViewContentWidth - rowChildViewInfo.currentRowUsedWidth) / 2
            }

            for (i in rowChildViews!!.indices) {
                val childViewInfo = rowChildViews[i]
                if (mHorizontalGravity == HORIZONTAL_GRAVITY_LEFT_RIGHT) {
                    // 左右两端对齐，需要特殊处理
                    childViewInfo.onLayout(offsetX * i)
                } else {
                    childViewInfo.onLayout(offsetX)
                }
                childViewInfo.addClickListener(mOnItemClickListener, this, mFlowLayoutAdapter)
            }
        }
        if (mOnChildLayoutFinishListener != null) mOnChildLayoutFinishListener!!.onLayoutFinish(
            this,
            mShowChildViewCount
        )
    }

    private var mInterceptDownX = 0f

    // 获取TouchSlop值
    var mTouchSlop: Float = ViewConfiguration.get(context).scaledTouchSlop.toFloat()

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        when (ev.action) {
            MotionEvent.ACTION_DOWN -> mInterceptDownX = ev.rawX
            MotionEvent.ACTION_MOVE -> {
                val mXMove = ev.rawX
                val diff = abs((mXMove - mInterceptDownX).toDouble()).toFloat()
                // 当手指拖动值大于TouchSlop值时，认为应该进行滚动，拦截子控件的事件
                if (diff > mTouchSlop) {
                    return true
                }
            }
        }
        return super.onInterceptTouchEvent(ev)
    }

    private var mTouchEventLastY = 0

    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (mViewReallyHeight > mViewContentHeight + paddingTop + paddingBottom) {
            if (mVelocityTracker == null) {
                mVelocityTracker = VelocityTracker.obtain()
            }
            mVelocityTracker!!.addMovement(event)
            val y = event.y.toInt()
            when (event.action) {
                MotionEvent.ACTION_DOWN -> mTouchEventLastY = y
                MotionEvent.ACTION_MOVE -> {
                    if (!mScroller!!.isFinished) {
                        mScroller!!.abortAnimation()
                    }
                    val dy = mTouchEventLastY - y
                    // 向上滑动
                    if (dy < 0) {
                        if (scrollY == 0) {
                            return super.onTouchEvent(event)
                        }
                        if (scrollY + dy < 0) {
                            scrollTo(0, 0)
                            return true
                        }
                    } else {
                        // 向下滑动
                        if (scrollY == mMaxScrollY) {
                            return super.onTouchEvent(event)
                        }
                        if (scrollY + dy > mMaxScrollY) {
                            scrollTo(0, mMaxScrollY)
                            return true
                        }
                    }
                    scrollBy(0, dy)
                    mTouchEventLastY = y
                }

                MotionEvent.ACTION_UP -> {
                    mVelocityTracker!!.computeCurrentVelocity(1000)
                    val initialVelocity = mVelocityTracker!!.yVelocity.toInt()
                    if (abs(initialVelocity.toDouble()) > 200) {
                        // 由于坐标轴正方向问题，要加负号。
                        mScroller!!.fling(0, scrollY, 0, -initialVelocity, 0, 0, 0, 10000)
                    }

                    if (mVelocityTracker != null) {
                        mVelocityTracker!!.clear()
                        mVelocityTracker!!.recycle()
                        mVelocityTracker = null
                    }
                }
            }
            postInvalidate()
            return true
        } else {
            return super.onTouchEvent(event)
        }
    }

    override fun computeScroll() {
        if (mScroller!!.computeScrollOffset()) {
            var currY = mScroller!!.currY
            // 快速滑动边界判断
            if (currY > mMaxScrollY) {
                currY = mMaxScrollY
                scrollTo(0, currY)
                mScroller!!.abortAnimation()
            }
            scrollTo(0, currY)
            postInvalidate()
        }
    }

    override fun generateLayoutParams(attrs: AttributeSet?): LayoutParams {
        return MarginLayoutParams(context, attrs)
    }

    override fun generateDefaultLayoutParams(): LayoutParams {
        return MarginLayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
    }

    override fun generateLayoutParams(p: LayoutParams?): LayoutParams {
        return MarginLayoutParams(p)
    }

    private fun dip2px(context: Context, dpValue: Float): Int {
        val scale = context.resources.displayMetrics.density
        return (dpValue * scale + 0.5f).toInt()
    }

    /**
     * 每一行信息
     */
    private class RowChildViewInfo {
        var currentRowUsedWidth: Int = 0 // 行使用宽度
        var rowNumber: Int = 0 // 行号
        var rowHeight: Int = 0 // 行高
        var rowChildViews: List<ChildViewInfo>? = null // 行内子控件列表
    }

    /**
     * 子控件信息
     */
    private class ChildViewInfo(
        private val childView: View, // 所在行位置
        private val rowNumber: Int, // 在父控件中的位置
        private var position: Int
    ) {
        var left: Int = 0
        var top: Int = 0
        var right: Int = 0
        var bottom: Int = 0

        init {
            this.position = position
        }

        fun onLayout(offsetX: Int) {
            childView.layout(left + offsetX, top, right + offsetX, bottom)
        }

        fun addClickListener(
            onItemClickListener: OnItemClickListener?,
            flowLayout: FlowLayout,
            flowLayoutAdapter: FlowLayoutAdapter?
        ) {
            childView.setOnClickListener {
                onItemClickListener?.onItemClick(
                    flowLayout, flowLayoutAdapter,
                    rowNumber, position
                )
            }
        }
    }

    /**
     * 子控件点击监听
     */
    interface OnItemClickListener {
        /**
         * 点击子控件回调方法
         *
         * @param flowLayout [FlowLayout] 控件对象
         * @param adapter    [FlowLayoutAdapter] 对象
         * @param rowNumber  所在行，行号从 1 开始
         * @param position   在父控件的位置，位置从 0 开始
         */
        fun onItemClick(
            flowLayout: FlowLayout?,
            adapter: FlowLayoutAdapter?,
            rowNumber: Int,
            position: Int
        )
    }

    /**
     * 孩子控件布局完成监听
     */
    interface OnChildLayoutFinishListener {
        /**
         * @param flowLayout     [FlowLayout] 控件对象
         * @param showChildCount 当前完成布局的孩子数（显示的孩子数）
         */
        fun onLayoutFinish(flowLayout: FlowLayout?, showChildCount: Int)
    }

    companion object {
        /**
         * 居左对齐，默认
         */
        const val HORIZONTAL_GRAVITY_LEFT: Int = 0

        /**
         * 居右对齐
         */
        const val HORIZONTAL_GRAVITY_RIGHT: Int = 1

        /**
         * 左右对齐
         */
        const val HORIZONTAL_GRAVITY_LEFT_RIGHT: Int = 2

        /**
         * 居中对齐
         */
        const val HORIZONTAL_GRAVITY_CENTER: Int = 3
    }
}