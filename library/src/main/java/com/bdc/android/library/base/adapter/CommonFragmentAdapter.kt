package com.bdc.android.library.base.adapter

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter

class CommonFragmentAdapter @JvmOverloads constructor(
    private val fm: FragmentManager,
    private val fragmentList: MutableList<Fragment>,
    private val titles: Array<String>
) : FragmentPagerAdapter(fm) {

    override fun getItem(position: Int): Fragment {
        return fragmentList[position]
    }

    override fun getCount(): Int {
        return if (isEmpty) 0 else fragmentList.size
    }

    val isEmpty: Boolean
        get() = fragmentList == null

    override fun getPageTitle(position: Int): CharSequence {
        return titles[position]
    }
}