package com.bdc.android.library.utils

import android.content.ContentValues
import android.graphics.Bitmap
import android.os.Environment
import android.provider.MediaStore
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.*

object BitmapUtil {
    /**
     * 保存图片到图库
     *
     * @param bmp
     */
    fun saveImageToGallery(bmp: Bitmap) {
        val content = ContentValues()
        content.put(
            MediaStore.Images.Media.DISPLAY_NAME,
            Calendar.getInstance().time.toString()
        )
        content.put(MediaStore.Images.Media.DESCRIPTION, "")
        content.put(MediaStore.Images.Media.MIME_TYPE, "image/png")    //文件类型
        AppManager.getApplication().contentResolver.apply {
            insert(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                content
            )?.let { openOutputStream(it).use {
                if (it != null) {
                    bmp.compress(Bitmap.CompressFormat.PNG, 80, it)
                }
            } }
        }
    }

    /**
     * 保存图片到图库
     *
     * @param bmp
     */
    fun save(bmp: Bitmap, bitName: String): String {
        // 首先保存图片
        val appDir = File(
            Environment.getExternalStorageDirectory(),
            "download"
        )
        if (!appDir.exists()) {
            appDir.mkdir()
        }
        val file = File(appDir, "$bitName.jpg")
        try {
            FileOutputStream(file).use { bmp.compress(Bitmap.CompressFormat.JPEG, 100, it) }
        } catch (e: IOException) {
            e.printStackTrace()
            return file.path
        }
        return file.path
    }
}