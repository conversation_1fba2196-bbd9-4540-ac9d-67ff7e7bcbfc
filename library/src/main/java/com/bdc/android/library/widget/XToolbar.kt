package com.bdc.android.library.widget

import android.app.Activity
import android.content.Context
import android.content.res.ColorStateList
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.view.isVisible
import com.bdc.android.library.R
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible

/**
 * Copyright (C), 2020-2021, 中传互动（湖北）信息技术有限公司
 * @Author: pym
 * @Date: 2021/5/19 16:45
 * @Description:
 */
class XToolbar : LinearLayout {
    var toolbar: ViewGroup? = null

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        tag = XToolbar::class.java.name
        toolbar = LayoutInflater.from(context)
            .inflate(R.layout.widget_toobar_layout, this, true) as ViewGroup
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.XToolbar)
        setNavigationImage(typedArray.getDrawable(R.styleable.XToolbar_x_navi))
        setTitle(typedArray.getText(R.styleable.XToolbar_x_title))
        typedArray.getColorStateList(R.styleable.XToolbar_x_title_color)?.let {
            setTitleColor(it)
        }
        val maxLength = typedArray.getResourceId(R.styleable.XToolbar_x_title_max_length, 0)
        if (maxLength != 0) {
            setTitleMaxLength(maxLength)
        }
        setActionText(typedArray.getString(R.styleable.XToolbar_x_action_title))
        typedArray.getColorStateList(R.styleable.XToolbar_x_action_text_color)?.let {
            setActionTextColor(it)
        }
        setActionTextBackground(
            typedArray.getResourceId(
                R.styleable.XToolbar_x_action_text_background, 0
            )
        )
        setBackground(typedArray.getResourceId(R.styleable.XToolbar_x_background, 0))
        showAction(
            typedArray.getBoolean(
                R.styleable.XToolbar_x_show_action, false
            )
        )
        setActionImage(typedArray.getDrawable(R.styleable.XToolbar_x_action))
        showLine(
            typedArray.getBoolean(
                R.styleable.XToolbar_x_show_line, false
            )
        )
        typedArray.recycle()
        setNavigationOnClickListener {
            when (context) {
                is Activity -> context.finish()
            }
        }
    }

    fun setBackground(@DrawableRes resId: Int) {
        this.setBackgroundResource(resId)
    }

    fun setBackgrounds(resId: Drawable?) {
        this.background = resId
    }

    fun setNavigationImage(drawable: Drawable?) {
        toolbar?.findViewById<ImageView>(R.id.iv_negative)?.setImageDrawable(drawable)
    }

    fun setNavigationImageColor(color: Int) {
        toolbar?.findViewById<ImageView>(R.id.iv_negative)?.setColorFilter(color)
    }

    fun setNavigationOnClickListener(
        @DrawableRes navigationRes: Int = 0, listener: OnClickListener
    ) {
        if (navigationRes != 0) {
            setNavigationImage(
                AppCompatResources.getDrawable(
                    context, navigationRes
                )
            )
        }
        toolbar?.findViewById<ImageView>(R.id.iv_negative)?.setOnClickListener(listener)
    }

    fun setTitle(title: CharSequence?) {
        toolbar?.findViewById<TextView>(R.id.tv_title)?.text = title ?: ""
    }

    fun getTitle(): CharSequence {
        return toolbar?.findViewById<TextView>(R.id.tv_title)?.text ?: ""
    }

    fun setTitleMaxLength(len: Int) {
        toolbar?.findViewById<TextView>(R.id.tv_title)?.maxEms = len
    }

    fun setTitleColor(color: ColorStateList) {
        toolbar?.findViewById<TextView>(R.id.tv_title)?.setTextColor(color)
    }

    fun showAction(show: Boolean) {
        toolbar?.findViewById<View>(R.id.layout_action)?.let {
            if (show) {
                it.makeVisible()
            } else {
                it.makeGone()
            }
        }
    }

    fun setActionImage(@DrawableRes resId: Int = 0) {
        if (resId != 0) {
            setActionImage(AppCompatResources.getDrawable(context, resId))
        }
    }

    fun setActionImageColor(color: Int) {
        toolbar?.findViewById<ImageView>(R.id.iv_action)?.setColorFilter(color)
    }

    fun setActionImage(drawable: Drawable? = null) {
        toolbar?.findViewById<ImageView>(R.id.iv_action)?.apply {
            if (drawable != null) {
                makeVisible()
                setImageDrawable(drawable)
            }
        }
    }

    fun setActionText(@StringRes resId: Int = 0) {
        setActionText(context.getString(resId))
    }

    fun setActionText(actionTitle: String?) {
        toolbar?.findViewById<TextView>(R.id.tv_action)?.text = actionTitle
    }

    fun setActionTextBackground(@DrawableRes resId: Int) {
        toolbar?.findViewById<TextView>(R.id.tv_action)?.setBackgroundResource(resId)
    }

    fun setActionTextColor(@ColorInt color: Int) {
        toolbar?.findViewById<TextView>(R.id.tv_action)?.setTextColor(color)
    }

    fun setActionTextColor(colors: ColorStateList) {
        toolbar?.findViewById<TextView>(R.id.tv_action)?.setTextColor(colors)
    }

    fun setActionOnClickListener(listener: OnClickListener) {
        if (toolbar?.findViewById<TextView>(R.id.tv_action)?.isVisible == true) {
            toolbar?.findViewById<TextView>(R.id.tv_action)?.setOnClickListener(listener)
        } else {
            toolbar?.findViewById<ImageView>(R.id.iv_action)?.setOnClickListener(listener)
        }
    }

    fun showLine(isShow: Boolean) {
        toolbar?.findViewById<View>(R.id.title_line)?.visibility =
            if (isShow) View.VISIBLE else View.GONE
    }
}

