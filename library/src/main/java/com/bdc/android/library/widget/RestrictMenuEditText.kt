package com.bdc.android.library.widget

import android.content.Context
import android.util.AttributeSet
import android.view.ActionMode
import android.view.Menu
import android.view.MenuItem
import android.view.MotionEvent
import android.widget.TextView
import androidx.appcompat.widget.AppCompatEditText

/**
 * Copyright (C), 2020-2021, 中传互动（湖北）信息技术有限公司
 * Author: HeChao
 * Date: 2021/10/27 11:27
 * Description:
 */
class RestrictMenuEditText : AppCompatEditText {

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attributeSet: AttributeSet?) : this(
        context,
        attributeSet,
        android.R.attr.editTextStyle
    )

    constructor(context: Context, attributeSet: AttributeSet?, defStyle: Int) : super(
        context,
        attributeSet,
        defStyle
    ) {
        this.isLongClickable = false
        this.customSelectionActionModeCallback = object : ActionMode.Callback {
            override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                menu?.apply {
                    //删除复制选项
                    findItem(android.R.id.copy)?.let {
                        removeItem(android.R.id.copy)
                    }

                    //删除剪切选项
                    findItem(android.R.id.cut)?.let { removeItem(android.R.id.cut) }
                }
                return true
            }

            override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean = false

            override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean = false

            override fun onDestroyActionMode(mode: ActionMode?) {
            }
        }

        this.setOnTouchListener { _, _ -> this.clearFocus();false }
    }

    override fun isSuggestionsEnabled(): Boolean = false

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (event?.action == MotionEvent.ACTION_DOWN) {
            this.setInsertionDisabled()
        }
        return super.onTouchEvent(event)
    }

    private fun setInsertionDisabled() {
        try {
            val editorField = TextView::class.java.getDeclaredField("mEditor")
            editorField.isAccessible = true
            val editorObject = editorField.get(this)
            val editorClass = Class.forName("android.widget.Editor")
            val mInsertionControllerEnabledField =
                editorClass.getDeclaredField("mInsertionControllerEnabled")
            mInsertionControllerEnabledField.isAccessible = true
            mInsertionControllerEnabledField.set(editorObject, false)
        } catch (ignored: Exception) {
        }
    }
}