package com.bdc.android.library.refreshlayout

import android.content.Context
import android.util.AttributeSet
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.angcyo.dsladapter.DslAdapter
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.DslAdapterStatusItem
import com.angcyo.dsladapter.DslItemDecoration
import com.angcyo.dsladapter.DslLoadMoreItem
import com.angcyo.dsladapter.FilterParams
import com.angcyo.dsladapter.adapterStatus
import com.angcyo.dsladapter.data.updateOrCreateItemByClass
import com.angcyo.dsladapter.data.updateSingleData
import com.angcyo.dsladapter.dslSpanSizeLookup
import com.angcyo.dsladapter.gridLayout
import com.angcyo.dsladapter.multiModel
import com.angcyo.dsladapter.singleModel
import com.angcyo.dsladapter.toLoadMoreEnd
import com.angcyo.dsladapter.toLoadNoMore
import com.bdc.android.library.R
import com.bdc.android.library.widget.DslLoadMoreItem2
import com.google.android.flexbox.AlignItems
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxLayoutManager
import com.google.android.flexbox.JustifyContent

/**
 * Copyright (C), 2019-2021, 中传互动（湖北）信息技术有限公司
 * Author: HeChao
 * Date: 2021/8/9 17:09
 * Description: 刷新控件封装
 */
class XRecyclerView : RecyclerView {

    val PAGE_LIMIT = 20
    var pageIndex = 1
    private var listener: (Boolean, Int) -> Unit? = { _, _ -> }
    val dslAdapter = DslAdapter().apply {
        dslAdapterStatusItem =
            DslAdapterStatusItem().apply { itemLayoutId = R.layout.item_base_empty }

        dslLoadMoreItem = DslLoadMoreItem2().apply {
            onLoadMore = {
                pageIndex += 1
                listener.invoke(false, pageIndex)
                setLoadMore(DslLoadMoreItem.LOAD_MORE_NORMAL)
            }
        }

//        dslLoadMoreItem.onLoadMore = {
//            pageIndex += 1
//            listener.invoke(false, pageIndex)
//            setLoadMore(DslLoadMoreItem.LOAD_MORE_NORMAL)
//        }
    }

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attributeSet: AttributeSet?) : this(context, attributeSet, 0)
    constructor(context: Context, attributeSet: AttributeSet?, defStyle: Int) : super(
        context, attributeSet, defStyle
    ) {
        if (layoutManager == null) {
            layoutManager = LinearLayoutManager(context)
        } else {
            if (layoutManager is GridLayoutManager) {
                (layoutManager as GridLayoutManager).apply {
                    dslSpanSizeLookup(dslAdapter)
                }
            }
        }
    }

    init {
        adapter = dslAdapter
    }

    fun setOrientation(orientation: Int, enablePageState: Boolean = true) {
        when (layoutManager) {
            is LinearLayoutManager -> {
                (layoutManager as? LinearLayoutManager)?.orientation = orientation
            }

            is GridLayoutManager -> {
                (layoutManager as? GridLayoutManager)?.orientation = orientation
            }

            is StaggeredGridLayoutManager -> {
                (layoutManager as? StaggeredGridLayoutManager)?.orientation = orientation
            }
        }
        enablePageState(enablePageState)
    }

    fun applyLinearLayoutManager(orientation: Int = VERTICAL, enablePageState: Boolean = true) {
        layoutManager = LinearLayoutManager(context, orientation, false)
        enablePageState(enablePageState)
    }

    fun applyGridLayoutManager(
        spanCount: Int, orientation: Int = VERTICAL, enablePageState: Boolean = true
    ) {
        layoutManager = gridLayout(context, dslAdapter, spanCount, orientation, false)
        enablePageState(enablePageState)
    }

    fun applyStaggeredGridLayoutManager(
        spanCount: Int, orientation: Int = VERTICAL, enablePageState: Boolean = true
    ) {
        layoutManager = StaggeredGridLayoutManager(spanCount, orientation)
        enablePageState(enablePageState)
    }

    fun applyFlexboxLayoutManager(
        flexDirection: Int = FlexDirection.ROW,
        flexWrap: Int = FlexWrap.WRAP,
        justifyContent: Int = JustifyContent.FLEX_START,
        alignItems: Int = AlignItems.FLEX_START,
        enablePageState: Boolean = true
    ) {
        layoutManager = FlexboxLayoutManager(this.context, flexDirection, flexWrap).apply {
                this.justifyContent = justifyContent
                this.alignItems = alignItems
            }
        enablePageState(enablePageState)
    }

    fun enablePageState(enable: Boolean) {
        dslAdapter.dslAdapterStatusItem.itemStateEnable = enable
    }

    fun applySingleSelectorModel(defaultSelectorIndex: Int = -1) {
        singleModel()
        dslAdapter.onDispatchUpdatesOnce { it.itemSelectorHelper.selector(defaultSelectorIndex) }
    }

    fun applySingleSelectorModel(defaultSelectorIndex: () -> Int) {
        applySingleSelectorModel(defaultSelectorIndex.invoke())
    }

    fun applyMultiSelectorModel(vararg defaultSelectorIndexs: Int) {
        multiModel()
        dslAdapter.onDispatchUpdatesOnce {
            defaultSelectorIndexs.forEach { index ->
                it.itemSelectorHelper.selector(index)
            }
        }
    }

    fun applyMultiSelectorModel(defaultSelectorIndexs: () -> IntArray) {
        defaultSelectorIndexs.invoke().forEach {
            applyMultiSelectorModel(it)
        }
    }

    fun enableLoadMore(enable: Boolean) {
        dslAdapter.render {
            setLoadMoreEnable(enable)
        }
//        dslAdapter.setLoadMoreEnable(enable)
    }

    fun setupPageStateItem(item: DslAdapterStatusItem) {
        dslAdapter.dslAdapterStatusItem = item
    }

    fun hasItemDecoration() {
        addItemDecoration(DslItemDecoration())
    }

    fun onRefresh() {
        pageIndex = 1
        listener.invoke(true, pageIndex)
    }

    fun setOnRefreshListener(l: (Boolean, Int) -> Unit) {
        listener = l
    }

    fun clearItems() {
        dslAdapter.clearItems()
    }

    fun clearHeaderItems() {
        dslAdapter.clearHeaderItems()
    }

    fun clearAllItems() {
        dslAdapter.clearAllItems()
    }

    fun notifyUpdateItem() {
        dslAdapter.updateAllItem(FilterParams(notifyDiffDelay = 100))
    }

    inline fun <reified T : DslAdapterItem> appendHeader(item: T) {
        dslAdapter.changeHeaderItems {
            it.add(item)
        }
    }

    inline fun <reified T : DslAdapterItem> appendHeader(index: Int, item: T) {
        dslAdapter.changeHeaderItems {
            it.add(index, item)
        }
    }

    inline fun <reified T : DslAdapterItem> append(item: T) {
        resetAdapterStatus()
        dslAdapter.addLastItem(item)
    }

    inline fun <reified T : DslAdapterItem> append(index: Int = -1, item: T) {
        resetAdapterStatus()
        dslAdapter.insertItem(index, item)
    }

    inline fun <reified T : DslAdapterItem> append(index: Int = -1, items: List<T>) {
        resetAdapterStatus()
        dslAdapter.insertItem(index, items)
    }

    inline fun <reified T : DslAdapterItem> append(
        index: Int = -1, items: List<Any>?, noinline initItem: T.() -> Unit = {}
    ) {
        resetAdapterStatus()
        val dslItems = arrayListOf<DslAdapterItem>()
        items?.forEach { bean ->
            updateOrCreateItemByClass(T::class.java, null, initItem)?.apply {
                    this.itemData = bean
                    dslItems.add(this)
                }
        }
        dslAdapter.insertItem(index, dslItems)
    }

    inline fun <reified T : DslAdapterItem> append(
        items: List<Any>?, crossinline initItem: T.(data: Any?) -> Unit = {}
    ) {
        append(false, items, initItem)
    }

    inline fun <reified T : DslAdapterItem> append(
        refresh: Boolean = false,
        items: List<Any>?,
        crossinline initItem: T.(data: Any?) -> Unit = {}
    ) {
        if (pageIndex == 0 || refresh) {
            dslAdapter.clearItems()
            dslAdapter.updateAllItem(FilterParams(notifyDiffDelay = 300))
        }
        dslAdapter.updateSingleData(
            dataList = items,
            requestPage = pageIndex * PAGE_LIMIT,
            requestPageSize = PAGE_LIMIT,
            initItem = initItem
        )

        if ((items?.size ?: 0) < PAGE_LIMIT) {
            dslAdapter.toLoadNoMore()
//            dslAdapter.setLoadMoreEnable(false)
//            dslAdapter.addLastItem(DslAdapterItem().apply {
//                itemSpanCount = -1
//                itemLayoutId = R.layout.item_base_no_more
//            })
        } else {
            dslAdapter.toLoadMoreEnd()
        }
    }

    fun resetAdapterStatus() {
        if (dslAdapter.adapterStatus() == DslAdapterStatusItem.ADAPTER_STATUS_EMPTY) {
            dslAdapter.setAdapterStatus(DslAdapterStatusItem.ADAPTER_STATUS_NONE)
        }
    }

    fun setAdapterStatus(state: Int = DslAdapterStatusItem.ADAPTER_STATUS_EMPTY/*默认空*/) {
        dslAdapter.setAdapterStatus(state)
    }
}