package com.bdc.android.library.utils

import android.app.Activity
import android.os.Build
import android.util.DisplayMetrics
import java.util.*

/**
 * 系统工具类
 * Created by zhuwentao on 2016-07-18.
 */
object SystemUtil {
    /**
     * 获取当前手机系统语言。
     *
     * @return 返回当前系统语言。例如：当前设置的是“中文-中国”，则返回“zh-CN”
     */
    @JvmStatic
    val systemLanguage: String
        get() {
            val language = Locale.getDefault().language
            return if (language == "zh" || language == "zh-CN" || language == "CN") {
                "中文"
            } else language
        }

    /**
     * 获取当前系统上的语言列表(Locale列表)
     *
     * @return  语言列表
     */
    val systemLanguageList: Array<Locale>
        get() = Locale.getAvailableLocales()

    /**
     * 获取当前手机系统版本号
     *
     * @return  系统版本号
     */
    @JvmStatic
    val systemVersion: String
        get() = if (isHarmonyOS) harmonyVersion else Build.VERSION.RELEASE

    private val harmonyVersion: String
        get() =
            try {
                val cls = Class.forName("android.os.SystemProperties");
                val method = cls.getDeclaredMethod("get", String::class.java)
                val version = method.invoke(cls, "hw_sc.build.platform.version")?.toString()
                version ?: Build.VERSION.RELEASE
            } catch (e: Exception) {
                Build.VERSION.RELEASE
            }

    /**
     * 获取手机型号
     *
     * @return  手机型号
     */
    val systemModel: String
        get() = Build.MODEL

    /**
     * 获取手机厂商
     *
     * @return  手机厂商
     */
    val deviceBrand: String
        get() = Build.BRAND

    /**
     * 获取分辨率
     *
     * @return  分辨率
     */
    fun getDevicePixels(activity: Activity): String {
        val metrics = DisplayMetrics()
        activity.windowManager.defaultDisplay.getMetrics(metrics)
        val widthPixels = metrics.widthPixels
        val heightPixels = metrics.heightPixels
        return widthPixels.toString() + "x" + heightPixels
    }

    /**
     * 获取手机地区
     *
     * @return  地区
     */
    val deviceLocale: String
        get() = Locale.getDefault().country + "(" + Locale.getDefault().displayCountry + ")"

    /**
     * 获取手机时区时间
     *
     * @return  时区
     */
    val deviceTimeZone: String
        get() = TimeZone.getDefault().getDisplayName(true, TimeZone.SHORT)

    /**
     * 获取手机时区id
     *
     * @return  时区
     */
    @JvmStatic
    val deviceTimeZoneID: String
        get() = TimeZone.getDefault().id

    @JvmStatic
    val isHarmonyOS: Boolean
        get() =
            try {
                Class.forName("ohos.utils.system.SystemCapability") != null
            } catch (e: Exception) {
                false
            }
}