package com.bdc.android.library.utils;

import android.text.Editable;
import android.text.InputFilter;
import android.text.InputType;
import android.text.TextWatcher;
import android.widget.EditText;

public class EditTextUtil {
    public static void setInputAmount(EditText editText, int decimalLength, int totalLength){
        //带小数点的数字
        editText.setInputType(InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_FLAG_DECIMAL | InputType.TYPE_NUMBER_VARIATION_NORMAL);
        editText.setFilters(new InputFilter[]{(source, start, end, dest, dStart, dEnd) -> {
            //以点开始的时候，自动在前面添加0
            if (source.toString().equals(".") && dStart == 0) {
                return "0.";
            }
            //如果限制位数剩下最后一位了，禁止输入.
            if (source.toString().equals(".") && dStart == totalLength - 1) {
                return "";
            }
            //如果起始位置为0,且第二位跟的不是".",则无法后续输入
            if (!source.toString().equals(".") && dest.toString().equals("0")) {
                return "";
            }

            // 删除等特殊字符，直接返回
            if ("".equals(source.toString())) {
                return null;
            }
            String dValue = dest.toString();
            String[] splitArray = dValue.split("\\.");
            if (splitArray.length > 1) {
                String dotValue = splitArray[1];
                int diff = dotValue.length() + 1 - decimalLength;
                if (diff > 0) {
                    return source.subSequence(start, end - diff);
                }
            }
            return null;
        }, new InputFilter.LengthFilter(totalLength)});
    }

    // 限制输入框不能输入汉字
    public static void disableChineseInput(final EditText editText) {
        editText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s.toString().contains(" ")) {
                    String[] str = s.toString().split(" ");
                    String str1 = "";
                    for (int i = 0; i < str.length; i++) {
                        str1 += str[i];
                    }
                    editText.setText(str1);
                    editText.setSelection(start);
                }
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s.length() > 0) {
                    for (int i = 0; i < s.length(); i++) {
                        char c = s.charAt(i);
                        if (c >= 0x4e00 && c <= 0X9fff) {
                            s.delete(i, i + 1);
                        }
                    }
                }
            }
        });
    }
}
