package com.bdc.android.library.utils

import android.text.InputFilter
import java.util.regex.Pattern


/**
 * Copyright (C), 2020-2021, 中传互动（湖北）信息技术有限公司
 * @Author: pym
 * @Date: 2021/4/12 17:23
 * @Description:
 */
object PatternUtil {


    fun checkedNickName(text: String): Bo<PERSON>an {
        var strPattern = "^(?![0-9]+\$)(?![a-zA-Z]+\$)[a-zA-Z][0-9A-Za-z]{7,20}\$"
        val compile = Pattern.compile(strPattern)
        return compile.matcher(text).matches()
    }

    fun checkedLetterNumber(text: String): Bo<PERSON>an {
        val strPattern = "^(?![0-9]+\$)(?![a-zA-Z]+\$)[0-9A-Za-z]{8,20}\$"
        val compile = Pattern.compile(strPattern)
        return compile.matcher(text).matches()
    }

    fun getDigital(text: String): Int {
        val newString = StringBuffer()
        val matcher = Pattern.compile("\\d").matcher(text)
        while (matcher.find()) {
            newString.append(matcher.group())
        }
        return newString.toString().toInt()
    }


    /**
     * 判定输入汉字
     *
     * @param c
     * @return
     */
    fun isChinese(c: Char): Boolean {
        val ub = Character.UnicodeBlock.of(c)
        if (ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS) {
            return true
        }
        return false
    }


    // 只允许字母和数字
    fun stringFilter(str: String): String {
        val regEx = "[^a-zA-Z0-9]"
        val p = Pattern.compile(regEx)
        val m = p.matcher(str)
        return m.replaceAll("").trim()
    }

    /**
     * 昵称正则
     * 长度为2-20字符，只允许中英文和数字~
     */
    fun regexNickName(username: String): Boolean {
        val mainRegex = "^[\u4E00-\u9FA50-9a-zA-Z]{2,20}\$"
        val p = Pattern.compile(mainRegex)
        val m = p.matcher(username)
        return m.matches()
    }

    /**
     * 只允许中英文和数字~
     */
    fun regexCED(text: String): Boolean {
        val mainRegex = "^[\u4E00-\u9FA50-9a-zA-Z]+\$"
//        var mainRegex = "^[\u4E00-\u9FA50-9a-zA-Z]\$"
        val p = Pattern.compile(mainRegex)
        val m = p.matcher(text)
        return m.matches()
    }


    /**
     * 只允许中英文
     */
    fun regexCE(text: String): Boolean {
        val mainRegex = "^[a-zA-Z|\u4E00-\u9FA5]+\$"
        val p = Pattern.compile(mainRegex)
        val m = p.matcher(text)
        return m.matches()
    }

    /**
     * 除去标点符号，
     *
     */
    fun strFilter(str: String): String {
        val regEx = "[^a-zA-Z0-9\\u4E00-\\u9FA5]"
        val p = Pattern.compile(regEx)
        val m = p.matcher(str)
        return m.replaceAll("").trim()
    }

    fun isEmoji(str: String): Boolean {
        val regEx = "[^\\u0000-\\uFFFF]"
        val p = Pattern.compile(regEx)
        val m = p.matcher(str)
        return m.find()
    }

    /**
     * 正常输入类型，中英文和数字及标点符号
     */
    fun getNormalFilter(): Array<InputFilter> {
        return arrayOf(InputFilter { source, _, _, _, _, _ ->
            if (regexCED(source.toString())) {
                source
            } else {
                ""
            }
        })
    }

    fun getEliminateEmojiFilter(): Array<InputFilter> {
        return arrayOf(InputFilter { source, _, _, _, _, _ ->
            val emoji = isEmoji(source.toString())
            val regexSpecial = regexSpecial(source.toString())
            if (!isEmoji(source.toString()) && !regexSpecial(source.toString())) {
                source
            } else {
                ""
            }
        })
    }

    /**
     * 长度4-30字数,只允许中英文和数字及标点符号~
     * 标点符号包含： `~!@#$%^&*()+=|{}':;',\[\].<>/?~！@#¥%……&*（）——+|{}【】‘；：”“’。，、？
     */
    fun regexCEDF(text: String): Boolean {
        val mainRegex =
            "^[\u4E00-\u9FA50-9a-zA-Z~!@#\$%^&*()+=|{}':;',\\[\\].<>/?~！@#¥%……&*（）——+|{}【】‘；：”“’。，、？]{4,30}\$"
        val p = Pattern.compile(mainRegex)
        val m = p.matcher(text)
        return m.matches()
    }

    /**
     * 特殊的字符，编码识别不到的
     */
    fun regexSpecial(str: String): Boolean {
        //特殊的字符，识别不到的
        val limitEx = "^[☮☯☪☦✴✳☠☢☣☹☸✨☺✡✝☎✔☄☂☃☀✒☁✌✍☝✏✈✉✊✋☘✅☕☔✂☑♨♠♣♥♦〰♻♿❣❤⌨♊♋♈♉♎♏♌♍ℹ⌛⌚™ ♓♒♑♐❄‼❇⏺⏸⏹⚡⚠➰⏲➿⏳⏰⏱⚫⚪⏫⏪⏩⚰⏯⚱⏮➡⏭⏬⚽⚾©▶®⚖⭐⚗⚔⭕⚒⚓ ⚜⚛⚙↘↙Ⓜ⛩⛪↔↕↖↗⛰⛱⛲⛳⛴⛵◀㊙⛷㊗⛸⛹⛺⛽⬅⬆⬇⛅⛄◾◼⛈◽⛏⛎⛓↪↩⛑⤵⛔⤴⬛]"
        val pattern = Pattern.compile(limitEx)
        val m = pattern.matcher(str)
        return m.find()
    }

}