package com.bdc.android.library.http

import com.bdc.android.library.http.exception.RequestException
import com.bdc.android.library.utils.ToastUtil
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import java.net.UnknownServiceException

/**
 * Copyright (C), 2020-2021, 中传互动（湖北）信息技术有限公司
 * @Author: pym
 * @Date: 2021/5/18 16:38
 * @Description:
 */
class Request<T> {
    internal var start: (() -> Unit)? = null
    internal var showToast: () -> Boolean = { true }
    internal lateinit var call: suspend () -> GeneralResult<T>
    internal var onSuccess: ((T?) -> Unit)? = null
    internal var onFailure: ((RequestException) -> Unit)? = null
    internal var onCompleted: (() -> Unit)? = null

    fun start(start: (() -> Unit)?) {
        this.start = start
    }

    fun showToast(showToast: () -> Boolean) {
        this.showToast = showToast
    }

    fun call(call: suspend () -> GeneralResult<T>) {
        this.call = call
    }

    fun onSuccess(onSuccess: ((T?) -> Unit)?) {
        this.onSuccess = onSuccess
    }

    fun onFailure(onFailure: ((RequestException) -> Unit)?) {
        this.onFailure = onFailure
    }

    fun onCompleted(onComplete: (() -> Unit)?) {
        this.onCompleted = onComplete
    }

    fun request() {
        MainScope().launch(CoroutineExceptionHandler { _, throwable ->
            handlerThrowable(throwable)
        }) {
            start?.invoke()
            try {
                val result = call()
                if (result.isSuccess()) {
                    runCatching {
                        onSuccess?.invoke(result.data)
                    }
                } else {
                    if (result.msg.isNotBlank() && showToast()) {
                        ToastUtil.show(result.msg)
                    }
                    onFailure?.invoke(
                        RequestException(
                            code = result.code,
                            message = result.msg,
                            cause = Throwable(message = "Internal Server Exception")
                        )
                    )
                }
            } catch (e: Exception) {
                handlerThrowable(e)
            } finally {
                onCompleted?.invoke()
                if (isActive) {
                    cancel()
                }
            }
        }
    }

    private fun handlerThrowable(throwable: Throwable) {
        var message = throwable.message
        when (throwable) {
            is UnknownHostException, is UnknownServiceException, is ConnectException, is SocketTimeoutException -> {
                ToastUtil.show("Network Connection Exception")
                message = "Network Connection Exception"
            }

            else -> {
                throwable.printStackTrace()
            }
        }
        onFailure?.invoke(
            RequestException(
                code = -1, message = message ?: "Unknown Exception", cause = throwable
            )
        )
    }

}

inline fun <T> request(buildRequest: Request<T>.() -> Unit) {
    Request<T>().apply(buildRequest).request()
}
