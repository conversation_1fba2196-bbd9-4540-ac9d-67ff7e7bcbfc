package com.bdc.android.library.utils

import android.annotation.SuppressLint
import android.content.ContentUris
import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Environment
import android.provider.MediaStore
import android.text.TextUtils
import java.io.File
import java.io.IOException
import java.math.BigDecimal
import java.util.Locale

/**
 * @PackageName : com.bdc.android.library.utils
 * <AUTHOR> hechao
 * @Date :   2019-12-20 21:49
 */
object FileUtil {

    const val FORMAT_BYTE = "Byte"
    const val FORMAT_KB = "KB"
    const val FORMAT_MB = "MB"
    const val FORMAT_GB = "GB"
    const val FORMAT_TB = "TB"

    fun getFileSize(path: String): Long {
        val dir = File(path)
        if (dir.exists() && dir.isDirectory) {
            var totalSize = 0L
            dir.list().forEach {
                totalSize += File(dir, it).length()
            }
            return totalSize
        }
        return 0L
    }

    /**
     * 获取指定文件大小
     * @param f
     * @return
     * @throws Exception 　　
     */
    fun getSpecifiedFileSize(path: String): Long {
        var size: Long = 0
        val file = File(path)
        if (file.exists()) {
            size = file.length()
        }
        return size
    }

    fun formatSize(size: Long): String {

        val kiloByte = (size / 1024).toDouble()
        if (kiloByte < 1) {
            return size.toString() + "Byte"
        }

        val megaByte = kiloByte / 1024
        if (megaByte < 1) {
            val result1 = BigDecimal(kiloByte.toString())
            return result1.setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString().toString() + "KB"
        }

        val gigaByte = megaByte / 1024
        if (gigaByte < 1) {
            val result2 = BigDecimal(megaByte.toString())
            return result2.setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString().toString() + "MB"
        }

        val teraBytes = gigaByte / 1024
        if (teraBytes < 1) {
            val result3 = BigDecimal(gigaByte.toString())
            return result3.setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString().toString() + "GB"
        }
        val result4 = BigDecimal(teraBytes)

        return result4.setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString().toString() + "TB"
    }


    fun formatTypeSize(size: Long, type: String): BigDecimal? {

        val kiloByte = (size / 1024).toDouble()
        if (type == FORMAT_BYTE) {
            return kiloByte.toBigDecimal()
        }

        val megaByte = kiloByte / 1024
        if (type == FORMAT_KB) {
            val result1 = BigDecimal(kiloByte.toString())
            return result1.setScale(2, BigDecimal.ROUND_HALF_UP)
        }

        val gigaByte = megaByte / 1024
        if (type == FORMAT_MB) {
            val result2 = BigDecimal(megaByte.toString())
            return result2.setScale(2, BigDecimal.ROUND_HALF_UP)
        }

        val teraBytes = gigaByte / 1024
        if (type == FORMAT_GB) {
            val result3 = BigDecimal(gigaByte.toString())
            return result3.setScale(2, BigDecimal.ROUND_HALF_UP)
        }

        val result4 = BigDecimal(teraBytes)
        return result4.setScale(2, BigDecimal.ROUND_HALF_UP)
    }


    fun isFileExist(path: String?): Boolean {
        if (TextUtils.isEmpty(path)) {
            return false
        }
        return File(path).exists()
    }

    fun getFilePath(context: Context): String {
        return context.filesDir.path + "/Luban/image"
    }

    fun getDownloadCacheFilePath(context: Context): String {
        val path: String =
            getExternalCacheDir(context).toString() + File.separator + "Download" + File.separator
        prepareDirs(path)
        return path
    }

    fun getDownVideoCacheFilePath(context: Context): String {
        val path: String =
            getExternalCacheDir(context).toString() + File.separator + "Download/Video" + File.separator
        prepareDirs(path)
        return path
    }

    /**
     * 缓存目录
     */
    fun getExternalCacheDir(context: Context): File {
        val cache = context.externalCacheDir ?: Environment.getDataDirectory()
        prepareDirs(cache.absolutePath)
        markNoMedia(cache.absolutePath)
        return cache
    }

    private fun prepareDirs(path: String): Boolean {
        val f = File(path)
        if (f.exists()) return true
        return f.mkdirs()
    }

    private fun markNoMedia(vararg paths: String): Boolean {
        for (path in paths) {
            if (prepareDirs(path)) {
                return try {
                    File(path + File.separator + ".nomedia").createNewFile()
                } catch (e: IOException) {
                    false
                }
            }
        }
        return true
    }

    @SuppressLint("Range")
    fun getMediaUriFromPath(context: Context, path: String): Uri? {
        val mediaUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
        val cursor: Cursor? = context.contentResolver.query(
            mediaUri,
            null,
            MediaStore.Images.Media.DISPLAY_NAME + "= ?",
            arrayOf(path.substring(path.lastIndexOf("/") + 1)),
            null
        )
        var uri: Uri? = null
        if (cursor?.moveToFirst() == true) {
            uri = ContentUris.withAppendedId(
                mediaUri,
                cursor.getLong(cursor.getColumnIndex(MediaStore.Images.Media._ID))
            )
        }
        cursor?.close()
        return uri
    }

    fun isVideo(path: String): Boolean =
        path.lowercase(Locale.getDefault())
            .endsWith("mp4") || path.lowercase(Locale.getDefault())
            .endsWith("avi") || path.lowercase(
            Locale.getDefault()
        ).endsWith("mov")


    fun getFileMimeType(fileName: String): String {
        return when (fileName.substring(fileName.lastIndexOf("."))) {
            ".doc", ".docx" -> "application/msword"
            ".pdf" -> "application/pdf"
            ".pps", ".ppt", ".pptx" -> "application/vnd.ms-powerpoint"
            ".xls", ".xlsx" -> "application/vnd.ms-excel"
            else -> "*/*"
        }
    }
}

