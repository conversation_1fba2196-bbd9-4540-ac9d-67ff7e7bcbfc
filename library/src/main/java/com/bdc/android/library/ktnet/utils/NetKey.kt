package com.bdc.android.library.ktnet.utils;

/**
 *
 * @Author： Lxf
 * @Time： 2022/2/17 3:47 下午
 * @description：
 */
object NetKey {
    const val KEY_CONTENT_TYPE = "Content-Type"
    const val KEY_ACCESS_TOKEN = "Authorization"
    const val KEY_VERSION_CODE = "x-versioncode"
    const val KEY_VERSION_NAME = "x-versionName"
    const val KEY_PKG = "x-pkg"
    const val KEY_MODEL = "x-model"
    const val KEY_DEVICEID = "x-deviceId"
    const val KEY_DEVICETYPE = "x-deviceType"

//    const val KEY_GOOGLE_INSTALL_REFERRER = "x-installReferrer"
//    const val KEY_APPSFLYER_INSTALL_REFERRER = "x-afReferrer"
    const val KEY_APPSFLYER_APPID = "x-appsFlyerAppId"
    const val KEY_FIREBASE_APPID = "x-firebaseAppId"
    const val KEY_GOOGLE_GAID = "x-googleGaId"

    const val KEY_OS_TYPE = "osType"
    const val KEY_DEVICE_ID = "deviceID"
    const val KEY_DEVICE_MODEL = "deviceModel"
    const val KEY_OS_VERSION = "osVersion"
    const val KEY_PACKAGE_NAME = "packageName"
    const val KEY_VERSION = "version"
    const val KEY_APP_ID = "appID"
    const val KEY_LANG = "lang"
    const val KEY_UID = "uid"
}
