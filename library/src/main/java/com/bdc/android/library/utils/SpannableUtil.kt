package com.bdc.android.library.utils

import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.AbsoluteSizeSpan
import android.text.style.BulletSpan
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.RelativeSizeSpan
import android.text.style.StrikethroughSpan
import android.text.style.StyleSpan
import android.text.style.SubscriptSpan
import android.text.style.SuperscriptSpan
import android.text.style.TypefaceSpan
import android.text.style.UnderlineSpan
import android.view.View
import android.widget.TextView
import androidx.annotation.ColorRes
import androidx.core.content.ContextCompat
import androidx.core.text.TextUtilsCompat
import com.bdc.android.library.widget.RadiusBackgroundSpan
import java.util.Locale

/**
 *
 * 常用Span封装，不支持点击时文字颜色（使用场景少，暂不添加）
 * 支持设置字体颜色、大小、下划线、删除线、点击事件 等，
 * 可以对一个字符串中的多个目标子串进行设置Span
 *
 */
class SpannableUtil(private val context: Context, text: CharSequence) : SpannableString(text) {

    private val spanMode = Spanned.SPAN_EXCLUSIVE_EXCLUSIVE

    // 初始时，待处理的索引范围为全部字符串
    private val rangeList = mutableListOf(Pair(0, text.length))
    private var textColor: Int = 0
    private var backgroundColor: Int = 0


    /**
     *  匹配出现的第一个目标子串[target]，并记录开始和结束的index
     */
    fun first(target: String): SpannableUtil {
        rangeList.clear()
        val index = toString().indexOf(target)
        val range = Pair(index, index + target.length)
        rangeList.add(range)
        return this
    }

    /**
     *  匹配出现的最后一个目标子串[target]，并记录开始和结束的index
     */
    fun last(target: String): SpannableUtil {
        rangeList.clear()
        val index = toString().lastIndexOf(target)
        val range = Pair(index, index + target.length)
        rangeList.add(range)
        return this
    }

    /**
     *   匹配出现的所有目标子串[target]，并记录开始和结束的index
     */
    fun all(target: String): SpannableUtil {
        rangeList.clear()
        val indexes = indexesOf(toString(), target)
        for (index in indexes) {
            val range = Pair(index, index + target.length)
            rangeList.add(range)
        }
        return this
    }

    /**
     *  记录源字符串[src]中目标子串 [target]出现的索引位置
     */
    fun indexesOf(src: String, target: String): MutableList<Int> {
        val positions = mutableListOf<Int>()
        var index = src.indexOf(target)
        while (index >= 0) {
            positions.add(index)
            index = src.indexOf(target, index + 1)
        }
        return positions
    }

    /**
     * 手动输入一个起点索引[from]和终点索引[to]
     */
    fun range(from: Int, to: Int): SpannableUtil {
        rangeList.clear()
        val range = Pair(from, to + 1)
        rangeList.add(range)
        return this
    }

    /**
     * 手动输入所有起点和终点的索引范围[ranges]
     */
    fun ranges(ranges: MutableList<Pair<Int, Int>>): SpannableUtil {
        rangeList.clear()
        rangeList.addAll(ranges)
        return this
    }

    /**
     * 计算两个字符串[startText] 和 [endText]之间的字符串的索引，加入到待处理的集合中，后续的Span设置都是对该索引范围内的字串进行的
     */
    fun between(startText: String, endText: String): SpannableUtil {
        rangeList.clear()
        val startIndex = toString().indexOf(startText) + startText.length + 1
        val endIndex = toString().lastIndexOf(endText) - 1
        val range = Pair(startIndex, endIndex)
        rangeList.add(range)
        return this
    }

    /**
     * 给target字串设置文字绝对大小为[dp]
     */
    fun size(dp: Int): SpannableUtil {
        for (range in rangeList) {
            setSpan(AbsoluteSizeSpan(dp, true), range.first, range.second, spanMode)
        }
        return this
    }

    /**
     * 给target字串设置文字相对大小，指相对于文本设定的大小的相对比例为[proportion]
     */
    fun scaleSize(proportion: Int): SpannableUtil {
        for (range in rangeList) {
            setSpan(RelativeSizeSpan(proportion.toFloat()), range.first, range.second, spanMode)
        }
        return this
    }

    /**
     * 给target字串设置样式（粗体）
     */
    fun bold(): SpannableUtil {
        for (range in rangeList) {
            setSpan(StyleSpan(Typeface.BOLD), range.first, range.second, spanMode)
        }
        return this
    }

    /**
     * 给target字串设置样式（斜体）
     */
    fun italic(): SpannableUtil {
        for (range in rangeList) {
            setSpan(StyleSpan(Typeface.ITALIC), range.first, range.second, spanMode)
        }
        return this
    }

    /**
     * 给target字串设置样式（正常）
     */
    fun normal(): SpannableUtil {
        for (range in rangeList) {
            setSpan(StyleSpan(Typeface.NORMAL), range.first, range.second, spanMode)
        }
        return this
    }

    /**
     * 给target字串设置样式（粗斜体）
     */
    fun bold_italic(): SpannableUtil {
        for (range in rangeList) {
            setSpan(StyleSpan(Typeface.BOLD_ITALIC), range.first, range.second, spanMode)
        }
        return this
    }

    /**
     * 字体样式，可以设置不同的字体，比如系统自带的SANS_SERIF、MONOSPACE和SERIF
     */
    fun font(font: String): SpannableUtil {
        for (range in rangeList) {
            setSpan(TypefaceSpan(font), range.first, range.second, spanMode)
        }
        return this
    }

    /**
     * 给target字串添加删除线
     */
    fun strikethrough(): SpannableUtil {
        for (range in rangeList) {
            setSpan(StrikethroughSpan(), range.first, range.second, spanMode)
        }
        return this
    }

    /**
     * 给target字串添加下划线
     */
    fun underline(): SpannableUtil {
        for (range in rangeList) {
            setSpan(UnderlineSpan(), range.first, range.second, spanMode)
        }
        return this
    }

    /**
     * 类似于HTML中的<li>标签的圆点效果,[dp]表示圆点和字体的间距，[colorRes]表示圆点的颜色
     */
    fun bullet(dp: Int, @ColorRes colorRes: Int?): SpannableUtil {
        for (range in rangeList) {
            setSpan(
                BulletSpan(
                    dp, ContextCompat.getColor(
                        context, colorRes ?: textColor
                    )
                ), range.first, range.second, spanMode
            )
        }
        return this
    }

    /**
     * 字体颜色 [colorRes]表示target字串的字体颜色
     */
    fun textColor(@ColorRes colorRes: Int): SpannableUtil {
        textColor = ContextCompat.getColor(context, colorRes)
        // 确保文本的方向是正确的
        val isRtl = TextUtilsCompat.getLayoutDirectionFromLocale(Locale.getDefault()) == View.LAYOUT_DIRECTION_RTL

//        for (range in rangeList) {
//            setSpan(ForegroundColorSpan(textColor), range.first, range.second, spanMode)
//        }
        for (range in rangeList) {
            // 获取文本的长度
            val textLength = this.length

            // 对于 RTL 语言，需要反转索引
            val start = if (isRtl) textLength - range.second else range.first
            val end = if (isRtl) textLength - range.first else range.second

            // 确保 start 和 end 不会超出文本范围
            val validStart = start.coerceIn(0, textLength)
            val validEnd = end.coerceIn(0, textLength)

            // 只有 start <= end 的情况下才设置 Span
            if (validStart < validEnd) {
                setSpan(ForegroundColorSpan(textColor), validStart, validEnd, spanMode)
            } else {
                // 可以添加日志或错误处理，防止越界
                Logger.e("SpannableUtil", "Invalid range: $start to $end")
            }
        }
        return this
    }

    /**
     * 将target字串作为下标
     */
    fun subscript(): SpannableUtil {
        for (range in rangeList) {
            setSpan(SubscriptSpan(), range.first, range.second, spanMode)
        }
        return this
    }

    /**
     * 将target字串作为上标
     */
    fun superscript(): SpannableUtil {
        for (range in rangeList) {
            setSpan(SuperscriptSpan(), range.first, range.second, spanMode)
        }
        return this
    }

    /**
     * 给[textView]设置一个点击事件[onTextClickListener]
     */
    fun onClick(textView: TextView, onTextClickListener: () -> Unit): SpannableUtil {
        for (range in rangeList) {
            val span = object : ClickableSpan() {
                override fun updateDrawState(ds: TextPaint) {
                    super.updateDrawState(ds)
                    ds.isUnderlineText = false  //设置下划线，默认有下划线
                }

                override fun onClick(widget: View) {
                    onTextClickListener.invoke()
                }
            }
            setSpan(span, range.first, range.second, spanMode)
        }
        //设置点击后的背景颜色为透明，否则会一直出现高亮(需要在代码里设置，xml里设置有时无效)
        textView.highlightColor = Color.TRANSPARENT
        textView.movementMethod = LinkMovementMethod.getInstance()
        return this
    }


    /**
     * 背景颜色[colorBg]表示target字串的背景颜色
     * 字体颜色[colorBg]表示target字串的颜色
     */
    fun background(@ColorRes colorBg: Int, @ColorRes colorText: Int): SpannableUtil {
        backgroundColor = ContextCompat.getColor(context, colorBg)
        val textColor = ContextCompat.getColor(context, colorText)
        for (range in rangeList) {
            setSpan(
//                RoundBackgroundColorSpan(backgroundColor, textColor),
                RadiusBackgroundSpan(backgroundColor, textColor, 2),
                range.first,
                range.second,
                spanMode
            )
        }
        return this
    }
}