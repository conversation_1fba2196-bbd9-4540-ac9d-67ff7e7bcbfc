package com.bdc.android.library.extension

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.ColorStateList
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Shader
import android.text.InputFilter
import android.view.Gravity
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.webkit.WebView
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.ColorRes
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter
import androidx.viewpager.widget.ViewPager
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.angcyo.tablayout.DslTabLayout
import com.bdc.android.library.R
import com.bdc.android.library.utils.AppManager
import com.bdc.android.library.utils.DateUtil
import com.bdc.android.library.utils.Logger
import com.bdc.android.library.utils.ShadowUtil
import java.math.BigDecimal
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

/**
 * Copyright (C), 2019-2021, 中传互动（湖北）信息技术有限公司
 * Author: HeChao
 * Date: 2021/6/8 11:33
 * Description: View扩展类
 */
fun <T : View> T.click(SKIP_DURATION: Int = 400, block: (T) -> Unit) = setOnClickListener {
    // 使用 View 的 Tag 存储独立的点击时间
    val key = R.id.tag_last_click_time
    val lastClickTime = (getTag(key) as? Long) ?: 0L
    if (System.currentTimeMillis() - lastClickTime > SKIP_DURATION) {
        @Suppress("UNCHECKED_CAST") block(it as T)
        setTag(key, System.currentTimeMillis()) // 更新点击时间
        setTag(R.id.tag_is_simulated_click, false)
    } else {
        Logger.i("OnThrottleClickListener: 重复点击")
    }
}

// 模拟点击时调用
fun View.performSimulatedClick() {
    setTag(R.id.tag_is_simulated_click, true)
    performClick()
}


fun View.makeVisible() {
    visibility = View.VISIBLE
}

fun View.makeVisibleAnimation() {
    visibility = View.VISIBLE
//    animation =
//        android.view.animation.AnimationUtils.loadAnimation(context, R.anim.fade_in)
}

fun View.makeVisible(boolean: Boolean) {
    visibility = if (boolean) View.VISIBLE else View.GONE
}

fun View.makeInVisible() {
    visibility = View.INVISIBLE
}

fun View.makeGone() {
    visibility = View.GONE
}

fun View.makeGoneAnimation() {
    visibility = View.GONE
//    animation =
//        android.view.animation.AnimationUtils.loadAnimation(context, R.anim.fade_out)
}

fun View.setHeight(height: Int) {
    val params = layoutParams
    params.height = height
    layoutParams = params
}

fun View.setWidth(width: Int) {
    val params = layoutParams
    params.width = width
    layoutParams = params
}

fun TextView.setDrawableTop(resId: Int, drawablePadding: Int = 0) {
    val drawable = ContextCompat.getDrawable(AppManager.getApplication(), resId)
    setCompoundDrawablesWithIntrinsicBounds(
        null, drawable, null, null
    )
    compoundDrawablePadding = drawablePadding.toFloat().dp2px()
}

fun TextView.setDrawableLeft(resId: Int, drawablePadding: Int = 0) {
    if (resId > 0) {
        val drawable = ContextCompat.getDrawable(AppManager.getApplication(), resId)
        setCompoundDrawablesWithIntrinsicBounds(
            drawable, null, null, null
        )
        compoundDrawablePadding = drawablePadding.toFloat().dp2px()
    } else {
        setCompoundDrawablesWithIntrinsicBounds(
            null, null, null, null
        )
    }
}

fun TextView.setDrawableRight(resId: Int, drawablePadding: Int = 0) {
    val drawable = ContextCompat.getDrawable(AppManager.getApplication(), resId)
    setCompoundDrawablesWithIntrinsicBounds(
        null, null, drawable, null
    )
    compoundDrawablePadding = drawablePadding.toFloat().dp2px()
}

fun ImageView.tintDrawable(res: Int, color: Int) {
    setImageResource(res)
    imageTintList = if (color <= 0) {
        null
    } else {
        ColorStateList.valueOf(ContextCompat.getColor(AppManager.getApplication(), color))
    }
}

fun TextView.setDrawableHorizontal(left: Int, right: Int, drawablePadding: Int = 0) {
    val drawableLeft = ContextCompat.getDrawable(AppManager.getApplication(), left)
    val drawableRight = ContextCompat.getDrawable(AppManager.getApplication(), right)
    setCompoundDrawablesWithIntrinsicBounds(
        drawableLeft, null, drawableRight, null
    )
    compoundDrawablePadding = drawablePadding.toFloat().dp2px()
}

fun TextView.clearDrawable() {
    setCompoundDrawablesWithIntrinsicBounds(
        null, null, null, null
    )
}

/**
 * 禁止EditText输入空格和换行符
 */
fun EditText.setEditTextInputSpace() {
    val filter = InputFilter { source, _, _, _, _, _ ->
        if (source == " " || source.toString().contentEquals("\n")) {
            ""
        } else {
            null
        }
    }
    filters = arrayOf(filter)
}

fun DslTabLayout.initial(
    titles: List<String>,
    fontSize: Float = 15F,
    textColor: Int = -1,
    defaultPadding: Float = 5F,
    defaultMargin: Float = 0F
) {
    titles.forEach {
        TextView(this.context).apply {
            text = it
            gravity = Gravity.CENTER
            setPaddingRelative(
                defaultPadding.dp2px(), 0, defaultPadding.dp2px(), 0
            )

            val params = DslTabLayout.LayoutParams(
                FrameLayout.LayoutParams.WRAP_CONTENT, FrameLayout.LayoutParams.MATCH_PARENT
            )
            if (defaultMargin > 0) {
                params.setMargins(0, 0, defaultMargin.dp2px(), 0)
            }
            textSize = fontSize
            if (textColor > 0) {
                setTextCompatColor(textColor)
            }
            addView(this, params)
        }
    }
}

fun ViewPager.initial(
    manager: FragmentManager, fragments: ArrayList<Fragment>, listener: (Int) -> Unit = {}
) {
    adapter = object : FragmentStatePagerAdapter(
        manager, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT
    ) {
        override fun getCount() = fragments.size
        override fun getItem(position: Int) = fragments[position]
    }

    addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
        override fun onPageScrolled(
            position: Int, positionOffset: Float, positionOffsetPixels: Int
        ) {
        }

        override fun onPageSelected(position: Int) {
            listener.invoke(position)
        }

        override fun onPageScrollStateChanged(state: Int) {
        }
    })
}

fun ViewPager2.initial(
    fragment: Fragment, fragments: List<Fragment>, listener: (Int) -> Unit = {}
) {
    adapter = object : FragmentStateAdapter(
        fragment
    ) {
        override fun getItemCount() = fragments.size
        override fun createFragment(position: Int) = fragments[position]
    }
    registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
        override fun onPageSelected(position: Int) {
            super.onPageSelected(position)
            listener.invoke(position)
        }
    })
}

fun ViewPager2.initial(
    activity: AppCompatActivity, fragments: List<Fragment>, listener: (Int) -> Unit = {}
) {
    adapter = object : FragmentStateAdapter(
        activity
    ) {
        override fun getItemCount() = fragments.size
        override fun createFragment(position: Int) = fragments[position]
    }
    registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
        override fun onPageSelected(position: Int) {
            super.onPageSelected(position)
            listener.invoke(position)
        }
    })
}


fun WebView.loadData(content: String?, margin: Int = 5) {
    content?.let {
        val css =
            "<style> img{max-width:100%;width:100% !important;height:auto !important;min-height:10px;border-radius:5px;} p{margin-top:0 !important;margin-bottom:0 !important;}</style>"
        val fontCss =
            "<style>p{font-size :16px;color:#333333; !important;line-height:25px !important}</style>"
        val style =
            "<html><header>$css$fontCss</header><body style='margin:${margin}px;padding:0'>${
                it.let {
                    if (it.contains("<p>")) it else "<p>$it</p>"
                }
            }</body></html>"

        loadDataWithBaseURL(null, style, "text/html", "utf-8", null)
    }
}

fun View.showSoftKeyboard() {
    val imm = this.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    imm.showSoftInput(this, InputMethodManager.SHOW_FORCED)
}

fun View.hideSoftKeyboard() {
    val imm = AppManager.getApplication().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    imm.hideSoftInputFromWindow(this.windowToken, 0)
}

fun TextView.setTextCompatColor(color: Int) {
    setTextColor(ContextCompat.getColor(AppManager.getApplication(), color))
}

fun TextView.setHintTextCompatColor(color: Int) {
    setHintTextColor(ContextCompat.getColor(AppManager.getApplication(), color))
}

/**
 * @param fixedEndString 末尾固定后缀 例如 刚刚加入 1天前加入
 * @date 2021/10/25添加
 */
@SuppressLint("SetTextI18n")
fun TextView.showAppTime(
    createTime: Long,
    beforeString: String? = "",
    afterString: String? = "",
    fixedEndString: String = ""
) {
    val now = System.currentTimeMillis()
    val cal = Calendar.getInstance()
    cal[Calendar.HOUR_OF_DAY] = 0
    cal[Calendar.SECOND] = 0
    cal[Calendar.MINUTE] = 0
    cal[Calendar.MILLISECOND] = 0
    val wee = cal.timeInMillis
    val span: Long = now - createTime
    if (span < 0) // U can read http://www.apihome.cn/api/java/Formatter.html to understand it.
        this.text = String.format("%tc", createTime).trim()
    when {
        span < 60000 -> {
            this.text = "刚刚$fixedEndString"
        }

        span < 3600000 -> { //不到1小时
            this.text = String.format(
                Locale.getDefault(),
                "$beforeString%d分钟$afterString$fixedEndString",
                span / DateUtil.TimeConstants.MIN
            ).trim()
        }

        createTime >= wee -> { //不到一天
//            this.text = String.format("发布于 今天%tR", createTime)
            this.text = String.format(
                Locale.getDefault(),
                "$beforeString%d小时$afterString$fixedEndString",
                span / DateUtil.TimeConstants.HOUR
            ).trim()

        }

        createTime >= wee - DateUtil.TimeConstants.DAY -> {
//            this.text = String.format("发布于 昨天%tR", createTime)
            this.text = String.format("${beforeString}昨天", createTime).trim()
        }

        span <= 2419200000 -> { //小于28天
//            this.text = String.format("发布于${span/TimeConstants.DAY}天前%tR", createTime)
            val bd =
                BigDecimal((span.toDouble() / DateUtil.TimeConstants.DAY.toDouble()).toString())
            this.text = String.format(
                "$beforeString${
                    bd.setScale(0, BigDecimal.ROUND_UP).toLong()
                }天$afterString$fixedEndString", createTime
            ).trim()
        }

        else -> {
            if (DateUtil.isLeapYear(createTime)) {
                if (span < DateUtil.TimeConstants.DAY * 366L) {
                    this.text = "$beforeString${String.format("%tm", createTime).trim()}-${
                        String.format(
                            "%td", createTime
                        ).trim()
                    } ${
                        SimpleDateFormat(
                            "HH:mm", Locale.getDefault()
                        ).format(createTime)
                    }$fixedEndString"

                } else {
                    this.text = "${String.format("$beforeString%tF", createTime).trim()} ${
                        SimpleDateFormat("HH:mm", Locale.getDefault()).format(createTime)
                    }$fixedEndString"
                }
            } else {
                if (span < DateUtil.TimeConstants.DAY * 365L) {
                    this.text = "${String.format("%tm", createTime).trim()}-${
                        String.format(
                            "%td", createTime
                        )
                    } ${
                        SimpleDateFormat(
                            "HH:mm", Locale.getDefault()
                        ).format(createTime)
                    }$fixedEndString"
                } else {
                    this.text = "${String.format("$beforeString%tF", createTime).trim()} ${
                        SimpleDateFormat("HH:mm", Locale.getDefault()).format(createTime)
                    }$fixedEndString"
                }
            }
        }
    }
}

fun View.applyShadow(radius: Float = 8f) {
    ShadowUtil.apply(
        this, ShadowUtil.Config().setShadowRadius(
            radius.dp2px().toFloat()
        ).setShadowColor(
            ContextCompat.getColor(
                this.context, R.color.color_F8F8F8
            )
        )
    )
}

fun View.setBackgroundCompatColor(color: Int) {
    setBackgroundColor(ContextCompat.getColor(context, color))
}

fun View.setBackgroundCompatResource(res: Int) {
    background = ContextCompat.getDrawable(
        this.context, res
    )
}

fun TextView.adapterWidth(): String {
    val rawText: String = getText().toString() //原始文本
    val tvPaint: Paint = getPaint() //paint，包含字体等信息
    val tvWidth: Float = getWidth() - getPaddingLeft() - getPaddingRight().toFloat() //控件可用宽度

    //将原始文本按行拆分
    val rawTextLines = rawText.replace("\r".toRegex(), "").split("\n".toRegex()).toTypedArray()
    val sbNewText = StringBuilder()
    for (rawTextLine in rawTextLines) {
        if (tvPaint.measureText(rawTextLine) <= tvWidth) {
            //如果整行宽度在控件可用宽度之内，就不处理了
            sbNewText.append(rawTextLine)
        } else {
            //如果整行宽度超过控件可用宽度，则按字符测量，在超过可用宽度的前一个字符处手动换行
            var lineWidth = 0f
            var cnt = 0
            while (cnt != rawTextLine.length) {
                val ch = rawTextLine[cnt]
                lineWidth += tvPaint.measureText(ch.toString())
                if (lineWidth <= tvWidth) {
                    sbNewText.append(ch)
                } else {
                    sbNewText.append("\n")
                    lineWidth = 0f
                    --cnt
                }
                ++cnt
            }
        }
        sbNewText.append("\n")
    }

    //把结尾多余的\n去掉

    //把结尾多余的\n去掉
    if (!rawText.endsWith("\n")) {
        sbNewText.deleteCharAt(sbNewText.length - 1)
    }

    return sbNewText.toString()
}

fun TextView.gradientColor(@ColorRes startColor: Int, @ColorRes endColor: Int) {
    val endX = paint.textSize * text.length
    val gradient = LinearGradient(
        0F,
        0F,
        endX,
        0F,
        ContextCompat.getColor(context, startColor),
        ContextCompat.getColor(context, endColor),
        Shader.TileMode.CLAMP
    )
    paint.shader = gradient
    invalidate()
}

fun ImageView.setBackgroundTint(color: Int) {
    backgroundTintList = ColorStateList.valueOf(ContextCompat.getColor(context, color))
}