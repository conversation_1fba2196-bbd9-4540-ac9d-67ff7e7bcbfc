package com.bdc.android.library.bus

import android.util.Log
import androidx.lifecycle.*
import kotlinx.coroutines.InternalCoroutinesApi
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch

/**
 *
 * @Author： Lxf
 * @Time： 2022/3/16 4:31 下午
 * @description：
 */
object FlowBus {
    private const val TAG = "BUS"
    private val busMap = mutableMapOf<String, EventBus<*>>()

    /**
     * Get EventBus
     * @return  returned object cannot be saved as a variable
     */
    @Synchronized
    fun <T> with(key: String): EventBus<T> {
        var eventBus = busMap[key]
        if (eventBus == null) {
            eventBus = EventBus<T>(key)
            busMap[key] = eventBus
        }
        return eventBus as EventBus<T>
    }

    class EventBus<T>(private val key: String) : LifecycleObserver {

        private val _events = MutableSharedFlow<T>() // private mutable shared flow
        private val events = _events.asSharedFlow() // publicly exposed as read-only shared flow

        /**
         * need main thread execute
         */
        fun register(lifecycleOwner: LifecycleOwner, action: (t: T) -> Unit) {
            lifecycleOwner.lifecycle.addObserver(this)
            lifecycleOwner.lifecycleScope.launch {
                events.collect {
                    try {
                        action(it)
                    } catch (e: Exception) {
                        Log.e(TAG, "KEY:%s---ERROR:%s".format(key, e.toString()))
                    }
                }
            }
        }

        /**
         * send value
         */
        suspend fun post(event: T) {
            _events.emit(event)
        }

        /**
         * When subscriptionCount less than 0,remove event object in map
         */
        @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
        fun onDestroy() {
            val subscriptCount = _events.subscriptionCount.value
            if (subscriptCount <= 0)
                busMap.remove(key)
        }
    }
}