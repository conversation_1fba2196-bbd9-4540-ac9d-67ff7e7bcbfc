package com.bdc.android.library.utils

import android.Manifest
import android.annotation.SuppressLint
import android.app.Application
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import androidx.core.content.FileProvider
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.bdc.android.library.extension.requestPermissions
import com.bdc.android.library.widget.popup.buildPermissionRemindPopup
import com.lxj.xpopup.core.BasePopupView
import com.permissionx.guolindev.PermissionX
import java.io.File


object AppManager {

    const val MEDIA_PERMISSION = 0
    const val LOCATION_PERMISSION = 1

    val permissionsMedia = arrayOf(
        Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.CAMERA
    )

    val permissionsLocation = arrayOf(
        Manifest.permission.ACCESS_COARSE_LOCATION, Manifest.permission.ACCESS_FINE_LOCATION
    )

    @SuppressLint("PrivateApi", "DiscouragedPrivateApi")
    fun getApplication(): Application {
        val activityThread = Class.forName("android.app.ActivityThread")
        return try {
            val currentApplication = activityThread.getDeclaredMethod("currentApplication")
            val currentActivityThread = activityThread.getDeclaredMethod("currentActivityThread")
            val current = currentActivityThread.invoke(null)
            currentApplication.invoke(current) as Application
        } catch (e: Exception) {
            e.printStackTrace()
            Application()
        }
    }

    fun getAppDetailSettingIntent(context: Context): Intent? {
        val localIntent = Intent()
//        localIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        if (Build.VERSION.SDK_INT >= 9) {
            localIntent.action = "android.settings.APPLICATION_DETAILS_SETTINGS"
            localIntent.data = Uri.fromParts("package", context.packageName, null)
        } else if (Build.VERSION.SDK_INT <= 8) {
            localIntent.action = Intent.ACTION_VIEW
            localIntent.setClassName(
                "com.android.settings", "com.android.settings.InstalledAppDetails"
            )
            localIntent.putExtra("com.android.settings.ApplicationPkgName", context.packageName)
        }
        return localIntent
    }

    fun appIsInstalled(context: Context, packageName: String): Boolean {
        val manager = context.packageManager
        val packageInfoList = manager.getInstalledPackages(0)
        packageInfoList.forEach {
            if (it.packageName.equals(packageName)) {
                return true
            }
        }
        return false
    }

    fun callPhone(context: Context, phoneNum: String) {
        val intent = Intent(Intent.ACTION_DIAL)
        val data = Uri.parse("tel:$phoneNum")
        intent.data = data
        context.startActivity(intent)
    }

    fun startInstallApk(context: Context, file: File) {
        context.startActivity(getInstallApkIntent(context, file))
    }

    fun getInstallApkIntent(context: Context, file: File): Intent {
        val intent = Intent(Intent.ACTION_VIEW)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        val apkUri: Uri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            FileProvider.getUriForFile(
                context, context.packageName + ".fileprovider", file
            )
        } else {
            Uri.fromFile(file)
        }
        intent.setDataAndType(apkUri, "application/vnd.android.package-archive")
        val resolveLists = context.packageManager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY)
        for (resolveInfo in resolveLists) {
            val packageName = resolveInfo.activityInfo.packageName
            context.grantUriPermission(
                packageName, apkUri, Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_WRITE_URI_PERMISSION
            )
        }
        return intent
    }

//    fun checkPermissions(context: FragmentActivity, block: ((Boolean) -> Unit)) {
//        context?.requestPermissions(
//            Manifest.permission.READ_EXTERNAL_STORAGE,
//            Manifest.permission.WRITE_EXTERNAL_STORAGE,
//            Manifest.permission.CAMERA
//        ) { name, granted ->
//            if (granted) {
//                block.invoke(true)
//            } else {
//                ToastUtil.show("请打开权限")
//                getAppDetailSettingIntent(context)?.let {
//                    context.startActivity(it)
//                }
//            }
//        }
//
//    }

    /**
     * type  0媒体权限  1定位权限
     */
    fun checkPermissions(
        context: FragmentActivity, type: Int = MEDIA_PERMISSION, isJump: Boolean = true, block: ((Boolean) -> Unit)
    ) {
        var popup: BasePopupView? = null
        (if (type == MEDIA_PERMISSION) permissionsMedia else permissionsLocation).map {
            PermissionX.init(context).permissions(it).showDialogCalled
        }.find { !it }.let {
            it?.let {
                popup = buildPermissionRemindPopup(
                    context,
                    if (type == 0) "V运动需申请您的存储和相机权限，以便您在使用修改头像、发布动态、发布比赛、修改俱乐部封面、实名认证、发送私信、举报、联系我们以及上传教练、裁判、陪练证书功能，不授权会使您无法使用该功能，但并不影响您正常产品与/或服务的其他功能。" else "V运动需申请您的位置权限，不授权会使您无法使用该功能，但并不影响您正常产品与/或服务的其他功能。"
                )
                popup?.show()
            }
        }

        context.requestPermissions(
            *(if (type == MEDIA_PERMISSION) permissionsMedia else permissionsLocation)
        ) { _, granted ->
            if (granted) {
                block.invoke(true)
                popup?.dismiss()
            } else {
                block.invoke(false)
                popup?.dismiss()
                if (!isJump) {
                    return@requestPermissions
                }
                ToastUtil.show("请打开权限")
                getAppDetailSettingIntent(context)?.let {
                    context.startActivity(it)
                }
            }
        }
    }

    /**
     * type  0媒体权限  1定位权限
     */
    fun checkPermissions(
        fragment: Fragment, type: Int = MEDIA_PERMISSION, isJump: Boolean = true, block: ((Boolean) -> Unit)
    ) {
        var popup: BasePopupView? = null
        (if (type == MEDIA_PERMISSION) permissionsMedia else permissionsLocation).map {
//            RxPermissions(fragment).isGranted(it)
            PermissionX.init(fragment).permissions(it).showDialogCalled
        }.find { !it }.let {
            it?.let {
                popup = buildPermissionRemindPopup(
                    fragment.requireContext(),
                    if (type == 0) "V运动需申请您的存储和相机权限，以便您在使用修改头像、发布动态、发布比赛、修改俱乐部封面、实名认证、发送私信、举报、联系我们以及上传教练、裁判、陪练证书功能，不授权会使您无法使用该功能，但并不影响您正常产品与/或服务的其他功能。" else "V运动需申请您的位置权限，不授权会使您无法使用该功能，但并不影响您正常产品与/或服务的其他功能。"
                )
                popup?.show()
            }
        }

        fragment?.requestPermissions(
            *(if (type == MEDIA_PERMISSION) permissionsMedia else permissionsLocation)
        ) { _, granted ->
            if (granted) {
                block.invoke(true)
                popup?.dismiss()
            } else {
                block.invoke(false)
                popup?.dismiss()
                if (!isJump) {
                    return@requestPermissions
                }
                ToastUtil.show("请打开权限")
                getAppDetailSettingIntent(fragment.requireContext())?.let {
                    fragment.requireContext().startActivity(it)
                }
            }
        }
    }
}