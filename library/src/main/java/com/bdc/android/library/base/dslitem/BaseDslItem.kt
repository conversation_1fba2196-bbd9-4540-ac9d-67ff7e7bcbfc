package com.bdc.android.library.base.dslitem

import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.DslViewHolder

/**
 * Copyright (C), 2020-2021, 中传互动（湖北）信息技术有限公司
 * @Author: pym
 * @Date: 2021/6/3 17:09
 * @Description:
 */
open class BaseDslItem<T>(var layout: Int) : DslAdapterItem() {
    var data: T? = null

    init {
        itemLayoutId = layout
    }

    override fun onItemBind(
        itemHolder: DslViewHolder,
        itemPosition: Int,
        adapterItem: DslAdapterItem,
        payloads: List<Any>
    ) {
        super.onItemBind(itemHolder, itemPosition, adapterItem, payloads)
        data = itemData as? T
    }
}