package com.bdc.android.library.lifecycle

import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager

/**
 * @PackageName : com.bdc.android.library.utils
 * <AUTHOR> Waiting
 * @Date :   2021/8/12 18:59
 */
class LifecycleFragment : Fragment() {
    companion object {
        const val FRAGMENT_TAG = "LifecycleFragment"

        fun newInstance(context: AppCompatActivity?): LifecycleFragment {
            val fragment = LifecycleFragment()
            addFragment(context?.supportFragmentManager,fragment)
            return fragment
        }

        fun newInstance(context: Fragment?): LifecycleFragment {
            val fragment = LifecycleFragment()
            addFragment(context?.childFragmentManager,fragment)
            return fragment
        }

        private fun addFragment(fragmentManager: FragmentManager?, fragment: LifecycleFragment) {
            fragmentManager?.beginTransaction()?.add(
                fragment,
                FRAGMENT_TAG
            )?.commitAllowingStateLoss()
        }
    }


//    init {
//        val current = ActivityManager.current as? AppCompatActivity
//        current?.supportFragmentManager?.beginTransaction()?.add(this, FRAGMENT_TAG)
//            ?.commitAllowingStateLoss()
//    }
}