package com.bdc.android.library.http

import android.annotation.SuppressLint
import com.bdc.android.library.http.interceptor.LoggerInterceptor
import com.bdc.android.library.utils.ClassUtil
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.security.SecureRandom
import java.security.cert.CertificateException
import java.security.cert.X509Certificate
import java.util.concurrent.TimeUnit
import javax.net.ssl.HostnameVerifier
import javax.net.ssl.SSLContext
import javax.net.ssl.SSLSocketFactory
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager

/**
 * Copyright (C), 2019-2021, 中传互动（湖北）信息技术有限公司
 * Author: HeChao
 * Date: 2021/6/8 15:20
 * Description:
 */
abstract class CoreRetrofit<T> {

    protected lateinit var retrofit: Retrofit

    fun buildRetrofitService(): T {
        val clientBuilder = OkHttpClient.Builder()
            .connectTimeout(getConnectTimeoutSeconds().toLong(), TimeUnit.SECONDS)
            .readTimeout(getReadTimeoutSeconds().toLong(), TimeUnit.SECONDS)
            .writeTimeout(getWriteTimeoutSeconds().toLong(), TimeUnit.SECONDS)

        // 添加所有拦截器
        getInterceptors().forEach { interceptor ->
            clientBuilder.addInterceptor(interceptor)
        }

        // 添加默认的日志拦截器（如果需要）
        if (shouldAddDefaultLogInterceptor()) {
            clientBuilder.addInterceptor(LoggerInterceptor())
        }

        val client = clientBuilder
            .sslSocketFactory(SSLSocketClient.sSLSocketFactory, SSLSocketClient.trustManager)
            .hostnameVerifier(SSLSocketClient.hostnameVerifier)
            .build()

        retrofit = Retrofit.Builder()
            .client(client)
            .baseUrl(getBaseUrl())
            .addConverterFactory(GsonConverterFactory.create())
            .build()

        return retrofit.create(ClassUtil.create<T>(javaClass.genericSuperclass, 0))
    }

    /**
     * 获取所有拦截器列表
     * 子类应该重写此方法来提供自定义拦截器
     */
    open fun getInterceptors(): List<Interceptor> {
        // 为了向后兼容，如果子类重写了getInterceptor()方法，则使用它
        return try {
            listOf(getInterceptor())
        } catch (e: Exception) {
            emptyList()
        }
    }

    /**
     * 获取单个拦截器（向后兼容）
     * 建议子类重写getInterceptors()方法而不是这个方法
     */
    @Deprecated("建议使用getInterceptors()方法", ReplaceWith("getInterceptors()"))
    open fun getInterceptor(): Interceptor {
        throw NotImplementedError("请重写getInterceptors()方法或getInterceptor()方法")
    }

    /**
     * 是否添加默认的日志拦截器
     */
    open fun shouldAddDefaultLogInterceptor(): Boolean = true

    /**
     * 获取连接超时时间（秒）
     */
    open fun getConnectTimeoutSeconds(): Int = 30

    /**
     * 获取读取超时时间（秒）
     */
    open fun getReadTimeoutSeconds(): Int = 30

    /**
     * 获取写入超时时间（秒）
     */
    open fun getWriteTimeoutSeconds(): Int = 30

    abstract fun getBaseUrl(): String

    object SSLSocketClient {
        //获取这个SSLSocketFactory
        val sSLSocketFactory: SSLSocketFactory
            get() = try {
                val sslContext = SSLContext.getInstance("SSL")
                sslContext.init(
                    null, trustManagers, SecureRandom()
                )
                sslContext.socketFactory
            } catch (e: Exception) {
                throw java.lang.RuntimeException(e)
            }

        //获取TrustManager
        private val trustManagers: Array<TrustManager>
            get() {
                return arrayOf(trustManager)
            }

        //获取HostnameVerifier
        val hostnameVerifier: HostnameVerifier
            get() {
                return HostnameVerifier { _, _ -> true }
            }

        val trustManager: X509TrustManager
            get() = MyTrustManager()

        @SuppressLint("TrustAllX509TrustManager", "CustomX509TrustManager")
        private class MyTrustManager : X509TrustManager {
            @Throws(CertificateException::class)
            override fun checkClientTrusted(
                chain: Array<X509Certificate>, authType: String
            ) {
            }

            @Throws(CertificateException::class)
            override fun checkServerTrusted(
                chain: Array<X509Certificate>, authType: String
            ) {
            }

            override fun getAcceptedIssuers(): Array<X509Certificate?> {
                return arrayOfNulls(0)
            }
        }
    }
}