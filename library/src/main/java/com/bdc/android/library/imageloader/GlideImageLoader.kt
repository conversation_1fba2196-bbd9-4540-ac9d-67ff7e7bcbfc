package com.bdc.android.library.imageloader

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Resources
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.text.TextUtils
import android.util.TypedValue
import com.bdc.android.library.R
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestBuilder
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import jp.wasabeef.glide.transformations.BlurTransformation
import jp.wasabeef.glide.transformations.RoundedCornersTransformation
import java.util.concurrent.ExecutionException

/**
 * Copyright (C), 2019-2020, 中传互动（湖北）信息技术有限公司
 * Author: He<PERSON><PERSON>
 * Date: 2020/10/10 16:26
 * Description:图片加载实现类
 */
class GlideImageLoader : ILoader {

    override fun request(context: Context, wrapper: RequestWrapper) {
        wrapper.imageView?.let {
            getRequestBuilder(load(context, wrapper), wrapper)?.into(it)
        }
    }

    private fun load(context: Context, wrapper: RequestWrapper): RequestBuilder<*>? {
        if (wrapper.isGif) {
            wrapper.imageView?.let {
                getRequestManager(context).asGif().diskCacheStrategy(DiskCacheStrategy.RESOURCE)
                    .load(wrapper.url)
                    .into(it)
            }
            return null
        }
        return if (!TextUtils.isEmpty(wrapper.url))
            getRequestManager(context).load(wrapper.url)
        else if (wrapper.resId > 0) {
            getRequestManager(context).load(wrapper.resId)
        } else if (wrapper.file != null && wrapper.file?.exists() == true) {
            getRequestManager(context).load(wrapper.file)
        } else if (wrapper.uri != null) {
            getRequestManager(context).load(wrapper.uri)
        } else {
            when {
                wrapper.placeDrawable != null -> {
                    getRequestManager(context).load(wrapper.placeDrawable)
                }
                wrapper.placeholderId > 0 -> {
                    getRequestManager(context).load(wrapper.placeholderId)
                }
                wrapper.errorDrawable != null -> {
                    getRequestManager(context).load(wrapper.errorDrawable)
                }
                wrapper.errorId > 0 -> {
                    getRequestManager(context).load(wrapper.errorId)
                }
                wrapper.isAvatar -> {
                    getRequestManager(context).load(R.mipmap.ic_default_avatar)
                }
                wrapper.isIcon -> {
                    getRequestManager(context).load(R.drawable.ic_launcher_background)
                }
                wrapper.isCover -> {
                    getRequestManager(context).load(R.drawable.ic_launcher_background)
                }
                else -> {
                    getRequestManager(context).load(wrapper.placeholderId)
                }
            }
        }
    }

    override fun get(context: Context, wrapper: RequestWrapper): Bitmap? {
        try {
            return load(
                context,
                wrapper
            )?.submit(
                if (wrapper.width == 0) Target.SIZE_ORIGINAL else wrapper.width,
                if (wrapper.height == 0) Target.SIZE_ORIGINAL else wrapper.height
            )?.get() as Bitmap
        } catch (e: InterruptedException) {
            e.printStackTrace()
        } catch (e: ExecutionException) {
            e.printStackTrace()
        }
        return null
    }

    private fun getRequestManager(context: Context): RequestManager = Glide.with(context)

    @SuppressLint("CheckResult")
    private fun getRequestBuilder(
        requestBuilder: RequestBuilder<*>?,
        wrapper: RequestWrapper
    ): RequestBuilder<*>? {
        val options = RequestOptions()

        /*处理默认加载头像模式*/
        if (wrapper.isAvatar) {
            options.placeholder(R.mipmap.ic_default_avatar)
                .override(100, 100)
                .error(R.mipmap.ic_default_avatar)
        }

        /*默认加载icon占位图*/
        if (wrapper.isIcon) {
            options.placeholder(R.drawable.ic_launcher_background)
                .error(R.drawable.ic_launcher_background)
        }

        /*默认加载背景占位图*/
        if (wrapper.isCover) {
            options.placeholder(R.drawable.ic_launcher_background)
                .error(R.drawable.ic_launcher_background)
        }

        if (wrapper.placeholderId > 0) {
            options.placeholder(wrapper.placeholderId)
        }

        wrapper.placeDrawable?.let { options.placeholder(it) }

        if (wrapper.errorId > 0) {
            options.error(wrapper.errorId)
        }

        wrapper.errorDrawable?.let { options.error(it) }

        if (wrapper.isRandomColor) {
            options.placeholder(
                createDrawable(
                    radius = wrapper.radius.toFloat(),
                    cornerType = wrapper.cornerType
                )
            )
        }

        if (!wrapper.crossFade) {
//            options.dontAnimate()
        }

        if (wrapper.width > 0 && wrapper.height > 0) {
            options.override(wrapper.width, wrapper.height)
            options.centerCrop()
        }

        options.skipMemoryCache(wrapper.skipMemoryCache)
        options.diskCacheStrategy(wrapper.diskCacheStrategy)

        if (wrapper.radius > 0) {
            options.transform(
                if (wrapper.scaleType == null) CenterCrop() else wrapper.scaleType,
                RoundedCornersTransformation(wrapper.radius, 0, wrapper.cornerType)
            )
        }

        if (wrapper.isCircle || wrapper.isAvatar) {
            options.transform(CircleCrop())
        }

        if (wrapper.blur > 0) {
            options.transform(BlurTransformation(wrapper.blur))
        }

        if (wrapper.isGif) {
            options.skipMemoryCache(true)
        }

        return requestBuilder?.apply(options)
    }

    private fun createDrawable(
        radius: Float = 8F,
        cornerType: RoundedCornersTransformation.CornerType
    ): Drawable {
        val drawable = GradientDrawable()
        when (cornerType) {
            RoundedCornersTransformation.CornerType.TOP -> {
                drawable.cornerRadii =
                    floatArrayOf(
                        radius,
                        radius,
                        radius,
                        radius,
                        0F,
                        0F,
                        0F,
                        0F
                    )
            }
            RoundedCornersTransformation.CornerType.BOTTOM -> {
                drawable.cornerRadii =
                    floatArrayOf(
                        0F,
                        0F,
                        0F,
                        0F,
                        radius.dp2px(),
                        radius.dp2px(),
                        radius.dp2px(),
                        radius.dp2px()
                    )
            }
            RoundedCornersTransformation.CornerType.TOP_LEFT -> {
                drawable.cornerRadii =
                    floatArrayOf(
                        radius.dp2px(),
                        radius.dp2px(),
                        0F,
                        0F,
                        0F,
                        0F,
                        0F,
                        0F
                    )
            }
            RoundedCornersTransformation.CornerType.TOP_RIGHT -> {
                drawable.cornerRadii =
                    floatArrayOf(
                        0F,
                        0F,
                        radius.dp2px(),
                        radius.dp2px(),
                        0F,
                        0F,
                        0F,
                        0F
                    )
            }
            RoundedCornersTransformation.CornerType.BOTTOM_LEFT -> {
                drawable.cornerRadii =
                    floatArrayOf(
                        0F,
                        0F,
                        0F,
                        0F,
                        radius.dp2px(),
                        radius.dp2px(),
                        0F,
                        0F
                    )
            }
            RoundedCornersTransformation.CornerType.BOTTOM_RIGHT -> {
                drawable.cornerRadii =
                    floatArrayOf(
                        0F,
                        0F,
                        0F,
                        0F,
                        0F,
                        0F,
                        radius.dp2px(),
                        radius.dp2px()
                    )
            }
            else -> drawable.cornerRadius = radius.dp2px()
        }
        drawable.setColor(randomColor())
        return drawable
    }

    private fun randomColor(): Int {
        val list = arrayListOf(
            "#583F66",
            "#677C7D",
            "#987E97",
            "#97989C",
            "#52788D",
            "#9B9DB6",
            "#B25D58",
            "#918A9C"
        )
        return Color.parseColor(list.random())
    }

    private fun Float.dp2px(): Float {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            this,
            Resources.getSystem().displayMetrics
        )
    }
}
