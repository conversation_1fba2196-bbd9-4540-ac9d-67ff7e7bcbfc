package com.bdc.android.library.utils

import android.content.Context
import android.content.pm.PackageManager

/**
 * Copyright (C), 2019-2021, 中传互动（湖北）信息技术有限公司
 * Author: <PERSON><PERSON><PERSON>
 * Date: 2021/7/28 9:34
 * Description: 获取manifest中meta配置数据
 */
object MetaDataUtil {

    fun getMetaData(ctx: Context, key: String): String? {
        return ctx.packageManager?.getApplicationInfo(
            ctx.packageName,
            PackageManager.GET_META_DATA
        )?.metaData?.get(key)?.toString()
    }

    fun putMetaData(ctx: Context, key: String, value: String) {
        ctx.packageManager?.getApplicationInfo(
            ctx.packageName,
            PackageManager.GET_META_DATA
        )?.metaData?.putString(key, value)
    }
}