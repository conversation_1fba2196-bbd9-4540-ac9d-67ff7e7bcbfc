package com.bdc.android.library.utils


import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: <PERSON>cha<PERSON>
 * @date: 2024/12/13 10:46
 * @description :
 */
object PreferencesUtil {

    private const val PREF_NAME = "com.bdc.android.library.utils.PreferencesUtil"

    @PublishedApi
    internal lateinit var sharedPreferences: SharedPreferences

    /**
     * 初始化 SharedPreferences
     * @param context 应用上下文
     * @param prefName 自定义 SharedPreferences 文件名，可选
     */
    fun init(context: Context, prefName: String = PREF_NAME) {
        sharedPreferences = context.getSharedPreferences(prefName, Context.MODE_PRIVATE)
    }

    /**
     * 保存数据
     * 支持基本数据类型和 StringSet
     * @param key 键
     * @param value 值
     */
    fun <T> put(key: String, value: T) {
        val editor = sharedPreferences.edit()
        when (value) {
            is String -> editor.putString(key, value)
            is Int -> editor.putInt(key, value)
            is Boolean -> editor.putBoolean(key, value)
            is Float -> editor.putFloat(key, value)
            is Long -> editor.putLong(key, value)
            is Set<*> -> {
                if (value.all { it is String }) {
                    @Suppress("UNCHECKED_CAST") editor.putStringSet(key, value as Set<String>)
                } else {
                    throw IllegalArgumentException("SharedPreferences only supports Set<String> for Set type.")
                }
            }

            else -> putObject(key, value)
        }
        editor.apply()
    }

    internal fun <T> putObject(key: String, value: T) {
        put(key, Gson().toJson(value))
    }

    /**
     * 获取数据
     * 支持基本数据类型和 StringSet
     * @param key 键
     * @param defaultValue 默认值
     */
    inline fun <reified T> get(key: String, defaultValue: T? = null): T? {
        return when (defaultValue) {
            is String -> sharedPreferences.getString(key, defaultValue) as T
            is Int -> sharedPreferences.getInt(key, defaultValue) as T
            is Boolean -> sharedPreferences.getBoolean(key, defaultValue) as T
            is Float -> sharedPreferences.getFloat(key, defaultValue) as T
            is Long -> sharedPreferences.getLong(key, defaultValue) as T
            is Set<*> -> sharedPreferences.getStringSet(key, defaultValue as Set<String>) as T
            else -> getObject(key, defaultValue)
        }
    }

    inline fun <reified T> getObject(key: String, defaultValue: T): T? {
        val json = sharedPreferences.getString(key, null)
        return if (json != null) {
            Gson().fromJson(json, T::class.java) ?: defaultValue
        } else {
            defaultValue
        }
    }

    /**
     * 删除指定键对应的数据
     * @param key 键
     */
    fun remove(key: String) {
        sharedPreferences.edit().remove(key).apply()
    }

    /**
     * 清除所有数据
     */
    fun clear() {
        sharedPreferences.edit().clear().apply()
    }

    /**
     * 检查某个键是否存在
     * @param key 键
     */
    fun contains(key: String): Boolean {
        return sharedPreferences.contains(key)
    }
}
