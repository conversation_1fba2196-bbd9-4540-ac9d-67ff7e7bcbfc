package com.bdc.android.library.imageloader

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.net.Uri
import android.widget.ImageView
import androidx.annotation.CheckResult
import androidx.annotation.DrawableRes
import androidx.annotation.IntRange
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation
import jp.wasabeef.glide.transformations.RoundedCornersTransformation
import java.io.File

/**
 * Copyright (C), 2019-2020, 中传互动（湖北）信息技术有限公司
 * Author: He<PERSON>hao
 * Date: 2020/10/12 15:54
 * Description:加载配置参数
 */
class RequestBuilder constructor(private val context: Context, private val loader: ILoader) {

    private var url: String? = null
    private var resId = 0
    private var file: File? = null
    private var uri: Uri? = null
    private var errorId = 0
    private var errorDrawable: Drawable? = null
    private var placeholderId = 0
    private var placeholderDrawable: Drawable? = null
    private var skipMemoryCache = false
    private var diskCacheStrategy: DiskCacheStrategy = DiskCacheStrategy.AUTOMATIC
    private var crossFade = false
    private var width = 0
    private var height = 0
    private var blur = 0
    private var cornerType: RoundedCornersTransformation.CornerType =
        RoundedCornersTransformation.CornerType.ALL
    private var radius = 0
    private var scaleType: BitmapTransformation? = null
    private var isCircle = false
    private var isAvatar = false
    private var isIcon = false
    private var isCover = false
    private var isRandomColor = false
    private var isGif = false

    internal fun url(url: String?): RequestBuilder {
        this.url = url
        return this
    }

    internal fun resId(resId: Int): RequestBuilder {
        this.resId = resId
        return this
    }

    internal fun file(file: File): RequestBuilder {
        this.file = file
        return this
    }

    internal fun uri(uri: Uri): RequestBuilder {
        this.uri = uri
        return this
    }

    @CheckResult
    fun error(@DrawableRes resId: Int): RequestBuilder {
        errorId = resId
        return this
    }

    @CheckResult
    fun error(drawable: Drawable?): RequestBuilder {
        errorDrawable = drawable
        return this
    }

    @CheckResult
    fun placeholder(@DrawableRes resId: Int): RequestBuilder {
        placeholderId = resId
        return this
    }

    @CheckResult
    fun placeholder(drawable: Drawable?): RequestBuilder {
        placeholderDrawable = drawable
        return this
    }

    @CheckResult
    fun skipMemoryCache(skip: Boolean): RequestBuilder {
        skipMemoryCache = skip
        return this
    }

    @CheckResult
    fun diskCacheStrategy(strategy: DiskCacheStrategy): RequestBuilder {
        diskCacheStrategy = strategy
        return this
    }

    @CheckResult
    fun crossFade(): RequestBuilder {
        crossFade = true
        return this
    }

    @CheckResult
    fun override(width: Int, height: Int): RequestBuilder {
        this.width = width
        this.height = height
        return this
    }

    @CheckResult
    fun blur(blur: Int): RequestBuilder {
        this.blur = blur
        return this
    }

    @CheckResult
    fun radius(@IntRange(from = 1, to = 100) radius: Int): RequestBuilder {
        this.radius = radius
        return this
    }

    @CheckResult
    fun scaleType(scaleType: BitmapTransformation): RequestBuilder {
        this.scaleType = scaleType
        return this
    }

    @CheckResult
    fun cornerType(cornerType: RoundedCornersTransformation.CornerType): RequestBuilder {
        this.cornerType = cornerType
        return this
    }

    @CheckResult
    fun asCircle(): RequestBuilder {
        isCircle = true
        return this
    }

    @CheckResult
    fun asGif(): RequestBuilder {
        isGif = true
        return this
    }

    @CheckResult
    fun asAvatar(): RequestBuilder {
        isAvatar = true
        return this
    }

    @CheckResult
    fun asIcon(): RequestBuilder {
        isIcon = true
        return this
    }

    @CheckResult
    fun asCover(): RequestBuilder {
        isCover = true
        return this
    }

    @CheckResult
    fun randomColor(): RequestBuilder {
        isRandomColor = true
        return this
    }

    fun into(imageView: ImageView) {
        loader.request(context, getWrapper(imageView))
    }

    fun asBitmap(): Bitmap? {
        return loader.get(context, getWrapper(null))
    }

    private fun getWrapper(imageView: ImageView?): RequestWrapper {
        return RequestWrapper.obtain(
            url = url,
            resId = resId,
            file = file,
            uri = uri,
            errorId = errorId,
            errorDrawable = errorDrawable,
            placeholderId = placeholderId,
            placeholderDrawable = placeholderDrawable,
            crossFade = crossFade,
            skipMemoryCache = skipMemoryCache,
            diskCacheStrategy = diskCacheStrategy,
            width = width,
            height = height,
            blur = blur,
            cornerType = cornerType,
            radius = radius,
            scaleType = scaleType,
            isCircle = isCircle,
            isGif = isGif,
            isAvatar = isAvatar,
            isIcon = isIcon,
            isCover = isCover,
            isRandomColor = isRandomColor,
            imageView = imageView
        )
    }
}