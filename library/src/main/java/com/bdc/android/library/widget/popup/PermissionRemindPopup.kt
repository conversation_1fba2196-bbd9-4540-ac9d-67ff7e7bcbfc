package com.bdc.android.library.widget.popup

import android.content.Context
import android.widget.TextView
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.core.PositionPopupView
import com.lxj.xpopup.enums.PopupAnimation
import com.bdc.android.library.R
import com.bdc.android.library.extension.dp2px
import com.bdc.android.library.utils.DisplayUtil

/**
 * Copyright (C), 2020-2021, 中传互动（湖北）信息技术有限公司
 * Author: He<PERSON>hao
 * Date: 2021/11/9 18:07
 * Description:
 */
class PermissionRemindPopup(context: Context, val text: String) :
    PositionPopupView(context) {

    override fun getImplLayoutId(): Int = R.layout.popup_permission_prompt


    override fun onCreate() {
        super.onCreate()
        findViewById<TextView>(R.id.tv_remind).text = text
    }

    override fun getPopupWidth(): Int {
        return DisplayUtil.getScreenWidth(context)
    }
}

fun buildPermissionRemindPopup(
    context: Context, text: String
): BasePopupView {
    return XPopup.Builder(context)
        .isDestroyOnDismiss(true)
        .popupAnimation(PopupAnimation.TranslateFromTop)
        .offsetY(50F.dp2px())
        .isCenterHorizontal(true)
        .asCustom(PermissionRemindPopup(context, text))
}