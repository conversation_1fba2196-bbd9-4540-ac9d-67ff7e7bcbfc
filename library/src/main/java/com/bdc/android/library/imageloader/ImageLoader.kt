package com.bdc.android.library.imageloader

import android.app.Activity
import android.content.Context
import android.view.View
import androidx.annotation.CheckResult
import androidx.fragment.app.Fragment

/**
 * Copyright (C), 2019-2020, 中传互动（湖北）信息技术有限公司
 * Author: <PERSON><PERSON><PERSON>
 * Date: 2020/10/10 16:26
 * Description:统一图片加载入口
 */
object ImageLoader {

    @CheckResult
    fun with(context: Context): RequestManager {
        return RequestManager(context)
    }

    @CheckResult
    fun with(activity: Activity): RequestManager {
        return RequestManager(activity)
    }

    @CheckResult
    fun with(fragment: Fragment): RequestManager {
        return RequestManager(fragment)
    }

    @CheckResult
    fun with(view: View): RequestManager {
        return RequestManager(view)
    }
}
