package com.bdc.android.library.ktnet.utils

import com.angcyo.dsladapter.elseNull
import kotlinx.coroutines.suspendCancellableCoroutine
import retrofit2.Call
import retrofit2.Callback
import retrofit2.HttpException
import retrofit2.Response
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 *
 * @Author： Lxf
 * @Time： 2022/2/17 3:48 下午
 * @description： 
 */
internal suspend fun <T> Call<T>.await(): T {
    return suspendCancellableCoroutine { continuation ->
        continuation.invokeOnCancellation {
            cancel()
        }
        enqueue(object : Callback<T> {

            override fun onResponse(call: Call<T>, response: Response<T>) {
                try {
                    if(response.isSuccessful) {
                        continuation.resume(response.body().elseNull { "" } as T)
                    } else {
                        continuation.resumeWithException(HttpException(response))
                    }
                } catch (t: Throwable) {
                    continuation.resumeWithException(t)
                }
            }

            override fun onFailure(call: Call<T>, t: Throwable) {
                continuation.resumeWithException(t)
            }
        })
    }
}