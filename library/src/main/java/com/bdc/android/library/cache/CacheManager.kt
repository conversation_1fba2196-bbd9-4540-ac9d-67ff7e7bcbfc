package com.bdc.android.library.cache

import android.content.Context
import com.bdc.android.library.utils.AppManager
import com.google.gson.Gson
import com.tencent.mmkv.MMKV

object CacheManager {

    init {
        build(AppManager.getApplication())
    }

    private fun build(context: Context): CacheManager {
        try {
            MMKV.initialize(context, context.filesDir.absolutePath)
        } catch (e: UnsatisfiedLinkError) {
            e.printStackTrace()
        }
        return this
    }

    private fun getMMKV(): MMKV {
        return MMKV.defaultMMKV()
    }

    fun putString(key: String, text: String) {
        getMMKV().encode(key, text.trim())
    }

    fun getString(key: String): String? {
        return if (hasKey(key)) getMMKV().decodeString(key) else null
    }

    fun putBoolean(key: String, boolean: Boolean) {
        MMKV.defaultMMKV().encode(key, boolean)
    }

    fun getBoolean(key: String, default: Boolean): Boolean {
        return if (hasKey(key)) getMMKV()?.decodeBool(key) ?: false else default
    }

    fun <T> put(key: String, t: T) {
        MMKV.defaultMMKV().encode(key, Gson().toJson(t))
    }

    inline fun <reified T : Any> get(key: String): T? {
        return if (hasKey(key)) Gson().fromJson(
            MMKV.defaultMMKV().decodeString(key), T::class.java
        ) else null
    }

    fun clear(key: String) {
        getMMKV()?.removeValueForKey(key)
    }

    fun clearAll() {
        getMMKV()?.clearAll()
    }

    fun hasKey(key: String): Boolean {
        return try {
            getMMKV()?.containsKey(key) ?: false
        } catch (e: IllegalStateException) {
            try {
                build(AppManager.getApplication().applicationContext)
            } catch (e: UnsatisfiedLinkError) {
                false
            }
            false
        }
    }
}
