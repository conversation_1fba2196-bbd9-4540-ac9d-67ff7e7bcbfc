package com.bdc.android.library.widget

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/7/12 19:28
 * @description :
 */
import androidx.lifecycle.Lifecycle
import com.angcyo.dsladapter.DslLoadMoreItem
import com.angcyo.dsladapter.DslViewHolder

/**
 * [RecyclerView.Adapter] 加载更多实现
 * Email:<EMAIL>
 * <AUTHOR>
 * @date 2019/08/09
 * Copyright (c) 2019 ShenZhen O&M Cloud Co., Ltd. All rights reserved.
 */
open class DslLoadMoreItem2 : DslLoadMoreItem() {

    override fun onItemViewDetachedToWindow(itemHolder: DslViewHolder, itemPosition: Int) {
        if (lifecycle.currentState != Lifecycle.State.DESTROYED) {
            super.onItemViewDetachedToWindow(itemHolder,itemPosition)
        }
        if (itemStateEnable) {
            //加载失败时, 下次是否还需要加载更多?
            if (itemState == LOAD_MORE_ERROR) {
                itemState = _LOAD_MORE_RETRY
            }
        }
    }
}