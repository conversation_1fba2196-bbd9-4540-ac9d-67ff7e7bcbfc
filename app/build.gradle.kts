import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.Properties

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.jetbrains.kotlin.android)
    alias(libs.plugins.google.gradle.ksp)
    alias(libs.plugins.compose.compiler)
    alias(libs.plugins.google.services)
    alias(libs.plugins.firebase.crashlytics)
    id("kotlin-parcelize")
}

val keystorePropertiesFile = rootProject.file("gradle.properties")
val keystoreProperties = Properties()
keystoreProperties.load(keystorePropertiesFile.inputStream())

android {
    namespace = "com.heart.heartmerge"
    compileSdk = 36

    defaultConfig {
        applicationId = "com.heart.heartmerge"
        minSdk = 24
        targetSdk = 35
        versionCode = 31
        versionName = "3.0.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary = true
        }

        ndk {
            abiFilters.addAll(listOf("arm64-v8a", "armeabi-v7a"))
        }
    }

    signingConfigs {
        create("release") {
            storeFile = file(keystoreProperties["storeFile"] as String)
            storePassword = keystoreProperties["storePassword"] as String
            keyAlias = keystoreProperties["keyAlias"] as String
            keyPassword = keystoreProperties["keyPassword"] as String
        }
    }

    buildTypes {
        getByName("release") {
            isMinifyEnabled = true
            signingConfig = signingConfigs.getByName("release")
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
            )
            buildConfigField("String", "HOST", "\"https://api.heart-merge.com\"")
            buildConfigField("String", "SOCKET_HOST", "\"wss://api.heart-merge.com/api/v1/socket/ws\"")
        }
        create("uat") {
            initWith(getByName("release")) // 复用 release 配置
            matchingFallbacks.add("release")
            versionNameSuffix = ".uat"
            buildConfigField("String", "HOST", "\"https://api.heart-merge.com\"")
            buildConfigField("String", "SOCKET_HOST", "\"wss://api.heart-merge.com/api/v1/socket/ws\"")
        }
        getByName("debug") {
            isMinifyEnabled = false
            isDebuggable = true
            versionNameSuffix = ".test"
            signingConfig = signingConfigs.getByName("release")
//            buildConfigField("String", "HOST", "\"https://testapi.heart-merge.com\"")
            buildConfigField("String", "HOST", "\"https://api.heart-merge.com\"")
//            buildConfigField("String", "SOCKET_HOST", "\"wss://testapi.heart-merge.com/api/v1/socket/ws\"")
            buildConfigField("String", "SOCKET_HOST", "\"wss://api.heart-merge.com/api/v1/socket/ws\"")
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
            )
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
//    kotlinOptions {
//        jvmTarget = "17"
//    }
    kotlin {
        jvmToolchain {
            languageVersion.set(JavaLanguageVersion.of(17))
        }
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_17)
        }
    }
    buildFeatures {
        compose = true
        viewBinding = true
        buildConfig = true
    }
    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.1"
    }
    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }

    lint {
        disable += "NullSafeMutableLiveData"
    }

    // 打包 APK 输出的路径
    applicationVariants.all {
        outputs.all {
            (this as com.android.build.gradle.internal.api.BaseVariantOutputImpl).outputFile
            val dateFormat = DateTimeFormatter.ofPattern("yyyyMMdd")
            val currentDate = LocalDate.now().format(dateFormat)
            val apkName =
                "heartmerge-${buildType.name}-${defaultConfig.versionName}-${defaultConfig.versionCode}-$currentDate.apk"
            outputFileName = apkName
        }
    }
}

tasks.matching {
    (it.name.contains("CrashlyticsMappingFile") || it.name.contains("CrashlyticsSymbolFile")) && (it.name.endsWith(
        "Debug"
    ) || it.name.endsWith("Uat"))
}.configureEach {
    enabled = false
}


dependencies {
    implementation(files("libs/tinypinyin-2.0.1.jar"))
    implementation(files("libs/rong_translation_5.7.5.aar"))
    implementation(project(":library"))

    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.ui)
    implementation(libs.ui.graphics)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material)
    implementation(libs.androidx.material3)
    implementation(libs.accompanist.permissions)
    implementation(libs.androidx.foundation)
    implementation(libs.androidx.navigation.compose)
    implementation(libs.androidx.constraintlayout.compose)
    implementation(libs.androidx.runtime)
    implementation(libs.androidx.room.runtime)
    implementation(libs.androidx.room.ktx)
    implementation(libs.androidx.room.common)
    implementation(libs.androidx.room.paging)
    implementation(libs.androidx.preference)

    // DataStore dependencies for I18n optimization
    implementation(libs.androidx.datastore.preferences)
    implementation(libs.androidx.datastore.preferences.core)
    implementation(libs.play.services.measurement.api)
    implementation(libs.firebase.crashlytics.ktx)
    implementation(libs.androidx.emoji2)
    ksp(libs.androidx.room.compiler)

    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.auth.ktx)
    implementation(libs.firebase.analytics.ktx)
    implementation(libs.play.services.auth)
    implementation(libs.facebook.android.sdk)
    implementation(libs.facebook.core.sdk)
    implementation(libs.billing)
    implementation(libs.billing.ktx)
    implementation(libs.androidpicker.wheelview)
    implementation(libs.androidpicker.addresspicker)
    implementation(libs.installreferrer)

    implementation(libs.squareup.moshi)
    implementation(libs.squareup.moshiKt)
    ksp(libs.squareup.moshiCodegen)
    implementation(libs.retrofitMoshi)
    implementation(libs.supertextview)
    implementation(libs.flycotablayout)
    implementation(libs.coilCompose)
    implementation(libs.pictureselector)
    implementation(libs.lucksiege.ucrop)
    implementation(libs.lucksiege.camerax)
    implementation(libs.lucksiege.compress)
    implementation(libs.rtcFull)
    implementation(libs.svgaplayer.android)
    implementation(libs.xxPermission)
    implementation(libs.rongYun)
    implementation(libs.gsyVideoPlayer)
    implementation(libs.splashscreen)
    implementation(libs.kyleduo.switchbutton)
    implementation(libs.datatower)
    implementation(libs.bannerViewPager)
    implementation(libs.andRatingBar)
    implementation(libs.af.android.sdk)
    implementation(libs.oaid)
    implementation(libs.lifecycleProcess)

    implementation("com.scottyab:rootbeer-lib:0.1.1")

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)
    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)
//    debugImplementation(libs.leakcanary.android)
}