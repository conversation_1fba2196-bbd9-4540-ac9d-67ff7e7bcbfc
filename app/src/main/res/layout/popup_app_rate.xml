<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_gravity="end"
        android:background="@mipmap/ic_dialog_close_white" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_20"
        android:background="@drawable/bg_center_popup"
        android:orientation="vertical">

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_16"
            android:text="@string/app_rate_popup_title"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_20" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_40"
            android:layout_marginTop="@dimen/dp_10"
            android:text="@string/app_rate_tip"
            android:textColor="@color/color_9F9CA6"
            android:textSize="@dimen/sp_14" />

        <per.wsj.library.AndRatingBar
            android:id="@+id/ratingBar"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_22"
            android:rating="5"
            app:bgDrawable="@drawable/ic_rate_star_normal"
            app:keepOriginColor="true"
            app:scaleFactor="2.0"
            app:starDrawable="@drawable/ic_rate_star_active" />
        <com.heart.heartmerge.i18n.I18nButton
            android:id="@+id/btn_submit"
            style="@style/PrimaryButton"
            android:layout_marginHorizontal="@dimen/dp_50"
            android:layout_marginTop="@dimen/dp_52"
            android:layout_marginBottom="@dimen/sp_20"
            android:text="@string/submit" />

    </LinearLayout>
</LinearLayout>