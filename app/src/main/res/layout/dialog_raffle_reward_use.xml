<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <ImageView
        android:layout_width="30dp"
        android:id="@+id/iv_close"
        android:background="@mipmap/ic_dialog_close_white"
        android:layout_alignParentRight="true"
        android:layout_marginRight="20dp"
        android:layout_height="30dp"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:background="@drawable/bg_dialog_shape"
        android:orientation="vertical"
        android:padding="20dp">

        <ImageView
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_gravity="center_horizontal"
            android:background="@mipmap/ic_reward1" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="20dp"
            android:textSize="20dp"
            android:textStyle="bold"
            android:text="头像框"
            android:textColor="@color/black" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="5dp"
            android:text="有效时长：终身有效" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_use"
            android:layout_width="100dp"
            android:layout_height="40dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="30dp"
            android:background="@drawable/bg_button_shape_ff6e58_f9453c"
            android:gravity="center"
            android:text="确定使用"
            android:textColor="@color/white" />
    </LinearLayout>

</RelativeLayout>