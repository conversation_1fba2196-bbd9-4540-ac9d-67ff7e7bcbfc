<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="@dimen/dp_24"
        android:layout_marginBottom="@dimen/dp_24"
        android:background="@drawable/shape_gradient_white20_24radius">

        <ImageView
            android:id="@+id/iv_background"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/dp_16"
            android:scaleType="centerCrop"
            android:src="@color/background" />

        <ImageView
            android:id="@+id/iv_avatar"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_30"
            android:src="@color/white" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_nickname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/iv_avatar"
            android:layout_alignBottom="@id/iv_avatar"
            android:layout_marginStart="@dimen/dp_10"
            android:layout_toEndOf="@id/iv_avatar"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_14"
            tools:text="GKGHjlhlhlhjlh" />
    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>