<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_40"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/ll_balance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_border_match_diamond"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp_6"
            android:paddingVertical="@dimen/dp_1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="@dimen/dp_17"
                android:layout_height="@dimen/dp_17"
                android:layout_marginEnd="@dimen/dp_4"
                android:src="@mipmap/ic_diamond_big" />

            <androidx.compose.ui.platform.ComposeView
                android:id="@+id/tv_match_balance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_vip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:background="@drawable/shape_border_match_diamond"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_6"
            android:paddingVertical="@dimen/dp_1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/vip_logo"
                android:layout_width="@dimen/dp_17"
                android:layout_height="@dimen/dp_17"
                android:layout_marginEnd="@dimen/dp_4"
                android:src="@mipmap/ic_match_vip" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/baloochettan2regular"
                android:text="VIP"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14" />
        </LinearLayout>
    </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/center_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_110"
        android:layout_weight="1">

        <View
            android:id="@+id/size_layout"
            android:layout_width="@dimen/dp_330"
            android:layout_height="@dimen/dp_330"
            android:visibility="invisible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@mipmap/ic_match_circle_bg"
            app:layout_constraintBottom_toBottomOf="@id/size_layout"
            app:layout_constraintEnd_toEndOf="@id/size_layout"
            app:layout_constraintStart_toStartOf="@id/size_layout"
            app:layout_constraintTop_toTopOf="parent" />

        <com.heart.heartmerge.ui.widget.RadarView
            android:id="@+id/radar"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:circleColor="#565cd6"
            app:circleNum="0"
            app:flicker="3"
            app:layout_constraintBottom_toBottomOf="@id/size_layout"
            app:layout_constraintEnd_toEndOf="@id/size_layout"
            app:layout_constraintStart_toStartOf="@id/size_layout"
            app:layout_constraintTop_toTopOf="parent"
            app:raindropNum="4"
            app:showCross="false"
            app:showRaindrop="false"
            app:speed="5"
            app:sweepColor="@color/white" />

        <ImageView
            android:id="@+id/ic_match_icon1"
            android:layout_width="@dimen/dp_32"
            android:layout_height="@dimen/dp_32"
            android:layout_marginEnd="@dimen/dp_113"
            android:layout_marginBottom="@dimen/dp_26"
            android:src="@mipmap/ic_match_icon1"
            app:layout_constraintBottom_toBottomOf="@id/radar"
            app:layout_constraintEnd_toEndOf="@id/radar" />

        <ImageView
            android:id="@+id/ic_match_icon2"
            android:layout_width="@dimen/dp_38"
            android:layout_height="@dimen/dp_38"
            android:layout_marginStart="@dimen/dp_36"
            android:layout_marginBottom="@dimen/dp_88"
            android:src="@mipmap/ic_match_icon2"
            app:layout_constraintBottom_toBottomOf="@id/radar"
            app:layout_constraintStart_toStartOf="@id/radar" />

        <ImageView
            android:id="@+id/ic_match_icon3"
            android:layout_width="@dimen/dp_28"
            android:layout_height="@dimen/dp_28"
            android:layout_marginStart="@dimen/dp_37"
            android:layout_marginTop="@dimen/dp_30"
            android:src="@mipmap/ic_match_icon3"
            app:layout_constraintStart_toStartOf="@id/radar"
            app:layout_constraintTop_toTopOf="@id/radar" />

        <ImageView
            android:id="@+id/ic_match_icon4"
            android:layout_width="@dimen/dp_26"
            android:layout_height="@dimen/dp_26"
            android:layout_marginStart="@dimen/dp_170"
            android:layout_marginTop="@dimen/dp_68"
            android:src="@mipmap/ic_match_icon4"
            app:layout_constraintStart_toStartOf="@id/radar"
            app:layout_constraintTop_toTopOf="@id/radar" />

        <ImageView
            android:id="@+id/ic_match_icon5"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:layout_marginTop="@dimen/dp_40"
            android:layout_marginEnd="@dimen/dp_40"
            android:src="@mipmap/ic_match_icon5"
            app:layout_constraintEnd_toEndOf="@id/radar"
            app:layout_constraintTop_toTopOf="@id/radar" />

        <FrameLayout
            android:id="@+id/rl_radar_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="@id/size_layout"
            app:layout_constraintEnd_toEndOf="@id/size_layout"
            app:layout_constraintStart_toStartOf="@id/size_layout"
            app:layout_constraintTop_toTopOf="parent">

            <View
                android:layout_width="@dimen/dp_104"
                android:layout_height="@dimen/dp_104"
                android:layout_gravity="center"
                android:background="@drawable/shape_match_header_bg" />

            <ImageView
                android:id="@+id/random_header"
                android:layout_width="@dimen/dp_102"
                android:layout_height="@dimen/dp_102"
                android:layout_gravity="center"
                android:src="@mipmap/random_header1" />
        </FrameLayout>

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_match_card_countdown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:textColor="@color/color_999999"
            android:textSize="@dimen/sp_16"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/rl_radar_container"
            tools:text="Free chances: 00:00:00"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.heart.heartmerge.i18n.I18nButton
            android:id="@+id/btn_match"
            style="@style/PrimaryButton"
            android:layout_width="@dimen/dp_200"
            android:layout_height="@dimen/dp_44"
            android:layout_marginBottom="@dimen/dp_50"
            android:text="@string/matching_go"
            android:textSize="@dimen/sp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <LinearLayout
            android:id="@+id/ll_vip_price_label"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_28"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="@dimen/dp_10"
            android:background="@mipmap/bg_vip_price_label"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_10"
            android:paddingBottom="@dimen/dp_5"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/btn_match"
            app:layout_constraintEnd_toEndOf="@+id/btn_match"
            app:layout_constraintStart_toStartOf="@id/btn_match"
            tools:visibility="visible">

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/label_match_vip_only"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_12" />

            <TextView
                android:id="@+id/tv_vip_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_5"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_12"
                app:drawableStartCompat="@mipmap/ic_diamond"
                tools:text="0/time" />
        </LinearLayout>

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/cancel_matching"
            android:layout_width="@dimen/dp_200"
            android:layout_height="@dimen/dp_44"
            android:layout_marginBottom="@dimen/dp_58"
            android:background="@drawable/shape_match_cancel_bg"
            android:gravity="center"
            android:text="@string/btn_cancel_match"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_14"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <LinearLayout
            android:id="@+id/ll_diamond"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_6"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@id/btn_match">

            <ImageView
                android:id="@+id/ic_diamond"
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_14"
                android:src="@mipmap/ic_diamond" />

            <TextView
                android:id="@+id/tv_match_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_12"
                tools:text="0/min" />

            <TextView
                android:id="@+id/tv_price_org"
                android:layout_width="wrap_content"
                android:layout_height="19dp"
                android:layout_marginStart="@dimen/dp_6"
                android:gravity="center"
                android:textColor="#60ffffff"
                android:textSize="@dimen/sp_10"
                android:visibility="gone"
                tools:text="0/min"
                tools:visibility="visible" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>