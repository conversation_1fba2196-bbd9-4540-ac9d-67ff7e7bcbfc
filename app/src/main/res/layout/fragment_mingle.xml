<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_mingle" />

    <LinearLayout
        android:id="@+id/ll_match_tab"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_36"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="?actionBarSize"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_tab1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:textStyle="bold"
            android:text="@string/match"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_24" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_tab2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:visibility="gone"
            android:text="@string/swipe"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16" />

    </LinearLayout>

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_match_record"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        android:paddingVertical="@dimen/dp_4"
        android:text="@string/match_record"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/ll_match_tab"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/ll_match_tab" />

    <com.heart.heartmerge.ui.widget.FreezeViewpager
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_match_tab" />


</androidx.constraintlayout.widget.ConstraintLayout>