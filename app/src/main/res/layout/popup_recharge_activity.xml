<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_gravity="end"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@mipmap/ic_dialog_close_white" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_500"
        android:layout_gravity="center_horizontal"
        android:background="@mipmap/bg_recharge_activity_popup"
        android:orientation="vertical">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_50"
            android:layout_marginTop="@dimen/dp_50"
            android:drawablePadding="@dimen/dp_3"
            android:fontFamily="@font/oleoscript"
            android:gravity="bottom"
            android:text="@string/vip_recharge_activity"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_20" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipChildren="false"
            android:clipToPadding="false">

            <com.bdc.android.library.refreshlayout.XRecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_345"
                android:layout_marginHorizontal="@dimen/dp_45"
                android:layout_marginTop="@dimen/dp_20"
                android:clipChildren="false"
                android:clipToPadding="false"
                tools:listitem="@layout/item_recharge_activity" />

            <com.google.android.material.progressindicator.CircularProgressIndicator
                android:id="@+id/progress_bar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                app:indicatorColor="@color/colorAccent"
                app:indicatorInset="@dimen/dp_40"
                app:indicatorSize="@dimen/dp_40"
                app:trackColor="@color/white_50" />
        </RelativeLayout>

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_tip"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_40"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="@dimen/sp_30"
            android:gravity="center"
            android:text="@string/recharge_activity_tips"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12" />
    </LinearLayout>

</LinearLayout>