<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_bottom_popup"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_50"
        android:paddingHorizontal="@dimen/dp_25">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/cancel"
            android:textColor="#bfbfbf"
            android:textSize="@dimen/sp_14" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:text="@string/choose_birthday"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_18" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:text="@string/confirm"
            android:textSize="@dimen/sp_16"
            android:textColor="@color/white" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_1"
        android:background="#201831" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.github.gzuliyujiang.wheelpicker.widget.DateWheelLayout
            android:id="@+id/date_picker_actions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:wheel_curtainColor="@color/color_5A5D7B"
            app:wheel_indicatorColor="@color/color_5A5D7B"
            app:wheel_itemTextColor="@color/color_999999"
            app:wheel_itemTextColorSelected="@color/white" />
    </LinearLayout>
</LinearLayout>