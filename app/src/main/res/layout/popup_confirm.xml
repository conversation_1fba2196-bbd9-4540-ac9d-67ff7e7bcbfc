<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/dp_25"
    android:background="@drawable/shape_white_radius8"
    android:orientation="vertical">

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_20"
        android:textColor="@color/color_010101"
        android:textSize="@dimen/sp_18"
        android:textStyle="bold"
        android:visibility="gone"
        android:gravity="center"
        android:layout_marginHorizontal="@dimen/dp_30"
        tools:text="标题"
        tools:visibility="visible" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_20"
        android:textColor="@color/color_010101"
        android:gravity="center"
        android:textSize="@dimen/sp_16"
        android:visibility="gone"
        android:layout_marginHorizontal="@dimen/dp_30"
        tools:text="内容内容内容内容内容内容内容内容内容内容内容内容内容内容"
        tools:visibility="visible" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginTop="@dimen/dp_18"
        android:background="@color/color_EEEEEE" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_47"
        android:orientation="horizontal">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_cancel"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/cancel"
            android:textColor="@color/color_999999"
            android:textSize="@dimen/sp_17" />

        <View
            android:id="@+id/divider"
            android:layout_width="@dimen/dp_1"
            android:layout_height="match_parent"
            android:background="@color/color_EEEEEE" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_confirm"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/confirm"
            android:textColor="@color/color_F84139"
            android:textSize="@dimen/sp_17" />
    </LinearLayout>
</LinearLayout>