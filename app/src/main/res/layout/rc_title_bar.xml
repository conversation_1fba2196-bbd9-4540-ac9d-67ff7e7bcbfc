<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/rc_title_bar_height"
    android:orientation="horizontal">

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tool_bar_left"
        style="@style/TextStyle.Alignment"
        android:layout_width="110dp"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:drawablePadding="3dp"
        android:gravity="center_vertical"
        android:paddingStart="11dp"
        android:paddingEnd="10dp"
        android:paddingRight="10dp"
        android:scaleType="center"
        android:textColor="@android:color/white"
        android:textSize="17sp"
        android:textStyle="bold"
        app:drawableStartCompat="@mipmap/ic_back_white"
        tools:ignore="RtlHardcoded" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp_33"
        android:layout_marginEnd="@dimen/dp_77">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tool_bar_middle"
            style="@style/TextStyle.Alignment"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:lines="1"
            android:maxEms="12"
            android:scaleType="center"
            android:textColor="@android:color/white"
            android:textSize="17sp"
            android:textStyle="bold" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tool_bar_middle_typing"
            style="@style/TextStyle.Alignment"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:ellipsize="end"
            android:lines="1"
            android:maxEms="12"
            android:scaleType="center"
            android:textColor="@android:color/white"
            android:textSize="17sp"
            android:textStyle="bold"
            android:visibility="gone" />
    </FrameLayout>


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="end"
        android:gravity="center_vertical">

        <FrameLayout
            android:id="@+id/fl_whatsapp"
            android:layout_width="@dimen/dp_40"
            android:layout_height="match_parent"
            android:visibility="gone"
            tools:visibility="visible">

            <!--            <ImageView-->
            <!--                android:id="@+id/iv_whatsapp"-->
            <!--                android:layout_width="@dimen/dp_20"-->
            <!--                android:layout_height="@dimen/dp_20"-->
            <!--                android:layout_gravity="center"-->
            <!--                android:src="@mipmap/ic_whatsapp" />-->
            <com.opensource.svgaplayer.SVGAImageView
                android:id="@+id/iv_whatsapp"
                android:layout_width="@dimen/dp_35"
                android:layout_height="@dimen/dp_35"
                android:layout_gravity="center"
                app:autoPlay="true"
                app:loopCount="2"
                app:source="whatsapp.svga"
                tools:src="@mipmap/ic_whatsapp" />
        </FrameLayout>

        <FrameLayout
            android:id="@+id/fl_sticky_top"
            android:layout_width="@dimen/dp_40"
            android:layout_height="match_parent"
            android:visibility="gone"
            tools:visibility="visible">

            <ImageView
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"
                android:layout_gravity="center"
                android:src="@mipmap/ic_sticky_top" />
        </FrameLayout>

        <FrameLayout
            android:id="@+id/fl_more"
            android:layout_width="@dimen/dp_40"
            android:layout_height="match_parent">

            <ImageView
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"
                android:layout_gravity="center"
                android:src="@mipmap/ic_message_title_more" />
        </FrameLayout>


    </LinearLayout>

    <LinearLayout
        android:layout_width="110dp"
        android:layout_height="match_parent"
        android:layout_gravity="end"
        android:orientation="horizontal">

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <ImageView
            android:id="@+id/rc_search"
            android:layout_width="19dp"
            android:layout_height="19dp"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="10dp"
            android:scaleType="centerCrop"
            android:src="@drawable/rc_search_icon"
            android:visibility="gone" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tool_bar_right"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:scaleType="center"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:visibility="gone" />
    </LinearLayout>
</FrameLayout>
