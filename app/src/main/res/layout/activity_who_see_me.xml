<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.bdc.android.library.widget.XToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:x_title="@string/who_see_me" />

    <com.bdc.android.library.refreshlayout.XRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <com.heart.heartmerge.ui.widget.TopCropImageView
        android:id="@+id/crop_image"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:src="@drawable/bg_blur_who_see_me"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_vip_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_152"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <FrameLayout
            android:id="@+id/fl_vip_avatar"
            android:layout_width="@dimen/dp_110"
            android:layout_height="@dimen/dp_110"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_my_avatar"
                android:layout_width="@dimen/dp_80"
                android:layout_height="@dimen/dp_80"
                android:layout_gravity="center"
                android:src="@mipmap/ic_default_header" />

            <ImageView
                android:layout_width="@dimen/dp_110"
                android:layout_height="@dimen/dp_110"
                android:src="@mipmap/bg_vip_header" />
        </FrameLayout>

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_vip_like_me"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_50"
            android:layout_marginHorizontal="@dimen/dp_40"
            android:layout_marginTop="@dimen/dp_28"
            android:background="@drawable/shape_primary_button"
            android:gravity="center"
            android:text="@string/check_who_liked_me"
            android:textColor="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/fl_vip_avatar" />

        <ImageView
            android:layout_width="@dimen/dp_50"
            android:layout_height="@dimen/dp_50"
            android:layout_gravity="end"
            android:layout_marginTop="-20dp"
            android:src="@mipmap/ic_diamond_vip_tip"
            app:layout_constraintEnd_toEndOf="@+id/tv_vip_like_me"
            app:layout_constraintTop_toTopOf="@id/tv_vip_like_me" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_50"
            android:layout_marginTop="@dimen/dp_20"
            android:gravity="center"
            android:text="@string/tip_who_see_me_vip"
            android:textColor="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_vip_like_me" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>