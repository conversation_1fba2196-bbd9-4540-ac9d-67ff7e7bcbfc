<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.bdc.android.library.widget.XToolbar
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:x_title="@string/level" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.zhpan.bannerview.BannerViewPager
            android:id="@+id/bannerView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_180"
            app:bvp_auto_play="false"
            app:bvp_indicator_visibility="gone" />

        <com.heart.heartmerge.ui.widget.level.LevelStepView
            android:id="@+id/levelStepView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_45"
            android:layout_below="@id/bannerView"
            android:layout_marginHorizontal="@dimen/dp_10"
            android:visibility="gone"
            tools:visibility="visible" />

        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/progress_bar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            app:indicatorColor="@color/colorAccent"
            app:indicatorInset="@dimen/dp_40"
            app:indicatorSize="@dimen/dp_40"
            app:trackColor="@color/background" />
    </RelativeLayout>

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />


</LinearLayout>