<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginEnd="@dimen/dp_30"
    android:animateLayoutChanges="true">

    <ImageView
        android:id="@+id/iv_tab_item"
        android:layout_width="@dimen/dp_45"
        android:layout_height="@dimen/dp_30"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_5"
        android:src="@mipmap/ic_home_tab_active"
        android:visibility="gone"
        tools:visibility="visible" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_tab_item"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:text="Hot"
        android:textColor="@color/white" />
</FrameLayout>
