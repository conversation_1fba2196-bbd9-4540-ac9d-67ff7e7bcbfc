<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/dp_280"
    android:layout_height="@dimen/dp_200"
    android:background="@mipmap/bg_exit_video"
    android:paddingHorizontal="@dimen/dp_24"
    android:paddingBottom="@dimen/dp_20">


    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/dialog_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_95"
        android:gravity="center"
        android:text="@string/tip_exit_video"
        android:textColor="@color/color_333333"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/dialog_left_btn"
        android:layout_width="@dimen/dp_110"
        android:layout_height="@dimen/dp_40"
        android:background="@drawable/shape_dialog_btn_exit"
        android:gravity="center"
        android:text="@string/btn_exit"
        android:textColor="@color/color_666666"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/dialog_right_btn"
        android:layout_width="110dp"
        android:layout_height="40dp"
        android:background="@drawable/selector_primary_button"
        android:gravity="center"
        android:text="@string/btn_going_video"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>