<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_50"
        android:background="@drawable/shape_white_center_popup"
        android:gravity="center"
        android:layout_marginHorizontal="@dimen/dp_10"
        android:orientation="vertical"
        android:paddingTop="@dimen/dp_50">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_20"
            android:layout_marginBottom="@dimen/dp_20"
            android:text="Content"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_16" />

        <com.heart.heartmerge.i18n.I18nButton
            android:id="@+id/btn"
            style="@style/PrimaryButton"
            android:backgroundTint="@color/color_F53D3D"
            android:layout_width="@dimen/dp_200"
            android:layout_marginBottom="@dimen/dp_26"
            android:text="@string/already_know" />
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_emoji"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_100"
        android:layout_centerHorizontal="true"
        android:src="@mipmap/ic_emoji_status_online" />

</RelativeLayout>