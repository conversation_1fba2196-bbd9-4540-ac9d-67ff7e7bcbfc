<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_65"
    android:layout_marginBottom="@dimen/dp_8"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_3"
        android:background="@drawable/shape_white50_radius12"
        android:paddingHorizontal="@dimen/dp_10"
        android:paddingVertical="@dimen/dp_10">

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="@dimen/dp_38"
            android:layout_height="@dimen/dp_38"
            android:background="@color/black"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@id/iv_icon"
            app:layout_constraintStart_toEndOf="@id/iv_icon"
            app:layout_constraintTop_toTopOf="@id/iv_icon">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/color_333333"
                    android:textSize="@dimen/sp_15"
                    tools:text="VIP 30 Days" />

                <TextView
                    android:id="@+id/tv_sub_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_10"
                    android:drawablePadding="@dimen/dp_2"
                    android:textColor="@color/color_D195FF"
                    android:textSize="@dimen/sp_12"
                    app:drawableEndCompat="@mipmap/ic_diamond"
                    tools:text="10" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_give_container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                app:layout_constraintStart_toStartOf="@id/tv_title"
                app:layout_constraintTop_toBottomOf="@id/tv_title">

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_level"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="LV10 Bonus:"
                    android:textColor="@color/color_666666"
                    android:textSize="@dimen/sp_11" />

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_give_diamond"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="+30"
                    android:textColor="@color/color_E244E8"
                    android:textSize="@dimen/sp_11" />

                <ImageView
                    android:layout_width="@dimen/dp_18"
                    android:layout_height="@dimen/dp_18"
                    android:background="@mipmap/ic_diamond" />
            </LinearLayout>
        </LinearLayout>

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/btn_recharge"
            style="@style/PrimaryButton"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_24"
            android:background="@drawable/shape_border_9656ff"
            android:minWidth="@dimen/dp_60"
            android:paddingHorizontal="@dimen/dp_6"
            android:textColor="@color/color_9656FF"
            android:textSize="@dimen/sp_11"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="IDR 22,800,201" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_label"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_26"
        android:background="@mipmap/ic_recharge_label"
        android:paddingHorizontal="@dimen/dp_5"
        android:paddingTop="@dimen/dp_3"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_10"
        android:translationY="-7dp"
        tools:text="Give away 3 matching cards" />
</RelativeLayout>