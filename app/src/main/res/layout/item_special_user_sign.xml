<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_108"
    android:background="@drawable/bg_user_sign_cover">

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_diamond"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:fontFamily="@font/urbanist_semibold"
        android:gravity="center_vertical"
        android:text="DAY0"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_diamond">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/baloochettan2"
            android:text="+200"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_24"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/iv_item"
            android:layout_width="@dimen/dp_54"
            android:layout_height="@dimen/dp_54"
            android:layout_marginStart="@dimen/dp_10"
            android:src="@mipmap/ic_user_sign_gift" />
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_sign_lock"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_12"
        android:layout_margin="@dimen/dp_4"
        android:src="@mipmap/ic_sign_lock"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />

    <ImageView
        android:id="@+id/iv_cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/bg_user_sign_cover"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />

    <ImageView
        android:id="@+id/iv_sign_status"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/white"
        tools:src="@mipmap/ic_sign_status_right"
        tools:visibility="gone" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_expired"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_1b1d39_8"
        android:paddingHorizontal="@dimen/dp_5"
        android:paddingVertical="@dimen/dp_2"
        android:rotation="18"
        android:text="@string/expired"
        android:visibility="gone"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>