<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.bdc.android.library.widget.XToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:x_title="@string/cancel_account" />

    <ImageView
        android:layout_width="@dimen/dp_116"
        android:layout_height="@dimen/dp_116"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/dp_60"
        android:background="@mipmap/ic_submit_success" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/dp_20"
        android:text="@string/cancel_account_submit_success"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_17" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_40"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_24"
        android:gravity="center"
        android:text="@string/cancel_account_submit_success_tips"
        android:textColor="@color/color_DCD7D7"
        android:textSize="@dimen/sp_16" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <com.heart.heartmerge.i18n.I18nButton
        android:id="@+id/btn_logout"
        style="@style/PrimaryButton"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_24"
        android:gravity="center"
        android:text="@string/login_out"
        android:textAllCaps="false" />
</LinearLayout>