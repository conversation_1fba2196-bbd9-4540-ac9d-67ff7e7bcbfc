<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_bottom_popup_bg"
    android:orientation="vertical">

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_block"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_58"
        android:layout_gravity="center_vertical"
        android:gravity="center"
        android:text="@string/block"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_complaint"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_58"
        android:layout_gravity="center_vertical"
        android:gravity="center"
        android:text="@string/complaint"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_clear_msg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_58"
        android:layout_gravity="center_vertical"
        android:gravity="center"
        android:visibility="gone"
        android:text="@string/clear_msg"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_1"
        android:background="#3f394e" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_cancel"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_58"
        android:layout_gravity="center_vertical"
        android:gravity="center"
        android:text="@string/cancel"
        android:textColor="@color/color_F53D3D"
        android:textSize="@dimen/sp_16" />
</LinearLayout>