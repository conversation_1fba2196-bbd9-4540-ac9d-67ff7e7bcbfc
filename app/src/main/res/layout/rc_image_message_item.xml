<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rl_content"
    android:layout_width="100dp"
    android:layout_height="100dp">

    <ImageView
        android:id="@+id/rc_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop" />

    <View
        android:id="@+id/main_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/picture_color_80" />

    <LinearLayout
        android:id="@+id/rl_progress"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <ProgressBar
            android:id="@+id/rc_progress"
            style="?android:attr/progressBarStyle"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:indeterminateDrawable="@drawable/rc_progress_sending_style" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/rc_margin_size_2"
            android:text="0%"
            android:textColor="@color/rc_white_color"
            android:textSize="@dimen/rc_font_describe_size" />
    </LinearLayout>

    <ImageView
        android:id="@+id/rc_image_block"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_centerInParent="true"
        android:src="@mipmap/ic_messge_block"
        android:visibility="gone" />

</RelativeLayout>



