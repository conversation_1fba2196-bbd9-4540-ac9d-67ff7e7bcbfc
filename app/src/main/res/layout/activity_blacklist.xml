<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.bdc.android.library.widget.XToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:x_title="@string/black_list" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/dp_34"
        android:background="@color/color_1E2132"
        android:gravity="center"
        android:visibility="gone"
        android:text="@string/black_list_tips"
        android:textColor="@color/color_666666"
        android:textSize="@dimen/sp_14" />

    <com.bdc.android.library.refreshlayout.XRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false"
        android:clipToPadding="false"  />
</LinearLayout>