<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:layout_marginHorizontal="@dimen/dp_15">

    <ImageView
        android:id="@+id/gift_img"
        android:layout_width="@dimen/dp_50"
        android:layout_height="@dimen/dp_50"
        android:adjustViewBounds="true"
        android:scaleType="fitXY" />

    <TextView
        android:id="@+id/gift_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/dp_6"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12" />

</LinearLayout>