<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="@dimen/dp_5"
    android:background="@drawable/shape_card_background"
    android:paddingTop="@dimen/dp_20"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingEnd="@dimen/dp_16"
    android:paddingBottom="@dimen/dp_20">

    <View
        android:id="@+id/indicator"
        android:layout_width="@dimen/dp_4"
        android:layout_height="@dimen/dp_12"
        android:background="@color/color_9F2AF8"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_diamond"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        tools:text="200"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/indicator"
        app:layout_constraintStart_toEndOf="@id/indicator"
        app:layout_constraintTop_toTopOf="@id/indicator" />

    <TextView
        android:id="@+id/tv_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="$2000"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toBottomOf="@id/tv_diamond"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_diamond" />

    <TextView
        android:id="@+id/tv_order_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        tools:text="2024-02-23"
        android:textColor="@color/white_40"
        android:textSize="@dimen/sp_12"
        app:layout_constraintStart_toStartOf="@id/tv_diamond"
        app:layout_constraintTop_toBottomOf="@id/tv_diamond" />


    <TextView
        android:id="@+id/tv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="Success"
        android:textColor="#35B244"
        android:textSize="@dimen/sp_14"
        app:layout_constraintBottom_toBottomOf="@id/tv_order_date"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_order_date" />

</androidx.constraintlayout.widget.ConstraintLayout>