<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_who_see_me"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_44"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@drawable/bg_who_see_me"
        android:paddingHorizontal="@dimen/dp_12">

        <ImageView
            android:id="@+id/iv_lock"
            android:layout_width="@dimen/dp_9"
            android:layout_height="@dimen/dp_11"
            android:src="@mipmap/ic_lock"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:text="@string/who_see_me"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_lock"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/right_arrow"
            android:layout_width="@dimen/dp_12"
            android:layout_height="@dimen/dp_7"
            android:src="@mipmap/ic_arrow_right_white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

<!--        <ImageView-->
<!--            android:id="@+id/image6"-->
<!--            android:layout_width="@dimen/dp_20"-->
<!--            android:layout_height="@dimen/dp_20"-->
<!--            android:layout_marginEnd="-5dp"-->
<!--            android:src="@mipmap/random_header6"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toStartOf="@id/image5"-->
<!--            app:layout_constraintTop_toTopOf="parent" />-->

<!--        <ImageView-->
<!--            android:id="@+id/image5"-->
<!--            android:layout_width="@dimen/dp_20"-->
<!--            android:layout_height="@dimen/dp_20"-->
<!--            android:layout_marginEnd="-5dp"-->
<!--            android:src="@mipmap/random_header5"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toStartOf="@id/image4"-->
<!--            app:layout_constraintTop_toTopOf="parent" />-->

<!--        <ImageView-->
<!--            android:id="@+id/image4"-->
<!--            android:layout_width="@dimen/dp_20"-->
<!--            android:layout_height="@dimen/dp_20"-->
<!--            android:layout_marginEnd="-5dp"-->
<!--            android:src="@mipmap/random_header4"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toStartOf="@id/image3"-->
<!--            app:layout_constraintTop_toTopOf="parent" />-->

        <androidx.compose.ui.platform.ComposeView
            android:id="@+id/tv_like_num"
            android:layout_width="@dimen/dp_26"
            android:layout_height="@dimen/dp_26"
            android:layout_marginEnd="@dimen/dp_8"
            android:layout_centerVertical="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/right_arrow"
            app:layout_constraintTop_toTopOf="parent"/>

        <ImageView
            android:id="@+id/image1"
            android:layout_width="@dimen/dp_26"
            android:layout_height="@dimen/dp_26"
            android:layout_marginEnd="-5dp"
            android:src="@mipmap/random_header1"
            android:paddingHorizontal="@dimen/dp_1"
            android:paddingVertical="@dimen/dp_1"
            android:background="@drawable/bg_circle_white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_like_num"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/image2"
            android:layout_width="@dimen/dp_26"
            android:layout_height="@dimen/dp_26"
            android:layout_marginEnd="-5dp"
            android:src="@mipmap/random_header3"
            android:paddingHorizontal="@dimen/dp_1"
            android:paddingVertical="@dimen/dp_1"
            android:background="@drawable/bg_circle_white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/image1"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/image3"
            android:layout_width="@dimen/dp_26"
            android:layout_height="@dimen/dp_26"
            android:paddingHorizontal="@dimen/dp_1"
            android:paddingVertical="@dimen/dp_1"
            android:layout_marginEnd="-5dp"
            android:src="@mipmap/random_header2"
            android:background="@drawable/bg_circle_white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/image2"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <io.rong.imkit.widget.refresh.SmartRefreshLayout
        android:id="@+id/rc_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dp_60">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rc_conversation_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never"
            tools:listitem="@layout/rc_conversationlist_item" />
    </io.rong.imkit.widget.refresh.SmartRefreshLayout>

    <include
        android:id="@+id/rc_conversationlist_notice_container"
        layout="@layout/rc_conversationlist_notice_view"
        android:visibility="gone" />
</FrameLayout>
