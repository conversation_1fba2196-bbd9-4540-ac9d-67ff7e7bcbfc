<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@mipmap/bg_user_sign_reward"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_20">

        <com.heart.heartmerge.ui.widget.GradientTextView
            android:id="@+id/tv_checked_in"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_120"
            android:fontFamily="@font/rammettoone"
            android:text="@string/checked_in"
            android:textColor="@color/colorAccent"
            android:textSize="@dimen/sp_18" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_80"
            android:fontFamily="@font/baloochettan2"
            android:text="2"
            android:textColor="@color/color_9F2AF8"
            android:textSize="@dimen/dp_38"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:layout_width="@dimen/dp_248"
            android:layout_height="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_10"
            android:background="@drawable/shape_ffffff_ffffff_8"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/dp_48"
                android:layout_height="@dimen/dp_36"
                android:src="@mipmap/ic_diamond_big" />

            <TextView
                android:id="@+id/tv_alert_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_EC12E2"
                android:textSize="@dimen/sp_20"
                tools:text="50" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:gravity="center"
            tools:text="@string/checked_in_successfully_reward_tips"
            android:textColor="#6D7096"
            android:textSize="@dimen/sp_12" />

        <com.heart.heartmerge.ui.widget.GradientTextView
            android:id="@+id/tv_claim_tomorrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_24"
            android:fontFamily="@font/rammettoone"
            android:text="@string/claimable_tomorrow"
            android:textColor="@color/colorAccent"
            android:textSize="@dimen/sp_18" />

        <LinearLayout
            android:layout_width="@dimen/dp_248"
            android:layout_height="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_10"
            android:background="@drawable/shape_ffffff_ffffff_8"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/dp_48"
                android:layout_height="@dimen/dp_36"
                android:src="@mipmap/ic_diamond_big" />

            <TextView
                android:id="@+id/tv_tomorrow_alert_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_EC12E2"
                android:textSize="@dimen/sp_20"
                tools:text="50" />
        </LinearLayout>

        <com.heart.heartmerge.i18n.I18nButton
            android:id="@+id/btn_ok"
            style="@style/PrimaryButton"
            android:layout_marginHorizontal="@dimen/dp_60"
            android:layout_marginTop="@dimen/sp_20"
            android:text="@string/ok"
            android:textAllCaps="false" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_24"
            android:text="@string/main_reward_user_sign_desc"
            android:textColor="#6D7096  "
            android:textSize="@dimen/sp_12"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btn_ok" />
    </LinearLayout>

    <ImageView
        android:id="@+id/ic_close"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginTop="@dimen/dp_20"
        android:src="@mipmap/ic_dialog_close_white" />
</LinearLayout>