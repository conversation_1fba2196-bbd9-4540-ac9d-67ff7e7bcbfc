<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.bdc.android.library.widget.XToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:x_action_text_background="@mipmap/ic_order_record"
        app:x_show_action="true"
        app:x_title="@string/my_wallet" />

    <com.bdc.android.library.refreshlayout.XRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:hasRecyclerView="false">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_20"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/sp_16">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_120"
                        android:background="@mipmap/bg_wallet_card"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.heart.heartmerge.i18n.I18nTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_24"
                        android:layout_marginTop="@dimen/dp_20"
                        android:text="@string/available_diamonds"
                        android:textColor="@color/white_60"
                        android:textSize="@dimen/sp_18"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <!--                    <TextView-->
                    <!--                        android:layout_width="wrap_content"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_centerVertical="true"-->
                    <!--                        android:layout_marginStart="@dimen/dp_24"-->
                    <!--                        android:layout_marginBottom="@dimen/dp_20"-->
                    <!--                        android:text="100"-->
                    <!--                        android:textColor="@color/white"-->
                    <!--                        android:textSize="@dimen/sp_40"-->
                    <!--                        app:layout_constraintBottom_toBottomOf="parent"-->
                    <!--                        app:layout_constraintStart_toStartOf="parent" />-->

                    <androidx.compose.ui.platform.ComposeView
                        android:id="@+id/tv_diamonds"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_24"
                        android:layout_marginBottom="@dimen/dp_20"
                        android:text="0.00"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp_20"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <LinearLayout
                    android:id="@+id/ll_first_recharge_container"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_40"
                    android:layout_marginHorizontal="@dimen/dp_16"
                    android:layout_marginTop="@dimen/dp_27"
                    android:background="@drawable/shape_wallet_banner"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <ImageView
                        android:layout_width="@dimen/dp_41"
                        android:layout_height="@dimen/dp_41"
                        android:layout_marginStart="@dimen/dp_8"
                        android:src="@mipmap/ic_gift_bag"
                        android:translationY="-10dp" />

                    <com.heart.heartmerge.i18n.I18nTextView
                        android:id="@+id/tv_first_recharge"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_8"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:layout_weight="1"
                        android:drawableTint="@color/white"
                        android:text="First recharge $0.99 for 200 diamonds"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp_12" />
                </LinearLayout>

                <com.heart.heartmerge.i18n.I18nTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/sp_16"
                    android:layout_marginTop="@dimen/dp_24"
                    android:drawablePadding="@dimen/dp_10"
                    android:gravity="center_vertical"
                    android:text="@string/choose_recharge_limit"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_14" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    android:minHeight="@dimen/dp_300">

                    <com.bdc.android.library.refreshlayout.XRecyclerView
                        android:id="@+id/recyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_20"
                        android:clipChildren="false"
                        android:clipToPadding="false"
                        android:paddingHorizontal="@dimen/dp_16"
                        android:paddingBottom="@dimen/dp_20"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:itemCount="5"
                        tools:listitem="@layout/item_wallet_diamond_recharge" />

                    <com.google.android.material.progressindicator.CircularProgressIndicator
                        android:id="@+id/progress_bar"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center"
                        app:indicatorColor="@color/colorAccent"
                        app:indicatorInset="@dimen/dp_40"
                        app:indicatorSize="@dimen/dp_40"
                        app:trackColor="@color/background" />
                </RelativeLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>
    </com.bdc.android.library.refreshlayout.XRefreshLayout>

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_agreement"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_20"
        android:text="@string/recharge_agreement_hint"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12"
        android:visibility="gone" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_1e2132_top8"
        android:orientation="vertical"
        android:visibility="gone">


        <!--        <TextView-->
        <!--            android:id="@+id/tv_agreement"-->
        <!--            android:layout_width="wrap_content"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:layout_marginHorizontal="@dimen/dp_16"-->
        <!--            android:layout_marginTop="@dimen/dp_12"-->
        <!--            android:text="@string/recharge_agreement_hint"-->
        <!--            android:textColor="@color/white"-->
        <!--            android:textSize="@dimen/sp_12" />-->

        <com.heart.heartmerge.i18n.I18nButton
            android:id="@+id/btn_recharge"
            style="@style/PrimaryButton"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginBottom="@dimen/dp_30"
            android:text="@string/recharge_right_now" />
    </LinearLayout>


</LinearLayout>