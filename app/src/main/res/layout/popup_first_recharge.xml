<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <!-- 弹框主体 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_30"
        android:background="@mipmap/bg_popup_first_recharge"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_15"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 标题 -->
        <com.heart.heartmerge.ui.widget.GradientTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_120"
            android:fontFamily="@font/oleoscript_bold"
            android:text="@string/first_recharge_title"
            android:textColor="@color/color_FFFABA"
            android:textSize="@dimen/sp_32"
            android:textStyle="bold"
            app:strokeColor="@color/color_FF782F"
            app:strokeWidth="@dimen/dp_4" />

        <!-- 倒计时 -->
        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_countdown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_8"
            android:textColor="@color/color_first_recharge_countdown"
            android:textSize="@dimen/sp_14"
            tools:text="End Time: 00:59:30" />

        <!-- 钻石数量区域 -->
        <LinearLayout
            android:layout_width="@dimen/dp_180"
            android:layout_height="@dimen/dp_145"
            android:layout_marginTop="@dimen/dp_20"
            android:background="@drawable/bg_first_recharge_diamond_area"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="@dimen/dp_10">

            <!-- 钻石图标 -->
            <ImageView
                android:layout_width="@dimen/dp_80"
                android:layout_height="@dimen/dp_80"
                android:src="@mipmap/ic_diamond_purple" />

            <!-- 199 Diamonds -->
            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_diamond_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_2"
                android:fontFamily="@font/urbanist_semibold"
                android:textColor="@color/color_first_recharge_diamond"
                android:textSize="@dimen/sp_20"
                android:textStyle="bold"
                tools:text="199 Diamonds" />

            <!-- 原价（删除线） -->
            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_original_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_2"
                android:alpha="0.3"
                android:textColor="@color/color_first_recharge_diamond"
                android:textSize="@dimen/sp_14"
                tools:text="140 Diamonds" />
        </LinearLayout>

        <!-- 折扣标签和价格按钮区域 -->
        <RelativeLayout
            android:layout_width="@dimen/dp_164"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_30">

            <!-- 30% OFF 标签 -->
            <LinearLayout
                android:id="@+id/layout_discount"
                android:layout_width="@dimen/dp_76"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:background="@mipmap/bg_first_recharge_discount_label"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingBottom="@dimen/dp_4">

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_discount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_14"
                    android:textStyle="bold"
                    tools:text="30% OFF" />
            </LinearLayout>

            <!-- 价格按钮 -->
            <com.heart.heartmerge.i18n.I18nButton
                android:id="@+id/btn_price"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_51"
                android:layout_below="@id/layout_discount"
                android:layout_marginTop="@dimen/dp_3"
                android:background="@mipmap/bg_first_recharge_button"
                android:fontFamily="@font/rammettoone"
                android:textColor="@color/color_first_recharge_button_text"
                android:textSize="@dimen/sp_20"
                android:textStyle="bold"
                tools:text="$2.99" />
        </RelativeLayout>
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginTop="@dimen/dp_30"
        android:layout_marginEnd="@dimen/dp_20"
        android:src="@mipmap/ic_dialog_close_white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
