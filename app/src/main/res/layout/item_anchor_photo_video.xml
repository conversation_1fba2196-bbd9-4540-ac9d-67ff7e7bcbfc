<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp_108"
    android:layout_height="@dimen/dp_108"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_marginStart="@dimen/dp_10">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/file_thumbnail"
        android:layout_width="@dimen/dp_108"
        android:layout_height="@dimen/dp_108"
        android:adjustViewBounds="true"
        android:scaleType="centerCrop"
        app:shapeAppearanceOverlay="@style/CustomShapeAppearance"
        app:strokeColor="@null" />
<!--    <ImageView-->
<!--        android:id="@+id/file_thumbnail"-->
<!--        android:layout_width="@dimen/dp_108"-->
<!--        android:layout_height="@dimen/dp_108"-->
<!--        android:adjustViewBounds="true"-->
<!--        android:scaleType="fitXY" />-->

    <ImageView
        android:id="@+id/file_play"
        android:layout_width="@dimen/dp_30"
        android:layout_height="@dimen/dp_30"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_8"
        android:adjustViewBounds="true"
        android:scaleType="fitXY"
        android:src="@mipmap/ic_video_play"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/file_lock"
        android:layout_width="@dimen/dp_16"
        android:layout_height="@dimen/dp_18"
        android:layout_gravity="center"
        android:adjustViewBounds="true"
        android:scaleType="fitXY"
        android:src="@mipmap/ic_file_lock" />


</FrameLayout>