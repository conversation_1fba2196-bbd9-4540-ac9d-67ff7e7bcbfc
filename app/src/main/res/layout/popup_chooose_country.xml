<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_bottom_popup"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_50"
        android:paddingHorizontal="@dimen/dp_12">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/cancel"
            android:textColor="#bfbfbf"
            android:textSize="@dimen/sp_16"
            android:visibility="gone" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:text="@string/choose_country"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_18" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="@dimen/dp_12"
            android:layout_height="@dimen/dp_12"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:background="@mipmap/ic_close_black"
            android:backgroundTint="@color/white"
            android:visibility="gone" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_500"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/item_choose_country" />

        <com.heart.heartmerge.ui.widget.SideBar
            android:id="@+id/sideBar"
            android:layout_width="30dp"
            android:layout_height="@dimen/dp_500"
            android:layout_alignParentEnd="true" />

        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/progress_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            app:indicatorColor="@color/colorAccent"
            app:indicatorInset="@dimen/dp_40"
            app:indicatorSize="@dimen/dp_40"
            app:trackColor="@color/background" />

    </RelativeLayout>
</LinearLayout>