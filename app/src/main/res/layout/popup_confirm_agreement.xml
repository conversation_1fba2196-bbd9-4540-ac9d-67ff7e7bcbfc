<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_10"
        android:layout_marginTop="@dimen/dp_50"
        android:background="@drawable/shape_white_center_popup"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingTop="@dimen/dp_50">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_20"
            android:layout_marginBottom="@dimen/dp_10"
            android:text="@string/please_agree_agreement"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_16" />

        <LinearLayout
            android:id="@+id/ll_bottom_agreement"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/dp_20">

                <ImageView
                    android:layout_width="@dimen/dp_14"
                    android:layout_height="@dimen/dp_14"
                    android:layout_marginTop="@dimen/dp_1"
                    android:layout_marginEnd="@dimen/dp_6"
                    android:background="@mipmap/ic_checkbox_red_checked2" />

                <!--                <CheckBox-->
                <!--                    android:id="@+id/cb_agreement"-->
                <!--                    android:layout_width="@dimen/dp_15"-->
                <!--                    android:layout_height="@dimen/dp_15"-->
                <!--                    android:layout_marginEnd="@dimen/dp_6"-->
                <!--                    android:background="@drawable/selector_checkbox"-->
                <!--                    android:button="@null"-->
                <!--                    android:checked="true"-->
                <!--                    android:paddingHorizontal="@dimen/dp_12" />-->

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_checkbox_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:breakStrategy="simple"
                    android:text="@string/login_agreement"
                    android:textAllCaps="false"
                    android:textColor="@color/color_666666"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="@dimen/dp_20"
                android:orientation="horizontal">

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_privacy_policy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:breakStrategy="simple"
                    android:text="@string/privacy_policy"
                    android:textAllCaps="false"
                    android:textColor="@color/color_F53D3D"
                    android:textSize="@dimen/sp_14" />

                <com.heart.heartmerge.i18n.I18nTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp_3"
                    android:breakStrategy="simple"
                    android:text="&amp;"
                    android:textAllCaps="false"
                    android:textColor="@color/color_666666" />

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_user_agreement"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:breakStrategy="simple"
                    android:text="@string/user_agreement"
                    android:textAllCaps="false"
                    android:textColor="@color/color_F53D3D"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>
        </LinearLayout>

        <com.heart.heartmerge.i18n.I18nButton
            android:id="@+id/btn"
            style="@style/PrimaryButton"
            android:layout_width="@dimen/dp_200"
            android:layout_marginBottom="@dimen/dp_10"
            android:backgroundTint="@color/color_F53D3D"
            android:text="@string/agree_and_continue"
            android:textAllCaps="false" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_later"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_20"
            android:text="@string/permission_later"
            android:textColor="@color/color_666666"
            android:textSize="@dimen/sp_14" />
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_emoji"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_100"
        android:layout_centerHorizontal="true"
        android:src="@mipmap/ic_dialog_warning" />

</RelativeLayout>