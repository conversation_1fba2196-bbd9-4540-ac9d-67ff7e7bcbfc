<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_bottom_popup"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/dp_16"
    android:paddingTop="@dimen/dp_20">

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_5"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/shape_popup_indicator" />

    <LinearLayout
        android:id="@+id/ll_membership_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_20"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"
                android:background="@mipmap/ic_diamond_purple" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_4"
                android:layout_weight="1"
                android:text="@string/available_diamonds"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_16" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_diamond"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="124"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_18" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_first_recharge_container"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_40"
            android:layout_marginTop="@dimen/dp_27"
            android:background="@drawable/shape_wallet_banner"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="visible">

            <ImageView
                android:layout_width="@dimen/dp_41"
                android:layout_height="@dimen/dp_41"
                android:layout_marginStart="@dimen/dp_8"
                android:src="@mipmap/ic_gift_bag"
                android:translationY="-10dp" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_first_recharge"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_8"
                android:layout_marginEnd="@dimen/dp_16"
                android:layout_weight="1"
                android:drawableTint="@color/white"
                android:text="First recharge $0.99 for 200 diamonds"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_12" />
        </LinearLayout>
    </LinearLayout>

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_normal_user"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_20"
        android:text="@string/vip_privilege"
        android:visibility="gone"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_marginTop="@dimen/dp_15"
        android:layout_marginBottom="@dimen/dp_20"
        android:layout_height="@dimen/dp_400">

    <com.bdc.android.library.refreshlayout.XRecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/dp_350"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:itemCount="5"
        tools:listitem="@layout/item_diamond_recharge" />

        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/progress_bar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            app:indicatorColor="@color/colorAccent"
            app:indicatorInset="@dimen/dp_40"
            app:indicatorSize="@dimen/dp_40"
            app:trackColor="@color/background" />
    </RelativeLayout>
</LinearLayout>