<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp_120"
    android:layout_height="@dimen/dp_160"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/layout_call_video"
        android:layout_width="@dimen/dp_120"
        android:layout_height="@dimen/dp_160" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal|bottom"
        android:layout_marginBottom="@dimen/dp_2"
        android:textColor="@color/white"
        android:textSize="12sp" />
</FrameLayout>