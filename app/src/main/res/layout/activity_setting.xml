<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.bdc.android.library.widget.XToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:x_title="@string/string_setting" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:paddingBottom="@dimen/dp_30">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <!--            <com.heart.heartmerge.ui.widget.CustomMineTextView-->
            <!--                android:id="@+id/tv_account_security"-->
            <!--                style="@style/CustomMineTextView_gray"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="@dimen/dp_55"-->
            <!--                android:paddingHorizontal="@dimen/dp_16"-->
            <!--                app:ctv_left="@string/setting_account_and_security" />-->

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_54"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:background="@drawable/shape_setting_panel"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/freeStrike"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:paddingHorizontal="@dimen/dp_16"
                    android:text="@string/label_do_not_disturb"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_14" />

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_do_not_disturb_countdown"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_10"
                    android:textColor="@color/color_F53D3D"
                    android:textSize="@dimen/sp_14"
                    tools:text="00:00:00" />

                <com.kyleduo.switchbutton.SwitchButton
                    android:id="@+id/busy_switch"
                    android:layout_width="@dimen/dp_36"
                    android:layout_height="@dimen/dp_23"
                    android:layout_marginEnd="@dimen/dp_16"
                    android:checked="false"
                    app:kswBackColor="@drawable/custom_back_color"
                    app:kswBackRadius="@dimen/dp_12"
                    app:kswThumbColor="@drawable/custom_thumb_color"
                    app:kswThumbWidth="@dimen/dp_18" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_10"
                android:background="@drawable/shape_setting_panel"
                android:orientation="vertical">

                <com.heart.heartmerge.ui.widget.CustomMineTextView
                    android:id="@+id/tv_black_list"
                    style="@style/CustomMineTextView_gray"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_55"
                    android:paddingHorizontal="@dimen/dp_16"
                    app:ctv_left="@string/black_list" />

                <com.heart.heartmerge.ui.widget.CustomMineTextView
                    android:id="@+id/tv_complaints"
                    style="@style/CustomMineTextView_gray"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_55"
                    android:paddingHorizontal="@dimen/dp_16"
                    android:visibility="gone"
                    app:ctv_left="@string/customer_complaints" />

                <com.heart.heartmerge.ui.widget.CustomMineTextView
                    android:id="@+id/tv_help"
                    style="@style/CustomMineTextView_gray"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_55"
                    android:paddingHorizontal="@dimen/dp_16"
                    app:ctv_left="@string/feedback_help" />

                <com.heart.heartmerge.ui.widget.CustomMineTextView
                    android:id="@+id/tv_cancellation"
                    style="@style/CustomMineTextView_gray"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_55"
                    android:paddingHorizontal="@dimen/dp_16"
                    app:ctv_left="@string/cancel_account" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_10"
                android:background="@drawable/shape_setting_panel"
                android:orientation="vertical">

                <com.heart.heartmerge.ui.widget.CustomMineTextView
                    android:id="@+id/tv_registration"
                    style="@style/CustomMineTextView_gray"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_55"
                    android:paddingHorizontal="@dimen/dp_16"
                    app:ctv_left="@string/setting_user_agreement" />

                <com.heart.heartmerge.ui.widget.CustomMineTextView
                    android:id="@+id/tv_privacy_policy"
                    style="@style/CustomMineTextView_gray"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_55"
                    android:paddingHorizontal="@dimen/dp_16"
                    app:ctv_left="@string/setting_privacy_policy" />

                <com.heart.heartmerge.ui.widget.CustomMineTextView
                    android:id="@+id/tv_use_terms"
                    style="@style/CustomMineTextView_gray"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_55"
                    android:paddingHorizontal="@dimen/dp_16"
                    app:ctv_left="@string/setting_use_terms" />

                <com.heart.heartmerge.ui.widget.CustomMineTextView
                    android:id="@+id/tv_child_protection"
                    style="@style/CustomMineTextView_gray"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_55"
                    android:paddingHorizontal="@dimen/dp_16"
                    app:ctv_left="@string/setting_child_protection" />

                <com.heart.heartmerge.ui.widget.CustomMineTextView
                    android:id="@+id/tv_about"
                    style="@style/CustomMineTextView_gray"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_55"
                    android:paddingHorizontal="@dimen/dp_16"
                    app:ctv_left="@string/setting_about_us" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_10"
                android:background="@drawable/shape_setting_panel"
                android:orientation="vertical">

                <!--                <com.heart.heartmerge.ui.widget.CustomMineTextView-->
                <!--                    android:id="@+id/ll_updates"-->
                <!--                    style="@style/CustomMineTextView_gray"-->
                <!--                    android:layout_width="match_parent"-->
                <!--                    android:layout_height="@dimen/dp_55"-->
                <!--                    android:paddingHorizontal="@dimen/dp_16"-->
                <!--                    app:ctv_left="@string/setting_check_update"-->
                <!--                    app:ctv_next="false"-->
                <!--                    app:ctv_right="v1.0"/>-->

                <LinearLayout
                    android:id="@+id/ll_updates"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_55"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp_16">

                    <com.heart.heartmerge.i18n.I18nTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/setting_check_update"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp_14" />

                    <com.heart.heartmerge.i18n.I18nTextView
                        android:id="@+id/tv_new_version_label"
                        style="@style/PrimaryButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_8"
                        android:paddingHorizontal="@dimen/dp_8"
                        android:paddingVertical="@dimen/dp_1"
                        android:text="@string/version_new_label"
                        android:textSize="@dimen/sp_12"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1" />

                    <com.heart.heartmerge.i18n.I18nTextView
                        android:id="@+id/tv_version"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="v1.0"
                        android:textColor="@color/white_40"
                        android:textSize="@dimen/sp_12" />
                </LinearLayout>

                <com.heart.heartmerge.ui.widget.CustomMineTextView
                    android:id="@+id/ll_clear_cache"
                    style="@style/CustomMineTextView_gray"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_55"
                    android:paddingHorizontal="@dimen/dp_16"
                    app:ctv_left="@string/setting_clear_cache" />

            </LinearLayout>

            <com.heart.heartmerge.ui.widget.CustomMineTextView
                android:id="@+id/tv_notice"
                style="@style/CustomMineTextView_gray"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_55"
                android:paddingHorizontal="@dimen/dp_16"
                android:visibility="gone"
                app:ctv_left="@string/notification_setting" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_10"
                android:background="@color/color_151827"
                android:visibility="gone" />

            <com.heart.heartmerge.ui.widget.CustomMineTextView
                android:id="@+id/tv_permission"
                style="@style/CustomMineTextView_gray"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_55"
                android:paddingHorizontal="@dimen/dp_16"
                android:visibility="gone"
                app:ctv_left="@string/permission_setting" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_10"
                android:visibility="gone" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <LinearLayout
        android:id="@+id/ll_logout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_44"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_30"
        android:background="@drawable/shape_setting_logout"
        android:gravity="center">

        <ImageView
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:src="@mipmap/ic_setting_logout" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:text="@string/login_out"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16" />

    </LinearLayout>
</LinearLayout>