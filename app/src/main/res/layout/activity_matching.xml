<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center">

        <ImageView
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:src="@mipmap/ic_default_avatar" />

        <com.opensource.svgaplayer.SVGAImageView
            android:layout_width="@dimen/dp_142"
            android:layout_height="@dimen/dp_72"
            app:autoPlay="true"
            app:source="ic_matching.svga" />

        <ImageView
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:src="@mipmap/ic_matching_anchor_avatar" />

    </LinearLayout>

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/label_searching"
        android:textColor="@color/white"
        android:textSize="24sp"
        android:layout_marginTop="@dimen/dp_41"
        android:layout_marginBottom="150dp"
        android:textStyle="bold" />
</LinearLayout>