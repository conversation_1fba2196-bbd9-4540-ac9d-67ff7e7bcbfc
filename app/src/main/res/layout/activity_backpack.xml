<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:orientation="vertical">

    <com.bdc.android.library.widget.XToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        app:x_title="@string/backpack"
        android:layout_height="wrap_content"/>

    <com.heart.heartmerge.ui.widget.SlidingTabLayoutViewPager2
        android:id="@+id/tab"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_32"
        app:tl_indicator_color="@color/color_FB2CA4"
        app:tl_indicator_gravity="BOTTOM"
        app:tl_indicator_height="@dimen/dp_4"
        app:tl_indicator_style="NORMAL"
        app:tl_indicator_width="@dimen/dp_25"
        app:tl_tab_padding="@dimen/dp_20"
        app:tl_tab_space_equal="true"
        app:tl_textBold="SELECT"
        app:tl_textSelectColor="@color/color_FB2CA4"
        app:tl_textUnselectColor="@color/color_999999"
        app:tl_textsize="@dimen/sp_16" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_5"
        android:background="@color/color_divider" />

    <LinearLayout
        android:id="@+id/ll_screen"
        android:layout_width="wrap_content"
        android:layout_height="23dp"
        android:layout_marginStart="@dimen/dp_16"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:src="@mipmap/ic_filter" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/dp_6"
            android:text="@string/all"
            android:textColor="@color/color_999999"
            android:textSize="@dimen/sp_12" />
    </LinearLayout>

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />
</LinearLayout>