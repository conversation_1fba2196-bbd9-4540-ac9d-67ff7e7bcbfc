<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_bottom_popup"
    android:orientation="vertical"
    android:paddingTop="@dimen/dp_20"
    android:paddingBottom="@dimen/dp_50">

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_5"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/shape_popup_indicator" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_16"
        android:text="@string/report"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_400">

        <com.bdc.android.library.refreshlayout.XRecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_16"
            tools:itemCount="5"
            tools:listitem="@layout/item_popup_report" />

        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/progress_bar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            app:indicatorColor="@color/colorAccent"
            app:indicatorInset="@dimen/dp_40"
            app:indicatorSize="@dimen/dp_40"
            app:trackColor="@color/background" />
    </RelativeLayout>

    <com.heart.heartmerge.i18n.I18nButton
        android:id="@+id/btn_report"
        style="@style/PrimaryButton"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_32"
        android:text="@string/report" />
</LinearLayout>