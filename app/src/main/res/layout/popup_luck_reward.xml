<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_410"
        android:background="@mipmap/bg_popup_lock_reward"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/dp_85"
            android:orientation="vertical"
            android:rotation="7">

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_diamond"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/wendyone_regular"
                android:text="400 Diamonds"
                android:textColor="#FF7700"
                android:textSize="@dimen/sp_22" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_sub_diamond"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:alpha="0.4"
                android:fontFamily="@font/wendyone_regular"
                android:text="200 Diamonds"
                android:textColor="#FF7700"
                android:textSize="@dimen/sp_12" />
        </LinearLayout>

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_amount"
            android:layout_width="@dimen/dp_280"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/dp_37"
            android:fontFamily="@font/wendyone_regular"
            android:gravity="center"
            android:text="$2.99"
            android:textColor="@color/white"
            android:textSize="@dimen/dp_36" />
    </RelativeLayout>

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:src="@mipmap/ic_dialog_close_white" />
</LinearLayout>