<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_gravity="end"
        android:background="@mipmap/ic_dialog_close_white" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_20"
        android:background="@drawable/bg_center_popup"
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_32">

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_16"
            android:text="@string/suggestion_popup_title"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_18" />

        <androidx.appcompat.widget.AppCompatEditText
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_160"
            android:layout_marginHorizontal="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_20"
            android:alpha="0.8"
            android:background="@drawable/shape_edittext_suggestion_popup"
            android:gravity="top"
            android:hint="@string/write_here"
            android:textSize="@dimen/sp_12"
            android:paddingHorizontal="@dimen/dp_16"
            android:paddingVertical="@dimen/dp_12"
            android:textColor="@color/white"
            android:textColorHint="@color/white" />

        <com.heart.heartmerge.i18n.I18nButton
            android:id="@+id/btn_submit"
            style="@style/PrimaryButton"
            android:layout_marginHorizontal="@dimen/dp_32"
            android:layout_marginTop="@dimen/dp_24"
            android:text="@string/submit" />
    </LinearLayout>
</LinearLayout>