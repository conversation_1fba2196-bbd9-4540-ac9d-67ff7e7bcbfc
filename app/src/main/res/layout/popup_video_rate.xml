<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_50"
        android:background="@drawable/shape_white_center_popup"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_32"
            android:layout_marginTop="@dimen/dp_70"
            android:gravity="center"
            android:text="@string/video_rate_popup_title"
            android:textColor="@color/color_text_dark"
            android:textSize="@dimen/sp_18"
            android:textStyle="bold" />

        <!-- 表情评价区域 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_32"
            android:gravity="center"
            android:orientation="horizontal">

            <!-- Bad 表情 -->
            <LinearLayout
                android:id="@+id/layout_bad"
                android:layout_width="@dimen/dp_84"
                android:layout_height="@dimen/dp_100"
                android:layout_marginEnd="@dimen/dp_10"
                android:background="@drawable/shape_emotion_normal"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="@dimen/dp_8">

                <ImageView
                    android:id="@+id/iv_bad"
                    android:layout_width="@dimen/dp_60"
                    android:layout_height="@dimen/dp_60"
                    android:src="@mipmap/ic_anchor_rate_bad" />

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_bad"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_6"
                    android:text="@string/emotion_bad"
                    android:textColor="@color/color_emotion_text"
                    android:textSize="@dimen/sp_14"
                    android:textStyle="bold" />
            </LinearLayout>

            <!-- General 表情 -->
            <LinearLayout
                android:id="@+id/layout_general"
                android:layout_width="@dimen/dp_84"
                android:layout_height="@dimen/dp_100"
                android:layout_marginEnd="@dimen/dp_10"
                android:background="@drawable/shape_emotion_normal"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="@dimen/dp_8">

                <ImageView
                    android:id="@+id/iv_general"
                    android:layout_width="@dimen/dp_60"
                    android:layout_height="@dimen/dp_60"
                    android:src="@mipmap/ic_anchor_rate_general" />

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_general"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_6"
                    android:text="@string/emotion_general"
                    android:textColor="@color/color_emotion_text"
                    android:textSize="@dimen/sp_14"
                    android:textStyle="bold" />
            </LinearLayout>

            <!-- Good 表情 -->
            <LinearLayout
                android:id="@+id/layout_good"
                android:layout_width="@dimen/dp_84"
                android:layout_height="@dimen/dp_100"
                android:background="@drawable/shape_emotion_selected"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="@dimen/dp_8">

                <ImageView
                    android:id="@+id/iv_good"
                    android:layout_width="@dimen/dp_60"
                    android:layout_height="@dimen/dp_60"
                    android:src="@mipmap/ic_anchor_rate_good" />

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_good"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_6"
                    android:text="@string/emotion_good"
                    android:textColor="@color/color_emotion_text"
                    android:textSize="@dimen/sp_14"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>

        <com.heart.heartmerge.ui.widget.CustomLabelsView
            android:id="@+id/labels"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_32"
            android:saveEnabled="false"
            app:labelBackground="@drawable/shape_label_item_white"
            app:labelTextColor="#A1A1A1"
            app:labelTextPaddingBottom="@dimen/dp_6"
            app:labelTextPaddingLeft="@dimen/dp_12"
            app:labelTextPaddingRight="@dimen/dp_12"
            app:labelTextPaddingTop="@dimen/dp_6"
            app:labelTextSize="@dimen/sp_14"
            app:layout_scrollFlags="scroll"
            app:lineMargin="@dimen/dp_12"
            app:maxSelect="0"
            app:selectType="MULTI"
            app:wordMargin="@dimen/dp_10" />

        <com.heart.heartmerge.i18n.I18nButton
            android:id="@+id/btn_submit"
            style="@style/PrimaryButton"
            android:layout_marginHorizontal="@dimen/dp_24"
            android:layout_marginTop="@dimen/dp_44"
            android:layout_marginBottom="@dimen/dp_44"
            android:text="@string/submit" />

    </LinearLayout>

    <!-- 头像 -->
    <ImageView
        android:id="@+id/iv_header"
        android:layout_width="@dimen/dp_100"
        android:layout_height="@dimen/dp_100"
        android:layout_gravity="center_horizontal"
        android:background="@mipmap/ic_default_avatar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 关闭按钮 -->
    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_20"
        android:layout_marginTop="@dimen/dp_70"
        android:layout_marginEnd="@dimen/dp_20"
        android:src="@drawable/ic_close_gray"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>