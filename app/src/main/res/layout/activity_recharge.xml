<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#101321"
    android:orientation="vertical">

    <com.bdc.android.library.widget.XToolbar
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_amount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="30dp"
        android:textColor="@color/white"
        android:textSize="25dp"
        android:textStyle="bold"
        tools:text="19.9" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_countdown"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="20dp"
        android:text="支付剩余时间: 12:00:00"
        android:textColor="#bbbbbb" />

    <View
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:layout_marginTop="50dp"
        android:background="#161a29" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="30dp"
        android:text="选择支付方式"
        android:textColor="@color/white" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="10dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:background="@mipmap/ic_payermax" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_weight="1"
            android:text="Payermax支付"
            android:textColor="@color/white"
            android:textSize="15dp" />

        <CheckBox
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:buttonTint="@color/white" />
    </LinearLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_weight="1" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_purchase"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="30dp"
        android:layout_marginHorizontal="20dp"
        android:background="@drawable/bg_button_shape_ff6e58_f9453c"
        android:gravity="center"
        android:text="立即支付"
        android:textColor="@color/white"
        android:textSize="15dp" />
</LinearLayout>