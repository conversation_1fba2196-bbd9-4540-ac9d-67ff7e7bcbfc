<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/banner_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop" />
    
    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_vip_unlock"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_40"
        android:layout_centerInParent="true"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp_20"
        android:background="@drawable/shape_border_vip_unlock"
        android:text="@string/vip_unlock"/>

</RelativeLayout>