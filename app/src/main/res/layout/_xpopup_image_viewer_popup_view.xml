<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.lxj.xpopup.widget.BlankView
        android:id="@+id/placeholderView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="invisible" />

    <com.lxj.xpopup.widget.PhotoViewContainer
        android:id="@+id/photoViewContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.lxj.xpopup.widget.HackyViewPager
            android:id="@+id/pager"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </com.lxj.xpopup.widget.PhotoViewContainer>

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_pager_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginStart="25dp"
        android:layout_marginBottom="20dp"
        android:textColor="@android:color/white"
        android:textSize="16sp"/>

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_save"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|right"
        android:layout_marginEnd="25dp"
        android:layout_marginBottom="20dp"
        android:text="@string/xpopup_save"
        android:textColor="@android:color/white"
        android:textSize="16sp"
        android:visibility="invisible" />

    <FrameLayout
        android:id="@+id/fl_back"
        android:layout_width="@dimen/dp_40"
        android:layout_height="@dimen/dp_40"
        android:layout_marginTop="@dimen/dp_50" >

        <ImageView
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:layout_gravity="center"
            android:src="@mipmap/ic_back_white" />
    </FrameLayout>


    <FrameLayout
        android:id="@+id/fl_report"
        android:layout_width="@dimen/dp_40"
        android:layout_height="@dimen/dp_40"
        android:layout_gravity="end"
        android:layout_marginTop="@dimen/dp_50"
        android:layout_marginEnd="@dimen/dp_12" >

        <ImageView
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:layout_gravity="center"
            android:src="@mipmap/ic_video_platform" />
    </FrameLayout>

</FrameLayout>