<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/dp_90"
    android:layout_height="@dimen/dp_90"
    android:gravity="center"
    android:orientation="vertical">

    <!--    <ImageView-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="match_parent"-->
    <!--        android:src="@mipmap/ic_gift_special_offer" />-->

    <com.opensource.svgaplayer.SVGAImageView
        android:layout_width="@dimen/dp_90"
        android:layout_height="@dimen/dp_90"
        app:autoPlay="true"
        app:source="first_recharge.svga" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:paddingBottom="@dimen/dp_5"
        android:text="@string/special_offer"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12"
        android:visibility="gone" />
</RelativeLayout>