<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_top8"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_75"
        android:paddingHorizontal="@dimen/dp_12">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/cancel"
            android:textColor="#bfbfbf"
            android:textSize="@dimen/sp_16" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:orientation="vertical">

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/choose_language"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_18" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/support_multiple"
                android:textColor="#5C66E0"
                android:textSize="@dimen/sp_12" />
        </LinearLayout>

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:text="@string/confirm"
            android:textColor="@color/colorAccent" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_1"
        android:layout_marginBottom="@dimen/dp_10"
        android:background="@color/color_EEEEEE" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/dp_50"
        android:layout_marginBottom="@dimen/dp_16"
        android:paddingHorizontal="@dimen/dp_13"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:spanCount="3"
        tools:listitem="@layout/item_choose_language" />
</LinearLayout>