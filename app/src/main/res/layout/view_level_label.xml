<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    tools:visibility="visible">

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_level"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_24"
        android:layout_centerVertical="true"
        android:fontFamily="@font/alegreyasanssc"
        android:gravity="center"
        android:minWidth="@dimen/dp_50"
        android:paddingStart="@dimen/dp_13"
        android:paddingBottom="@dimen/dp_2"
        android:textSize="@dimen/sp_12"
        tools:background="@mipmap/bg_level_label_1"
        tools:text="Lv0" />
</RelativeLayout>