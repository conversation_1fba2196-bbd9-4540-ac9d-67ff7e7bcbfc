<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/dp_16"
    android:layout_marginVertical="@dimen/dp_8"
    android:paddingBottom="@dimen/dp_8"
    android:background="@drawable/shape_1d2031_10">

    <ImageView
        android:id="@+id/iv_header"
        android:layout_width="@dimen/dp_16"
        android:layout_height="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="@dimen/dp_16"
        app:layout_constraintTop_toTopOf="@id/tv_title" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_50"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_16"
        android:gravity="center_vertical"
        android:text="@string/title_match_with_anchor"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14"
        app:layout_constraintEnd_toStartOf="@id/iv_header"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_5"
        android:layout_marginTop="@dimen/dp_15"
        android:background="@color/color_divider"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14"
        android:layout_marginEnd="@dimen/dp_16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/call_duration"
        android:textColor="@color/white_30"
        android:textSize="@dimen/sp_14"
        android:layout_marginStart="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="@id/tv_time"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_time" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_diamond"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14"
        android:layout_marginEnd="@dimen/dp_16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_time" />

    <ImageView
        android:layout_width="8dp"
        android:layout_height="12dp"
        android:src="@mipmap/ic_diamond"
        app:layout_constraintBottom_toBottomOf="@id/tv_diamond"
        app:layout_constraintEnd_toStartOf="@id/tv_diamond"
        app:layout_constraintTop_toTopOf="@id/tv_diamond" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/expense_diamond"
        android:textColor="@color/white_30"
        android:textSize="@dimen/sp_14"
        android:layout_marginStart="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="@id/tv_diamond"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_diamond" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_juan"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14"
        android:layout_marginEnd="@dimen/dp_16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_diamond" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/expense_juan"
        android:textColor="@color/white_30"
        android:textSize="@dimen/sp_14"
        android:layout_marginStart="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="@id/tv_juan"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_juan" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_create_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14"
        android:layout_marginEnd="@dimen/dp_16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_juan" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/label_create_time"
        android:textColor="@color/white_30"
        android:layout_marginStart="@dimen/dp_16"
        android:textSize="@dimen/sp_14"
        app:layout_constraintBottom_toBottomOf="@id/tv_create_time"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_create_time" />


</androidx.constraintlayout.widget.ConstraintLayout>