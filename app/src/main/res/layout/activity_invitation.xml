<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.bdc.android.library.widget.XToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:x_title="@string/share_friend" />

    <ImageView
        android:id="@+id/iv_image"
        android:layout_width="@dimen/dp_78"
        android:layout_height="@dimen/dp_78"
        android:layout_marginStart="@dimen/dp_36"
        app:layout_constraintBottom_toTopOf="@id/ll_bottom"
        app:layout_constraintStart_toStartOf="parent" />

    <com.allen.library.shape.ShapeLinearLayout
        android:id="@+id/ll_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="260dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.heart.heartmerge.i18n.I18nButton
            android:id="@+id/btn_share"
            style="@style/PrimaryButton"
            android:layout_alignParentBottom="true"
            android:layout_marginHorizontal="@dimen/dp_24"
            android:layout_marginTop="@dimen/dp_24"
            android:layout_marginBottom="@dimen/dp_30"
            android:text="@string/quick_share" />

        <com.allen.library.shape.ShapeTextView
            android:id="@+id/tv_save"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_40"
            android:layout_alignParentBottom="true"
            android:layout_marginHorizontal="@dimen/dp_24"
            android:layout_marginBottom="@dimen/dp_25"
            android:gravity="center"
            android:text="保存图片到相册"
            android:visibility="gone"
            android:textColor="#FF9EA3D8"
            android:textSize="16sp"
            app:shapeCornersRadius="@dimen/dp_20"
            app:shapeSolidColor="#FF5A5D7B"
            app:shapeStrokeWidth="@dimen/dp_1" />

    </com.allen.library.shape.ShapeLinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>