<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/ll_center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginTop="@dimen/dp_35"
        android:background="@drawable/shape_white_center_popup"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/dp_28"
        android:paddingTop="@dimen/dp_35"
        android:paddingBottom="@dimen/dp_24"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_6"
            android:fontFamily="@font/baloochettan2"
            android:gravity="center"
            android:textColor="@color/color_EC12E2"
            android:textSize="@dimen/sp_24"
            android:textStyle="bold"
            tools:text="Title" />

        <TextView
            android:id="@+id/tv_sub_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="#100821"
            android:textSize="@dimen/sp_14"
            android:visibility="gone"
            tools:text="subTitle"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_20"
            android:gravity="center"
            android:visibility="gone"
            tools:visibility="visible"
            android:textColor="#100821"
            android:textSize="@dimen/sp_14"
            tools:text="@string/payment_status_success_title_content" />

        <TextView
            android:id="@+id/btn_sure"
            style="@style/PrimaryButton"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44"
            android:gravity="center"
            android:layout_marginTop="@dimen/dp_20"
            android:maxLines="1"
            tools:text="@string/already_know"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/btn_cancel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:gravity="center"
            android:maxLines="1"
            android:text="@string/already_know"
            android:textColor="#999999"
            android:textSize="@dimen/sp_12"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_emoji"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_70"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="-35dp"
        android:src="@mipmap/ic_emoji_status_online"
        app:layout_constraintEnd_toEndOf="@id/ll_center"
        app:layout_constraintStart_toStartOf="@id/ll_center"
        app:layout_constraintTop_toTopOf="@+id/ll_center" />
</androidx.constraintlayout.widget.ConstraintLayout>