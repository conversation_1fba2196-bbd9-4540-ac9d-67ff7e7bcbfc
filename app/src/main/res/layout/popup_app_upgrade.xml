<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_height="@dimen/dp_422"
    android:background="@mipmap/bg_app_upgrade"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_gravity="end"
        android:layout_marginEnd="@dimen/dp_24"
        android:background="@mipmap/ic_dialog_close_white" />

    <ScrollView
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_110"
        android:layout_marginHorizontal="@dimen/dp_60"
        android:layout_marginTop="@dimen/dp_150">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:orientation="vertical">

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_10"
                android:text="@string/new_update_available"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_20" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_update_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_7"
                tools:text="1.Updated content \n 2.Updated content \n 3.Updated content"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14" />
        </LinearLayout>

    </ScrollView>

    <com.heart.heartmerge.i18n.I18nButton
        android:id="@+id/btn_update"
        style="@style/PrimaryButton"
        android:layout_marginHorizontal="@dimen/dp_80"
        android:layout_marginTop="@dimen/sp_20"
        android:text="@string/update_now" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_12"
        android:visibility="gone"
        android:text="@string/no_thanks"
        android:textColor="@color/color_9F9CA6"
        android:textSize="@dimen/sp_12" />

</LinearLayout>