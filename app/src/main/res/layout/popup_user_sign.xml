<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@mipmap/bg_user_sign"
        android:paddingBottom="@dimen/dp_20">

        <!--        <TextView-->
        <!--            android:id="@+id/tv_sign_day"-->
        <!--            android:layout_width="wrap_content"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:layout_marginTop="@dimen/dp_80"-->
        <!--            android:elevation="@dimen/dp_4"-->
        <!--            android:fontFamily="@font/baloochettan2"-->
        <!--            android:text="0"-->
        <!--            android:textColor="@color/color_9F2AF8"-->
        <!--            android:textSize="@dimen/dp_42"-->
        <!--            android:textStyle="bold"-->
        <!--            android:visibility="gone"-->
        <!--            app:layout_constraintEnd_toEndOf="parent"-->
        <!--            app:layout_constraintStart_toStartOf="parent"-->
        <!--            app:layout_constraintTop_toTopOf="parent"-->
        <!--            tools:text="1" />-->

        <com.heart.heartmerge.ui.widget.GradientTextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_60"
            android:fontFamily="@font/rammettoone"
            android:text="@string/vip_check_in_bonus"
            android:textColor="@color/colorAccent"
            android:textSize="@dimen/sp_18"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_sign_day"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="@string/checked_in_for_x_days"
            android:textColor="#FF87F9"
            app:layout_constraintStart_toStartOf="@id/tv_title"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/ll_sign_container"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_140"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp_5"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <!-- 四个等分的列参考线 -->
            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.0" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.25" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.5" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.75" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="1.0" />

            <!-- 上面四个元素 -->
            <com.heart.heartmerge.popup.UserSignItemView
                android:id="@+id/item_sign_1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_5"
                app:layout_constraintEnd_toStartOf="@id/guideline2"
                app:layout_constraintStart_toStartOf="@id/guideline1"
                app:layout_constraintTop_toTopOf="parent" />

            <com.heart.heartmerge.popup.UserSignItemView
                android:id="@+id/item_sign_2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_5"
                app:layout_constraintEnd_toStartOf="@id/guideline3"
                app:layout_constraintStart_toStartOf="@id/guideline2"
                app:layout_constraintTop_toTopOf="parent" />

            <com.heart.heartmerge.popup.UserSignItemView
                android:id="@+id/item_sign_3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_5"
                app:layout_constraintEnd_toStartOf="@id/guideline4"
                app:layout_constraintStart_toStartOf="@id/guideline3"
                app:layout_constraintTop_toTopOf="parent" />

            <com.heart.heartmerge.popup.UserSignItemView
                android:id="@+id/item_sign_4"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_5"
                app:layout_constraintEnd_toStartOf="@id/guideline5"
                app:layout_constraintStart_toStartOf="@id/guideline4"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- 底下的三个元素，第二行 -->
            <com.heart.heartmerge.popup.UserSignItemView
                android:id="@+id/item_sign_5"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_5"
                android:layout_marginTop="@dimen/dp_10"
                app:layout_constraintEnd_toStartOf="@id/guideline2"
                app:layout_constraintStart_toStartOf="@id/guideline1"
                app:layout_constraintTop_toBottomOf="@id/item_sign_1" />

            <com.heart.heartmerge.popup.UserSignItemView
                android:id="@+id/item_sign_6"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_5"
                android:layout_marginTop="@dimen/dp_10"
                app:layout_constraintEnd_toStartOf="@id/guideline3"
                app:layout_constraintStart_toStartOf="@id/guideline2"
                app:layout_constraintTop_toBottomOf="@id/item_sign_2" />

            <com.heart.heartmerge.popup.UserSignSpecialItemView
                android:id="@+id/item_sign_7"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_5"
                android:layout_marginTop="@dimen/dp_10"
                app:layout_constraintEnd_toStartOf="@id/guideline5"
                app:layout_constraintStart_toStartOf="@id/guideline3"
                app:layout_constraintTop_toBottomOf="@id/item_sign_3" />

            <LinearLayout
                android:id="@+id/ll_lock_container"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginHorizontal="@dimen/dp_5"
                android:alpha="0.7"
                android:background="@color/white"
                android:gravity="center"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/item_sign_7"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/item_sign_1">

                <ImageView
                    android:layout_width="@dimen/dp_100"
                    android:layout_height="@dimen/dp_100"
                    android:src="@mipmap/ic_user_sign_lock" />
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.heart.heartmerge.i18n.I18nButton
            android:id="@+id/btn_sign"
            style="@style/PrimaryButton"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_48"
            android:layout_marginHorizontal="@dimen/dp_40"
            android:layout_marginTop="@dimen/sp_20"
            android:enabled="false"
            android:text="@string/claim"
            android:textAllCaps="false"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_sign_container" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 关闭按钮 -->
    <ImageView
        android:id="@+id/ic_close"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_20"
        android:src="@mipmap/ic_dialog_close_white" />

</LinearLayout>
