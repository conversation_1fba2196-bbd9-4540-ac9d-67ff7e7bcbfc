<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/dp_14"
    android:padding="@dimen/dp_1"
    tools:background="@drawable/shape_default_avatar_border">

    <ImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/dp_54"
        android:layout_height="@dimen/dp_54"
        tools:background="@mipmap/ic_default_avatar_male1" />

    <ImageView
        android:id="@+id/iv_status"
        android:layout_width="@dimen/dp_13"
        android:layout_height="@dimen/dp_13"
        android:layout_gravity="end|bottom"
        android:background="@mipmap/ic_checkbox_red_checked"
        tools:visibility="visible"
        android:visibility="gone" />
</FrameLayout>