<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_dialog"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:translationZ="1dp"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center_horizontal"
        android:background="@mipmap/ic_launcher" />

    <ImageView
        android:layout_width="30dp"
        android:id="@+id/iv_close"
        android:layout_height="30dp"
        android:layout_marginRight="10dp"
        android:layout_alignParentRight="true"
        android:background="@mipmap/ic_dialog_close_white"/>

    <LinearLayout
        android:layout_below="@id/iv_dialog"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_dialog_shape"
        android:layout_marginTop="-25dp"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingTop="40dp"
        android:paddingBottom="30dp">

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="恭喜中奖"
            android:textColor="#f9453c"
            android:textSize="30dp" />

        <ImageView
            android:id="@+id/iv_reward"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:background="@mipmap/ic_reward1" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_reward_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="头像框"
            android:textColor="@color/black"
            android:textSize="20dp" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:text="可前往至‘’我的奖品‘中心查看" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_check"
            android:layout_width="185dp"
            android:layout_height="40dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/bg_button_shape_ff6e58_f9453c"
            android:gravity="center"
            android:text="去查看"
            android:textColor="@color/white"
            android:textSize="20dp" />
    </LinearLayout>
</RelativeLayout>