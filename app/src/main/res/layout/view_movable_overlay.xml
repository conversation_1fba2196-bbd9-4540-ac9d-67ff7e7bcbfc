<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_home_renew"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/dp_16"
    android:paddingVertical="@dimen/dp_5">

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/gave_a_gift_to"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14" />

    <ImageView
        android:id="@+id/iv_gift"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_20"
        android:layout_marginStart="@dimen/dp_5"
        android:src="@color/white" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_gift_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_3"
        android:text="x1"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12" />
</LinearLayout>