<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#100821"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:background="#231b32" />

    <RelativeLayout
        android:id="@+id/rc_ext_attached_info_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone" />

    <!--    <View-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="@dimen/rc_divider_height"-->
    <!--        android:background="@color/rc_divider_color"/>-->

    <RelativeLayout
        android:id="@+id/rc_ext_input_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <RelativeLayout
        android:id="@+id/rc_ext_board_container"
        android:layout_width="match_parent"
        android:layout_height="@dimen/message_extension_board_height" />
</LinearLayout>