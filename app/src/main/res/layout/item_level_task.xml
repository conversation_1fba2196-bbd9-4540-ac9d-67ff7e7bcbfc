<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="@dimen/dp_16"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/iv_task"
        android:layout_width="@dimen/dp_36"
        android:layout_height="@dimen/dp_36"
        android:src="@mipmap/ic_level_task_recharge_bonus" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_task_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:drawablePadding="@dimen/dp_8"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            tools:text="Recharge bonus" />

        <TextView
            android:id="@+id/tv_task_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_10"
            android:drawablePadding="@dimen/dp_8"
            android:textColor="#625D6E"
            android:textSize="@dimen/sp_10"
            tools:text="Recharge bonus" />
    </LinearLayout>

    <View
        android:layout_width="0dp"
        android:layout_height="1dp" />

    <TextView
        android:id="@+id/tv_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawablePadding="@dimen/dp_8"
        android:textColor="@color/color_B047FF"
        android:textSize="@dimen/sp_14"
        android:visibility="gone"
        tools:text="2%"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_get"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_9f2af8_50"
        android:drawablePadding="@dimen/dp_8"
        android:paddingHorizontal="@dimen/dp_10"
        android:paddingVertical="@dimen/dp_3"
        tools:text="@string/get"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12"
        android:visibility="gone"
        tools:visibility="visible" />
</LinearLayout>