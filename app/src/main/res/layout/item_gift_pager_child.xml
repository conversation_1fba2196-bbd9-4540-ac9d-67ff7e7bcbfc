<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/dp_13"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:paddingVertical="@dimen/dp_8">

    <ImageView
        android:id="@+id/iv_gift"
        android:layout_width="@dimen/dp_55"
        android:layout_height="@dimen/dp_50" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_gift_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:textColor="@color/white"
        android:visibility="gone"
        android:textSize="@dimen/sp_12" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:gravity="center_vertical">

        <ImageView
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:layout_marginStart="@dimen/dp_1"
            android:src="@mipmap/ic_diamond_purple" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_gift_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawablePadding="@dimen/dp_4"
            android:textColor="@color/color_F691FF"
            android:textSize="@dimen/sp_11" />

    </LinearLayout>

</LinearLayout>