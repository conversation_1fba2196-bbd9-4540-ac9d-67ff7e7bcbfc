<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="10dp"
    android:paddingHorizontal="10dp"
    android:orientation="vertical">

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="与主播XXX匹配"
        android:textColor="@color/white"
        android:textSize="18sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:orientation="horizontal">

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="通话时长："
            android:textColor="#bababa" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_contact_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="00:00:00"
            android:textColor="@color/white" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:orientation="horizontal">

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="消耗钻石："
            android:textColor="#bababa" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_diamond"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableLeft="@mipmap/ic_diamond"
            android:text="1"
            android:drawablePadding="5dp"
            android:textColor="@color/white" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:orientation="horizontal">

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="消耗匹配券："
            android:textColor="#bababa" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_match_coupon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="1"
            android:textColor="@color/white" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="10dp"
        android:background="#141826" />
</LinearLayout>