<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_33"
        android:background="@drawable/shape_white_top20"
        android:orientation="vertical">

        <FrameLayout
            android:id="@+id/fl_close"
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"
            android:layout_gravity="end"
            android:layout_marginTop="@dimen/dp_7">

            <ImageView
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_14"
                android:layout_gravity="center"
                android:src="@mipmap/ic_dialog_close_small" />

        </FrameLayout>

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_21"
            android:gravity="center"
            android:text="@string/popup_title_black"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_18"
            android:textStyle="bold" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_24"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_38"
            android:text="@string/popup_message_black"
            android:textColor="#80333333"
            android:textSize="@dimen/sp_14"  />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/btn_black"
            style="@style/PrimaryButton"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_40"
            android:layout_marginTop="@dimen/dp_30"
            android:text="@string/block"
            android:layout_marginBottom="@dimen/dp_20"
            android:layout_marginHorizontal="@dimen/dp_38" />

    </LinearLayout>

    <ImageView
        android:id="@+id/anchor_header"
        android:layout_width="@dimen/dp_66"
        android:layout_height="@dimen/dp_66"
        android:layout_gravity="center_horizontal"
        android:src="@mipmap/ic_pic_default_oval" />
</FrameLayout>