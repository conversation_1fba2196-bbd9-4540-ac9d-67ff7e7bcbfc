<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_swipe_close"
        android:layout_width="@dimen/dp_50"
        android:layout_height="@dimen/dp_50"
        android:layout_marginStart="@dimen/dp_53"
        android:layout_marginBottom="@dimen/dp_20"
        android:src="@mipmap/ic_swipe_close"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageView
        android:id="@+id/iv_swipe_phone"
        android:layout_width="@dimen/dp_50"
        android:layout_height="@dimen/dp_50"
        android:layout_marginEnd="@dimen/dp_53"
        android:layout_marginBottom="@dimen/dp_20"
        android:src="@mipmap/ic_swipe_phone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_swipe_close" />

    <com.bdc.android.library.refreshlayout.XRecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_20"
        android:layout_marginBottom="@dimen/dp_10"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:orientation="horizontal"
        app:layout_constraintBottom_toTopOf="@id/iv_swipe_close"
        app:layout_constraintTop_toTopOf="parent"
        tools:listitem="@layout/item_swipe_child" />


</androidx.constraintlayout.widget.ConstraintLayout>