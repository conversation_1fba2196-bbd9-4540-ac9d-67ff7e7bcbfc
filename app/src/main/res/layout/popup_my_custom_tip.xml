<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content" >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/dp_20"
        android:paddingHorizontal="@dimen/dp_35"
        android:layout_marginTop="@dimen/dp_41"
        android:background="@drawable/shape_white_center_popup"
        android:orientation="vertical">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_50"
            android:gravity="center"
            android:textColor="@color/color_010101"
            android:textSize="@dimen/sp_16"
            tools:text="内容内容内容内容内容内容内容内容内容内容内容内容内容内容"
            tools:visibility="visible" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:layout_marginTop="@dimen/dp_30"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/dialog_left_btn"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp_40"
                android:layout_marginEnd="@dimen/dp_20"
                android:layout_weight="1"
                android:background="@drawable/shape_dialog_btn_exit"
                android:gravity="center"
                android:text="@string/cancel"
                android:textColor="@color/color_666666" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/dialog_right_btn"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp_40"
                android:layout_weight="1"
                android:background="@drawable/selector_primary_button"
                android:gravity="center"
                android:text="@string/string_ok"
                android:textColor="@color/white" />
        </LinearLayout>

    </LinearLayout>
    
    
    <ImageView
        android:layout_width="@dimen/dp_88"
        android:layout_height="@dimen/dp_82"
        android:layout_gravity="center_horizontal"
        android:src="@mipmap/ic_defalut_popup_top"/>



</FrameLayout>