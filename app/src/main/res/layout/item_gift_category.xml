<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:textColor="#81839A"
        android:ellipsize="end"
        android:gravity="center"
        android:textSize="@dimen/sp_16"
        tools:text="礼物" />

    <View
        android:id="@+id/indicator"
        android:layout_width="@dimen/dp_10"
        android:layout_height="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_5"
        android:visibility="invisible"
        android:background="@drawable/shape_gift_indicator" />
</LinearLayout>