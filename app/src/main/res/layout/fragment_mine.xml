<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:ignore="MissingDefaultResource">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@mipmap/bg_mine" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:fillViewport="true"
        android:paddingBottom="@dimen/dp_30">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_person"
                android:layout_width="@dimen/dp_0"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_77"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <RelativeLayout
                    android:id="@+id/avatar_container"
                    android:layout_width="@dimen/dp_72"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_20"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/iv_avatar"
                        android:layout_width="@dimen/dp_72"
                        android:layout_height="@dimen/dp_72"
                        android:scaleType="centerCrop"
                        tools:src="@mipmap/ic_pic_default_oval" />

                    <com.heart.heartmerge.i18n.I18nTextView
                        android:id="@+id/tv_online_status"
                        android:layout_width="@dimen/dp_88"
                        android:layout_height="@dimen/dp_28"
                        android:layout_alignParentBottom="true"
                        android:layout_centerHorizontal="true"
                        android:layout_marginBottom="@dimen/dp_0"
                        android:background="@mipmap/ic_online_status_half_circle"
                        android:gravity="center"
                        android:paddingBottom="@dimen/dp_3"
                        android:text="@string/on_line"
                        android:textColor="@color/color_21C76E"
                        android:textSize="@dimen/sp_12"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </RelativeLayout>

                <TextView
                    android:id="@+id/tv_name"
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_5"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_20"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toEndOf="@+id/avatar_container"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintWidth_default="wrap"
                    tools:text="张三111" />

                <com.heart.heartmerge.ui.widget.level.LevelLabelView
                    android:id="@+id/level_label_view"
                    android:layout_width="@dimen/dp_55"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_5"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/tv_name"
                    app:layout_constraintStart_toEndOf="@+id/tv_name"
                    app:layout_constraintTop_toTopOf="@id/tv_name" />

                <LinearLayout
                    android:id="@+id/ll_sex"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dp_30"
                    android:layout_marginTop="@dimen/dp_4"
                    android:gravity="center"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/tv_name"
                    app:layout_constraintTop_toBottomOf="@id/tv_name">

                    <ImageView
                        android:id="@+id/iv_sex"
                        android:layout_width="@dimen/dp_12"
                        android:layout_height="@dimen/dp_12"
                        android:src="@mipmap/ic_gender_male"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tv_age"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp_4"
                        android:background="@drawable/shape_border_5b397a"
                        android:paddingHorizontal="@dimen/dp_8"
                        android:paddingVertical="@dimen/dp_3"
                        android:textColor="#7E6B95"
                        android:textSize="@dimen/sp_12"
                        tools:text="25" />

                    <TextView
                        android:id="@+id/tv_country"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_3"
                        android:background="@drawable/shape_border_5b397a"
                        android:paddingHorizontal="@dimen/dp_8"
                        android:paddingVertical="@dimen/dp_3"
                        android:textColor="#7E6B95"
                        android:textSize="@dimen/sp_12"
                        tools:text="Indonesia" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp_10"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:src="@mipmap/ic_arrow_right_gray" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_5"
                    android:drawablePadding="@dimen/dp_5"
                    android:gravity="center_vertical"
                    android:text="ID：111111"
                    android:textColor="#826a9b"
                    android:textSize="@dimen/sp_12"
                    app:drawableEndCompat="@mipmap/ic_copy"
                    app:layout_constraintStart_toStartOf="@id/ll_sex"
                    app:layout_constraintTop_toBottomOf="@id/ll_sex" />

                <com.heart.heartmerge.ui.widget.CustomLabelsView
                    android:id="@+id/labels"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_10"
                    android:saveEnabled="false"
                    android:visibility="gone"
                    app:labelBackground="@drawable/shape_mine_language_item_bg"
                    app:labelTextColor="#40FFFFFF"
                    app:labelTextPaddingBottom="@dimen/dp_3"
                    app:labelTextPaddingLeft="@dimen/dp_8"
                    app:labelTextPaddingRight="@dimen/dp_8"
                    app:labelTextPaddingTop="@dimen/dp_3"
                    app:labelTextSize="@dimen/sp_12"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/ll_sex"
                    app:layout_constraintTop_toBottomOf="@id/ll_sex"
                    app:layout_scrollFlags="scroll"
                    app:lineMargin="@dimen/dp_6"
                    app:maxSelect="0"
                    app:selectType="NONE"
                    app:wordMargin="@dimen/dp_6" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.heart.heartmerge.ui.widget.level.LevelCardView
                android:id="@+id/level_cardView"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_160"
                android:layout_marginHorizontal="@dimen/dp_16"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cl_person" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_reward_task"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_100"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_20"
                android:background="@mipmap/bg_reward_task"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/level_cardView">

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_reward_task"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_40"
                    android:layout_marginTop="@dimen/dp_15"
                    android:fontFamily="@font/rammettoone"
                    android:text="@string/reward_tasks"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_20"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_5"
                    android:background="@mipmap/bg_get_now"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingBottom="@dimen/dp_2"
                    app:layout_constraintEnd_toEndOf="@id/tv_reward_task"
                    app:layout_constraintTop_toBottomOf="@id/tv_reward_task">

                    <com.heart.heartmerge.i18n.I18nTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/rammettoone"
                        android:text="@string/get_now"
                        android:textColor="#7BD585"
                        android:textSize="@dimen/sp_10" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@mipmap/ic_get_now_arrow" />
                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.cardview.widget.CardView
                android:id="@+id/card_wallet"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_20"
                app:cardBackgroundColor="@color/color_card_background"
                app:cardCornerRadius="@dimen/dp_10"
                app:layout_constraintTop_toBottomOf="@id/cl_reward_task">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/dp_12"
                    android:paddingVertical="@dimen/dp_16">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <ImageView
                            android:layout_width="@dimen/dp_16"
                            android:layout_height="@dimen/dp_16"
                            android:background="@mipmap/ic_mine_crown"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <com.heart.heartmerge.i18n.I18nTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_8"
                            android:layout_weight="1"
                            android:text="@string/vip_bonus"
                            android:textColor="@color/white"
                            android:textSize="@dimen/sp_14" />

                        <com.heart.heartmerge.i18n.I18nTextView
                            android:id="@+id/tv_checkIn"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/checkin"
                            android:textColor="@color/white"
                            android:textSize="@dimen/sp_14" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/dp_10"
                            android:layout_marginStart="6dp"
                            android:src="@mipmap/ic_arrow_right_gray" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <RelativeLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp_5"
                            android:layout_weight="1">

                            <ImageView
                                android:id="@+id/iv_diamond_card"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp_82"
                                android:layout_centerHorizontal="true"
                                android:src="@mipmap/bg_mine_diamond_card" />

                            <com.heart.heartmerge.i18n.I18nTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_12"
                                android:layout_marginTop="@dimen/dp_30"
                                android:text="@string/diamond"
                                android:textColor="#9d5acf"
                                android:textSize="@dimen/sp_12" />

                            <androidx.compose.ui.platform.ComposeView
                                android:id="@+id/tv_diamond_value"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_12"
                                android:layout_marginTop="@dimen/dp_45" />
                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_5"
                            android:layout_weight="1">

                            <ImageView
                                android:id="@+id/iv_vip_card"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp_82"
                                android:layout_centerHorizontal="true"
                                android:scaleType="fitCenter"
                                android:src="@mipmap/bg_mine_inactive_vip" />

                            <TextView
                                android:id="@+id/tv_vip_type"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_12"
                                android:layout_marginTop="@dimen/dp_30"
                                android:textColor="@color/white"
                                android:textSize="@dimen/sp_12"
                                tools:text="vip" />

                            <TextView
                                android:id="@+id/tv_vip_value"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_12"
                                android:layout_marginTop="@dimen/dp_50"
                                android:textColor="@color/white"
                                android:textSize="@dimen/sp_10"
                                tools:text="@string/vip_non_activated" />
                        </RelativeLayout>
                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_20"
                android:orientation="vertical"
                app:cardBackgroundColor="@color/color_card_background"
                app:cardCornerRadius="@dimen/dp_10"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/card_wallet">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/dp_16"
                    android:paddingVertical="@dimen/dp_8">

                    <LinearLayout
                        android:id="@+id/ll_who_see_me"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="@dimen/dp_16"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="@dimen/dp_26"
                            android:layout_height="@dimen/dp_26"
                            android:background="@mipmap/ic_mine_who_like_me" />

                        <com.heart.heartmerge.i18n.I18nTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_12"
                            android:layout_weight="1"
                            android:text="@string/who_see_me"
                            android:textColor="@color/white"
                            android:textSize="@dimen/sp_14" />

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_12"
                            android:gravity="end">

                            <androidx.compose.ui.platform.ComposeView
                                android:id="@+id/tv_like_num"
                                android:layout_width="@dimen/dp_26"
                                android:layout_height="@dimen/dp_26"
                                android:layout_marginEnd="@dimen/dp_8"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toStartOf="@id/right_arrow"
                                app:layout_constraintTop_toTopOf="parent" />

                            <ImageView
                                android:id="@+id/image1"
                                android:layout_width="@dimen/dp_26"
                                android:layout_height="@dimen/dp_26"
                                android:layout_marginEnd="-5dp"
                                android:background="@drawable/bg_circle_white"
                                android:paddingHorizontal="@dimen/dp_1"
                                android:paddingVertical="@dimen/dp_1"
                                android:src="@mipmap/random_header1"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toStartOf="@id/tv_like_num"
                                app:layout_constraintTop_toTopOf="parent" />

                            <ImageView
                                android:id="@+id/image2"
                                android:layout_width="@dimen/dp_26"
                                android:layout_height="@dimen/dp_26"
                                android:layout_marginEnd="-5dp"
                                android:background="@drawable/bg_circle_white"
                                android:paddingHorizontal="@dimen/dp_1"
                                android:paddingVertical="@dimen/dp_1"
                                android:src="@mipmap/random_header3"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toStartOf="@id/image1"
                                app:layout_constraintTop_toTopOf="parent" />

                            <ImageView
                                android:id="@+id/image3"
                                android:layout_width="@dimen/dp_26"
                                android:layout_height="@dimen/dp_26"
                                android:layout_marginEnd="-5dp"
                                android:background="@drawable/bg_circle_white"
                                android:paddingHorizontal="@dimen/dp_1"
                                android:paddingVertical="@dimen/dp_1"
                                android:src="@mipmap/random_header2"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toStartOf="@id/image2"
                                app:layout_constraintTop_toTopOf="parent" />


                            <ImageView
                                android:id="@+id/right_arrow"
                                android:layout_width="wrap_content"
                                android:layout_height="@dimen/dp_10"
                                android:src="@mipmap/ic_arrow_right_gray"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />
                        </androidx.constraintlayout.widget.ConstraintLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_menu_task"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="@dimen/dp_16"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <ImageView
                            android:layout_width="@dimen/dp_26"
                            android:layout_height="@dimen/dp_26"
                            android:background="@mipmap/ic_mine_menu_reward_tasks" />

                        <com.heart.heartmerge.i18n.I18nTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_12"
                            android:layout_weight="1"
                            android:text="@string/mine_menu_reward_tasks"
                            android:textColor="@color/white"
                            android:textSize="@dimen/sp_14" />

                        <TextView
                            android:id="@+id/tv_task_status"
                            android:layout_width="@dimen/dp_10"
                            android:layout_height="@dimen/dp_10"
                            android:layout_marginEnd="@dimen/dp_8"
                            android:background="@drawable/shape_busy_dot"
                            android:visibility="gone"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/right_arrow"
                            tools:visibility="visible" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/dp_10"
                            android:src="@mipmap/ic_arrow_right_gray" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_menu_customer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="@dimen/dp_16"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="@dimen/dp_26"
                            android:layout_height="@dimen/dp_26"
                            android:background="@mipmap/ic_mine_menu_customer_service" />

                        <com.heart.heartmerge.i18n.I18nTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_12"
                            android:layout_weight="1"
                            android:text="@string/mine_menu_customer_service"
                            android:textColor="@color/white"
                            android:textSize="@dimen/sp_14" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/dp_10"
                            android:src="@mipmap/ic_arrow_right_gray" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_menu_history"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="@dimen/dp_16"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="@dimen/dp_26"
                            android:layout_height="@dimen/dp_26"
                            android:background="@mipmap/ic_mine_menu_diamond_history" />

                        <com.heart.heartmerge.i18n.I18nTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_12"
                            android:layout_weight="1"
                            android:text="@string/mine_menu_diamond_history"
                            android:textColor="@color/white"
                            android:textSize="@dimen/sp_14" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/dp_10"
                            android:src="@mipmap/ic_arrow_right_gray" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_menu_sign"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="@dimen/dp_16"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="@dimen/dp_26"
                            android:layout_height="@dimen/dp_26"
                            android:background="@mipmap/ic_mine_menu_sign_in" />

                        <com.heart.heartmerge.i18n.I18nTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_12"
                            android:layout_weight="1"
                            android:text="@string/mine_menu_sign_in"
                            android:textColor="@color/white"
                            android:textSize="@dimen/sp_14" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/dp_10"
                            android:src="@mipmap/ic_arrow_right_gray" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_menu_setting"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="@dimen/dp_16"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="@dimen/dp_26"
                            android:layout_height="@dimen/dp_26"
                            android:background="@mipmap/ic_mine_menu_setting" />

                        <com.heart.heartmerge.i18n.I18nTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_12"
                            android:layout_weight="1"
                            android:text="@string/mine_menu_setting"
                            android:textColor="@color/white"
                            android:textSize="@dimen/sp_14" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/dp_10"
                            android:src="@mipmap/ic_arrow_right_gray" />
                    </LinearLayout>
                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/cardView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_25"
                android:visibility="gone"
                app:cardCornerRadius="@dimen/dp_10"
                app:layout_constraintHorizontal_chainStyle="spread"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cl_person">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ll_vip"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@mipmap/ic_mine_vip_card"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp_20"
                    android:paddingVertical="@dimen/dp_13"
                    android:visibility="gone">

                    <com.heart.heartmerge.i18n.I18nTextView
                        android:id="@+id/tv_vip_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/mine_vip"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp_16"
                        android:textStyle="bold"
                        app:drawableStartCompat="@mipmap/ic_diamond"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.heart.heartmerge.i18n.I18nTextView
                        android:id="@+id/tv_vip_desc"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_10"
                        android:layout_marginEnd="@dimen/dp_4"
                        android:text="@string/mine_vip"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp_12"
                        app:layout_constraintEnd_toStartOf="@id/tv_activate"
                        app:layout_constraintStart_toStartOf="@id/tv_vip_title"
                        app:layout_constraintTop_toBottomOf="@id/tv_vip_title" />

                    <com.heart.heartmerge.i18n.I18nTextView
                        android:id="@+id/tv_activate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/shape_mine_vip_card_button"
                        android:gravity="center"
                        android:paddingHorizontal="@dimen/dp_15"
                        android:paddingVertical="@dimen/dp_5"
                        android:text="@string/activate_membership_now"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp_15"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_20"
                android:background="@drawable/shape_card_background"
                android:orientation="vertical"
                android:paddingHorizontal="@dimen/dp_16"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/card_wallet">

                <LinearLayout
                    android:id="@+id/ll_wallet"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_60"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/iv_next"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp_6"
                        app:srcCompat="@mipmap/ic_mine_wallet" />

                    <com.heart.heartmerge.i18n.I18nTextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_8"
                        android:layout_weight="1"
                        android:text="@string/my_wallet"
                        android:textColor="@color/color_D6D6D6"
                        android:textSize="@dimen/sp_16" />

                    <ImageView
                        android:layout_width="@dimen/dp_14"
                        android:layout_height="@dimen/dp_14"
                        android:src="@mipmap/ic_diamond" />

                    <androidx.compose.ui.platform.ComposeView
                        android:id="@+id/tv_diamond"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_6" />

                    <com.heart.heartmerge.i18n.I18nTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/diamond"
                        android:textColor="@color/color_FDA829"
                        android:textSize="@dimen/sp_14" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_10"
                        android:src="@mipmap/ic_right_gray" />
                </LinearLayout>

                <com.heart.heartmerge.ui.widget.CustomMineTextView
                    android:id="@+id/ll_privacy"
                    style="@style/mine"
                    android:gravity="center_vertical"
                    android:visibility="gone"
                    app:ctv_left="@string/privacy_setting"
                    app:ctv_left_icon="@mipmap/ic_mine_privacy"
                    app:ctv_right="@string/on_line"
                    app:ctv_right_color="@color/color_21C76E"
                    app:ctv_right_textSize="@dimen/dp_15" />

                <com.heart.heartmerge.ui.widget.CustomMineTextView
                    android:id="@+id/ll_daily_attendance"
                    style="@style/mine"
                    android:gravity="center_vertical"
                    android:visibility="gone"
                    app:ctv_left="@string/daily_attendance"
                    app:ctv_left_icon="@mipmap/ic_mine_daliy_signup"
                    app:ctv_right="@string/signin_day"
                    app:ctv_right_textSize="@dimen/dp_15" />

                <com.heart.heartmerge.ui.widget.CustomMineTextView
                    android:id="@+id/ll_matching_card"
                    style="@style/mine"
                    android:gravity="center_vertical"
                    app:ctv_left="@string/matching_card"
                    app:ctv_left_icon="@mipmap/ic_mine_matching_card"
                    app:ctv_next_icon="@mipmap/ic_right_gray" />

                <com.heart.heartmerge.ui.widget.CustomMineTextView
                    android:id="@+id/ll_turntable_raffle"
                    style="@style/mine"
                    android:gravity="center_vertical"
                    android:visibility="gone"
                    app:ctv_left="@string/turntable_raffle"
                    app:ctv_left_icon="@mipmap/ic_mine_turntable_raffle"
                    app:ctv_next_icon="@mipmap/ic_right_gray" />

                <com.heart.heartmerge.ui.widget.CustomMineTextView
                    android:id="@+id/ll_backpack"
                    style="@style/mine"
                    android:gravity="center_vertical"
                    android:visibility="gone"
                    app:ctv_left="@string/backpack"
                    app:ctv_left_icon="@mipmap/ic_mine_backpack"
                    app:ctv_next_icon="@mipmap/ic_right_gray" />

                <com.heart.heartmerge.ui.widget.CustomMineTextView
                    android:id="@+id/ll_share_friend"
                    style="@style/mine"
                    android:gravity="center_vertical"
                    app:ctv_left="@string/share_friend"
                    app:ctv_left_icon="@mipmap/ic_mine_share"
                    app:ctv_next_icon="@mipmap/ic_right_gray" />

                <com.heart.heartmerge.ui.widget.CustomMineTextView
                    android:id="@+id/ll_setting"
                    style="@style/mine"
                    android:gravity="center_vertical"
                    app:ctv_left="@string/string_setting"
                    app:ctv_left_icon="@mipmap/ic_mine_setting"
                    app:ctv_next_icon="@mipmap/ic_right_gray" />

            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

</FrameLayout>