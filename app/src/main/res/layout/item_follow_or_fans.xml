<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="@dimen/dp_14"
    android:layout_marginHorizontal="@dimen/dp_16">

    <ImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/dp_44"
        android:layout_height="@dimen/dp_44"
        android:src="@mipmap/ic_default_avatar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/online_dot"
        android:layout_width="@dimen/dp_8"
        android:layout_height="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_2"
        android:background="@drawable/shape_online_dot"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/iv_avatar"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar" />

    <TextView
        android:id="@+id/tv_nickname"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:maxLines="1"
        android:ellipsize="middle"
        android:maxWidth="@dimen/dp_150"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        android:includeFontPadding="false"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar"
        tools:text="李生" />

    <TextView
        android:id="@+id/tv_age"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:background="@drawable/shape_home_anchor_age"
        android:paddingHorizontal="@dimen/dp_8"
        android:paddingVertical="@dimen/dp_1"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12"
        app:layout_constraintStart_toStartOf="@id/tv_nickname"
        app:layout_constraintTop_toBottomOf="@id/tv_nickname"
        tools:text="26" />

    <TextView
        android:id="@+id/tv_country"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginTop="@dimen/dp_5"
        android:background="@drawable/shape_home_anchor_price"
        android:paddingHorizontal="@dimen/dp_8"
        android:paddingVertical="@dimen/dp_1"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12"
        app:layout_constraintStart_toEndOf="@id/tv_age"
        app:layout_constraintTop_toBottomOf="@id/tv_nickname"
        tools:text="26" />
    <ImageView
        android:id="@+id/iv_video"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:src="@mipmap/ic_anchor_video"
        android:layout_marginEnd="@dimen/dp_8"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@id/iv_msg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <ImageView
        android:id="@+id/iv_msg"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:src="@mipmap/ic_anchor_message"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />



</androidx.constraintlayout.widget.ConstraintLayout>