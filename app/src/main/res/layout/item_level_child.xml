<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_privilege"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_20"
        android:background="@mipmap/bg_privilege_banner"
        android:fontFamily="@font/rammettoone"
        android:gravity="center"
        android:text="Lv0-10 Privilege"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14"
        android:textStyle="bold" />

    <com.bdc.android.library.refreshlayout.XRecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@drawable/shape_card_background"
        android:paddingHorizontal="@dimen/dp_14"
        android:paddingVertical="@dimen/dp_4"
        android:visibility="gone"
        tools:itemCount="4"
        tools:listitem="@layout/item_level_task"
        tools:visibility="visible" />

</LinearLayout>