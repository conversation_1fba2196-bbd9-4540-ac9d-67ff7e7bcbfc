<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp_123"
    android:layout_height="@dimen/dp_150"
    android:layout_marginStart="@dimen/dp_12">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/anchor_header"
        android:layout_width="@dimen/dp_123"
        android:layout_height="@dimen/dp_150"
        android:adjustViewBounds="true"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/CustomShapeAppearance"
        app:strokeColor="@null" />

    <ImageView
        android:id="@+id/iv_location"
        android:layout_width="8dp"
        android:layout_height="@dimen/dp_10"
        android:layout_marginStart="@dimen/dp_6"
        android:layout_marginBottom="@dimen/dp_7"
        android:src="@mipmap/ic_location_you_may_like"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/anchor_country"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:includeFontPadding="false"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="@id/iv_location"
        app:layout_constraintStart_toEndOf="@id/iv_location"
        app:layout_constraintTop_toTopOf="@id/iv_location"
        tools:text="ID" />

    <TextView
        android:id="@+id/anchor_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_6"
        android:layout_marginBottom="@dimen/dp_2"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        android:maxLines="1"
        android:maxWidth="@dimen/dp_70"
        android:ellipsize="end"
        app:layout_constraintBottom_toTopOf="@id/anchor_country"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="AuroraAuroraAuroraAuroraAurora" />

    <ImageView
        android:id="@+id/file_lock"
        android:layout_width="@dimen/dp_36"
        android:layout_height="@dimen/dp_36"
        android:layout_marginEnd="@dimen/dp_6"
        android:layout_marginBottom="7dp"
        android:scaleType="fitXY"
        android:src="@mipmap/ic_video_you_may_like"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>