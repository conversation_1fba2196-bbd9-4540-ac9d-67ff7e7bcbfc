<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <io.rong.imkit.widget.refresh.SmartRefreshLayout
        android:id="@+id/rc_refresh"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/ll_conversation_tip"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rc_message_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:overScrollMode="never" />
    </io.rong.imkit.widget.refresh.SmartRefreshLayout>

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/rc_new_message_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="4dp"
        android:background="@drawable/rc_conversation_newmsg"
        android:gravity="center"
        android:paddingBottom="5dp"
        android:textColor="#ffffff"
        android:textSize="12dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/rc_refresh"
        app:layout_constraintEnd_toEndOf="parent" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/rc_unread_message_count"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/rc_unread_height"
        android:layout_marginStart="3dp"
        android:layout_marginTop="@dimen/rc_margin_size_30"
        android:layout_marginEnd="5dp"
        android:background="@drawable/rc_unread_msg_bg_style"
        android:drawablePadding="3dp"
        android:gravity="center"
        android:maxLines="1"
        android:minWidth="120dp"
        android:paddingStart="7dp"
        android:paddingEnd="7dp"
        android:textColor="@color/rc_text_main_color"
        android:textSize="14sp"
        android:visibility="gone"
        app:drawableStartCompat="@drawable/rc_unread_msg_arrow"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/rc_mention_message_count"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/rc_unread_height"
        android:layout_marginStart="3dp"
        android:layout_marginTop="@dimen/rc_margin_size_80"
        android:layout_marginEnd="5dp"
        android:background="@drawable/rc_unread_msg_bg_style"
        android:drawablePadding="5dp"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:minWidth="120dp"
        android:paddingStart="7dp"
        android:paddingEnd="7dp"
        android:text="@string/rc_mention_messages"
        android:textColor="@color/rc_text_main_color"
        android:textSize="14sp"
        android:visibility="gone"
        app:drawableStartCompat="@drawable/rc_unread_msg_arrow"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <io.rong.imkit.conversation.extension.MyRongExtension
        android:id="@+id/rc_extension"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:RCStyle="SCE"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <LinearLayout
        android:id="@+id/rc_notification_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#660F0F0F"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/ll_conversation_tip"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_66"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:background="@mipmap/bg_conversation_vip_tip"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_8"
        app:layout_constraintBottom_toTopOf="@id/rc_extension">

        <ImageView
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"
            android:src="@mipmap/ic_conversation_vip_tip" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_weight="1"
            android:text="@string/tip_conversation_vip" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="@dimen/dp_86"
            android:layout_height="@dimen/dp_32"
            android:background="@drawable/shape_conversation_tip_btn"
            android:gravity="center"
            android:text="@string/open_now"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
