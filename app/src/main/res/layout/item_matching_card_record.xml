<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/dp_16">

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_16"
        android:lines="1"
        android:singleLine="true"
        android:text="@string/registration_giveaway"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:text="2020-09-12  12:45"
        android:textColor="@color/color_666666"
        android:textSize="@dimen/sp_12"
        app:layout_constraintEnd_toStartOf="@id/tv_money"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_name" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_money"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="+8"
        android:textColor="@color/color_BBA7FF"
        android:textSize="@dimen/sp_14"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_5"
        android:layout_marginTop="@dimen/dp_12"
        android:background="@color/color_divider"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_time" />

</androidx.constraintlayout.widget.ConstraintLayout>