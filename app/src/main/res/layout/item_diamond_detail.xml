<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="@dimen/dp_5"
    android:background="@drawable/shape_card_background"
    android:paddingVertical="@dimen/dp_20">

    <View
        android:id="@+id/indicator"
        android:layout_width="@dimen/dp_4"
        android:layout_height="@dimen/dp_12"
        android:background="@color/color_9F2AF8"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/indicator"
        app:layout_constraintStart_toEndOf="@id/indicator"
        app:layout_constraintTop_toTopOf="@id/indicator"
        tools:text="Recharge" />

    <TextView
        android:id="@+id/tv_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="2000Rp" />

    <TextView
        android:id="@+id/tv_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        android:textColor="@color/white_40"
        android:textSize="@dimen/sp_12"
        app:layout_constraintStart_toStartOf="@id/tv_title"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        tools:text="2025-02-25" />

</androidx.constraintlayout.widget.ConstraintLayout>