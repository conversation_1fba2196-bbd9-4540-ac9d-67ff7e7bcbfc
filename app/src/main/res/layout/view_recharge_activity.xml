<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical">

    <com.opensource.svgaplayer.SVGAImageView
        android:layout_width="@dimen/dp_90"
        android:layout_height="@dimen/dp_90"
        app:autoPlay="true"
        app:source="ic_gift.svga" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_countdown"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@mipmap/bg_recharge_countdown"
        android:gravity="center"
        android:paddingBottom="@dimen/dp_3"
        android:paddingHorizontal="@dimen/dp_4"
        android:text="12:12:00"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_11"
        android:visibility="gone"
        tools:visibility="visible" />
</LinearLayout>