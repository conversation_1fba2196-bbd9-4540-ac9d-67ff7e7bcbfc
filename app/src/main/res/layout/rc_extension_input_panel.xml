<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_36"
    android:layout_marginHorizontal="@dimen/dp_16"
    android:layout_marginVertical="@dimen/dp_12">

    <ImageView
        android:id="@+id/input_panel_emoji_btn"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_20"
        android:layout_marginStart="@dimen/dp_16"
        android:src="@mipmap/ic_emoji"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/input_panel_voice_toggle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/rc_ext_toggle_keyboard_btn"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <io.rong.imkit.widget.RongEditText
        android:id="@+id/edit_btn"
        style="@style/EditTextStyle.Alignment"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_6"
        android:layout_marginEnd="@dimen/dp_6"
        android:background="@drawable/shape_message_input_edit"
        android:gravity="center_vertical"
        android:hint="@string/message_input_hint"
        android:maxLines="4"
        android:minHeight="@dimen/dp_36"
        android:paddingHorizontal="@dimen/dp_16"
        android:textColor="@color/white"
        android:textColorHint="#746988"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/input_panel_add_or_send"
        app:layout_constraintStart_toEndOf="@+id/input_panel_voice_toggle"
        app:layout_constraintTop_toTopOf="parent" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/press_to_speech_btn"
        style="@style/TextStyle.Alignment"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_36"
        android:layout_marginStart="@dimen/dp_6"
        android:background="@drawable/rc_ext_voice_idle_button"
        android:gravity="center"
        android:text="@string/rc_voice_press_to_input"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/input_panel_voice_toggle"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@id/input_panel_add_or_send"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/input_panel_add_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            android:src="@drawable/rc_ext_input_panel_add"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <FrameLayout
            android:id="@+id/input_panel_send_btn"
            android:layout_width="@dimen/dp_60"
            android:layout_height="@dimen/dp_36"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="@dimen/dp_60"
                android:layout_height="@dimen/dp_36"
                android:background="@drawable/shape_primary_button"
                android:gravity="center"
                android:text="@string/rc_send"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14" />

            <!--            <ImageView-->
            <!--                android:layout_width="@dimen/dp_20"-->
            <!--                android:layout_height="@dimen/dp_20"-->
            <!--                android:layout_gravity="center"-->
            <!--                android:src="@mipmap/ic_send" />-->
        </FrameLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
