<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.bdc.android.library.widget.XToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:x_action_text_background="@mipmap/ic_order_record"
        app:x_show_action="true"
        app:x_title="@string/membership_center" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.bdc.android.library.refreshlayout.XRecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp_10"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:spanCount="3"
                    tools:listitem="@layout/item_membership_subscribe" />

                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/progress_bar"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_centerInParent="true"
                    android:layout_gravity="center"
                    app:indicatorColor="@color/colorAccent"
                    app:indicatorInset="@dimen/dp_40"
                    app:indicatorSize="@dimen/dp_40"
                    app:trackColor="@color/background" />
            </RelativeLayout>

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_restore_purchase"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/dp_8"
                android:text="@string/restore_purchase"
                android:textColor="@color/color_666666"
                android:textSize="@dimen/sp_14" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:id="@+id/tv_privilege"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_15"
                android:background="@mipmap/bg_privilege_banner"
                android:fontFamily="@font/rammettoone"
                android:gravity="center"
                tools:text="@string/seven_exclusive_privilege"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14" />

            <com.bdc.android.library.refreshlayout.XRecyclerView
                android:id="@+id/recyclerViewRights"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_10"
                android:background="@drawable/shape_card_background"
                android:paddingHorizontal="@dimen/dp_16"
                android:paddingVertical="@dimen/dp_4"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="5"
                tools:listitem="@layout/item_membership_rights" />

            <LinearLayout
                android:id="@+id/ll_agree"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginBottom="@dimen/dp_20"
                android:gravity="center"
                android:orientation="vertical">

                <CheckBox
                    android:id="@+id/checkbox"
                    android:layout_width="@dimen/dp_16"
                    android:layout_height="@dimen/dp_16"
                    android:button="@drawable/selector_checkbox"
                    android:checked="false"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tv_agreement"
                    style="@style/textTitle2_12"
                    android:text="@string/recharge_agreement_hint" />

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_cancel_unsubscribe"
                    style="@style/textTitle2_12"
                    android:text="@string/unsubscribe"
                    android:textColor="@color/color_F53D3D"
                    android:visibility="gone" />
            </LinearLayout>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <com.heart.heartmerge.i18n.I18nButton
        android:id="@+id/btn_buy"
        style="@style/PrimaryButton"
        android:layout_width="match_parent"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_20"
        tools:text="@string/label_renew_now"
        app:layout_constraintBottom_toBottomOf="parent" />

    <TextView
        android:id="@+id/tv_expired_day"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="@dimen/dp_10"
        tools:text="@string/expired_on"
        android:textColor="@color/color_white"
        android:textSize="@dimen/sp_12"
        android:visibility="gone"
        tools:visibility="visible" />
</LinearLayout>