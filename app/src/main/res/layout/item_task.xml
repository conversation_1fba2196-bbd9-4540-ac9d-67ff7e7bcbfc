<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/dp_16"
    android:paddingVertical="@dimen/dp_10">

    <LinearLayout
        android:id="@+id/ll_diamond"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_task"
            android:layout_width="@dimen/dp_30"
            android:layout_height="@dimen/dp_30"
            android:src="@mipmap/ic_diamond_big" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_task_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="+100"
            android:textColor="@color/colorAccent"
            android:textSize="@dimen/sp_14" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="@dimen/dp_0"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp_13"
        android:layout_marginEnd="@dimen/dp_10"
        android:gravity="center_vertical"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_go"
        app:layout_constraintStart_toEndOf="@id/ll_diamond"
        app:layout_constraintTop_toTopOf="@id/ll_diamond">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_task_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Cumulative Recharge"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_14"
            app:layout_constraintStart_toEndOf="@id/iv_task"
            app:layout_constraintTop_toTopOf="@id/iv_task" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_task_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_2"
            android:text="Cumulative Recharge"
            android:textColor="@color/white_40"
            android:textSize="@dimen/sp_10"
            app:layout_constraintStart_toStartOf="@id/tv_task_name"
            app:layout_constraintTop_toBottomOf="@id/tv_task_name" />

        <ProgressBar
            android:id="@+id/progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_3"
            android:layout_marginTop="@dimen/dp_5"
            android:progress="50"
            android:progressDrawable="@drawable/progress_drawable_task"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_task_desc"
            app:layout_constraintStart_toStartOf="@id/tv_task_desc"
            app:layout_constraintTop_toBottomOf="@id/tv_task_desc"
            tools:visibility="visible" />

    </LinearLayout>

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_go"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_28"
        android:background="@drawable/selector_primary_button"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp_10"
        android:text="Recharge"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>