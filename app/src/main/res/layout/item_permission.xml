<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="@dimen/dp_8"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/dp_40">

    <ImageView
        android:id="@+id/iv_permission"
        android:layout_width="@dimen/dp_40"
        android:layout_height="@dimen/dp_40"
        android:src="@mipmap/ic_permission_camera" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_permission"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:text="Camera permissions"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16" />
</LinearLayout>