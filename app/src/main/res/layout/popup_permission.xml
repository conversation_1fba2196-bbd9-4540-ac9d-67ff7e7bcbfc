<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_gravity="end"
        android:background="@mipmap/ic_dialog_close_white" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_20"
        android:background="@drawable/bg_center_popup"
        android:minHeight="@dimen/dp_110">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:orientation="vertical">

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/dp_16"
                android:text="@string/permission_popup_title"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_20" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_40"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginBottom="@dimen/dp_16"
                android:text="@string/permission_request_tip"
                android:textColor="@color/color_9F9CA6"
                android:textSize="@dimen/sp_14" />

            <com.bdc.android.library.refreshlayout.XRecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:itemCount="3"
                tools:listitem="@layout/item_permission" />

            <com.heart.heartmerge.i18n.I18nButton
                android:id="@+id/btn_allow"
                style="@style/PrimaryButton"
                android:layout_marginHorizontal="@dimen/dp_50"
                android:layout_marginTop="@dimen/sp_20"
                android:text="@string/permission_allow" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/dp_12"
                android:text="@string/permission_later"
                android:layout_marginBottom="@dimen/sp_20"
                android:textColor="@color/color_9F9CA6"
                android:textSize="@dimen/sp_12" />
        </LinearLayout>
    </ScrollView>

</LinearLayout>