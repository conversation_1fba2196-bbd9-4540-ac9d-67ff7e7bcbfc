<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/dp_5"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/rl_container"
        android:layout_width="@dimen/dp_107"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@drawable/shape_subscribe_item_bronze_active_border"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingVertical="@dimen/dp_15">

        <TextView
            android:id="@+id/tv_diamond_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:drawablePadding="@dimen/dp_3"
            android:textColor="@color/white_40"
            android:textSize="@dimen/sp_18"
            android:textStyle="bold"
            android:visibility="gone"
            tools:text="1" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:textSize="@dimen/sp_18"
            tools:text="Week" />

        <TextView
            android:id="@+id/tv_origin_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_8"
            android:drawablePadding="@dimen/dp_3"
            android:textColor="@color/white_60"
            android:textSize="@dimen/sp_12"
            tools:text="+900" />

        <TextView
            android:id="@+id/tv_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginBottom="@dimen/dp_10"
            android:autoSizeMaxTextSize="@dimen/sp_15"
            android:autoSizeMinTextSize="@dimen/sp_12"
            android:autoSizeStepGranularity="@dimen/dp_2"
            android:autoSizeTextType="uniform"
            android:textSize="@dimen/sp_14"
            tools:text="￥39.99" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_label"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_24"
        android:layout_centerHorizontal="true"
        android:layout_margin="-10dp"
        android:background="@drawable/shape_subscribe_save_normal_label"
        android:drawableEnd="@mipmap/ic_diamond"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp_18"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12"
        tools:text="30% OFF" />
</RelativeLayout>