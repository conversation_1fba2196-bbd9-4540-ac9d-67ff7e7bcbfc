<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <com.bdc.android.library.widget.XToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        app:x_title="@string/cancel_account"
        android:layout_height="wrap_content" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_16"
        android:text="@string/cancel_account_tips"
        android:textColor="@color/color_C4C4C4" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:background="@color/color_divider" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_submit"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"
        android:gravity="center"
        android:text="@string/submit_cancellation_request"
        android:textAllCaps="false"
        android:textColor="@color/color_9EA3D8"
        android:textSize="@dimen/sp_14" />
</LinearLayout>