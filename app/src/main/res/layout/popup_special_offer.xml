<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <!-- 弹框主体 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_30"
        android:background="@mipmap/bg_popup_special_offer"
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_50"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/rammettoone"
            android:text="@string/special_offer"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_32"
            android:textStyle="italic"
            android:translationY="-10dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_130"
            android:layout_marginTop="@dimen/dp_75"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.heart.heartmerge.ui.widget.GradientTextView
                android:id="@+id/tv_diamond"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/rammettoone"
                android:text="700"
                android:textColor="#B346F1"
                android:textSize="@dimen/sp_28"
                android:textStyle="bold|italic"
                app:strokeColor="@android:color/white"
                app:strokeWidth="@dimen/dp_4" />

            <ImageView
                android:layout_width="@dimen/dp_42"
                android:layout_height="@dimen/dp_30"
                android:src="@mipmap/ic_special_offer_diamond"
                android:translationX="-15dp"
                android:translationY="@dimen/dp_15" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:alpha="0.5"
                android:text="350diamonds"
                android:textColor="@color/white" />
        </LinearLayout>

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_210"
            android:layout_marginTop="@dimen/dp_55"
            android:background="@mipmap/bg_special_offer_discount_label"
            android:fontFamily="@font/fugazone_regular"
            android:gravity="center_horizontal"
            android:paddingTop="@dimen/dp_3"
            android:text="50% OFF"
            android:textColor="#FEAB3D"
            android:textSize="@dimen/sp_14"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.heart.heartmerge.ui.widget.GradientTextView
            android:id="@+id/tv_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_120"
            android:layout_marginEnd="@dimen/dp_30"
            android:fontFamily="@font/rammettoone"
            android:text="USD $4.99 ONLY!"
            android:textSize="@dimen/sp_20"
            android:textStyle="bold|italic"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:strokeColor="@android:color/white"
            app:strokeWidth="@dimen/dp_4" />

        <!-- 签到列表 -->
        <com.bdc.android.library.refreshlayout.XRecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_40"
            android:layout_marginTop="@dimen/dp_170"
            android:minHeight="@dimen/dp_300"
            android:orientation="vertical"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:itemCount="3"
            tools:listitem="@layout/item_special_offer" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 关闭按钮 -->
    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_gravity="center_horizontal"
        android:src="@mipmap/ic_dialog_close_white" />

</LinearLayout>
