<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_bottom_popup"
    android:orientation="vertical"
    android:paddingTop="@dimen/dp_20">

    <View
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_5"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/shape_popup_indicator" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_16"
        android:text="@string/select_gift"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/dp_10"
        android:paddingEnd="@dimen/dp_16"
        android:visibility="gone">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            app:tabIndicatorHeight="0dp"
            app:tabMode="scrollable"
            app:tabRippleColor="@null" />

    </RelativeLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_250"
        android:layout_marginTop="@dimen/dp_25">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:paddingHorizontal="@dimen/dp_12" />

        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/progress_bar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            app:indicatorColor="@color/colorAccent"
            app:indicatorInset="@dimen/dp_40"
            app:indicatorSize="@dimen/dp_40"
            app:trackColor="@color/background" />
    </FrameLayout>

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_indicator"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_10"
        android:layout_gravity="center"
        android:layout_marginBottom="@dimen/dp_20"
        app:tabIndicatorHeight="0dp"
        app:tabMaxWidth="@dimen/dp_13"
        app:tabMinWidth="@dimen/dp_13"
        app:tabMode="scrollable"
        app:tabPaddingEnd="@dimen/dp_3"
        app:tabPaddingStart="@dimen/dp_3"
        app:tabRippleColor="@null" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:background="@color/color_divider" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_16"
        android:paddingHorizontal="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_30"
        android:visibility="visible">

        <ImageView
            android:id="@+id/iv_diamond"
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:src="@mipmap/ic_diamond" />

        <androidx.compose.ui.platform.ComposeView
            android:id="@+id/tv_diamonds"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/dp_4"
            android:layout_toEndOf="@id/iv_diamond" />

        <com.heart.heartmerge.i18n.I18nButton
            android:id="@+id/btn_recharge"
            style="@style/PrimaryButton"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_32"
            android:paddingHorizontal="@dimen/dp_8"
            android:layout_alignParentEnd="true"
            android:text="@string/recharge" />
    </RelativeLayout>

</LinearLayout>