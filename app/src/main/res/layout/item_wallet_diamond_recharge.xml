<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/dp_8"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:id="@+id/container_card_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_2"
        android:layout_marginTop="@dimen/dp_10"
        app:cardBackgroundColor="@color/color_card_background"
        app:cardCornerRadius="@dimen/dp_16">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/dp_10"
            android:paddingVertical="@dimen/dp_10">

            <ImageView
                android:id="@+id/iv_icon"
                android:layout_width="@dimen/dp_38"
                android:layout_height="@dimen/dp_38"
                android:background="@mipmap/ic_diamond_recharge_level3"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_8"
                android:gravity="center_vertical"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="@id/iv_icon"
                app:layout_constraintStart_toEndOf="@id/iv_icon"
                app:layout_constraintTop_toTopOf="@id/iv_icon">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp_15"
                        tools:text="VIP 30 Days" />

                    <TextView
                        android:id="@+id/tv_sub_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_10"
                        android:drawablePadding="@dimen/dp_2"
                        android:textColor="@color/color_D195FF"
                        android:textSize="@dimen/sp_12"
                        tools:text="10" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_give_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    app:layout_constraintStart_toStartOf="@id/tv_title"
                    app:layout_constraintTop_toBottomOf="@id/tv_title">

                    <TextView
                        android:id="@+id/tv_level"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/white_30"
                        android:textSize="@dimen/sp_11"
                        tools:text="LV10 Bonus: " />

                    <TextView
                        android:id="@+id/tv_give_diamond"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_5"
                        android:layout_marginEnd="@dimen/dp_5"
                        android:textColor="@color/color_D195FF"
                        android:textSize="@dimen/sp_12"
                        tools:text="30" />

                    <ImageView
                        android:layout_width="@dimen/dp_18"
                        android:layout_height="@dimen/dp_18"
                        android:background="@mipmap/ic_diamond_purple" />
                </LinearLayout>
            </LinearLayout>

            <TextView
                android:id="@+id/btn_recharge"
                style="@style/PrimaryButton"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_30"
                android:autoSizeMaxTextSize="@dimen/sp_15"
                android:autoSizeMinTextSize="@dimen/sp_12"
                android:autoSizeStepGranularity="@dimen/dp_2"
                android:autoSizeTextType="uniform"
                android:background="@drawable/shape_9f2af8_50"
                android:paddingHorizontal="@dimen/dp_6"
                android:textSize="@dimen/sp_14"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="$40000000000" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/tv_label"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_26"
        android:background="@mipmap/ic_recharge_label"
        android:elevation="100dp"
        android:paddingHorizontal="@dimen/dp_5"
        android:paddingTop="@dimen/dp_3"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_10"
        android:translationY="-5dp"
        tools:text="赠送3张匹配卡" />
</RelativeLayout>