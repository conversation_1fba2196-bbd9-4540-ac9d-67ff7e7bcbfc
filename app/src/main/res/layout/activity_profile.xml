<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.bdc.android.library.widget.XToolbar
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:x_title="@string/personal_data" />

    <LinearLayout
        android:id="@+id/ll_avatar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_54"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:background="@drawable/shape_card_background"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp_16">

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/avatar"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_14" />

        <ImageView
            android:id="@+id/iv_avatar"
            android:layout_width="@dimen/dp_32"
            android:layout_height="@dimen/dp_32"
            android:layout_marginEnd="@dimen/dp_10" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/ic_arrow_right_gray" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@drawable/shape_card_background"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingVertical="@dimen/dp_2">

        <LinearLayout
            android:id="@+id/ll_nickmame"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/dp_16"
            android:orientation="horizontal">

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/name"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_nickname"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@null"
                android:drawablePadding="@dimen/dp_10"
                android:singleLine="true"
                android:textCursorDrawable="@color/colorPrimary"
                android:maxLength="15"
                android:textColor="@color/white_40"
                android:textSize="@dimen/sp_14"
                app:drawableEndCompat="@mipmap/ic_arrow_right_gray"
                tools:text="xxxx" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_birthday"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/dp_16"
            android:orientation="horizontal">

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/date_of_birth"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_birthday"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="@dimen/dp_10"
                android:textColor="@color/white_40"
                android:textSize="@dimen/sp_14"
                app:drawableEndCompat="@mipmap/ic_arrow_right_gray"
                tools:text="xxxx" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/dp_16"
            android:orientation="horizontal">

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/country"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_country"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="@dimen/dp_10"
                android:textColor="@color/white_40"
                android:textSize="@dimen/sp_14"
                tools:text="xxxx" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/dp_16"
            android:orientation="horizontal">

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/gender"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_gender"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="@dimen/dp_10"
                android:textColor="@color/white_40"
                android:textSize="@dimen/sp_14"
                tools:text="xxxx" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@drawable/shape_card_background"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/dp_16"
        android:visibility="gone">

        <LinearLayout
            android:id="@+id/ll_google_account"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/dp_10"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/google_account"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14" />

            <ImageView
                android:id="@+id/iv_google_avatar"
                android:layout_width="@dimen/dp_32"
                android:layout_height="@dimen/dp_32"
                android:layout_marginEnd="@dimen/dp_10"
                android:background="@color/white" />
        </LinearLayout>

        <com.heart.heartmerge.i18n.I18nButton
            android:id="@+id/btn_unbound"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_36"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_marginBottom="@dimen/dp_18"
            android:background="@drawable/shape_9f2af8_50"
            android:text="@string/unbound"
            android:textAllCaps="false"
            android:textColor="@color/white_40" />
    </LinearLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <com.heart.heartmerge.i18n.I18nButton
        android:id="@+id/btn_save"
        style="@style/PrimaryButton"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_30"
        android:text="@string/save" />
</LinearLayout>
