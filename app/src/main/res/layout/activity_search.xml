<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="@dimen/dp_20"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/back"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_32"
        android:paddingStart="@dimen/dp_15"
        android:paddingEnd="@dimen/dp_15"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@mipmap/ic_back_white" />
    </FrameLayout>

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/btn_search"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_32"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp_16"
        android:text="@string/rc_search"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/topView" />
    <!-- 顶部视图 -->
    <EditText
        android:id="@+id/topView"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_32"
        android:background="@drawable/shape_1b1d39_50"
        android:drawablePadding="@dimen/dp_12"
        android:enabled="true"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:gravity="center_vertical"
        android:hint="@string/home_search_hint_text"
        android:paddingHorizontal="@dimen/dp_12"
        android:textColor="#BFBFBF"
        android:textColorHint="#5A5D7B"
        android:textSize="@dimen/sp_14"
        android:imeOptions="actionSearch"
        android:maxLines="1"
        android:singleLine="true"
        app:layout_constraintStart_toEndOf="@id/back"
        app:layout_constraintEnd_toStartOf="@id/btn_search"
        app:layout_constraintTop_toTopOf="parent" />

    <com.bdc.android.library.refreshlayout.XRecyclerView
        android:id="@+id/message_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_6"
        android:layout_marginBottom="@dimen/dp_10"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topView" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_history_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_16"
        android:text="@string/label_search_history"
        android:textColor="@color/color_9EA3D8"
        android:textSize="@dimen/sp_16"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topView" />

    <ImageView
        android:id="@+id/del_history"
        android:layout_width="@dimen/dp_12"
        android:layout_height="@dimen/dp_14"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_16"
        android:src="@mipmap/ic_search_del"
        app:layout_constraintBottom_toBottomOf="@id/tv_history_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topView"
        app:layout_constraintTop_toTopOf="@id/tv_history_title" />

    <com.bdc.android.library.flowlayout.FlowLayout
        android:id="@+id/flow_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="12dp"
        android:paddingEnd="8dp"
        android:paddingBottom="12dp"
        android:layout_marginTop="@dimen/dp_16"
        app:flow_horizontal_spacing="6dp"
        app:flow_vertical_spacing="6dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_history_title" />

</androidx.constraintlayout.widget.ConstraintLayout>
