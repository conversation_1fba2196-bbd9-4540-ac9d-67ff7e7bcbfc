<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <ImageView
        android:id="@+id/rc_block"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:src="@mipmap/ic_messge_block"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/rc_text"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@id/rc_text"
        android:layout_marginEnd="@dimen/dp_8"
        app:layout_constraintBottom_toBottomOf="@id/rc_text" />
    <TextView
        android:id="@+id/rc_text"
        style="@style/TextStyle.Alignment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="223dp"
        android:textColor="@color/white"
        android:textColorLink="@color/white"
        android:autoLink="none"
        android:textSize="@dimen/sp_14"
        android:breakStrategy="simple"
        app:layout_constraintBottom_toTopOf="@+id/rc_translated_text"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/rc_block"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/rc_translated_text"
        style="@style/TextStyle.Alignment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:maxWidth="223dp"
        android:textColor="@color/white"
        android:textColorLink="@color/white"
        android:autoLink="none"
        android:textSize="@dimen/sp_14"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/rc_pb_translating"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rc_text" />

    <ProgressBar
        android:id="@+id/rc_pb_translating"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginTop="12dp"
        android:minHeight="25dp"
        android:maxHeight="25dp"
        android:minWidth="25dp"
        android:maxWidth="25dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rc_translated_text" />


</androidx.constraintlayout.widget.ConstraintLayout>