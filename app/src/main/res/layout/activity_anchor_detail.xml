<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true" >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="@dimen/dp_90">

            <FrameLayout
                android:id="@+id/image_banner_fl"
                android:layout_width="match_parent"
                android:layout_height="450dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.zhpan.bannerview.BannerViewPager
                    android:id="@+id/image_banner"
                    android:layout_width="match_parent"
                    android:layout_height="450dp" />

                <!--                <ImageView-->
                <!--                    android:id="@+id/image_placeholder"-->
                <!--                    android:layout_width="match_parent"-->
                <!--                    android:layout_height="450dp"-->
                <!--                    android:scaleType="centerCrop"-->
                <!--                    android:src="@mipmap/ic_default_anchor_bg" />-->
            </FrameLayout>


            <View
                android:id="@+id/bottom"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_0"
                android:layout_marginTop="@dimen/dp__25"
                android:background="@drawable/shape_detail_info_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/image_banner_fl" />

            <FrameLayout
                android:id="@+id/fl_header"
                android:layout_width="@dimen/dp_60"
                android:layout_height="@dimen/dp_60"
                android:layout_marginStart="@dimen/dp_27"
                android:layout_marginTop="-30dp"
                android:background="@drawable/shape_detail_header_bg"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/bottom">

                <ImageView
                    android:id="@+id/iv_header"
                    android:layout_width="@dimen/dp_56"
                    android:layout_height="@dimen/dp_56"
                    android:layout_gravity="center"
                    android:src="@mipmap/ic_default_avatar" />
            </FrameLayout>

            <FrameLayout
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_14"
                android:background="@drawable/shape_detail_header_bg"
                app:layout_constraintBottom_toBottomOf="@id/fl_header"
                app:layout_constraintRight_toRightOf="@id/fl_header">

                <View
                    android:id="@+id/online_status"
                    android:layout_width="@dimen/dp_10"
                    android:layout_height="@dimen/dp_10"
                    android:layout_gravity="center"
                    android:background="@drawable/shape_online_dot" />
            </FrameLayout>

            <TextView
                android:id="@+id/tv_user_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_16"
                android:ellipsize="middle"
                android:maxWidth="@dimen/dp_200"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_18"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/fl_header"
                tools:text="d大白菜" />


            <TextView
                android:id="@+id/tv_age"
                android:layout_width="@dimen/dp_30"
                android:layout_height="wrap_content"
                android:layout_marginStart="-12dp"
                android:background="@drawable/shape_call_info_female_age_bg"
                android:gravity="end"
                android:paddingEnd="@dimen/dp_4"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_10"
                app:layout_constraintBottom_toBottomOf="@id/iv_sex"
                app:layout_constraintStart_toEndOf="@id/iv_sex"
                app:layout_constraintTop_toTopOf="@id/iv_sex"
                tools:text="25" />

            <ImageView
                android:id="@+id/iv_sex"
                android:layout_width="@dimen/dp_17"
                android:layout_height="@dimen/dp_17"
                android:layout_marginStart="@dimen/dp_14"
                android:src="@mipmap/ic_detail_female"
                app:layout_constraintBottom_toBottomOf="@id/tv_user_name"
                app:layout_constraintStart_toEndOf="@id/tv_user_name"
                app:layout_constraintTop_toTopOf="@id/tv_user_name" />

            <ImageView
                android:id="@+id/iv_location"
                android:layout_width="@dimen/dp_12"
                android:layout_height="@dimen/dp_14"
                android:layout_marginTop="@dimen/dp_8"
                android:src="@mipmap/ic_detail_location"
                app:layout_constraintStart_toStartOf="@id/tv_user_name"
                app:layout_constraintTop_toBottomOf="@id/tv_user_name" />

            <TextView
                android:id="@+id/tv_country"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_6"
                android:textColor="#9F9CA6"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="@id/iv_location"
                app:layout_constraintStart_toEndOf="@id/iv_location"
                app:layout_constraintTop_toTopOf="@id/iv_location"
                tools:text="ind" />

            <ImageView
                android:id="@+id/iv_fans"
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_12"
                android:layout_marginStart="@dimen/dp_32"
                android:layout_marginTop="@dimen/dp_8"
                android:src="@mipmap/ic_detail_fans"
                app:layout_constraintStart_toEndOf="@id/tv_country"
                app:layout_constraintTop_toBottomOf="@id/tv_user_name" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_fans"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_6"
                android:textColor="#9F9CA6"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="@id/iv_fans"
                app:layout_constraintStart_toEndOf="@id/iv_fans"
                app:layout_constraintTop_toTopOf="@id/iv_fans"
                tools:text="300 Follow" />

            <ImageView
                android:id="@+id/iv_anchor_id"
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_12"
                android:layout_marginStart="@dimen/dp_32"
                android:layout_marginTop="@dimen/dp_8"
                android:src="@mipmap/ic_anchor_id"
                app:layout_constraintStart_toEndOf="@id/tv_fans"
                app:layout_constraintTop_toBottomOf="@id/tv_user_name" />


            <TextView
                android:id="@+id/tv_anchor_id"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_6"
                android:textColor="#9F9CA6"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="@id/iv_anchor_id"
                app:layout_constraintStart_toEndOf="@id/iv_anchor_id"
                app:layout_constraintTop_toTopOf="@id/iv_anchor_id"
                tools:text="300" />
            <LinearLayout
                android:id="@+id/ll_top_follow"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_28"
                android:layout_marginTop="-14dp"
                android:layout_marginEnd="@dimen/dp_16"
                android:background="@drawable/shape_call_top_follow_btn_bg"
                android:gravity="center"
                android:paddingHorizontal="@dimen/dp_12"
                android:paddingVertical="@dimen/dp_8"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/bottom">

                <ImageView
                    android:id="@+id/iv_top_follow"
                    android:layout_width="@dimen/dp_7"
                    android:layout_height="@dimen/dp_7"
                    android:src="@mipmap/ic_call_top_follow" />

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_top_follow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_2"
                    android:includeFontPadding="false"
                    tools:text="@string/label_cancel_follow"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_10" />

            </LinearLayout>

            <ImageView
                android:id="@+id/iv_signature"
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_14"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_24"
                android:src="@mipmap/ic_signature"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_location" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_6"
                android:text="@string/label_personal_signature"
                android:textColor="#9F9CA6"
                android:textSize="@dimen/sp_12"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/iv_signature"
                app:layout_constraintStart_toEndOf="@id/iv_signature"
                app:layout_constraintTop_toTopOf="@id/iv_signature" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_signature"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_8"
                android:text="@string/label_personal_signature"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14"
                android:textStyle="bold"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="@id/iv_signature"
                app:layout_constraintTop_toBottomOf="@id/iv_signature" />

            <ImageView
                android:id="@+id/iv_label"
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_14"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_20"
                android:src="@mipmap/ic_detail_label"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_signature" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_6"
                android:text="@string/label_labels"
                android:textColor="#9F9CA6"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="@id/iv_label"
                app:layout_constraintStart_toEndOf="@id/iv_label"
                app:layout_constraintTop_toTopOf="@id/iv_label" />

            <com.heart.heartmerge.ui.widget.CustomLabelsView
                android:id="@+id/labels"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginEnd="@dimen/dp_16"
                android:saveEnabled="false"
                app:labelBackground="@drawable/shape_label_item"
                app:labelTextColor="@color/white"
                app:labelTextPaddingBottom="@dimen/dp_6"
                app:labelTextPaddingLeft="@dimen/dp_12"
                app:labelTextPaddingRight="@dimen/dp_12"
                app:labelTextPaddingTop="@dimen/dp_6"
                app:labelTextSize="@dimen/sp_11"
                app:layout_constraintStart_toStartOf="@id/iv_label"
                app:layout_constraintTop_toBottomOf="@id/iv_label"
                app:layout_scrollFlags="scroll"
                app:lineMargin="@dimen/dp_6"
                app:maxSelect="0"
                app:selectType="NONE"
                app:wordMargin="@dimen/dp_6" />

            <ImageView
                android:id="@+id/iv_photo"
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_14"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_24"
                android:src="@mipmap/ic_detail_photo"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/labels" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_photo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_6"
                android:text="@string/label_photo"
                android:textColor="#9F9CA6"
                android:textSize="@dimen/sp_12"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/iv_photo"
                app:layout_constraintStart_toEndOf="@id/iv_photo"
                app:layout_constraintTop_toTopOf="@id/iv_photo" />

            <com.bdc.android.library.refreshlayout.XRecyclerView
                android:id="@+id/rv_list_photo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp_16"
                app:layout_constraintTop_toBottomOf="@id/iv_photo" />

            <ImageView
                android:id="@+id/iv_video"
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_14"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_24"
                android:src="@mipmap/ic_detail_video"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/rv_list_photo" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_video"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_6"
                android:text="@string/label_video"
                android:textColor="#9F9CA6"
                android:textSize="@dimen/sp_12"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/iv_video"
                app:layout_constraintStart_toEndOf="@id/iv_video"
                app:layout_constraintTop_toTopOf="@id/iv_video" />

            <com.bdc.android.library.refreshlayout.XRecyclerView
                android:id="@+id/rv_list_video"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp_16"
                app:layout_constraintTop_toBottomOf="@id/iv_video" />

            <ImageView
                android:id="@+id/iv_gift"
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_14"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_24"
                android:src="@mipmap/ic_detail_gift"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/rv_list_video" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_gift"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_6"
                android:text="@string/user_gift"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_12"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/iv_gift"
                app:layout_constraintStart_toEndOf="@id/iv_gift"
                app:layout_constraintTop_toTopOf="@id/iv_gift" />

            <com.bdc.android.library.refreshlayout.XRecyclerView
                android:id="@+id/rv_list_gift"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp_10"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/iv_gift" />


            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_you_may_like"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_24"
                android:text="@string/label_you_may_like"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_16"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/rv_list_gift" />

            <com.bdc.android.library.refreshlayout.XRecyclerView
                android:id="@+id/rv_list_you_may_like"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp_16"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/tv_you_may_like" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

<!--    <View-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="@dimen/dp_0_5"-->
<!--        android:background="#10ffffff"-->
<!--        app:layout_constraintBottom_toTopOf="@id/ll_bottom" />-->

    <LinearLayout
        android:id="@+id/ll_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_7"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:gravity="bottom"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:id="@+id/ll_chat"
            android:layout_width="@dimen/dp_125"
            android:layout_height="@dimen/dp_44"
            android:layout_marginStart="@dimen/dp_13"
            android:background="@drawable/shape_detail_message"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/dp_16"
                android:layout_height="@dimen/dp_16"
                android:src="@mipmap/ic_user_chat" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_5"
                android:text="@string/user_chat"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_12" />

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_22"
            android:layout_weight="1"
            android:clipChildren="false"
            android:clipToPadding="false">

            <LinearLayout
                android:id="@+id/ll_phone"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_44"
                android:background="@drawable/shape_primary_button"
                android:gravity="center"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="@dimen/dp_16"
                    android:layout_height="@dimen/dp_16"
                    android:src="@mipmap/ic_user_phone" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_10"
                    android:orientation="vertical">

                    <com.heart.heartmerge.i18n.I18nTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/user_phone"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp_16" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="@dimen/dp_14"
                            android:layout_height="@dimen/dp_9"
                            android:src="@mipmap/ic_home_diamond" />

                        <com.heart.heartmerge.i18n.I18nTextView
                            android:id="@+id/tv_price"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/white"
                            android:textSize="@dimen/sp_12"
                            tools:text="0/min" />

                        <com.heart.heartmerge.i18n.I18nTextView
                            android:id="@+id/tv_price_org"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_6"
                            android:textColor="#60ffffff"
                            android:textSize="@dimen/sp_10"
                            tools:text="0/min" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:id="@+id/ll_vip_price_label"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_38"
                android:layout_gravity="center_horizontal"
                android:background="@mipmap/bg_vip_price_label"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:paddingHorizontal="@dimen/dp_18"
                android:paddingBottom="@dimen/dp_5"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="@id/ll_phone"
                app:layout_constraintEnd_toEndOf="@id/ll_phone"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toTopOf="@id/ll_phone"
                tools:visibility="visible">

                <com.heart.heartmerge.i18n.I18nTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/gold_vip"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_12" />

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_vip_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_5"
                    android:text="0/min"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_12"
                    app:drawableStartCompat="@mipmap/ic_diamond" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>

    <FrameLayout
        android:id="@+id/llBackIcon"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/dp_35"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/ivBackIcon"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:layout_gravity="center"
            android:src="@mipmap/ic_detail_back" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/llWhatsAppIcon"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:layout_gravity="center"
        app:layout_constraintBottom_toBottomOf="@id/llMoreIcon"
        app:layout_constraintRight_toLeftOf="@id/llMoreIcon"
        app:layout_constraintTop_toTopOf="@id/llMoreIcon">

        <!--        <ImageView-->
        <!--            android:id="@+id/iv_whatsapp"-->
        <!--            android:layout_width="@dimen/dp_24"-->
        <!--            android:layout_height="@dimen/dp_24"-->
        <!--            android:layout_gravity="center"-->
        <!--            android:visibility="gone"-->
        <!--            android:src="@mipmap/ic_whatsapp" />-->

        <com.opensource.svgaplayer.SVGAImageView
            android:id="@+id/iv_whatsapp"
            android:layout_width="@dimen/dp_38"
            android:layout_height="@dimen/dp_38"
            android:layout_gravity="center"
            tools:src="@mipmap/ic_whatsapp"
            app:autoPlay="true"
            app:loopCount="2"
            app:source="whatsapp.svga" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/llMoreIcon"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:layout_gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/llBackIcon">

        <ImageView
            android:id="@+id/iv_more"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:layout_gravity="center"
            android:src="@mipmap/ic_detail_more" />
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>