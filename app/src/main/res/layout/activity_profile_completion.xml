<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#101321"
    android:orientation="vertical">

    <com.bdc.android.library.widget.XToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:paddingBottom="@dimen/dp_30">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp_24">

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginHorizontal="@dimen/dp_25"
                android:layout_marginTop="@dimen/dp_50"
                android:gravity="center"
                android:text="@string/completion_profile_hint"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_20" />

            <RelativeLayout
                android:id="@+id/rl_avatar"
                android:layout_width="@dimen/dp_120"
                android:layout_height="@dimen/dp_120"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/dp_20">

                <ImageView
                    android:id="@+id/iv_avatar"
                    android:layout_width="match_parent"
                    android:src="@mipmap/ic_launcher"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_alignParentBottom="true"
                    android:src="@mipmap/ic_camera" />
            </RelativeLayout>

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_32"
                android:text="@string/label_title_nickname"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14" />

            <EditText
                style="@style/EditText"
                android:id="@+id/et_nickname"
                android:layout_marginTop="@dimen/sp_12"
                android:hint="@string/label_title_nickname_hint" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_16"
                android:text="@string/label_title_birthday"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_choose_birthday"
                style="@style/EditText"
                android:layout_marginTop="@dimen/sp_12"
                android:hint="@string/label_title_birthday_hint" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_16"
                android:text="@string/label_title_country"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_choose_country"
                style="@style/EditText"
                android:layout_marginTop="@dimen/sp_12"
                android:gravity="center_vertical"
                android:hint="@string/label_title_country_hint" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_16"
                android:text="@string/label_title_language"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_choose_language"
                style="@style/EditText"
                android:layout_marginTop="@dimen/sp_12"
                android:hint="@string/label_title_language_hint" />
        </LinearLayout>
    </ScrollView>

    <com.heart.heartmerge.i18n.I18nButton
        android:id="@+id/btn_commit"
        style="@style/PrimaryButton"
        android:layout_marginHorizontal="@dimen/dp_24"
        android:layout_marginBottom="@dimen/dp_24"
        android:text="@string/completed" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_skip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginBottom="@dimen/dp_32"
        android:text="@string/skip"
        android:textColor="#555878" />
</LinearLayout>