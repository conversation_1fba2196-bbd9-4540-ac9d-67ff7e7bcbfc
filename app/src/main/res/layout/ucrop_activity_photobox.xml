<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ucrop_photobox"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@mipmap/bg_global"
    android:fitsSystemWindows="true">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        android:minHeight="?attr/actionBarSize">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/toolbar_title"
            style="@style/TextAppearance.Widget.AppCompat.Toolbar.Title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/ucrop_label_edit_photo"
            android:textColor="@color/ucrop_color_toolbar_widget"
            android:textSize="18sp" />

    </androidx.appcompat.widget.Toolbar>

    <FrameLayout
        android:id="@+id/ucrop_frame"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/controls_wrapper"
        android:layout_below="@+id/toolbar"
        android:layout_marginBottom="-12dp">

        <ImageView
            android:id="@+id/image_view_logo"
            android:layout_width="@dimen/ucrop_default_crop_logo_size"
            android:layout_height="@dimen/ucrop_default_crop_logo_size"
            android:layout_gravity="center"
            app:srcCompat="@drawable/ucrop_vector_ic_crop"
            tools:background="@drawable/ucrop_vector_ic_crop"
            tools:ignore="ContentDescription,MissingPrefix" />

        <com.yalantis.ucrop.view.UCropView
            android:id="@+id/ucrop"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:alpha="0" />

    </FrameLayout>

    <FrameLayout
        android:id="@+id/controls_wrapper"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:visibility="gone" />

</RelativeLayout>
