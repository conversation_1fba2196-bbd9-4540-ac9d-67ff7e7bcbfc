<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.bdc.android.library.widget.XToolbar
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:x_title="@string/setting_about_us" />

    <ImageView
        android:layout_width="@dimen/dp_110"
        android:layout_height="@dimen/dp_110"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_50"
        android:background="@mipmap/ic_launcher" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_ver"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_10"
        tools:text="@string/current_ver"
        android:textColor="@color/color_D8D7DC"
        android:textSize="@dimen/sp_14" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginVertical="@dimen/dp_10"
        android:layout_weight="1">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_summary"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:text="@string/app_summary"
            android:layout_marginHorizontal="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_20"
            android:textColor="@color/white_50" />
    </ScrollView>

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="@dimen/dp_20"
        android:gravity="center"
        android:text="@string/copyright"
        android:textColor="@color/color_666666"
        android:textSize="@dimen/sp_12" />
</LinearLayout>