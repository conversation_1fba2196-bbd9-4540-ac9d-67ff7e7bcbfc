<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="@dimen/dp_8">

    <ImageView
        android:id="@+id/iv_rights"
        android:layout_width="@dimen/dp_42"
        android:layout_height="@dimen/dp_42"
        android:src="@mipmap/ic_membership_rights_check_bonus"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_10"
        android:text="消息特权"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14"
        app:layout_constraintStart_toEndOf="@id/iv_rights"
        app:layout_constraintTop_toTopOf="parent" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_2"
        android:gravity="start"
        android:maxWidth="@dimen/dp_180"
        android:text="会员可获得专属头像框"
        android:textColor="@color/color_999999"
        android:textSize="@dimen/sp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_title"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />
</androidx.constraintlayout.widget.ConstraintLayout>