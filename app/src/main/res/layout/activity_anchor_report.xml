<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <com.bdc.android.library.widget.XToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:x_title="@string/complaint" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/report_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:text="@string/tip_report_detail"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_2"
        android:text="*"
        android:textColor="@color/colorPrimary"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toBottomOf="@id/report_title"
        app:layout_constraintStart_toEndOf="@id/report_title"
        app:layout_constraintTop_toTopOf="@id/report_title" />


    <EditText
        android:id="@+id/et_reason"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_12"
        android:background="@drawable/shape_edittext_radius8"
        android:gravity="top"
        android:hint="@string/cancel_account_custom_reason_placeholder"
        android:minHeight="@dimen/dp_200"
        android:paddingHorizontal="@dimen/dp_12"
        android:paddingTop="@dimen/dp_16"
        android:textColor="@color/white"
        android:textColorHint="@color/color_5A5D7B"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/report_title" />


    <com.heart.heartmerge.i18n.I18nButton
        android:id="@+id/btn_submit"
        style="@style/PrimaryButton"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_24"
        android:gravity="center"
        android:text="@string/btn_commit"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>