<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/activity_play"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000">

    <com.shuyu.gsyvideoplayer.video.StandardGSYVideoPlayer
        android:id="@+id/video_player"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true" />


    <FrameLayout
        android:id="@+id/fl_report"
        android:layout_width="@dimen/dp_40"
        android:layout_height="@dimen/dp_40"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="@dimen/dp_12" >

        <ImageView
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:layout_gravity="center"
            android:src="@mipmap/ic_video_platform" />
    </FrameLayout>
</RelativeLayout>
