<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.bdc.android.library.refreshlayout.XRefreshLayout
        android:id="@+id/xRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="@dimen/dp_10"
        app:hasRecyclerView="true" />

    <ImageView
        android:id="@+id/back_top"
        android:layout_width="@dimen/dp_50"
        android:layout_height="@dimen/dp_50"
        android:layout_gravity="end|bottom"
        android:layout_marginEnd="@dimen/dp_16"
        android:layout_marginBottom="16dp"
        android:src="@mipmap/ic_back_top" />
</FrameLayout>
