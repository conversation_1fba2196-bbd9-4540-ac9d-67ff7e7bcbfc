<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.bdc.android.library.widget.XToolbar
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:x_title="@string/feedback_help" />

    <EditText
        android:id="@+id/et_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_20"
        android:background="@drawable/shape_edittext_radius8"
        android:hint="@string/feedback_content_empty"
        android:minHeight="@dimen/dp_120"
        android:padding="@dimen/dp_10"
        android:textColor="@color/white"
        android:textColorHint="@color/color_999999"
        android:textSize="@dimen/sp_14" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_16"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:spanCount="3"
        tools:itemCount="9"
        tools:listitem="@layout/item_feedback_file" />

    <com.heart.heartmerge.i18n.I18nButton
        android:id="@+id/btn_submit"
        style="@style/PrimaryButton"
        android:layout_marginHorizontal="@dimen/dp_30"
        android:layout_marginTop="@dimen/dp_30"
        android:text="@string/submit" />
</LinearLayout>