<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

   <com.bdc.android.library.widget.XToolbar
       android:layout_width="match_parent"
       android:layout_height="wrap_content"/>

    <com.google.android.material.tabs.TabLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:id="@+id/tabLayout"
        android:layout_marginHorizontal="20dp"
        app:tabIndicatorHeight="0dp"
        android:background="@drawable/bg_tablayout_shape"/>

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="wrap_content"
        android:text="全部"
        android:id="@+id/tv_filter"
        android:visibility="gone"
        tools:visibility="visible"
        android:drawableLeft="@mipmap/ic_raffle_record_filter"
        android:textColor="#bbbbbb"
        android:drawablePadding="5dp"
        android:layout_marginTop="20dp"
        android:layout_marginLeft="20dp"
        android:layout_height="wrap_content"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_marginTop="10dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        android:layout_width="match_parent"
        tools:listitem="@layout/item_raffle_winning_record"
        android:layout_height="wrap_content" />
</LinearLayout>