<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/placeholder_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@mipmap/bg_call_default"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.heart.heartmerge.ui.widget.MyStandardGSYVideoPlayer
        android:id="@+id/video_player"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/top_anchor_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_44"
        android:background="@drawable/shape_call_top_info_bg"
        android:paddingHorizontal="@dimen/dp_10"
        android:paddingVertical="@dimen/dp_4"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/anchor_header_top"
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"
            android:layout_marginBottom="@dimen/dp_19"
            android:src="@mipmap/ic_pic_default_oval"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/anchor_name_top"
            android:layout_width="@dimen/dp_80"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_10"
            android:ellipsize="middle"
            android:maxWidth="@dimen/dp_100"
            android:maxLines="1"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold"
            app:layout_constraintStart_toEndOf="@id/anchor_header_top"
            app:layout_constraintTop_toTopOf="@id/anchor_header_top"
            tools:text="lcha Yuwandire" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/anchor_age_top"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_10"
            android:background="@drawable/shape_call_info_man_age_bg"
            android:paddingHorizontal="@dimen/dp_8"
            android:paddingVertical="@dimen/dp_3"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12"
            app:layout_constraintStart_toEndOf="@id/anchor_header_top"
            app:layout_constraintTop_toBottomOf="@id/anchor_name_top"
            tools:text="26" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/anchor_country_top"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="@dimen/dp_60"
            android:ellipsize="end"
            android:maxLines="1"
            android:layout_marginStart="@dimen/dp_4"
            android:background="@drawable/shape_call_info_country_bg"
            android:paddingHorizontal="@dimen/dp_8"
            android:paddingVertical="@dimen/dp_3"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12"
            app:layout_constraintStart_toEndOf="@id/anchor_age_top"
            app:layout_constraintTop_toBottomOf="@id/anchor_name_top"
            tools:text="INdo" />

        <LinearLayout
            android:id="@+id/ll_top_follow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_16"
            android:background="@drawable/shape_call_top_follow_btn_bg"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_12"
            android:paddingVertical="@dimen/dp_8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/anchor_name_top"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_top_follow"
                android:layout_width="@dimen/dp_7"
                android:layout_height="@dimen/dp_7"
                android:src="@mipmap/ic_call_top_follow" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_top_follow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_2"
                android:text="@string/label_cancel_follow"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_10" />

        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/fl_close"
        android:layout_width="@dimen/dp_40"
        android:layout_height="@dimen/dp_40"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_8"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/top_anchor_info"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/top_anchor_info">

        <ImageView
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:layout_gravity="center"
            android:src="@mipmap/ic_video_close" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/fl_platform"
        android:layout_width="@dimen/dp_40"
        android:layout_height="@dimen/dp_40"
        android:layout_marginEnd="@dimen/dp_12"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/top_anchor_info"
        app:layout_constraintEnd_toStartOf="@id/fl_close"
        app:layout_constraintTop_toTopOf="@id/top_anchor_info">

        <ImageView
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:layout_gravity="center"
            android:src="@mipmap/ic_video_platform" />
    </FrameLayout>

<!--    <TextView-->
<!--        android:id="@+id/tv_free_tag"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_marginStart="@dimen/dp_16"-->
<!--        android:layout_marginTop="8dp"-->
<!--        android:background="@drawable/bg_video_free_tag"-->
<!--        android:gravity="center"-->
<!--        android:text="@string/label_free"-->
<!--        android:textColor="@color/color_F691FF"-->
<!--        android:visibility="gone"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@id/top_anchor_info" />-->

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/call_anchor_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_28"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/anchor_image"
            android:layout_width="match_parent"
            android:layout_height="560dp"
            android:adjustViewBounds="true"
            android:scaleType="centerCrop"
            android:src="@mipmap/ic_default_anchor_bg"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearanceOverlay="@style/CallInfoShape"
            app:strokeColor="@null" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_202"
            android:background="@drawable/shape_call_info_bottom_lock"
            app:layout_constraintBottom_toBottomOf="@id/anchor_image" />

        <ImageView
            android:id="@+id/anchor_header_center"
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_19"
            android:contentDescription="TODO"
            android:src="@mipmap/ic_pic_default_oval"
            app:layout_constraintBottom_toBottomOf="@id/anchor_image"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/anchor_name_center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_10"
            android:layout_marginTop="@dimen/dp__8"
            android:textColor="@color/white"
            android:maxWidth="@dimen/dp_100"
            android:maxLines="1"
            android:ellipsize="end"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold"
            app:layout_constraintStart_toEndOf="@id/anchor_header_center"
            app:layout_constraintTop_toTopOf="@id/anchor_header_center"
            tools:text="Aurora" />

        <TextView
            android:id="@+id/anchor_age"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_10"
            android:background="@drawable/shape_call_info_man_age_bg"
            android:paddingHorizontal="@dimen/dp_8"
            android:paddingVertical="@dimen/dp_3"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12"
            app:layout_constraintStart_toEndOf="@id/anchor_header_center"
            app:layout_constraintTop_toBottomOf="@id/anchor_name_center"
            tools:text="26" />

        <TextView
            android:id="@+id/anchor_country_center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_4"
            android:background="@drawable/shape_call_info_country_bg"
            android:paddingHorizontal="@dimen/dp_8"
            android:paddingVertical="@dimen/dp_3"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12"
            app:layout_constraintStart_toEndOf="@id/anchor_age"
            app:layout_constraintTop_toBottomOf="@id/anchor_name_center"
            tools:text="INdo" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_follow"
            android:layout_width="@dimen/dp_142"
            android:layout_height="@dimen/dp_78"
            android:background="@mipmap/bg_call_info_follow"
            app:layout_constraintBottom_toBottomOf="@id/anchor_image"
            app:layout_constraintEnd_toEndOf="@id/anchor_image">

            <ImageView
                android:id="@+id/followed_iv"
                android:layout_width="@dimen/dp_18"
                android:layout_height="@dimen/dp_18"
                android:layout_marginStart="@dimen/dp_50"
                android:layout_marginBottom="@dimen/dp_18"
                android:src="@mipmap/ic_follow"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_follow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_6"
                android:text="@string/off_attention"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14"
                app:layout_constraintStart_toEndOf="@id/followed_iv"
                app:layout_constraintTop_toTopOf="@id/followed_iv" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/ll_org_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginEnd="@dimen/dp_4"
            android:gravity="center_vertical"
            android:layout_marginBottom="30dp"
            app:layout_constraintBottom_toTopOf="@id/cl_follow">
            
            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@mipmap/ic_diamond"/>
            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_diamond_center"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_4"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_16"
                android:includeFontPadding="false"
                tools:text="20/min" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_vip_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginEnd="@dimen/dp_4"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp_10"
            android:paddingVertical="@dimen/dp_5"
            android:layout_marginBottom="10dp"
            android:background="@drawable/shape_call_vip_price_bg"
            app:layout_constraintBottom_toTopOf="@id/ll_org_price">
            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@mipmap/ic_label_gold_vip"/>
            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:layout_marginHorizontal="@dimen/dp_5"
                android:text="@string/gold_vip"/>
            <ImageView
                android:layout_width="@dimen/dp_16"
                android:layout_height="@dimen/dp_16"
                android:src="@mipmap/ic_diamond"/>
            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_diamond_vip_center"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_4"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14"
                android:includeFontPadding="false"
                tools:text="20/min" />
        </LinearLayout>

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_connecting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_20"
            android:text="@string/label_connecting"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_14"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/anchor_image" />

        <LinearLayout
            android:id="@+id/video_call_action"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_20"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_connecting">

            <ImageView
                android:id="@+id/cancel_call"
                android:layout_width="@dimen/video_big_btn_size"
                android:layout_height="@dimen/video_big_btn_size"
                android:src="@mipmap/ic_cancel_call" />

            <FrameLayout
                android:id="@+id/anchor_listen"
                android:layout_width="@dimen/dp_103"
                android:layout_height="@dimen/video_big_btn_size"
                android:layout_marginStart="@dimen/dp_103">

                <com.opensource.svgaplayer.SVGAImageView
                    android:layout_width="@dimen/video_big_btn_size"
                    android:layout_height="@dimen/video_big_btn_size"
                    app:autoPlay="true"
                    app:source="ic_video.svga" />

                <ImageView
                    android:id="@+id/iv_free_tag"
                    android:layout_width="@dimen/dp_52"
                    android:layout_height="@dimen/dp_30"
                    android:layout_gravity="end"
                    android:visibility="gone"
                    android:src="@mipmap/ic_video_free" />

            </FrameLayout>

            <!--            <com.opensource.svgaplayer.SVGAImageView-->
            <!--                android:id="@+id/anchor_listen"-->
            <!--                android:layout_width="0dp"-->
            <!--                android:layout_height="@dimen/dp_80"-->
            <!--                android:layout_weight="1"-->
            <!--                app:autoPlay="true"-->
            <!--                app:source="ic_video.svga" />-->
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/video_calling_action"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_34"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp_12"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent">

        <ImageView
            android:id="@+id/iv_send_message"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_36"
            android:layout_weight="1"
            android:src="@mipmap/ic_call_send_msg" />

        <ImageView
            android:id="@+id/calling_switch_camera"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_36"
            android:layout_weight="1"
            android:src="@mipmap/ic_switch_camera" />

        <ImageView
            android:id="@+id/calling_switch_audio"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_36"
            android:layout_weight="1"
            android:src="@mipmap/ic_video_audio_open" />

        <ImageView
            android:id="@+id/calling_switch_video"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_36"
            android:layout_weight="1"
            android:src="@mipmap/ic_video_view_open" />

    </LinearLayout>

    <com.opensource.svgaplayer.SVGAImageView
        android:id="@+id/gift_send"
        android:layout_width="@dimen/dp_58"
        android:layout_height="@dimen/dp_60"
        android:layout_marginEnd="@dimen/dp_19"
        android:layout_marginBottom="@dimen/dp_189"
        android:visibility="gone"
        app:autoPlay="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:source="ic_video_send_gift.svga" />

    <com.bdc.android.library.refreshlayout.XRecyclerView
        android:id="@+id/message_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_80"
        android:layout_marginBottom="@dimen/dp_10"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/recharge_fl"
        app:layout_constraintHeight_max="@dimen/dp_300"
        app:layout_constraintStart_toStartOf="parent" />

    <FrameLayout
        android:id="@+id/recharge_fl"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_44"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_25"
        android:background="@drawable/shape_video_recharge_bg"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toTopOf="@id/video_calling_action">

        <com.heart.heartmerge.ui.widget.CustomProgressView
            android:id="@+id/customProgressView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44"
            android:paddingHorizontal="@dimen/dp_14">

            <ImageView
                android:id="@+id/lock"
                android:layout_width="@dimen/dp_28"
                android:layout_height="@dimen/dp_28"
                android:src="@mipmap/ic_call_recharge_lock"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_8"
                android:gravity="center_vertical"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/lock"
                app:layout_constraintTop_toTopOf="parent">

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/label_insufficient_diamonds"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/lable_unlock_call"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_12"
                    android:textStyle="bold"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/recharge_countTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_2"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_12"
                    app:layout_constraintTop_toBottomOf="@id/label_insufficient_diamonds" />

            </LinearLayout>

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/btn_recharge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_video_recharge_btn"
                android:paddingHorizontal="@dimen/dp_6"
                android:paddingVertical="@dimen/dp_5"
                android:text="@string/unlock"
                android:textColor="@color/color_F53D3D"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </FrameLayout>

    <FrameLayout
        android:id="@+id/remote_video_view_container_fl"
        android:layout_width="@dimen/dp_120"
        android:layout_height="@dimen/dp_160"
        android:layout_marginTop="@dimen/dp_146"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@color/transparent"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <FrameLayout
            android:id="@+id/remote_video_view_container"
            android:layout_width="@dimen/dp_120"
            android:layout_height="@dimen/dp_160"
            android:background="@color/transparent"
            tools:ignore="MissingConstraints" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/video_time"
            android:layout_width="@dimen/dp_120"
            android:layout_height="@dimen/dp_26"
            android:layout_gravity="bottom"
            android:background="#40000000"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12" />

        <View
            android:id="@+id/view_hide_remote"
            android:layout_width="@dimen/dp_120"
            android:layout_height="@dimen/dp_160"
            android:background="@color/black"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/iv_remote_close_video_header"
            android:layout_width="@dimen/dp_50"
            android:layout_height="@dimen/dp_50"
            android:layout_gravity="center"
            android:visibility="gone" />

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
