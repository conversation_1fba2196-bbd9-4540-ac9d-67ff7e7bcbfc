<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_108"
    android:background="@drawable/bg_user_sign_cover">

    <TextView
        android:id="@+id/tv_diamond"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:fontFamily="@font/urbanist_semibold"
        android:gravity="center_vertical"
        tools:text="DAY0"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_item"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_36"
        android:layout_marginTop="@dimen/dp_5"
        android:src="@mipmap/ic_diamond_big"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_diamond" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/baloochettan2"
        tools:text="+0"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        android:translationY="-6dp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_item" />

    <ImageView
        android:id="@+id/iv_sign_lock"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_12"
        android:layout_margin="@dimen/dp_4"
        android:src="@mipmap/ic_sign_lock"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />

    <ImageView
        android:id="@+id/iv_cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/bg_user_sign_cover"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />

    <ImageView
        android:id="@+id/iv_sign_status"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        app:tint="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@mipmap/ic_sign_status_right"
        tools:visibility="visible" />


    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_expired"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_1b1d39_8"
        android:paddingHorizontal="@dimen/dp_5"
        android:paddingVertical="@dimen/dp_2"
        android:rotation="18"
        android:text="@string/expired"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>