<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/dp_3">

    <ImageView
        android:id="@+id/iv_bg"
        android:layout_width="@dimen/dp_95"
        android:layout_height="@dimen/dp_95"
        android:background="@mipmap/bg_raffle_item"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_lottery"
        android:layout_width="@dimen/dp_95"
        android:layout_height="@dimen/dp_95"
        android:layout_marginTop="@dimen/dp_10"
        android:src="@mipmap/ic_launcher"
        app:layout_constraintLeft_toLeftOf="@+id/iv_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_bg"
        app:layout_constraintTop_toTopOf="@+id/iv_bg" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_lottery_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_10"
        android:includeFontPadding="false"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_13"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/iv_bg"
        app:layout_constraintLeft_toLeftOf="@+id/iv_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_bg"
        tools:text="奖品" />

    <View
        android:id="@+id/view_shadow"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@mipmap/ic_raffle_shadow"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/iv_bg"
        app:layout_constraintLeft_toLeftOf="@+id/iv_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_bg"
        app:layout_constraintTop_toTopOf="@+id/iv_bg" />
</androidx.constraintlayout.widget.ConstraintLayout>