<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_dialog_top_radius_shape"
    android:backgroundTint="#fee8bc"
    android:orientation="vertical"
    android:paddingBottom="20dp">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="20dp">

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:text="选择"
            android:textColor="#905611"
            android:textSize="20dp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_alignParentRight="true"
            android:layout_marginRight="10dp"
            android:background="@mipmap/ic_dialog_close_white"
            app:tint="@color/black" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="15dp"
        android:layout_marginTop="30dp"
        android:orientation="horizontal">

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="当前可用钻石:"
            android:textColor="@color/black" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:drawableLeft="@mipmap/ic_diamond"
            android:text="100"
            android:textStyle="bold" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_recharge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:drawableLeft="@color/black"
            android:text="去充值>"
            android:textColor="#de3953" />
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="15dp"
        android:layout_marginTop="20dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:itemCount="5"
        tools:listitem="@layout/item_raffle_times_purchase" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_buy"
        android:layout_width="200dp"
        android:layout_height="40dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="20dp"
        android:background="@drawable/bg_button_shape_ff6e58_f9453c"
        android:gravity="center"
        android:text="购买"
        android:textColor="@color/white" />

</LinearLayout>