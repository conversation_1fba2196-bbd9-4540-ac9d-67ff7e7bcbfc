<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/ll_center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginTop="@dimen/dp_35"
        android:background="@drawable/shape_white_center_popup"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/dp_28"
        android:paddingTop="@dimen/dp_35"
        android:paddingBottom="@dimen/dp_24"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_6"
            android:fontFamily="@font/baloochettan2"
            android:gravity="center"
            android:text="@string/payment_status_success_title"
            android:textColor="@color/color_35B244"
            android:textSize="@dimen/sp_24"
            android:textStyle="bold"
            tools:text="Title" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_20"
            android:gravity="center"
            android:text="@string/payment_status_success_title_content"
            android:textColor="#100821"
            android:textSize="@dimen/sp_14"
            tools:visibility="visible" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/btn_sure"
            style="@style/PrimaryButton"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44"
            android:layout_marginTop="@dimen/dp_20"
            android:backgroundTint="@color/color_35B244"
            android:gravity="center"
            android:maxLines="1"
            android:text="@string/confirm"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/btn_cancel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:gravity="center"
            android:maxLines="1"
            android:text="@string/cancel"
            android:textColor="#999999"
            android:textSize="@dimen/sp_12"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>

    <com.opensource.svgaplayer.SVGAImageView
        android:layout_width="@dimen/dp_70"
        android:layout_height="@dimen/dp_70"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="-35dp"
        app:autoPlay="true"
        app:loopCount="1"
        app:layout_constraintEnd_toEndOf="@id/ll_center"
        app:layout_constraintStart_toStartOf="@id/ll_center"
        app:layout_constraintTop_toTopOf="@+id/ll_center"
        app:source="payment_success.svga" />
</androidx.constraintlayout.widget.ConstraintLayout>