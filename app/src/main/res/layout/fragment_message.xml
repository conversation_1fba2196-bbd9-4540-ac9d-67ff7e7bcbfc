<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.heart.heartmerge.ui.widget.SlidingTabLayoutViewPager2
        android:id="@+id/tab"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_40"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginTop="@dimen/dp_32"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tl_indicator_color="@color/color_FB2CA4"
        app:tl_indicator_corner_radius="@dimen/dp_24"
        app:tl_indicator_end_color="@color/color_8531FF"
        app:tl_indicator_height="@dimen/dp_10"
        app:tl_indicator_margin_bottom="@dimen/dp_10"
        app:tl_indicator_start_color="@color/color_EC12E2"
        app:tl_indicator_style="NORMAL"
        app:tl_indicator_width_equal_title="true"
        app:tl_tab_padding="@dimen/dp_12"
        app:tl_textBold="SELECT"
        app:tl_textSelectColor="@color/white"
        app:tl_textUnselectColor="@color/white"
        app:tl_textsize="@dimen/sp_18" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tab" />

</androidx.constraintlayout.widget.ConstraintLayout>