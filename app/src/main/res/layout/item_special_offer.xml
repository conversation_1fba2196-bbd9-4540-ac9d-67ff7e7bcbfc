<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_90"
    android:layout_marginBottom="@dimen/dp_10">

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_special_offer_item_inactive"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/dp_12"
        android:paddingVertical="@dimen/dp_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/urbanist_semibold"
                android:text="Day1"
                android:textColor="@color/color_three_day_checkin_text"
                android:textSize="@dimen/sp_18"
                android:textStyle="bold" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_claim_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/sp_10"
                android:alpha="0.4"
                android:text="(Claim Time:2025.05.16)"
                android:textColor="@color/color_three_day_checkin_text"
                android:textSize="@dimen/sp_12" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginVertical="@dimen/dp_8"
            android:alpha="0.3"
            android:background="@color/color_606270" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"
                android:src="@mipmap/ic_diamond_purple" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_diamonds"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_4"
                android:layout_weight="1"
                android:fontFamily="@font/urbanist_semibold"
                android:text="200"
                android:textColor="@color/color_three_day_checkin_diamond"
                android:textSize="@dimen/sp_14" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/btn_claim"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_28"
                android:layout_gravity="center_vertical"
                android:background="@drawable/bg_special_offer_claim_button"
                android:fontFamily="@font/urbanist_semibold"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/dp_10"
                android:text="@string/three_day_checkin_claim"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_12" />
        </LinearLayout>
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_claimed"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:src="@mipmap/ic_claim_special_offer"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>