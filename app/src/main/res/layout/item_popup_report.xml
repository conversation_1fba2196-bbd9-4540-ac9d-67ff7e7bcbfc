<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_subscribe_item_normal_border"
    android:orientation="horizontal"
    android:id="@+id/container"
    android:layout_marginBottom="@dimen/dp_16"
    android:padding="@dimen/dp_16">

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textColor="@color/color_6F7490"
        android:textSize="@dimen/sp_13"
        tools:text="xxx" />

    <CheckBox
        android:id="@+id/checkbox"
        android:layout_width="@dimen/dp_14"
        android:layout_height="@dimen/dp_14"
        android:layout_marginEnd="@dimen/dp_6"
        android:background="@drawable/selector_checkbox"
        android:button="@null" />
</LinearLayout>