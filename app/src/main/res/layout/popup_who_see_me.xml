<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/blurredBackground"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop" />

    <LinearLayout
        android:id="@+id/ll_center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginHorizontal="@dimen/dp_28"
        android:layout_marginTop="@dimen/dp_35"
        android:background="@drawable/shape_white_center_popup"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/dp_28"
        android:paddingTop="@dimen/dp_35"
        android:paddingBottom="@dimen/dp_24"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_6"
            android:layout_marginBottom="@dimen/dp_20"
            android:gravity="center"
            android:fontFamily="@font/baloochettan2"
            android:text="@string/dailog_tip_who_see_me"
            android:textColor="@color/color_FFCC00"
            android:textSize="@dimen/sp_24"
            android:textStyle="bold" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_20"
            android:layout_marginBottom="@dimen/dp_20"
            android:gravity="center"
            android:text="@string/content_who_see_me"
            android:textColor="#100821"
            android:textSize="@dimen/sp_14" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/btn_sure"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44"
            android:background="@drawable/bg_dialog_btn_normal_who_see_me"
            android:gravity="center"
            android:maxLines="1"
            android:text="@string/already_know"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold" />
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_emoji"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_70"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="-35dp"
        android:src="@mipmap/dialog_icon_vip"
        app:layout_constraintEnd_toEndOf="@id/ll_center"
        app:layout_constraintStart_toStartOf="@id/ll_center"
        app:layout_constraintTop_toTopOf="@+id/ll_center" />

</androidx.constraintlayout.widget.ConstraintLayout>