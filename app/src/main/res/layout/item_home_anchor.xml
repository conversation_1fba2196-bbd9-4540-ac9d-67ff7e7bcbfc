<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_234"
    android:layout_margin="@dimen/dp_4"
    android:orientation="vertical">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/anchor_image"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_234"
        android:adjustViewBounds="true"
        android:scaleType="centerCrop"
        app:shapeAppearanceOverlay="@style/CustomShapeAppearance"
        app:strokeColor="@null" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_5"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_top"
            android:layout_width="@dimen/dp_41"
            android:layout_height="@dimen/dp_14"
            android:layout_marginEnd="1dp"
            android:src="@mipmap/ic_home_anchor_top_label" />

        <ImageView
            android:id="@+id/iv_star"
            android:layout_width="@dimen/dp_41"
            android:layout_height="@dimen/dp_14"
            android:layout_marginEnd="1dp"
            android:src="@mipmap/ic_home_anchor_star_label" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_100"
        android:layout_gravity="bottom"
        android:background="@drawable/shape_home_item_bottom_shadow" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!--        <ImageView-->
        <!--            android:id="@+id/iv_online_status"-->
        <!--            android:layout_width="@dimen/dp_18"-->
        <!--            android:layout_height="@dimen/dp_18"-->
        <!--            app:layout_constraintTop_toTopOf="parent"-->
        <!--            app:layout_constraintEnd_toEndOf="parent"-->
        <!--            android:src="@mipmap/ic_online_dot" />-->

        <TextView
            android:id="@+id/iv_online_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_6"
            android:layout_marginEnd="@dimen/dp_10"
            android:background="@drawable/shape_black_60_22"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp_5"
            android:paddingVertical="@dimen/dp_1"
            android:textSize="@dimen/sp_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <FrameLayout
            android:id="@+id/anchor_video_or_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/dp_6"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <ImageView
                android:id="@+id/anchor_message"
                android:layout_width="@dimen/dp_40"
                android:layout_height="@dimen/dp_40"
                android:src="@mipmap/ic_anchor_message" />

            <com.opensource.svgaplayer.SVGAImageView
                android:id="@+id/anchor_video"
                android:layout_width="@dimen/dp_40"
                android:layout_height="@dimen/dp_40"
                app:autoPlay="true"
                app:source="ic_video_small.svga" />
        </FrameLayout>

        <TextView
            android:id="@+id/anchor_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_6"
            android:layout_marginBottom="@dimen/dp_3"
            android:ellipsize="end"
            android:maxWidth="@dimen/dp_80"
            android:maxLength="8"
            android:maxLines="1"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_14"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@id/ll_videoPrice"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="主播名主播名主播名主播名主播名主播名" />

        <ImageView
            android:id="@+id/iv_country"
            android:layout_width="@dimen/dp_18"
            android:layout_height="@dimen/dp_14"
            android:layout_marginStart="@dimen/dp_4"
            app:layout_constraintBottom_toBottomOf="@id/anchor_name"
            app:layout_constraintStart_toEndOf="@id/anchor_name"
            app:layout_constraintTop_toTopOf="@id/anchor_name"
            tools:background="@mipmap/country_ac" />

        <TextView
            android:id="@+id/tv_age"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/sp_10"
            android:background="@drawable/shape_home_anchor_age"
            android:paddingHorizontal="@dimen/dp_8"
            android:paddingVertical="@dimen/dp_1"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_10"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="26" />

        <LinearLayout
            android:id="@+id/ll_videoPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_6"
            android:layout_marginBottom="@dimen/dp_10"
            android:gravity="center_vertical"
            android:paddingVertical="@dimen/dp_1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <ImageView
                android:layout_width="@dimen/dp_13"
                android:layout_height="@dimen/dp_13"
                android:background="@mipmap/ic_diamond" />

            <TextView
                android:id="@+id/videoPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_2"
                android:gravity="center_vertical"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_10"
                tools:text="50/min" />

            <TextView
                android:id="@+id/tv_origin_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_5"
                android:alpha="0.6"
                android:gravity="center_vertical"
                tools:text="30/min"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_10"
                app:drawableStartCompat="@mipmap/ic_diamond" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <RelativeLayout
        android:id="@+id/placeholder_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_placeholder"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_234"
            android:scaleType="centerCrop"
            app:shapeAppearanceOverlay="@style/CustomShapeAppearance"
            app:strokeColor="@null" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:src="@mipmap/ic_file_lock"
            android:visibility="gone"
            app:tint="@color/color_FE4918" />
    </RelativeLayout>
</FrameLayout>