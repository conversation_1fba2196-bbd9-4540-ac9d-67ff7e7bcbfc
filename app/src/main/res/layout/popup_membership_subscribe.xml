<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="650dp"
    android:background="@drawable/bg_bottom_popup"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingTop="@dimen/dp_20">

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="@dimen/dp_60"
            android:layout_height="5dp"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/shape_popup_indicator" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_16"
            android:text="@string/subscribe_vip_to_continue"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_20">

            <com.bdc.android.library.refreshlayout.XRecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_10"
                android:orientation="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/item_membership_subscribe" />

            <com.google.android.material.progressindicator.CircularProgressIndicator
                android:id="@+id/progress_bar_primary"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                app:indicatorColor="@color/colorAccent"
                app:indicatorInset="@dimen/dp_40"
                app:indicatorSize="@dimen/dp_40"
                app:trackColor="@color/background" />
        </RelativeLayout>

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_24"
            android:background="@mipmap/bg_privilege_banner"
            android:fontFamily="@font/rammettoone"
            android:gravity="center"
            android:text="@string/seven_exclusive_privilege"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_14" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10">

            <com.bdc.android.library.refreshlayout.XRecyclerView
                android:id="@+id/recyclerViewRights"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_300"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:background="@drawable/shape_card_background"
                android:paddingHorizontal="@dimen/dp_16"
                android:paddingVertical="@dimen/dp_4"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="5"
                tools:listitem="@layout/item_membership_rights" />

            <com.google.android.material.progressindicator.CircularProgressIndicator
                android:id="@+id/progress_bar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                app:indicatorColor="@color/colorAccent"
                app:indicatorInset="@dimen/dp_40"
                app:indicatorSize="@dimen/dp_40"
                app:trackColor="@color/background" />
        </RelativeLayout>
    </LinearLayout>

    <com.heart.heartmerge.i18n.I18nButton
        android:id="@+id/btn_confirm"
        style="@style/PrimaryButton"
        android:layout_alignParentBottom="true"
        android:layout_marginHorizontal="@dimen/sp_16"
        android:layout_marginBottom="@dimen/dp_20"
        android:text="@string/btn_subscribe"
        android:textAllCaps="false"
        android:visibility="visible" />
</RelativeLayout>