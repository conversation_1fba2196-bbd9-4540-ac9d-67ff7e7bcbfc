<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/dp_16"
    android:paddingTop="@dimen/dp_12">

    <ImageView
        android:id="@+id/iv_image"
        android:layout_width="@dimen/dp_65"
        android:layout_height="@dimen/dp_65"
        android:background="@drawable/shape_1b1d39_8"
        android:padding="@dimen/dp_6"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        app:layout_constraintStart_toEndOf="@id/iv_image"
        app:layout_constraintTop_toTopOf="@id/iv_image"
        tools:text="对话框" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_date_expiry"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:text="@string/date_expiry"
        android:textColor="@color/color_FFF8F8"
        android:textSize="@dimen/sp_12"
        app:layout_constraintStart_toStartOf="@id/tv_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_name" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_date_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:text="@string/duration_valid_life"
        android:textColor="@color/color_FDA829"
        android:textSize="@dimen/sp_12"
        app:layout_constraintStart_toEndOf="@id/tv_date_expiry"
        app:layout_constraintTop_toBottomOf="@+id/tv_name" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:text="@string/draw_time"
        android:textColor="@color/color_535663"
        android:textSize="@dimen/sp_12"
        app:layout_constraintStart_toStartOf="@id/tv_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_date_expiry" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_type"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_32"
        android:background="@drawable/shape_gradient_f84139_ff765c_50radius"
        android:gravity="center"
        android:text="@string/be_service_go"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14"
        app:layout_constraintBottom_toBottomOf="@+id/iv_image"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_image" />

    <ImageView
        android:id="@+id/tv_user_service"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:src="@mipmap/ic_gift_status_used"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/iv_image"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_image"
        tools:visibility="visible" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_5"
        android:layout_marginTop="@dimen/dp_12"
        android:background="@color/color_divider"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_time" />

</androidx.constraintlayout.widget.ConstraintLayout>