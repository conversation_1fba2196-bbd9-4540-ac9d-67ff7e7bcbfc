<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.bdc.android.library.widget.XToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:x_action_title="@string/matching_record"
        app:x_show_action="true"
        app:x_title="@string/matching_card" />

    <RelativeLayout
        android:id="@+id/rl_image"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_12"
        android:background="@drawable/shape_1d2031_20"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <ImageView
            android:id="@+id/iv_matching_card"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_180"
            android:layout_marginVertical="@dimen/dp_16"
            android:src="@mipmap/bg_matching_card"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/iv_matching_card_no"
            android:layout_width="@dimen/dp_70"
            android:layout_height="@dimen/dp_70"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp_16"
            android:src="@mipmap/ic_no_data" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:id="@+id/tv_matching_card_no"
            android:layout_height="wrap_content"
            android:layout_below="@id/iv_matching_card_no"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_marginBottom="@dimen/dp_16"
            android:text="@string/matching_card_no"
            android:textColor="@color/color_535663"
            android:textSize="@dimen/sp_14" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:id="@+id/tv_label_match_card"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@id/iv_matching_card"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/dp_44"
            android:text="@string/match_card_with_dot"
            android:textColor="#FC5AAD"
            android:visibility="gone"
            android:textStyle="bold"
            android:textSize="@dimen/sp_24" />

    </RelativeLayout>

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_match"
        style="@style/PrimaryButton"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_28"
        android:layout_margin="@dimen/dp_16"
        android:text="@string/matching_go"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rl_image" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_residue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:text="@string/residue"
        android:textColor="@color/color_6D7096"
        android:textSize="@dimen/sp_14"
        app:layout_constraintBottom_toBottomOf="@id/tv_match"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_match" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_residue_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_DE3953"
        android:textSize="@dimen/sp_14"
        app:layout_constraintBottom_toBottomOf="@id/tv_match"
        app:layout_constraintStart_toEndOf="@id/tv_residue"
        app:layout_constraintTop_toTopOf="@id/tv_match" />

</androidx.constraintlayout.widget.ConstraintLayout>