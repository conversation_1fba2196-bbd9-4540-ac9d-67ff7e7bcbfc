<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@mipmap/bg_login" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentBottom="true"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_160"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="@dimen/dp_88"
                android:layout_height="@dimen/dp_88"
                android:background="@mipmap/ic_logo" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/rammettoone"
                android:text="@string/app_name"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_20" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <LinearLayout
            android:id="@+id/ll_google_login"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_40"
            android:layout_marginHorizontal="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_12"
            android:background="@drawable/shape_translucent_white_button"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/dp_16"
                android:layout_height="@dimen/dp_16"
                android:layout_marginEnd="@dimen/dp_10"
                android:src="@mipmap/ic_login_google" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/google_login"
                android:textAllCaps="false"
                android:textSize="@dimen/sp_15"
                android:textColor="@color/white" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_guest_login"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_40"
            android:layout_marginHorizontal="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_12"
            android:background="@drawable/shape_translucent_white_button"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/dp_16"
                android:layout_height="@dimen/dp_16"
                android:layout_marginEnd="@dimen/dp_10"
                android:src="@mipmap/ic_login_guest" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/guest_login"
                android:textAllCaps="false"
                android:textSize="@dimen/sp_15"
                android:textColor="@color/white" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_facebook_login"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_40"
            android:layout_marginHorizontal="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_12"
            android:background="@drawable/shape_translucent_white_button"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="gone">

            <ImageView
                android:layout_width="@dimen/dp_16"
                android:layout_height="@dimen/dp_16"
                android:layout_marginEnd="@dimen/dp_10"
                android:src="@mipmap/ic_login_facebook" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/facebook_login"
                android:textAllCaps="false"
                android:textColor="@color/white" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_email_login"
            style="@style/PrimaryButton"
            android:layout_marginHorizontal="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_12"
            android:orientation="horizontal"
            android:visibility="gone">

            <ImageView
                android:layout_width="@dimen/dp_16"
                android:layout_height="@dimen/dp_16"
                android:layout_marginEnd="@dimen/dp_10"
                android:src="@mipmap/ic_login_email" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/email_login"
                android:textAllCaps="false"
                android:textColor="@color/white" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_bottom_agreement"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/dp_16"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/dp_20">

                <CheckBox
                    android:id="@+id/cb_agreement"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:layout_marginTop="@dimen/dp_1"
                    android:layout_marginEnd="@dimen/dp_6"
                    android:background="@drawable/selector_checkbox"
                    android:button="@null"
                    android:paddingHorizontal="@dimen/dp_12" />

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_checkbox_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:breakStrategy="simple"
                    android:text="@string/login_agreement"
                    android:textAllCaps="false"
                    android:textColor="@color/white_50"
                    android:textSize="@dimen/sp_15" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginBottom="@dimen/dp_30"
                android:orientation="horizontal">

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_user_agreement"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:breakStrategy="simple"
                    android:text="@string/user_agreement"
                    android:textAllCaps="false"
                    android:textColor="@color/color_F53D3D"
                    android:textSize="@dimen/sp_15" />

                <com.heart.heartmerge.i18n.I18nTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:breakStrategy="simple"
                    android:text="&amp;"
                    android:layout_marginHorizontal="@dimen/dp_3"
                    android:textAllCaps="false"
                    android:textColor="@color/white_50" />

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_privacy_policy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:breakStrategy="simple"
                    android:text="@string/privacy_policy"
                    android:textAllCaps="false"
                    android:textColor="@color/color_F53D3D"
                    android:textSize="@dimen/sp_15" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_finish"
        android:layout_width="@dimen/dp_30"
        android:layout_height="@dimen/dp_30"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="@dimen/dp_50"
        android:layout_marginEnd="@dimen/dp_20"
        android:padding="@dimen/dp_10"
        android:src="@mipmap/ic_close_black"
        android:visibility="gone"
        app:tint="@color/white"
        tools:visibility="visible" />
</RelativeLayout>