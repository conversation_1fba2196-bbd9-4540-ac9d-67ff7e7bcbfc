<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_32"
        android:orientation="vertical">

        <!-- 顶部视图 -->
        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/topView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_32"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginTop="?actionBarSize"
            android:background="@drawable/shape_1b1d39_50"
            android:drawablePadding="@dimen/dp_12"
            android:gravity="center_vertical"
            android:hint="@string/home_search_hint_text"
            android:paddingHorizontal="@dimen/dp_12"
            android:textColorHint="#5A5D7B"
            android:textSize="@dimen/sp_14"
            android:visibility="gone" />

        <com.bdc.android.library.refreshlayout.XRecyclerView
            android:id="@+id/language_hor_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_8"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_8"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <com.heart.heartmerge.ui.widget.SlidingTabLayoutViewPager2
                android:id="@+id/tabLayout"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp_40"
                android:layout_weight="1"
                app:tl_indicator_color="@color/color_FB2CA4"
                app:tl_indicator_corner_radius="@dimen/dp_24"
                app:tl_indicator_end_color="@color/color_8531FF"
                app:tl_indicator_height="@dimen/dp_10"
                app:tl_indicator_margin_bottom="@dimen/dp_10"
                app:tl_indicator_start_color="@color/color_EC12E2"
                app:tl_indicator_style="NORMAL"
                app:tl_indicator_width_equal_title="true"
                app:tl_tab_padding="@dimen/dp_12"
                app:tl_textBold="SELECT"
                app:tl_textSelectColor="@color/white"
                app:tl_textUnselectColor="@color/white"
                app:tl_textsize="@dimen/sp_18" />


            <LinearLayout
                android:id="@+id/ll_like_me"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <FrameLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:layout_width="@dimen/dp_32"
                        android:layout_height="@dimen/dp_32"
                        android:src="@mipmap/ic_main_like_me" />

                    <androidx.compose.ui.platform.ComposeView
                        android:id="@+id/tv_like_me_count"
                        android:layout_width="@dimen/dp_18"
                        android:layout_height="@dimen/dp_14"
                        android:layout_gravity="end|bottom"/>
                </FrameLayout>

            </LinearLayout>
            <!--            <LinearLayout-->
            <!--                android:id="@+id/ll_diamond"-->
            <!--                android:layout_width="wrap_content"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:background="@drawable/shape_border_953aa3"-->
            <!--                android:gravity="center_vertical"-->
            <!--                android:paddingHorizontal="@dimen/dp_5"-->
            <!--                android:paddingVertical="@dimen/dp_1">-->

            <!--                <ImageView-->
            <!--                    android:layout_width="@dimen/dp_17"-->
            <!--                    android:layout_height="@dimen/dp_17"-->
            <!--                    android:layout_marginEnd="@dimen/dp_3"-->
            <!--                    android:src="@mipmap/ic_diamond" />-->

            <!--                <androidx.compose.ui.platform.ComposeView-->
            <!--                    android:id="@+id/tv_diamond"-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:layout_marginHorizontal="@dimen/dp_6" />-->
            <!--            </LinearLayout>-->
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_notice_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:orientation="vertical" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </LinearLayout>
</FrameLayout>
