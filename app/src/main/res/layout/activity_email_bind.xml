<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#101321"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_24"
        android:layout_marginTop="@dimen/dp_30"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:layout_marginRight="@dimen/dp_8"
            android:background="@mipmap/ic_login_account" />

        <EditText
            android:id="@+id/et_email"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            android:hint="@string/login_placeholder_account"
            android:singleLine="true"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textColorHint="@color/color_999999"
            android:textSize="@dimen/sp_14" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginHorizontal="@dimen/dp_24"
        android:layout_marginVertical="@dimen/dp_10"
        android:background="@color/color_divider" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_24"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:layout_marginRight="@dimen/dp_8"
            android:background="@mipmap/ic_login_captcha" />

        <EditText
            android:id="@+id/et_captcha"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@color/transparent"
            android:hint="@string/login_placeholder_captcha"
            android:singleLine="true"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textColorHint="@color/color_999999"
            android:textSize="@dimen/sp_14" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_captcha"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/get_code"
            android:textAllCaps="false"
            android:textColor="#9EA3D8"
            android:textSize="@dimen/sp_14" />
    </LinearLayout>

    <com.heart.heartmerge.i18n.I18nButton
        android:id="@+id/btn_login"
        style="@style/PrimaryButton"
        android:layout_marginHorizontal="@dimen/dp_32"
        android:layout_marginTop="@dimen/dp_40"
        android:text="@string/confirm"
        android:textAllCaps="false" />
</LinearLayout>