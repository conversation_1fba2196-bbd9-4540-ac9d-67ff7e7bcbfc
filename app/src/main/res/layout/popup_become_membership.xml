<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_300"
        android:layout_marginTop="@dimen/dp_160"
        android:background="@mipmap/bg_become_membership"
        android:orientation="vertical">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginHorizontal="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_60"
            android:fontFamily="@font/rammettoone"
            android:gravity="center"
            android:text="@string/congratulations_become_vip"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_18" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_20"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <View
                android:layout_width="@dimen/dp_25"
                android:layout_height="@dimen/dp_1"
                android:background="@mipmap/ic_become_indicator_left" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_10"
                android:gravity="center"
                android:maxWidth="@dimen/dp_200"
                android:text="@string/you_have_vip_rights"
                android:textColor="@color/color_9656FF"
                android:textSize="@dimen/sp_12" />

            <View
                android:layout_width="@dimen/dp_25"
                android:layout_height="@dimen/dp_1"
                android:background="@mipmap/ic_become_indicator_right" />
        </LinearLayout>

        <com.heart.heartmerge.i18n.I18nButton
            android:id="@+id/btn_viewer"
            style="@style/PrimaryButton"
            android:layout_width="@dimen/dp_150"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_30"
            android:text="@string/already_know" />
    </LinearLayout>

    <com.opensource.svgaplayer.SVGAImageView
        android:layout_width="@dimen/dp_300"
        android:layout_height="@dimen/dp_300"
        android:layout_centerHorizontal="true"
        app:autoPlay="true"
        app:source="subscribe_success.svga" />

</RelativeLayout>