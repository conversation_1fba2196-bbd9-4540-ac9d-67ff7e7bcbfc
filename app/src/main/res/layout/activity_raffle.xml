<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@mipmap/bg_raffle"
    android:orientation="vertical">

    <com.bdc.android.library.widget.XToolbar
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true" />

    <ImageView
        android:id="@+id/backgroundImageView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scaleType="fitCenter" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="@id/backgroundImageView"
        app:layout_constraintEnd_toEndOf="@id/backgroundImageView">

        <com.heart.heartmerge.ui.widget.RaffleView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_320" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/btn_purchase"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_30"
            android:text="购买抽奖次数>"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_20" />

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_20"
            android:gravity="center_horizontal"
            android:orientation="horizontal">

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/btn_rules"
                style="@style/PrimaryButton"
                android:layout_width="@dimen/dp_100"
                android:layout_height="@dimen/dp_40"
                android:layout_marginHorizontal="@dimen/dp_10"
                android:gravity="center"
                android:text="活动规则" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/btn_record"
                android:layout_width="@dimen/dp_100"
                android:layout_height="@dimen/dp_40"
                android:layout_marginHorizontal="@dimen/dp_10"
                android:background="@drawable/bg_button_shape_ffbaad_ff9c96"
                android:gravity="center"
                android:text="抽奖记录"
                android:textColor="#571b23" />
        </androidx.appcompat.widget.LinearLayoutCompat>
    </LinearLayout>
</RelativeLayout>