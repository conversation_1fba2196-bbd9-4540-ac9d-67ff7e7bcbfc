<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/id_fl_container_id"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="24dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_68"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:background="@drawable/bg_message_notice"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp_12">

        <com.heart.heartmerge.ui.widget.RoundImageView
            android:id="@+id/iv_head_id"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_36"
            android:scaleType="centerCrop"
            android:src="@mipmap/ic_pic_default_oval"
            app:is_circle="true" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_title_id"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="16sp"
                tools:text="AAAAAAAAAAAAAAAAAAAAAAAAA" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_content_id"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="3dp"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:textColor="#797483"
                android:textSize="@dimen/sp_12"
                tools:text="AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" />
        </LinearLayout>

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="@dimen/dp_60"
            android:layout_height="@dimen/dp_28"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dp_6"
            android:background="@drawable/bg_message_replay"
            android:gravity="center"
            android:text="Reply"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold" />

    </LinearLayout>
</FrameLayout>

