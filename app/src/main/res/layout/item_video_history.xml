<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/dp_16"
    android:paddingVertical="@dimen/dp_14">

    <ImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/dp_44"
        android:layout_height="44dp"
        android:src="@mipmap/ic_default_avatar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_nickname"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="@dimen/dp_150"
        android:layout_marginStart="@dimen/dp_8"
        android:maxLines="1"
        android:ellipsize="middle"
        android:includeFontPadding="false"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar"
        tools:text="李生" />

    <ImageView

        android:id="@+id/iv_status"
        android:layout_width="@dimen/dp_12"
        android:layout_height="@dimen/dp_12"
        android:layout_marginTop="@dimen/dp_6"
        android:src="@mipmap/ic_call_history_suc"
        app:layout_constraintStart_toStartOf="@id/tv_nickname"
        app:layout_constraintTop_toBottomOf="@id/tv_nickname" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/videoStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginTop="@dimen/dp_4"
        android:includeFontPadding="false"
        android:textColor="@color/color_DE3953"
        android:textSize="@dimen/sp_12"
        app:layout_constraintStart_toEndOf="@id/iv_status"
        app:layout_constraintTop_toBottomOf="@id/tv_nickname"
        tools:text="fafa " />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/videoTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:textColor="@color/color_999999"
        android:textSize="@dimen/sp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_avatar" />


</androidx.constraintlayout.widget.ConstraintLayout>