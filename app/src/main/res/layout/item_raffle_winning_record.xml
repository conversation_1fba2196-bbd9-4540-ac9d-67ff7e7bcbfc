<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#101321"
    android:paddingHorizontal="10dp"
    android:paddingVertical="5dp">

    <ImageView
        android:id="@+id/iv_reward"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:background="@mipmap/ic_reward1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_reward_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:text="对话框"
        android:layout_marginTop="5dp"
        android:textColor="@color/white"
        android:textSize="18dp"
        app:layout_constraintLeft_toRightOf="@id/iv_reward"
        app:layout_constraintTop_toTopOf="@id/iv_reward" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="有效时长：终身有效"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="@id/iv_reward"
        app:layout_constraintStart_toStartOf="@id/tv_reward_name"
        app:layout_constraintTop_toTopOf="@id/iv_reward" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_lottery_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="抽奖时间：2024-11-12 19：10"
        android:textColor="#bbbbbb"
        android:layout_marginBottom="5dp"
        app:layout_constraintBottom_toBottomOf="@id/iv_reward"
        app:layout_constraintStart_toStartOf="@id/tv_reward_name" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_use"
        android:layout_width="80dp"
        android:layout_height="30dp"
        android:layout_marginRight="5dp"
        android:background="@drawable/bg_button_shape_ff6e58_f9453c"
        android:gravity="center"
        android:text="去使用"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@mipmap/ic_raffle_reward_used"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>

