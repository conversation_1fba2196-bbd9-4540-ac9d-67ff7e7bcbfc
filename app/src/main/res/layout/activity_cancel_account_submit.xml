<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <com.bdc.android.library.widget.XToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        app:x_title="@string/cancel_account"
        android:layout_height="wrap_content" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_16"
        android:text="@string/cancel_account"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:text="@string/cancel_account_warning"
        android:textColor="@color/color_C4C4C4" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_24"
        android:text="@string/cancel_account_choose_reason"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16" />

    <RadioGroup
        android:id="@+id/radio_reason"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16">

        <androidx.appcompat.widget.AppCompatRadioButton
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_22"
            android:button="@drawable/selector_checkbox"
            android:paddingStart="@dimen/dp_12"
            android:text="@string/cancel_account_choose_reason_option1"
            android:textColor="@color/color_C4C4C4"
            android:textSize="@dimen/sp_14" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginVertical="@dimen/dp_16"
            android:background="@color/color_divider" />

        <androidx.appcompat.widget.AppCompatRadioButton
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/selector_checkbox"
            android:paddingStart="@dimen/dp_12"
            android:text="@string/cancel_account_choose_reason_option2"
            android:textColor="@color/color_C4C4C4"
            android:textSize="@dimen/sp_14" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginVertical="@dimen/dp_16"
            android:background="@color/color_divider" />

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/radio_other"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/selector_checkbox"
            android:paddingStart="@dimen/dp_12"
            android:text="@string/cancel_account_choose_reason_option3"
            android:textColor="@color/color_C4C4C4"
            android:textSize="@dimen/sp_14" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginVertical="@dimen/dp_16"
            android:background="@color/color_divider" />
    </RadioGroup>

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:text="@string/cancel_account_custom_reason_tips"
        android:textColor="@color/color_5A5D7B" />

    <EditText
        android:id="@+id/et_reason"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_12"
        android:background="@drawable/shape_edittext_radius8"
        android:gravity="top"
        android:hint="@string/cancel_account_custom_reason_placeholder"
        android:minHeight="@dimen/dp_100"
        android:paddingHorizontal="@dimen/dp_12"
        android:paddingTop="@dimen/dp_16"
        android:textColor="@color/white"
        android:textColorHint="@color/color_5A5D7B" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <com.heart.heartmerge.i18n.I18nButton
        android:id="@+id/btn_submit"
        style="@style/PrimaryButton"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_24"
        android:gravity="center"
        android:text="@string/cancel_account_next_step"
        android:textAllCaps="false" />
</LinearLayout>