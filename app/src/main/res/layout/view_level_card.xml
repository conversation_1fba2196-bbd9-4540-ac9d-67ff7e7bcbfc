<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/iv_level"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_160"
        android:scaleType="fitCenter"
        android:src="@mipmap/bg_level_1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_level_label"
        android:layout_width="@dimen/dp_28"
        android:layout_height="@dimen/dp_25"
        android:layout_marginStart="@dimen/dp_20"
        android:layout_marginTop="@dimen/dp_50"
        android:src="@mipmap/ic_label_level_1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_level"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_10"
        android:fontFamily="@font/rammettoone"
        android:textColor="#6571A4"
        android:textSize="@dimen/sp_25"
        app:layout_constraintBottom_toBottomOf="@id/iv_level_label"
        app:layout_constraintStart_toEndOf="@id/iv_level_label"
        app:layout_constraintTop_toTopOf="@id/iv_level_label"
        tools:text="Lv0" />

    <TextView
        android:id="@+id/tv_not_locked"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp_12"
        app:layout_constraintStart_toStartOf="@id/iv_level_label"
        app:layout_constraintTop_toBottomOf="@id/tv_level"
        tools:text="@string/level_not_unlocked" />

    <TextView
        android:id="@+id/tv_next_level_value"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@id/progressBar"
        android:layout_marginBottom="@dimen/dp_4"
        android:textColor="#7581B6"
        android:textSize="@dimen/sp_12"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/progressBar"
        app:layout_constraintStart_toStartOf="@id/progressBar"
        tools:text="12345 to next level"
        tools:visibility="visible" />

    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_5"
        android:layout_marginHorizontal="@dimen/dp_20"
        android:layout_marginBottom="@dimen/dp_20"
        android:indeterminate="false"
        android:max="100"
        android:orientation="horizontal"
        android:progressDrawable="@drawable/progress_drawable_level_1"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv_level"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>