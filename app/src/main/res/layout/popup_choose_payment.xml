<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_bottom_popup"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/dp_16"
    android:paddingTop="@dimen/dp_20">

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_5"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/shape_popup_indicator" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_16">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_diamond"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:drawablePadding="@dimen/dp_3"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_25"
            android:textStyle="bold"
            app:drawableStartCompat="@mipmap/ic_diamond_recharge_level3"
            tools:text="100" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_dialog_close_white"
            android:visibility="gone" />
    </RelativeLayout>

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_16"
        android:text="@string/choose_country"
        android:textColor="@color/color_9F9CA6"
        android:textSize="@dimen/sp_12" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_6"
        android:background="@drawable/shape_card_background"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/sp_16">

        <LinearLayout
            android:id="@+id/ll_change_country"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_flag"
                android:layout_width="@dimen/dp_30"
                android:layout_height="@dimen/sp_20"
                tools:src="@mipmap/country_cn" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_country"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_10"
                android:layout_weight="1"
                android:text="中国"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_16" />

            <ImageView
                android:layout_width="@dimen/dp_28"
                android:layout_height="@dimen/dp_14"
                android:rotation="90"
                android:src="@mipmap/ic_right_gray" />
        </LinearLayout>
    </LinearLayout>

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_20"
        android:text="@string/choose_payment_coupon"
        android:textColor="@color/color_9F9CA6"
        android:textSize="@dimen/sp_12"
        android:visibility="gone" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_6"
        android:background="@drawable/shape_card_background"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/sp_16"
        android:visibility="gone">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_coupon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_5"
            android:hint="@string/no_available_coupons"
            android:textColor="@color/white"
            android:textColorHint="@color/white"
            android:textSize="@dimen/sp_16" />

        <ImageView
            android:layout_width="@dimen/dp_28"
            android:layout_height="@dimen/dp_14"
            android:rotation="90"
            android:src="@mipmap/ic_right_gray"
            android:visibility="gone" />
    </LinearLayout>

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_20"
        android:drawablePadding="@dimen/dp_15"
        android:gravity="center_vertical"
        android:text="@string/choose_payment_method"
        android:textColor="@color/color_9F9CA6"
        android:textSize="@dimen/sp_12" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.bdc.android.library.refreshlayout.XRecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_340"
            android:layout_marginTop="@dimen/dp_10"
            android:minHeight="@dimen/dp_150"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/item_choose_payment" />

        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/progress_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            app:indicatorColor="@color/colorAccent"
            app:indicatorInset="@dimen/dp_40"
            app:indicatorSize="@dimen/dp_40"
            app:trackColor="@color/background" />
    </RelativeLayout>

</LinearLayout>