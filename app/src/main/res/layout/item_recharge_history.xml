<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="@dimen/dp_5"
    android:background="@drawable/shape_card_background"
    android:paddingHorizontal="@dimen/dp_16"
    android:paddingTop="@dimen/dp_20"
    android:paddingBottom="@dimen/dp_12">

    <TextView
        android:id="@+id/tv_diamond"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="200" />

    <ImageView
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_21"
        android:layout_marginStart="@dimen/dp_4"
        android:src="@mipmap/ic_diamond"
        app:layout_constraintBottom_toBottomOf="@id/tv_diamond"
        app:layout_constraintStart_toEndOf="@id/tv_diamond"
        app:layout_constraintTop_toTopOf="@id/tv_diamond" />

    <TextView
        android:id="@+id/tv_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toBottomOf="@id/tv_diamond"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_diamond"
        tools:text="2000Rp" />

    <TextView
        android:id="@+id/tv_order_id"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        android:textColor="@color/white_40"
        android:textSize="@dimen/sp_12"
        app:layout_constraintStart_toStartOf="@id/tv_diamond"
        app:layout_constraintTop_toBottomOf="@id/tv_diamond"
        tools:text="@string/order_id" />

    <TextView
        android:id="@+id/tv_order_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:textColor="@color/white_40"
        android:textSize="@dimen/sp_12"
        app:layout_constraintStart_toStartOf="@id/tv_diamond"
        app:layout_constraintTop_toBottomOf="@id/tv_order_id"
        tools:text="@string/order_time" />

    <TextView
        android:id="@+id/tv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#35B244"
        android:textSize="@dimen/sp_14"
        app:layout_constraintBottom_toBottomOf="@id/tv_order_time"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_order_time"
        tools:text="Success" />

</androidx.constraintlayout.widget.ConstraintLayout>