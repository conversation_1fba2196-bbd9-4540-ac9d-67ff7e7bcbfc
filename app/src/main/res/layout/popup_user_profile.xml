<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_white_top16">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_55"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_12"
            android:text="@string/cancel"
            android:textColor="@color/color_BFBFBF"
            android:textSize="@dimen/sp_16"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_55"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_12"
            android:text="@string/my_person_data"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_18"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_save"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_55"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_12"
            android:text="@string/save"
            android:textColor="@color/color_F84139"
            android:textSize="@dimen/sp_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_5"
            android:background="@color/color_EEEEEE"
            app:layout_constraintTop_toBottomOf="@id/tv_cancel" />

        <ImageView
            android:id="@+id/iv_header"
            android:layout_width="@dimen/dp_80"
            android:layout_height="@dimen/dp_80"
            android:layout_marginTop="@dimen/dp_16"
            android:scaleType="centerCrop"
            android:src="@mipmap/ic_default_header"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_cancel" />

        <ImageView
            android:id="@+id/iv_capture"
            android:layout_width="@dimen/dp_32"
            android:layout_height="@dimen/dp_32"
            android:scaleType="centerCrop"
            android:src="@mipmap/ic_camera"
            app:layout_constraintBottom_toBottomOf="@id/iv_header"
            app:layout_constraintEnd_toEndOf="@id/iv_header"
            app:layout_constraintStart_toStartOf="@id/iv_header"
            app:layout_constraintTop_toTopOf="@id/iv_header" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_id_text"
            android:layout_width="@dimen/dp_75"
            android:layout_height="@dimen/dp_52"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_12"
            android:gravity="center_vertical"
            android:text="ID"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_header" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_id"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_52"
            android:background="@null"
            android:gravity="center_vertical"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="@id/tv_id_text"
            app:layout_constraintStart_toEndOf="@id/tv_id_text"
            app:layout_constraintTop_toTopOf="@id/tv_id_text"
            tools:text="ID" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_5"
            android:background="@color/color_EEEEEE"
            app:layout_constraintTop_toBottomOf="@id/tv_id_text" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_name_text"
            android:layout_width="@dimen/dp_75"
            android:layout_height="@dimen/dp_52"
            android:layout_marginStart="@dimen/dp_16"
            android:gravity="center_vertical"
            android:text="@string/nickname"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_id_text" />

        <EditText
            android:id="@+id/et_nickname"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_52"
            android:layout_marginEnd="@dimen/dp_16"
            android:background="@null"
            android:gravity="center_vertical"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            android:singleLine="true"
            app:layout_constraintBottom_toBottomOf="@id/tv_name_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_name_text"
            app:layout_constraintTop_toTopOf="@id/tv_name_text"
            tools:text="苏沐橙" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_5"
            android:background="@color/color_EEEEEE"
            app:layout_constraintTop_toBottomOf="@id/tv_name_text" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_age_text"
            android:layout_width="@dimen/dp_75"
            android:layout_height="@dimen/dp_52"
            android:layout_marginStart="@dimen/dp_16"
            android:gravity="center_vertical"
            android:text="@string/age"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_name_text" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_age"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_52"
            android:gravity="center_vertical"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="@id/tv_age_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_age_text"
            app:layout_constraintTop_toTopOf="@id/tv_age_text"
            tools:text="18" />

        <ImageView
            android:layout_width="@dimen/dp_7"
            android:layout_height="@dimen/dp_13"
            android:layout_marginEnd="@dimen/dp_16"
            android:src="@mipmap/ic_arrow_right_gray"
            app:layout_constraintBottom_toBottomOf="@id/tv_age_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_age_text" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_5"
            android:background="@color/color_EEEEEE"
            app:layout_constraintTop_toBottomOf="@id/tv_age_text" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_language_text"
            android:layout_width="@dimen/dp_75"
            android:layout_height="@dimen/dp_52"
            android:layout_marginStart="@dimen/dp_16"
            android:gravity="center_vertical"
            android:text="@string/label_title_language"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_age_text" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_language"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_52"
            android:gravity="center_vertical"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="@id/tv_language_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_language_text"
            app:layout_constraintTop_toTopOf="@id/tv_language_text"
            tools:text="汉语、英语" />

        <ImageView
            android:layout_width="@dimen/dp_7"
            android:layout_height="@dimen/dp_13"
            android:layout_marginEnd="@dimen/dp_16"
            android:src="@mipmap/ic_arrow_right_gray"
            app:layout_constraintBottom_toBottomOf="@id/tv_language_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_language_text" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_5"
            android:background="@color/color_EEEEEE"
            app:layout_constraintTop_toBottomOf="@id/tv_language_text" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_country_text"
            android:layout_width="@dimen/dp_75"
            android:layout_height="@dimen/dp_52"
            android:layout_marginStart="@dimen/dp_16"
            android:gravity="center_vertical"
            android:visibility="gone"
            android:text="@string/country"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_language" />

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_country"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_52"
            android:gravity="center_vertical"
            android:visibility="gone"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="@id/tv_country_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_country_text"
            app:layout_constraintTop_toTopOf="@id/tv_country_text"
            tools:text="中国" />

        <ImageView
            android:layout_width="@dimen/dp_7"
            android:layout_height="@dimen/dp_13"
            android:layout_marginEnd="@dimen/dp_16"
            android:visibility="gone"
            android:src="@mipmap/ic_arrow_right_gray"
            app:layout_constraintBottom_toBottomOf="@id/tv_country_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_country_text" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_5"
            android:visibility="gone"
            android:background="@color/color_EEEEEE"
            app:layout_constraintTop_toBottomOf="@id/tv_country_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>