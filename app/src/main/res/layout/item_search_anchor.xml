<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="@dimen/dp_12"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/anchor_image"
        android:layout_width="@dimen/dp_44"
        android:layout_height="@dimen/dp_44"
        android:adjustViewBounds="true"
        android:scaleType="fitXY"
        android:src="@mipmap/ic_pic_default_oval"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/anchor_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginBottom="@dimen/dp_6"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14"
        app:layout_constraintStart_toEndOf="@id/anchor_image"
        app:layout_constraintTop_toTopOf="@id/anchor_image" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/online_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:background="@drawable/shape_black_60_22"
        android:gravity="center"
        android:text="@string/on_line"
        android:textColor="@color/color_21C76E"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="@id/anchor_image"
        app:layout_constraintStart_toEndOf="@id/anchor_image" />

    <ImageView
        android:id="@+id/anchor_country"
        android:layout_width="@dimen/dp_40"
        android:layout_height="@dimen/dp_30"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>