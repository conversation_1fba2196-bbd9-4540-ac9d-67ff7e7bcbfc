<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.bdc.android.library.widget.XToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:paddingBottom="@dimen/dp_50">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_20"
                android:gravity="center_horizontal"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/iv_gender_female"
                    android:layout_width="@dimen/dp_100"
                    android:layout_height="@dimen/dp_106"
                    android:layout_marginHorizontal="@dimen/dp_28"
                    android:scaleType="fitStart"
                    android:src="@mipmap/ic_gender_female_normal" />

                <ImageView
                    android:id="@+id/iv_gender_male"
                    android:layout_width="@dimen/dp_100"
                    android:layout_height="@dimen/dp_106"
                    android:layout_marginHorizontal="@dimen/dp_28"
                    android:scaleType="fitStart"
                    android:src="@mipmap/ic_gender_male_checked" />
            </LinearLayout>

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_37"
                android:layout_marginTop="@dimen/dp_37"
                android:text="@string/avatar"
                android:textColor="@color/color_999999"
                android:textSize="@dimen/sp_12" />

            <com.bdc.android.library.refreshlayout.XRecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_7"
                android:orientation="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="4"
                tools:listitem="@layout/item_avatar_default" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_37"
                android:layout_marginTop="@dimen/dp_20"
                android:text="@string/nickname"
                android:textColor="@color/color_999999"
                android:textSize="@dimen/sp_12" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_nickname"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_48"
                android:layout_marginHorizontal="@dimen/dp_37"
                android:layout_marginTop="@dimen/dp_7"
                android:background="@drawable/shape_edittext"
                android:gravity="center|center_horizontal"
                android:hint="@string/label_title_nickname_hint"
                android:paddingHorizontal="@dimen/dp_16"
                android:singleLine="true"
                android:maxLength="15"
                android:text="Random nickname"
                android:textColor="@color/white"
                android:textColorHint="@color/color_999999"
                android:textSize="@dimen/sp_14" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_37"
                android:layout_marginTop="@dimen/dp_20"
                android:singleLine="true"
                android:text="@string/date_of_birth"
                android:textColor="@color/color_999999"
                android:textSize="@dimen/sp_12" />


            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_birthday"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_48"
                android:layout_marginHorizontal="@dimen/dp_37"
                android:layout_marginTop="@dimen/dp_7"
                android:background="@drawable/shape_edittext"
                android:gravity="center"
                android:hint="@string/choose_birthday"
                android:paddingHorizontal="@dimen/dp_16"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textColorHint="@color/color_999999"
                android:textSize="@dimen/sp_14"
                app:drawableEndCompat="@mipmap/ic_arrow_right_gray"
                app:drawableTint="@color/white" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_37"
                android:layout_marginTop="@dimen/dp_20"
                android:text="@string/country"
                android:textColor="@color/color_999999"
                android:textSize="@dimen/sp_12" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_country"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_48"
                android:layout_marginHorizontal="@dimen/dp_37"
                android:layout_marginTop="@dimen/dp_7"
                android:background="@drawable/shape_edittext"
                android:gravity="center"
                android:hint="@string/choose_country"
                android:paddingHorizontal="@dimen/dp_16"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textColorHint="@color/color_999999"
                android:textSize="@dimen/sp_14"
                app:drawableEndCompat="@mipmap/ic_arrow_right_gray"
                app:drawableTint="@color/white" />

            <View
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

        </LinearLayout>
    </ScrollView>

    <com.heart.heartmerge.i18n.I18nButton
        android:id="@+id/btn_submit"
        style="@style/PrimaryButton"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:text="@string/letstart" />

    <com.heart.heartmerge.i18n.I18nTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_36"
        android:gravity="center"
        android:text="@string/build_profile_explain"
        android:textColor="@color/color_EC12E2" />

</androidx.appcompat.widget.LinearLayoutCompat>