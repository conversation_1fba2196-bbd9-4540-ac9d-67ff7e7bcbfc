<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:angle="90"
                android:centerColor="#FEFEFE"
                android:endColor="#B4B3BB"
                android:startColor="#6F6E73" />
            <corners android:radius="@dimen/dp_12" />
        </shape>
    </item>

    <item
        android:bottom="@dimen/dp_2"
        android:left="@dimen/dp_2"
        android:right="@dimen/dp_2"
        android:top="@dimen/dp_2">
        <shape android:shape="rectangle">
            <solid android:color="@color/background" />
            <corners android:radius="@dimen/dp_12" />
        </shape>
    </item>

</layer-list>
