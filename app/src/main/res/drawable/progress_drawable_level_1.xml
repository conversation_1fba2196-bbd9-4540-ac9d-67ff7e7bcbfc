<?xml version="1.0" encoding="utf-8"?><!-- res/drawable/progress_drawable.xml -->
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 背景层（轨道颜色） -->
    <item android:id="@android:id/background">
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/dp_8" /> <!-- 设置圆角 -->
            <solid android:color="@color/white" /> <!-- 轨道颜色 -->
        </shape>
    </item>

    <!-- 前景层（进度条渐变颜色） -->
    <item android:id="@android:id/progress">
        <clip android:gravity="left">
            <shape>
                <corners android:radius="@dimen/dp_8" /> <!-- 设置圆角 -->
                <gradient
                    android:angle="0"
                    android:endColor="#6D79AD"
                    android:startColor="#B6BEEE" /> <!-- 水平渐变 -->
            </shape>
        </clip>
    </item>
</layer-list>
