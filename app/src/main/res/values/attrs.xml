<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="CustomMineTextView">
        <!--     左侧文字   -->
        <attr name="ctv_left" format="string" />
        <!--     左侧文字颜色   -->
        <attr name="ctv_left_color" format="reference" />
        <!--     左侧文字大小   -->
        <attr name="ctv_left_textSize" format="dimension|reference" />
        <!--     左侧图片   -->
        <attr name="ctv_left_icon" format="reference" />
        <!--     左侧文字是否加粗   -->
        <attr name="ctv_left_is_bold" format="boolean" />
        <attr name="ctv_left_marginLeft" format="dimension" />
        <attr name="ctv_left_marginRight" format="dimension" />

        <!--     右侧文字  -->
        <attr name="ctv_right" format="string" />
        <!--     右侧文字默认文字  -->
        <attr name="ctv_right_hint" format="string" />
        <!--     右侧文字颜色  -->
        <attr name="ctv_right_color" format="reference" />
        <!--     右侧文字默认文字颜色  -->
        <attr name="ctv_right_hint_color" format="reference" />
        <!--     右侧文字大小 -->
        <attr name="ctv_right_textSize" format="dimension|reference" />
        <!--     右侧能否输入 -->
        <attr name="ctv_right_isCanInput" format="boolean" />
        <attr name="ctv_right_marginHorizontal" format="dimension" />
        <attr name="ctv_right_marginLeft" format="dimension" />
        <attr name="ctv_right_marginRight" format="dimension" />
        <!--     右侧文字靠左还是靠右 -->
        <attr name="ctv_right_gravity" format="enum">
            <enum name="ctv_left" value="1" />
            <enum name="ctv_right" value="2" />
        </attr>


        <attr name="ctv_next" format="boolean" />
        <attr name="ctv_next_icon" format="reference" />

        <attr name="ctv_show_line" format="boolean" />

    </declare-styleable>

    <declare-styleable name="MyChronometer">
        <!-- Format string: if specified, the Chronometer will display this
             string, with the first "%s" replaced by the current timer value
             in "MM:SS" or "H:MM:SS" form.
             If no format string is specified, the Chronometer will simply display
             "MM:SS" or "H:MM:SS". -->
        <attr name="format" format="string" localization="suggested" />
        <!-- Specifies whether this Chronometer counts down or counts up from the base.
              If not specified this is false and the Chronometer counts up. -->
        <attr name="countDown" format="boolean" />
    </declare-styleable>

    <declare-styleable name="RoundImageView">
        <attr name="is_circle" format="boolean" />
        <attr name="is_cover_src" format="boolean" />
        <attr name="corner_radius" format="dimension" />
        <attr name="corner_top_left_radius" format="dimension" />
        <attr name="corner_top_right_radius" format="dimension" />
        <attr name="corner_bottom_left_radius" format="dimension" />
        <attr name="corner_bottom_right_radius" format="dimension" />
        <attr name="border_width" format="dimension" />
        <attr name="border_color" format="color" />
        <attr name="inner_border_width" format="dimension" />
        <attr name="inner_border_color" format="color" />
        <attr name="mask_color" format="color" />
        <attr name="view_size" format="dimension" />
    </declare-styleable>

    <declare-styleable name="CustomProgressView">
        <attr name="progress" format="float" />
        <attr name="startColor" format="color" />
        <attr name="endColor" format="color" />
    </declare-styleable>


    <declare-styleable name="labels_view">
        <attr format="enum" name="selectType">
            <enum name="NONE" value="1"/>
            <enum name="SINGLE" value="2"/>
            <enum name="SINGLE_IRREVOCABLY" value="3"/>
            <enum name="MULTI" value="4"/>
        </attr>

        <attr name="labelGravity">
            <!-- Push object to the top of its container, not changing its size. -->
            <flag name="top" value="0x30"/>
            <!-- Push object to the bottom of its container, not changing its size. -->
            <flag name="bottom" value="0x50"/>
            <!-- Push object to the left of its container, not changing its size. -->
            <flag name="left" value="0x03"/>
            <!-- Push object to the right of its container, not changing its size. -->
            <flag name="right" value="0x05"/>
            <!-- Place object in the vertical center of its container, not changing its size. -->
            <flag name="center_vertical" value="0x10"/>
            <!-- Grow the vertical size of the object if needed so it completely fills its container. -->
            <flag name="fill_vertical" value="0x70"/>
            <!-- Place object in the horizontal center of its container, not changing its size. -->
            <flag name="center_horizontal" value="0x01"/>
            <!-- Grow the horizontal size of the object if needed so it completely fills its container. -->
            <flag name="fill_horizontal" value="0x07"/>
            <!-- Place the object in the center of its container in both the vertical and horizontal axis, not changing its size. -->
            <flag name="center" value="0x11"/>
            <!-- Grow the horizontal and vertical size of the object if needed so it completely fills its container. -->
            <flag name="fill" value="0x77"/>
            <!-- Additional option that can be set to have the top and/or bottom edges of
                 the child clipped to its container's bounds.
                 The clip will be based on the vertical gravity: a top gravity will clip the bottom
                 edge, a bottom gravity will clip the top edge, and neither will clip both edges. -->
            <flag name="clip_vertical" value="0x80"/>
            <!-- Additional option that can be set to have the left and/or right edges of
                 the child clipped to its container's bounds.
                 The clip will be based on the horizontal gravity: a left gravity will clip the right
                 edge, a right gravity will clip the left edge, and neither will clip both edges. -->
            <flag name="clip_horizontal" value="0x08"/>
            <!-- Push object to the beginning of its container, not changing its size. -->
            <flag name="start" value="0x00800003"/>
            <!-- Push object to the end of its container, not changing its size. -->
            <flag name="end" value="0x00800005"/>
        </attr>

        <attr format="integer" name="maxSelect"/>
        <attr format="integer" name="minSelect"/>
        <attr format="integer" name="maxLines"/>
        <attr format="boolean" name="isIndicator"/>
        <attr format="reference|color" name="labelTextColor"/>
        <attr format="dimension" name="labelTextSize"/>
        <attr format="dimension" name="labelTextWidth">
            <enum name="fill_parent" value="-1"/>
            <enum name="match_parent" value="-1"/>
            <enum name="wrap_content" value="-2"/>
        </attr>
        <attr format="dimension" name="labelTextHeight">
            <enum name="fill_parent" value="-1"/>
            <enum name="match_parent" value="-1"/>
            <enum name="wrap_content" value="-2"/>
        </attr>
        <attr format="dimension" name="labelTextPadding"/>
        <attr format="dimension" name="labelTextPaddingLeft"/>
        <attr format="dimension" name="labelTextPaddingTop"/>
        <attr format="dimension" name="labelTextPaddingRight"/>
        <attr format="dimension" name="labelTextPaddingBottom"/>
        <attr format="dimension" name="lineMargin"/>
        <attr format="dimension" name="wordMargin"/>
        <attr format="reference|color" name="labelBackground"/>
        <attr format="boolean" name="singleLine"/>
    </declare-styleable>
    <item name="tag_key_data" type="id"/>
    <item name="tag_key_position" type="id"/>

    <declare-styleable name="RadarView">
        <!--圆圈和交叉线的颜色-->
        <attr name="circleColor" format="color" />
        <!--圆圈的数量-->
        <attr name="circleNum" format="integer" />
        <!--扫描的颜色 RadarView会对这个颜色做渐变透明处理-->
        <attr name="sweepColor" format="color" />
        <!--水滴的颜色-->
        <attr name="raindropColor" format="color" />
        <!--水滴的数量 这里表示的是水滴最多能同时出现的数量。因为水滴是随机产生的，数量是不确定的-->
        <attr name="raindropNum" format="integer" />
        <!--是否显示交叉线-->
        <attr name="showCross" format="boolean" />
        <!--是否显示水滴-->
        <attr name="showRaindrop" format="boolean" />
        <!--扫描的转速，表示几秒转一圈-->
        <attr name="speed" format="float" />
        <!--水滴显示和消失的速度-->
        <attr name="flicker" format="float" />
    </declare-styleable>


    <declare-styleable name="SlidingTabLayoutExtend">
        <!-- indicator -->
        <attr name="tl_indicator_start_color" format="color"/>
        <attr name="tl_indicator_end_color" format="color"/>

    </declare-styleable>

    <declare-styleable name="GradientTextView">
        <!-- 渐变颜色，支持多色 -->
        <attr name="gradientColors" format="reference" />
        <!-- 边框颜色 -->
        <attr name="strokeColor" format="color" />
        <!-- 边框宽度 -->
        <attr name="strokeWidth" format="dimension" />
    </declare-styleable>
</resources>