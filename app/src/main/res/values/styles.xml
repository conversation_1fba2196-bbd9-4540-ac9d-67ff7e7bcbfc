<resources>

    <style name="Dialog" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <style name="BottomDialog" parent="@style/Base.V7.Theme.AppCompat.Light.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="BottomDialog.Animation" parent="Animation.AppCompat.Dialog">
        <item name="android:windowEnterAnimation">@anim/translate_dialog_in</item>
        <item name="android:windowExitAnimation">@anim/translate_dialog_out</item>
    </style>


    <!-- ******************************************************************************************   -->

    <!--    一级标题-->
    <style name="textTitle1_16">
        <item name="android:textColor">@color/text_title1</item>
        <item name="android:textSize">@dimen/sp_16</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">@font/urbanist_semibold</item>
    </style>

    <style name="textTitle1_14">
        <item name="android:textColor">@color/text_title1</item>
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">@font/urbanist_semibold</item>
    </style>

    <style name="textTitle1_12">
        <item name="android:textColor">@color/text_title1</item>
        <item name="android:textSize">@dimen/sp_12</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">@font/urbanist_semibold</item>
    </style>

    <style name="textTitle1_10">
        <item name="android:textColor">@color/text_title1</item>
        <item name="android:textSize">@dimen/sp_10</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">@font/urbanist_semibold</item>
    </style>
    <!--    二级标题-->
    <style name="textTitle2_16">
        <item name="android:textColor">@color/text_title2</item>
        <item name="android:textSize">@dimen/sp_16</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">@font/urbanist_semibold</item>
    </style>

    <style name="textTitle2_14">
        <item name="android:textColor">@color/text_title2</item>
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">@font/urbanist_semibold</item>
    </style>

    <style name="textTitle2_12">
        <item name="android:textColor">@color/text_title2</item>
        <item name="android:textSize">@dimen/sp_12</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">@font/urbanist_semibold</item>
    </style>

    <style name="textTitle2_10">
        <item name="android:textColor">@color/text_title2</item>
        <item name="android:textSize">@dimen/sp_10</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">@font/urbanist_semibold</item>
    </style>

    <!-- customMineText   -->
    <style name="CustomMineTextView">
        <item name="ctv_left_is_bold">false</item>
        <item name="ctv_next_icon">@mipmap/ic_right_black</item>
        <item name="ctv_left_color">@color/text_title1</item>
        <item name="ctv_left_textSize">@dimen/sp_16</item>
        <item name="ctv_right_hint_color">@color/color_999999</item>
        <item name="ctv_right_color">@color/color_333333</item>
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="CustomMineTextView_gray">
        <item name="ctv_left_is_bold">false</item>
        <item name="ctv_next_icon">@mipmap/ic_right_gray</item>
        <item name="ctv_left_color">@color/text_title1</item>
        <item name="ctv_left_textSize">@dimen/sp_14</item>
        <item name="ctv_right_hint_color">@color/color_9EA3D8</item>
        <item name="ctv_right_color">@color/white_40</item>
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="mine">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/dp_60</item>
        <item name="android:gravity">center_vertical</item>
        <item name="ctv_left_textSize">@dimen/sp_16</item>
        <item name="ctv_left_color">@color/color_D6D6D6</item>
        <item name="ctv_left_is_bold">false</item>
        <item name="ctv_left_marginLeft">@dimen/dp_10</item>
    </style>

    <style name="DialogStyle" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Dialog的windowFrame框 -->
        <item name="android:windowFrame">@null</item>
        <!-- 是否浮现在activity之上 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 是否半透明 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 是否显示title -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <!-- Dialog的背景 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!-- 背景是否模糊显示 -->
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
    </style>


    <!-- 自定义圆角样式 -->
    <style name="CustomShapeAppearance" parent="ShapeAppearance.MaterialComponents.SmallComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">8dp</item> <!-- 设置圆角大小 -->
    </style>

    <!-- 自定义圆角样式 -->
    <style name="CallInfoShape" parent="ShapeAppearance.MaterialComponents.SmallComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">@dimen/call_info_image_corner</item> <!-- 设置圆角大小 -->
    </style>

    <style name="RatingBar" parent="@android:style/Widget.RatingBar">
        <item name="android:progressDrawable">@drawable/rate_drawable</item>
        <item name="android:minHeight">@dimen/dp_28</item>
        <item name="android:maxHeight">@dimen/dp_28</item>
        <item name="android:numColumns">5</item>
    </style>

</resources>
