<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Theme.HeartMerge" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="android:statusBarColor">@color/transparent</item>
        <item name="android:navigationBarColor">@color/transparent</item>
        <item name="android:windowBackground">@mipmap/bg_global</item>
        <item name="android:textViewStyle">@style/TextViewStyle.TextDirection</item>
        <item name="editTextStyle">@style/EditTextStyle.Alignment</item>
        <item name="android:fontFamily">@font/urbanist_semibold</item>
    </style>

    <style name="TextViewStyle.TextDirection" parent="android:Widget.TextView">
        <item name="android:textDirection">locale</item>
    </style>

    <style name="EditTextStyle.Alignment" parent="@android:style/Widget.EditText">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:gravity">start</item>
        <item name="android:textDirection">locale</item>
    </style>

    <style name="PrimaryButton">
        <item name="android:background">@drawable/selector_primary_button</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/dp_14</item>
        <item name="android:gravity">center</item>
        <item name="textAllCaps">false</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/dp_40</item>
    </style>

    <style name="SecondaryButton" parent="PrimaryButton">
        <item name="android:background">@drawable/selector_secondary_button</item>
        <item name="android:textColor">@color/color_9EA3D8</item>
    </style>

    <style name="EditText">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/dp_44</item>
        <item name="android:background">@drawable/shape_edittext_radius</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textColorHint">#545660</item>
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:paddingStart">@dimen/dp_16</item>
        <item name="android:paddingEnd">@dimen/dp_16</item>
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="Theme.AppCompat.Translucent" parent="Theme.HeartMerge">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="Theme.App.Starting" parent="Theme.SplashScreen">
        <!-- Set the splash screen background, animated icon, and animation
        duration. -->
        <item name="windowSplashScreenBackground">@color/background</item>

        <!-- Use windowSplashScreenAnimatedIcon to add a drawable or an animated
             drawable. One of these is required. -->
        <item name="windowSplashScreenAnimatedIcon">@mipmap/ic_splash_logo</item>
        <!-- Required for animated icons. -->
        <item name="windowSplashScreenAnimationDuration">200</item>
        <item name="windowSplashScreenIconBackgroundColor">@android:color/transparent</item>

        <!-- Set the theme of the Activity that directly follows your splash
        screen. This is required. -->
        <item name="postSplashScreenTheme">@style/LaunchTheme</item>
    </style>

    <style name="LaunchTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowBackground">@mipmap/bg_splash</item>
        <item name="android:windowNoTitle">true</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

</resources>