package io.rong.imkit.conversation.extension.provider;

import android.content.Context;
import android.text.Spannable;
import android.text.SpannableString;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.heart.heartmerge.R;
import com.heart.heartmerge.ui.activities.message.MyRongConversationActivity;

import java.util.List;

import io.rong.imkit.conversation.extension.parsemessage.MikChatVideoCallMessage;
import io.rong.imkit.conversation.messgelist.provider.BaseMessageItemProvider;
import io.rong.imkit.model.UiMessage;
import io.rong.imkit.widget.adapter.IViewProviderListener;
import io.rong.imkit.widget.adapter.ViewHolder;
import io.rong.imlib.model.Message;
import io.rong.imlib.model.MessageContent;

public class VideCallMessageItemProvider extends BaseMessageItemProvider<MikChatVideoCallMessage> {

    public VideCallMessageItemProvider() {
//        mConfig.showReadState = true;
    }

    @Override
    protected ViewHolder onCreateMessageContentViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.rc_video_call_message_item, parent, false);
        return new ViewHolder(parent.getContext(), view);
    }

    @Override
    protected void bindMessageContentViewHolder(final ViewHolder holder, ViewHolder parentHolder, MikChatVideoCallMessage message, final UiMessage uiMessage, int position, List<UiMessage> list, IViewProviderListener<UiMessage> listener) {
        final TextView callTime = holder.getView(R.id.call_time);
        callTime.setText(message.callTime);

        final TextView callTimeReceive = holder.getView(R.id.call_time_receive);
        callTimeReceive.setText(message.callTime);

        boolean isSender = uiMessage.getMessage().getMessageDirection().equals(Message.MessageDirection.SEND);
        if (isSender) {
            callTimeReceive.setVisibility(View.GONE);
            callTime.setVisibility(View.VISIBLE);
            holder.setBackgroundRes(R.id.CL, R.drawable.ic_gift_right_bg);
        } else {
            callTimeReceive.setVisibility(View.VISIBLE);
            callTime.setVisibility(View.GONE);
            holder.setBackgroundRes(R.id.CL, R.drawable.ic_gift_left_bg);
        }
    }

    private void setDirection(View view, boolean isSender) {
        ConstraintLayout.LayoutParams lp = ((ConstraintLayout.LayoutParams) view.getLayoutParams());
        if (isSender) {
            lp.startToStart = ConstraintLayout.LayoutParams.UNSET;
            lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
        } else {
            lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
            lp.endToEnd = ConstraintLayout.LayoutParams.UNSET;
        }
        view.setLayoutParams(lp);
    }

    @Override
    protected boolean onItemClick(ViewHolder holder, MikChatVideoCallMessage message, UiMessage uiMessage, int position, List<UiMessage> list, IViewProviderListener<UiMessage> listener) {
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("id", message.userId);
//            jsonObject.put("nickName", uiMessage.getUserInfo().getName());
//            Intent intent = new Intent(holder.itemView.getContext(), AnchorVideoActivity.class);
//            Bundle bundle = new Bundle();
//            bundle.putString(Constants.INTENT_PARAM_KEY_ANCHOR_INFO, jsonObject.toString());
//            intent.putExtras(bundle);
//            holder.itemView.getContext().startActivity(intent);
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
        if (holder.getContext() instanceof MyRongConversationActivity rongConversationActivity){
            rongConversationActivity.startVideo();
        }
        return false;
    }

    @Override
    protected boolean isMessageViewType(MessageContent messageContent) {
        return messageContent instanceof MikChatVideoCallMessage && !messageContent.isDestruct();
    }


    @Override
    public Spannable getSummarySpannable(Context context, MikChatVideoCallMessage message) {
        return new SpannableString("[videoCall]");
    }

    @Override
    public boolean showBubble() {
        return false;
    }
}
