package io.rong.imkit.conversation.extension;

import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.core.content.res.ResourcesCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;

import com.heart.heartmerge.R;

import java.util.Iterator;
import java.util.List;

import io.rong.common.rlog.RLog;
import io.rong.imkit.IMCenter;
import io.rong.imkit.config.RongConfigCenter;
import io.rong.imkit.conversation.extension.component.plugin.IPluginModule;
import io.rong.imkit.conversation.extension.component.plugin.IPluginRequestPermissionResultCallback;
import io.rong.imkit.manager.MySendImageManager;
import io.rong.imkit.manager.SendMediaManager;
import io.rong.imkit.picture.PictureSelector;
import io.rong.imkit.picture.config.PictureMimeType;
import io.rong.imkit.picture.entity.LocalMedia;
import io.rong.imkit.utils.PermissionCheckUtil;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.ConversationIdentifier;

/**
 * Author:Lxf
 * Create on:2024/12/2
 * Description:
 */
public class MyImagePluginCopy implements IPluginModule, IPluginRequestPermissionResultCallback {
    private static final String TAG = "ImagePlugin";
    ConversationIdentifier conversationIdentifier;
    private int mRequestCode = -1;

    public MyImagePluginCopy() {
    }

    public Drawable obtainDrawable(Context context) {
        return ResourcesCompat.getDrawable(context.getResources(), R.mipmap.ic_message_image, null);
    }

    public String obtainTitle(Context context) {
        return context.getString(R.string.rc_ext_plugin_image);
    }

    public void onClick(Fragment currentFragment, RongExtension extension, int index) {
        if (extension == null) {
            RLog.e("ImagePlugin", "onClick extension null");
        } else {
            this.conversationIdentifier = extension.getConversationIdentifier();
            this.mRequestCode = (index + 1 << 8) + 188;
            FragmentActivity activity = currentFragment.getActivity();
            if (activity != null && !activity.isDestroyed() && !activity.isFinishing()) {
                if (PermissionCheckUtil.checkMediaStoragePermissions(currentFragment.getContext())) {
                    this.openPictureSelector(currentFragment);
                } else {
                    String[] permissions = PermissionCheckUtil.getMediaStoragePermissions(currentFragment.getContext());
                    extension.requestPermissionForPluginResult(permissions, 255, this);
                }

            } else {
                RLog.e("ImagePlugin", "onClick activity null");
            }
        }
    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (resultCode == -1) {
            if (this.conversationIdentifier == null) {
                RLog.e("ImagePlugin", "onActivityResult conversationIdentifier is null, requestCode=" + requestCode + ",resultCode=" + resultCode);
                return;
            }

            List<LocalMedia> selectList = PictureSelector.obtainMultipleResult(data);
            sendImage(selectList);
//            if (selectList != null && selectList.size() > 0) {
//                boolean sendOrigin = ((LocalMedia) selectList.get(0)).isOriginal();
//                Iterator var6 = selectList.iterator();

//                while (var6.hasNext()) {
//                    LocalMedia item = (LocalMedia) var6.next();
//                    String mimeType = item.getMimeType();
//                    if (mimeType.startsWith("image")) {
//                        MySendImageManager.getInstance().sendImage(this.conversationIdentifier, item, sendOrigin);
//                        if (this.conversationIdentifier.getType().equals(Conversation.ConversationType.PRIVATE)) {
//                            RongIMClient.getInstance().sendTypingStatus(this.conversationIdentifier.getType(), this.conversationIdentifier.getTargetId(), "RC:ImgMsg");
//                        }
//                    } else if (mimeType.startsWith("video")) {
//                        Uri path = Uri.parse(item.getPath());
//                        if (TextUtils.isEmpty(path.getScheme())) {
//                            path = Uri.parse("file://" + item.getPath());
//                        }
//
//                        SendMediaManager.getInstance().sendMedia(IMCenter.getInstance().getContext(), this.conversationIdentifier, path, item.getDuration());
//                        if (this.conversationIdentifier.getType().equals(Conversation.ConversationType.PRIVATE)) {
//                            RongIMClient.getInstance().sendTypingStatus(this.conversationIdentifier.getType(), this.conversationIdentifier.getTargetId(), "RC:SightMsg");
//                        }
//                    }
//                }
//            }
        }

    }

    public void sendImage(List<LocalMedia> selectList){
        if (selectList != null && !selectList.isEmpty()) {
            boolean sendOrigin = ((LocalMedia) selectList.get(0)).isOriginal();
            Iterator var6 = selectList.iterator();

            while (var6.hasNext()) {
                LocalMedia item = (LocalMedia) var6.next();
                String mimeType = item.getMimeType();
                if (mimeType.startsWith("image")) {
                    MySendImageManager.getInstance().sendImage(this.conversationIdentifier, item, sendOrigin);
                    if (this.conversationIdentifier.getType().equals(Conversation.ConversationType.PRIVATE)) {
                        RongIMClient.getInstance().sendTypingStatus(this.conversationIdentifier.getType(), this.conversationIdentifier.getTargetId(), "RC:ImgMsg");
                    }
                } else if (mimeType.startsWith("video")) {
                    Uri path = Uri.parse(item.getPath());
                    if (TextUtils.isEmpty(path.getScheme())) {
                        path = Uri.parse("file://" + item.getPath());
                    }

                    SendMediaManager.getInstance().sendMedia(IMCenter.getInstance().getContext(), this.conversationIdentifier, path, item.getDuration());
                    if (this.conversationIdentifier.getType().equals(Conversation.ConversationType.PRIVATE)) {
                        RongIMClient.getInstance().sendTypingStatus(this.conversationIdentifier.getType(), this.conversationIdentifier.getTargetId(), "RC:SightMsg");
                    }
                }
            }
        }
    }
    public boolean onRequestPermissionResult(Fragment fragment, RongExtension extension, int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if (PermissionCheckUtil.checkPermissions(fragment.getActivity(), permissions)) {
            if (requestCode != -1) {
                this.openPictureSelector(fragment);
            }
        } else if (fragment.getActivity() != null) {
            PermissionCheckUtil.showRequestPermissionFailedAlter(fragment.getContext(), permissions, grantResults);
        }

        return true;
    }

    private void openPictureSelector(Fragment currentFragment) {
        PictureSelector.create(currentFragment).openGallery(RongConfigCenter.conversationConfig().rc_media_selector_contain_video ? PictureMimeType.ofAll() : PictureMimeType.ofImage()).loadImageEngine(RongConfigCenter.featureConfig().getKitImageEngine()).setRequestedOrientation(1).videoDurationLimit(RongIMClient.getInstance().getVideoLimitTime()).gifSizeLimit(RongIMClient.getInstance().getGIFLimitSize() * 1024).maxSelectNum(9).imageSpanCount(3).isGif(true).forResult(this.mRequestCode);
    }
}