package io.rong.imkit.conversation.extension

import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.Fragment
import com.heart.heartmerge.R
import com.heart.heartmerge.ui.activities.message.MyRongConversationActivity
import io.rong.imkit.conversation.extension.component.plugin.IPluginModule

/**
 * Author:Lxf
 * Create on:2024/8/6
 * Description:
 */
class MyVideoPlugin : IPluginModule {
    override fun obtainDrawable(context: Context): Drawable? = ResourcesCompat.getDrawable(context.resources, R.mipmap.ic_message_video, null)

    override fun obtainTitle(context: Context): String = context.getString(R.string.label_video)

    override fun onClick(currentFragment: Fragment, extension: RongExtension, index: Int) {
//        if (MMKVDataRep.userInfo.isVIP) {
            currentFragment.activity?.let {
                val myRongConversationActivity = it as MyRongConversationActivity
                myRongConversationActivity.startVideo()
            }
//        } else {
//            currentFragment.activity?.let {
//                showMembershipSubscribePopup(it) {}
//            }
//        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent) {
    }
}