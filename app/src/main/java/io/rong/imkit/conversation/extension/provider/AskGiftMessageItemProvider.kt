package io.rong.imkit.conversation.extension.provider

import android.content.Context
import android.text.Spannable
import android.text.SpannableString
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import coil.load
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.GiftItemBean
import com.heart.heartmerge.ui.activities.message.MyRongConversationActivity
import io.rong.imkit.conversation.extension.parsemessage.MikChatAskGiftMessage
import io.rong.imkit.conversation.messgelist.provider.BaseMessageItemProvider
import io.rong.imkit.model.UiMessage
import io.rong.imkit.widget.adapter.IViewProviderListener
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imlib.model.Message
import io.rong.imlib.model.MessageContent

/**
 * Author:Lxf
 * Create on:2024/8/19
 * Description:
 */
class AskGiftMessageItemProvider : BaseMessageItemProvider<MikChatAskGiftMessage>() {
    override fun getSummarySpannable(context: Context?, t: MikChatAskGiftMessage?): Spannable {
        return SpannableString("[Gift]")
    }

    override fun onCreateMessageContentViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view: View =
            LayoutInflater.from(parent.context)
                .inflate(R.layout.rc_ask_gift_text_message_item, parent, false)
        return ViewHolder(parent.context, view)
    }

    override fun isMessageViewType(messageContent: MessageContent): Boolean {
        return messageContent is MikChatAskGiftMessage && !messageContent.isDestruct()
    }

    override fun onItemClick(
        holder: ViewHolder?,
        t: MikChatAskGiftMessage?,
        uiMessage: UiMessage?,
        position: Int,
        list: MutableList<UiMessage>?,
        listener: IViewProviderListener<UiMessage>?
    ): Boolean {
        return false
    }

    override fun bindMessageContentViewHolder(
        holder: ViewHolder,
        parentHolder: ViewHolder,
        message: MikChatAskGiftMessage,
        uiMessage: UiMessage,
        position: Int,
        list: MutableList<UiMessage>,
        listener: IViewProviderListener<UiMessage>
    ) {
        holder.apply {
            getView<TextView>(R.id.giftName).text = message.showName + "X1"
            getView<ImageView>(R.id.gift).load(message.icon)
            val isSender = uiMessage.message.messageDirection == Message.MessageDirection.SEND
            setBackgroundRes(R.id.CL, if (isSender) R.drawable.ic_gift_right_bg else R.drawable.ic_gift_left_bg)
            if (message.id.isNullOrEmpty()) {
                getView<View>(R.id.fast_give).visibility = View.GONE
            } else {
                getView<View>(R.id.fast_give).visibility = View.VISIBLE
                getView<View>(R.id.fast_give).setOnClickListener {
                    if (holder.context is MyRongConversationActivity) {
                        val conversationActivity = holder.context as MyRongConversationActivity
                        val giftItemBean = GiftItemBean(
                            id = message.id,
                            showName = message.showName,
                            coin = message.coin.toInt(),
                            icon = message.icon,
                            giftSvgaUrl = message.svga_url
                        )
                        conversationActivity.sendGift(giftItemBean)
                    }
                }
            }

        }

    }

    override fun showBubble(): Boolean {
        return false
    }
}