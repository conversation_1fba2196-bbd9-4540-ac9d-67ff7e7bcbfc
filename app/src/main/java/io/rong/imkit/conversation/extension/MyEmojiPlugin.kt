package io.rong.imkit.conversation.extension

import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.Fragment
import com.heart.heartmerge.R
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.popup.showMembershipSubscribePopup
import com.heart.heartmerge.ui.activities.message.MyConversationFragment
import com.heart.heartmerge.utils.Constants
import io.rong.imkit.conversation.extension.component.plugin.IPluginModule

/**
 * Author:Lxf
 * Create on:2024/8/6
 * Description:
 */
class MyEmojiPlugin : IPluginModule {
    override fun obtainDrawable(context: Context): Drawable? =
        ResourcesCompat.getDrawable(context.resources, R.mipmap.ic_emoji, null)

    override fun obtainTitle(context: Context): String = context.getString(R.string.label_emoji)

    override fun onClick(currentFragment: Fragment, extension: RongExtension, index: Int) {
        val fragment: MyConversationFragment = currentFragment as MyConversationFragment
        if (MMKVDataRep.userInfo.isVIP || fragment.mTargetId == Constants.RONG_YUN_ID_CUSTOM_SERVICE) {
            val tmpExt: MyRongExtension = extension as MyRongExtension
            tmpExt.myInputPanel.changeToEmoji()
        } else {
//            currentFragment.activity?.let {
//                showMembershipSubscribePopup(it)
//            }

            fragment.startVipTipAnimation()
        }

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent) {
    }
}