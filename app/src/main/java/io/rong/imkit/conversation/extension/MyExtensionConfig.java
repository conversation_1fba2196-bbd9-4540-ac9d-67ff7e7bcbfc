package io.rong.imkit.conversation.extension;

import com.heart.heartmerge.utils.Constants;

import java.util.List;
import java.util.ListIterator;

import io.rong.imkit.conversation.extension.component.plugin.FilePlugin;
import io.rong.imkit.conversation.extension.component.plugin.IPluginModule;
import io.rong.imkit.conversation.extension.component.plugin.ImagePlugin;
import io.rong.imlib.model.Conversation;

/**
 * Author:Lxf
 * Create on:2024/8/6
 * Description:
 */
public class MyExtensionConfig extends DefaultExtensionConfig {
    @Override
    public List<IPluginModule> getPluginModules(Conversation.ConversationType conversationType, String targetId) {
        List<IPluginModule> pluginModules = super.getPluginModules(conversationType, targetId);
        ListIterator<IPluginModule> iterator = pluginModules.listIterator();

        // 删除扩展项
        while (iterator.hasNext()) {
            IPluginModule integer = iterator.next();
            // 以删除 FilePlugin 为例
            if (integer instanceof FilePlugin) {
                iterator.remove();
            }
            if (integer instanceof ImagePlugin) {
                iterator.remove();
            }
        }

        // 增加扩展项, 以添加自定义插件 MyConnectionPlugin 为例
//        pluginModules.add(new MyVoicePlugin());
        pluginModules.add(new MyEmojiPlugin());
        pluginModules.add(new MyImagePlugin());
        if (!targetId.equals(Constants.RONG_YUN_ID_CUSTOM_SERVICE)) {
            pluginModules.add(new MyGiftPlugin());
            pluginModules.add(new MyVideoPlugin());
        }
        return pluginModules;
    }
}
