package io.rong.imkit.conversation.extension

import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.Fragment
import com.heart.heartmerge.R
import com.heart.heartmerge.manager.GiftManager
import com.heart.heartmerge.popup.showGiftPopup
import com.heart.heartmerge.ui.activities.message.MyRongConversationActivity
import com.lxj.xpopup.core.BasePopupView
import io.rong.imkit.conversation.extension.component.plugin.IPluginModule

/**
 * Author:Lxf
 * Create on:2024/8/6
 * Description:
 */
class MyGiftPlugin : IPluginModule {
    private var giftPopup: BasePopupView? = null
    override fun obtainDrawable(context: Context): Drawable? =
        ResourcesCompat.getDrawable(context.resources, R.mipmap.ic_message_gift, null)

    override fun obtainTitle(context: Context): String = context.getString(R.string.label_gift)

    override fun onClick(currentFragment: Fragment, extension: RongExtension, index: Int) {
//        if (MMKVDataRep.userInfo.isVIP) {
        currentFragment.activity?.let {
            giftPopup = giftPopup?.show() ?: showGiftPopup(
                it,
                (it as? MyRongConversationActivity)?.getTargetId()
            ) { giftBean ->
                val myRongConversationActivity = it as MyRongConversationActivity
                myRongConversationActivity.sendGift(giftBean)
                GiftManager.getInstance(it).playGiftAnimation(it, it, giftBean.giftSvgaUrl)
            }
        }
//        } else {
//            val fragment: MyConversationFragment = currentFragment as MyConversationFragment
//            fragment.startVipTipAnimation()
//        }

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent) {
    }
}