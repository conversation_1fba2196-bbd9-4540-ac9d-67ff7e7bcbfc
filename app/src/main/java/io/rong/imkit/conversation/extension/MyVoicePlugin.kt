package io.rong.imkit.conversation.extension

import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.Fragment
import com.heart.heartmerge.R
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.popup.showMembershipSubscribePopup
import com.heart.heartmerge.utils.PurchaseScene
import io.rong.imkit.conversation.extension.component.plugin.IPluginModule

/**
 * Author:Lxf
 * Create on:2024/8/6
 * Description:
 */
class MyVoicePlugin : IPluginModule {
    override fun obtainDrawable(context: Context): Drawable? = ResourcesCompat.getDrawable(context.resources, R.mipmap.ic_message_voice, null)

    override fun obtainTitle(context: Context): String = context.getString(R.string.label_voice)

    override fun onClick(currentFragment: Fragment, extension: RongExtension, index: Int) {
        if (MMKVDataRep.userInfo.isVIP) {
            val tmpExt: MyRongExtension = extension as MyRongExtension
            tmpExt.myInputPanel.changeToVoiceStyle()
        } else {
            currentFragment.activity?.let {
                showMembershipSubscribePopup(it, PurchaseScene.Voice)
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent) {
    }
}