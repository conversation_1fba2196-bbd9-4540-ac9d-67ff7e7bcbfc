package io.rong.imkit.manager;

import android.net.Uri;

import com.heart.heartmerge.R;
import com.heart.heartmerge.utils.AppUtil;
import com.heart.heartmerge.utils.RongMessageUtil;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import io.rong.common.rlog.RLog;
import io.rong.imkit.IMCenter;
import io.rong.imkit.feature.destruct.DestructManager;
import io.rong.imkit.picture.config.PictureMimeType;
import io.rong.imkit.picture.entity.LocalMedia;
import io.rong.imlib.IRongCallback;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.ConversationIdentifier;
import io.rong.imlib.model.Message;
import io.rong.imlib.model.MessageContent;
import io.rong.message.GIFMessage;
import io.rong.message.ImageMessage;

/**
 * Author:Lxf
 * Create on:2024/12/2
 * Description:
 */
public class MySendImageManager {
    private static final String TAG = "SendImageManager";
    private ExecutorService executorService;
    private MySendImageManager.UploadController uploadController;

    public static MySendImageManager getInstance() {
        return MySendImageManager.SingletonHolder.sInstance;
    }

    private MySendImageManager() {
        this.executorService = this.getExecutorService();
        this.uploadController = new MySendImageManager.UploadController();
    }

    public void sendImage(ConversationIdentifier conversationIdentifier, LocalMedia image, boolean isFull) {
        if (image.getPath() != null) {
            String mimeType = image.getMimeType();
            String path = image.getPath();
            if (!path.startsWith("content://") && !path.startsWith("file://")) {
                path = "file://" + path;
            }

            Uri uri = Uri.parse(path);
            MessageContent content;
            if (PictureMimeType.isGif(mimeType)) {
                content = GIFMessage.obtain(uri);
            } else {
                content = ImageMessage.obtain(uri, uri, isFull);
            }

            if (DestructManager.isActive() && content != null) {
                content.setDestruct(true);
                content.setDestructTime((long) DestructManager.IMAGE_DESTRUCT_TIME);
            }
            IMCenter.getInstance().insertOutgoingMessage(conversationIdentifier, Message.SentStatus.SENDING, content, System.currentTimeMillis(), new RongIMClient.ResultCallback<Message>() {
                public void onSuccess(Message message) {
                    message.setCanIncludeExpansion(true);
                    message.getContent().setUserInfo(RongMessageUtil.INSTANCE.getSelfUserInfo());
                    MySendImageManager.this.uploadController.execute(message);
                }

                public void onError(RongIMClient.ErrorCode errorCode) {
                }
            });
        }
    }

    public void cancelSendingImages(Conversation.ConversationType conversationType, String targetId) {
        RLog.d("SendImageManager", "cancelSendingImages");
        if (conversationType != null && targetId != null && this.uploadController != null) {
            this.uploadController.cancel(conversationType, targetId);
        }

    }

    public void cancelSendingImage(Conversation.ConversationType conversationType, String targetId, int messageId) {
        RLog.d("SendImageManager", "cancelSendingImages");
        if (conversationType != null && targetId != null && this.uploadController != null && messageId > 0) {
            this.uploadController.cancel(conversationType, targetId, messageId);
        }

    }

    private ExecutorService getExecutorService() {
        if (this.executorService == null) {
            this.executorService = new ThreadPoolExecutor(1, Integer.MAX_VALUE, 60L, TimeUnit.SECONDS, new SynchronousQueue(), this.threadFactory());
        }

        return this.executorService;
    }

    private ThreadFactory threadFactory() {
        return new ThreadFactory() {
            public Thread newThread(Runnable runnable) {
                Thread result = new Thread(runnable, "Rong SendMediaManager");
                result.setDaemon(false);
                return result;
            }
        };
    }

    private class UploadController implements Runnable {
        final List<Message> pendingMessages = new ArrayList();
        Message executingMessage;

        public UploadController() {
        }

        public void execute(Message message) {
            synchronized (this.pendingMessages) {
                this.pendingMessages.add(message);
                if (this.executingMessage == null) {
                    this.executingMessage = (Message) this.pendingMessages.remove(0);
                    MySendImageManager.this.executorService.submit(this);
                }

            }
        }

        public void cancel(Conversation.ConversationType conversationType, String targetId) {
            synchronized (this.pendingMessages) {
                Iterator<Message> it = this.pendingMessages.iterator();

                while (it.hasNext()) {
                    Message msg = (Message) it.next();
                    if (msg.getConversationType().equals(conversationType) && msg.getTargetId().equals(targetId)) {
                        it.remove();
                    }
                }

                if (this.pendingMessages.size() == 0) {
                    this.executingMessage = null;
                }

            }
        }

        public void cancel(Conversation.ConversationType conversationType, String targetId, int messageId) {
            synchronized (this.pendingMessages) {
                int count = this.pendingMessages.size();

                for (int i = 0; i < count; ++i) {
                    Message msg = (Message) this.pendingMessages.get(i);
                    if (msg.getConversationType().equals(conversationType) && msg.getTargetId().equals(targetId) && msg.getMessageId() == messageId) {
                        this.pendingMessages.remove(msg);
                        break;
                    }
                }

                if (this.pendingMessages.size() == 0) {
                    this.executingMessage = null;
                }

            }
        }

        private void polling() {
            synchronized (this.pendingMessages) {
                RLog.d("SendImageManager", "polling " + this.pendingMessages.size());
                if (this.pendingMessages.size() > 0) {
                    this.executingMessage = (Message) this.pendingMessages.remove(0);
                    MySendImageManager.this.executorService.submit(this);
                } else {
                    this.executingMessage = null;
                }

            }
        }

        public void run() {
            boolean isDestruct = false;
            if (this.executingMessage.getContent() != null) {
                isDestruct = this.executingMessage.getContent().isDestruct();
            }

            IMCenter.getInstance().sendMediaMessage(this.executingMessage, isDestruct ? IMCenter.getInstance().getContext().getString(R.string.rc_conversation_summary_content_burn) : null, (String) null, new IRongCallback.ISendMediaMessageCallback() {
                public void onAttached(Message message) {
                }

                public void onError(Message message, RongIMClient.ErrorCode code) {
                    MySendImageManager.UploadController.this.polling();
                }

                public void onSuccess(Message message) {
                    MySendImageManager.UploadController.this.polling();
                }

                public void onProgress(Message message, int progress) {
                }

                public void onCanceled(Message message) {
                }
            });
        }
    }

    static class SingletonHolder {
        static MySendImageManager sInstance = new MySendImageManager();

        SingletonHolder() {
        }
    }
}

