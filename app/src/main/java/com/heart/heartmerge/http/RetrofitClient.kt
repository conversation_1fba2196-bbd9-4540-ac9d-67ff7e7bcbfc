package com.heart.heartmerge.http

import com.bdc.android.library.http.CoreRetrofit
import com.heart.heartmerge.BuildConfig
import com.heart.heartmerge.http.interceptor.DetailedLogInterceptor
import com.heart.heartmerge.http.interceptor.XInterceptor
import com.heart.heartmerge.repo.service.AggregatedService
import okhttp3.Interceptor

object RetrofitClient : CoreRetrofit<AggregatedService>() {

    val aggregatedService by lazy {
        buildRetrofitService()
    }

    override fun getBaseUrl(): String = BuildConfig.HOST

    /**
     * 提供所有需要的拦截器
     * 按照执行顺序排列：参数拦截器 -> 详细日志拦截器
     */
    override fun getInterceptors(): List<Interceptor> = listOf(
        XInterceptor(),              // 参数拦截器（添加请求头、查询参数等）
        DetailedLogInterceptor()     // 详细日志拦截器（记录详细的请求响应信息）
    )

    /**
     * 保留默认的日志拦截器
     */
    override fun shouldAddDefaultLogInterceptor(): Boolean = false
}