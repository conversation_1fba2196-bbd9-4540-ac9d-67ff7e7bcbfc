package com.heart.heartmerge.http.interceptor

import android.os.Build
import android.text.TextUtils
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.http.GeneralResult
import com.bdc.android.library.http.interceptor.CoreParameterInterceptor
import com.bdc.android.library.ktnet.utils.NetKey
import com.bdc.android.library.utils.ActivityManager
import com.bdc.android.library.utils.AppManager
import com.heart.heartmerge.BuildConfig
import com.heart.heartmerge.http.ResultX
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.mmkv.MMKVBaseDataRep
import com.heart.heartmerge.mmkv.MMKVGuiYinDataRep
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.RequestParametersBuilder
import com.heart.heartmerge.utils.UniqueIDUtil
import kotlinx.coroutines.launch
import okhttp3.Request
import java.net.HttpURLConnection.HTTP_BAD_REQUEST
import java.net.HttpURLConnection.HTTP_INTERNAL_ERROR

class XInterceptor : CoreParameterInterceptor<GeneralResult<Any>>() {

    override fun getRequestBuilder(request: Request): Request.Builder {
        return request.newBuilder().apply {
            runCatching {
                // 添加 Headers
                if (TextUtils.isEmpty(MMKVBaseDataRep.token)) {
                    removeHeader(Constants.TOKEN)
                } else {
                    header(
                        Constants.TOKEN, MMKVBaseDataRep.token ?: ""
                    )
                }
                header(NetKey.KEY_VERSION_CODE, BuildConfig.VERSION_CODE.toString())
                header(NetKey.KEY_VERSION_NAME, BuildConfig.VERSION_NAME)
                header(NetKey.KEY_PKG, BuildConfig.APPLICATION_ID)
                header(NetKey.KEY_MODEL, Build.MODEL)
                header(NetKey.KEY_DEVICEID, UniqueIDUtil.getUniqueID(AppManager.getApplication()))
                header(NetKey.KEY_DEVICETYPE, "android")

                header(NetKey.KEY_FIREBASE_APPID, MMKVGuiYinDataRep.fetchFirebaseAppId())
                header(NetKey.KEY_APPSFLYER_APPID, MMKVGuiYinDataRep.fetchAppsflyerAppId())
                header(NetKey.KEY_GOOGLE_GAID, MMKVGuiYinDataRep.fetchGoogleAdvertisingId())

                // 添加固定的 Query 参数到 URL
                val newUrl = request.url.newBuilder().apply {
                    RequestParametersBuilder.build().forEach { key, value ->
                        addQueryParameter(key, value)
                    }
//                    addQueryParameter(NetKey.KEY_OS_TYPE, "1")
//                    addQueryParameter(
//                        NetKey.KEY_DEVICE_ID, UniqueIDUtil.getUniqueID(AppManager.getApplication())
//                    )
//                    addQueryParameter(NetKey.KEY_DEVICE_MODEL, Build.MODEL)
//                    addQueryParameter(NetKey.KEY_OS_VERSION, Build.VERSION.RELEASE)
//                    addQueryParameter(
//                        NetKey.KEY_PACKAGE_NAME, BuildConfig.APPLICATION_ID
//                    )
//                    addQueryParameter(NetKey.KEY_VERSION, "${BuildConfig.VERSION_CODE}")
//                    addQueryParameter(
//                        NetKey.KEY_LANG, Locale.getDefault().language
//                    )
//                    addQueryParameter(
//                        NetKey.KEY_APP_ID, MMKVDataRep.userInfo.app_id.toString()
//                    )
//                    if (MMKVDataRep.userInfo.id.isNotEmpty()) {
//                        addQueryParameter(
//                            "uid", MMKVDataRep.userInfo.id
//                        )
//                    }
                }.build()

                // 将修改后的 URL 应用到请求中
                url(newUrl)
            }.onFailure {
                LogX.e(Log.getStackTraceString(it))
            }
        }
    }

    override fun getResponseClass(): GeneralResult<Any> = ResultX()

    override fun handleResponse(data: GeneralResult<Any>) {
        when (data.code) {
            Constants.ErrorCode.USER_NOT_EXIST, Constants.ErrorCode.TOKEN_EXPIRED -> {
                ActivityManager.current?.let {
                    it as AppCompatActivity
                }?.let {
                    it.lifecycleScope.launch {
                        FlowBus.with<Int>(Constants.TOKEN_EXPIRED)
                            .post(Constants.ErrorCode.TOKEN_EXPIRED)
                    }
                }
            }

            HTTP_BAD_REQUEST, HTTP_INTERNAL_ERROR -> {
//                ActivityManager.current?.let {
//                    it as? AppCompatActivity
//                }?.let {
//                    it.runOnUiThread {
//                        ToastUtil.show(data.msg)
//                    }
//                }
            }
        }
    }
}