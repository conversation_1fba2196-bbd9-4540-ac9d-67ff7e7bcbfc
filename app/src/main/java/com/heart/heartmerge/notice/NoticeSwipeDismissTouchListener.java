package com.heart.heartmerge.notice;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.os.Build;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;

import androidx.annotation.RequiresApi;


public class NoticeSwipeDismissTouchListener implements View.OnTouchListener {

    private View mView;
    private DismissCallbacks mCallbacks;

    private float mDownX;
    private float mDownY;
    private float mTranslationY;
    private boolean mSwiping;
    private int mSwipingSlop;
    private int mViewHeight = 1;
    private int mSlop;
    private int mMinFlingVelocity;
    private long mAnimationTime;
    private VelocityTracker mVelocityTracker;

    public NoticeSwipeDismissTouchListener(View view, DismissCallbacks callbacks) {
        this.mView = view;
        this.mCallbacks = callbacks;

        ViewConfiguration vc = ViewConfiguration.get(mView.getContext());
        mSlop = vc.getScaledTouchSlop();
        mMinFlingVelocity = vc.getScaledMinimumFlingVelocity() * 16;
        mAnimationTime = mView.getContext().getResources().getInteger(android.R.integer.config_shortAnimTime);
    }

    @Override
    public boolean onTouch(View view, MotionEvent motionEvent) {
        // offset because the view is translated during swipe
        motionEvent.offsetLocation(0f, mTranslationY);

//        LogUtils.debug("Touch",motionEvent.getActionMasked());
        if (mViewHeight < 2) {
            mViewHeight = mView.getHeight();
        }

        switch (motionEvent.getActionMasked()) {
            case MotionEvent.ACTION_DOWN:
                mDownX = motionEvent.getRawX();
                mDownY = motionEvent.getRawY();
                if (mCallbacks.canDismiss()) {
                    mVelocityTracker = VelocityTracker.obtain();
                    mVelocityTracker.addMovement(motionEvent);
                }
                mCallbacks.onTouch(view, true);
                return false;
            case MotionEvent.ACTION_UP:
                float deltaY = motionEvent.getRawY() - mDownY;
                mVelocityTracker.addMovement(motionEvent);
                mVelocityTracker.computeCurrentVelocity(1000);
                float velocityY = mVelocityTracker.getYVelocity();
                float absVelocityY = Math.abs(velocityY);
                float absVelocityX = Math.abs(mVelocityTracker.getXVelocity());
                boolean dismiss = false;
                boolean dismissRight = false;
                if (Math.abs(deltaY) > mViewHeight / 2 && mSwiping) {
                    dismiss = true;
                    dismissRight = deltaY > 0;
                } else if (mMinFlingVelocity <= absVelocityX && absVelocityY < absVelocityX && mSwiping) {
                    // dismiss only if flinging in the same direction as dragging
                    dismiss = velocityY < 0 == absVelocityX < 0;
                    dismissRight = mVelocityTracker.getXVelocity() > 0;
                }
                if (dismiss) {
                    // dismiss
                    mView.animate()
                            .translationY(dismissRight ? mViewHeight : -mViewHeight)
                            .alpha(0f)
                            .setDuration(mAnimationTime)
                            .setListener(new Animator.AnimatorListener() {
                                @Override
                                public void onAnimationStart(Animator animation) {

                                }

                                @Override
                                public void onAnimationEnd(Animator animation) {
                                    performDismiss();
                                }

                                @Override
                                public void onAnimationCancel(Animator animation) {

                                }

                                @Override
                                public void onAnimationRepeat(Animator animation) {

                                }
                            });
                } else if (mSwiping) {
                    // cancel
                    mView.animate()
                            .translationY(0f)
                            .alpha(1f)
                            .setDuration(mAnimationTime)
                            .setListener(null);
                    mCallbacks.onTouch(view, false);
                }
                mVelocityTracker.recycle();
                mVelocityTracker = null;
                mTranslationY = 0f;
                mDownX = 0f;
                mDownY = 0f;
                mSwiping = false;
                break;
            case MotionEvent.ACTION_CANCEL:
                mView.animate()
                        .translationX(0f)
                        .alpha(1f)
                        .setDuration(mAnimationTime)
                        .setListener(null);
                mVelocityTracker.recycle();
                mVelocityTracker = null;
                mTranslationY = 0f;
                mDownX = 0f;
                mDownY = 0f;
                mSwiping = false;
                break;

            case MotionEvent.ACTION_MOVE:
                mVelocityTracker.addMovement(motionEvent);
                float deltaX2 = motionEvent.getRawX() - mDownX;
                float deltaY2 = motionEvent.getRawY() - mDownY;
                //左右滑动
                if (Math.abs(deltaY2) > mSlop && Math.abs(deltaX2) < Math.abs(deltaY2) / 2) {
                    mSwiping = true;
                    mSwipingSlop = deltaY2 > 0 ? mSlop : -mSlop;
                    mView.getParent().requestDisallowInterceptTouchEvent(true);

                    // Cancel listview's touch
                    MotionEvent cancelEvent = MotionEvent.obtain(motionEvent);
                    cancelEvent.setAction(MotionEvent.ACTION_CANCEL | motionEvent.getActionIndex() << MotionEvent.ACTION_POINTER_INDEX_SHIFT);
                    mView.onTouchEvent(cancelEvent);
                    cancelEvent.recycle();
                    if (mSwiping) {
                        mTranslationY = deltaY2;
                        mView.setTranslationY(deltaY2 - mSwipingSlop);
                        // TODO: use an ease-out interpolator or such
                        mView.setAlpha(Math.max(0f, Math.min(1f,
                                1f - 2f * Math.abs(deltaY2) / mViewHeight)));
                        return true;
                    }
                }
                return false;
        }
        return false;
    }

    @RequiresApi(api = Build.VERSION_CODES.HONEYCOMB)
    private void performDismiss() {
        // Animate the dismissed view to zero-height and then fire the dismiss callback.
        // This triggers layout on each animation frame; in the future we may want to do something
        // smarter and more performant.

        ViewGroup.LayoutParams lp = mView.getLayoutParams();
        int originalHeight = mView.getHeight();

        ValueAnimator animator = ValueAnimator.ofInt(originalHeight, 1).setDuration(mAnimationTime);

        animator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                mCallbacks.onDismiss(mView);
                // Reset view presentation
                mView.setAlpha(1f);
                mView.setTranslationX(0f);
                lp.height = originalHeight;
                mView.setLayoutParams(lp);
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });

        animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                lp.height = (int) animation.getAnimatedValue();
                mView.setLayoutParams(lp);
            }
        });


        animator.start();
    }

    /**
     * The callback interface used by [SwipeDismissTouchListener] to inform its client
     * about a successful dismissal of the view for which it was created.
     */
    public interface DismissCallbacks {
        /**
         * Called to determine whether the view can be dismissed.
         *
         * @return boolean The view can dismiss.
         */
        boolean canDismiss();

        /**
         * Called when the user has indicated they she would like to dismiss the view.
         *
         * @param view The originating [View]
         */
        void onDismiss(View view);

        /**
         * Called when the user touches the view or release the view.
         *
         * @param view  The originating [View]
         * @param touch The view is being touched.
         */
        void onTouch(View view, Boolean touch);
    }
}
