package com.heart.heartmerge.notice

import android.graphics.PixelFormat
import android.view.Gravity
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.LifecycleObserver
import com.bdc.android.library.extension.click
import com.heart.heartmerge.beans.ChatTipBean
import io.rong.imlib.RongIMClient

class ChatNoticeManager private constructor() : LifecycleObserver {
    private var chatTipView: ChatNoticeView? = null
    private var bean: ChatTipBean? = null
    private var windowManager: WindowManager? = null

    companion object {
        val instance: ChatNoticeManager by lazy { ChatNoticeManager() }
    }

    fun show(activity: AppCompatActivity, chatTipView: ChatNoticeView?, bean: ChatTipBean?) {
        if (activity.isFinishing || activity.isDestroyed || chatTipView == null || bean == null) {
            return
        }

        windowManager?.let {
            try {
                it.removeViewImmediate(this.chatTipView)
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                this.chatTipView = null
            }
        }

        this.chatTipView = chatTipView
        this.bean = bean
        windowManager = activity.windowManager
        activity.lifecycle.addObserver(this)

        try {
            windowManager?.addView(chatTipView, initLayoutParams())
        } catch (e: Exception) {
            this.chatTipView = null
        }

        // 设置延迟关闭
        chatTipView.postDelayed({ hideAndDestroy(false) }, ChatNoticeView.SHOW_TIME)
        chatTipView.mFlContainer?.click{
            hideAndDestroy(false)
            val hashMap = hashMapOf<String, String>().apply {
                put("headFileName", bean.headFileName)
                put("nikeName", bean.nickName)
                bean.id.let {
                    put("id", it)
//                    RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_TO_CONVERSATION, GsonUtil.GsonString(this))
                }
            }
        }
    }

    private fun initLayoutParams(): WindowManager.LayoutParams {
        return WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            0, 0,
            PixelFormat.TRANSPARENT
        ).apply {
            height = WindowManager.LayoutParams.WRAP_CONTENT
            gravity = Gravity.TOP
            flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                    WindowManager.LayoutParams.FLAG_LAYOUT_INSET_DECOR
            type = WindowManager.LayoutParams.TYPE_APPLICATION_SUB_PANEL
        }
    }

    /**
     * 隐藏并销毁
     */
    fun hideAndDestroy(removeNow: Boolean) {
        chatTipView?.hide(removeNow)
        chatTipView = null
        windowManager = null
    }
}