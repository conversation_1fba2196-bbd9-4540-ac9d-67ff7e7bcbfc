package com.heart.heartmerge.notice

import android.animation.ObjectAnimator
import android.content.Context
import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.animation.AnticipateOvershootInterpolator
import android.view.animation.OvershootInterpolator
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import com.heart.heartmerge.R
import com.heart.heartmerge.notice.NoticeSwipeDismissTouchListener.DismissCallbacks
import com.heart.heartmerge.ui.widget.RoundImageView

/**
 * 私聊顶部提醒
 */
class ChatNoticeView(context: Context) : FrameLayout(context), DismissCallbacks {
    var mIvPic: RoundImageView? = null
    var mTvTitle: TextView? = null
    var mTvContent: TextView? = null
    var mFlContainer: FrameLayout? = null
    var mLlCallBack: LinearLayout? = null

    var userId: String? = null

    private var type = 0

    init {
        initView(context)
    }

    private fun initView(context: Context?) {
        val view: View = LayoutInflater.from(context).inflate(R.layout.layout_chat_notice, this, false)
        addView(view)

        mIvPic = view.findViewById(R.id.iv_head_id)
        mTvTitle = view.findViewById<TextView>(R.id.tv_title_id)
        mTvContent = view.findViewById<TextView>(R.id.tv_content_id)
        mFlContainer = view.findViewById<FrameLayout>(R.id.id_fl_container_id)

        mFlContainer?.setOnTouchListener(NoticeSwipeDismissTouchListener(mFlContainer, this))
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        //出现动画
        val animEnter: ObjectAnimator = ObjectAnimator.ofFloat(mFlContainer, "translationY", -measuredHeight.toFloat(), 0f)
        animEnter.interpolator = OvershootInterpolator()
        animEnter.setDuration(ANIMATION_DURATION)
        animEnter.start()
    }

    fun hide(removeNow: Boolean) {
        if (!isAttachedToWindow()) {
            return
        }
        val windowManager: WindowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        if (removeNow) {
            if (isAttachedToWindow) {
                windowManager.removeViewImmediate(this)
            }
            return
        }
        mFlContainer?.isClickable = false
        val anim: ObjectAnimator = ObjectAnimator.ofFloat(mFlContainer, "translationY", 0f, -measuredHeight.toFloat())
        anim.interpolator = AnticipateOvershootInterpolator()
        anim.setDuration(ANIMATION_DURATION)
        anim.start()
        Handler().postDelayed(object : Runnable {
            override fun run() {
                if (isAttachedToWindow) {
                    windowManager.removeViewImmediate(this@ChatNoticeView)
                }
            }
        }, ANIMATION_DURATION)
    }

    fun setTitle(title: String?) {
        mTvTitle!!.setText(title)
    }

    fun setContent(content: String?) {
        mTvContent!!.setText(content)
    }

    /**
     * Called to determine whether the view can be dismissed.
     *
     * @return boolean The view can dismiss.
     */
    override fun canDismiss(): Boolean {
        return true
    }

    /**
     * Called when the user has indicated they she would like to dismiss the view.
     *
     * @param view The originating [View]
     */
    override fun onDismiss(view: View?) {
        hide(true)
    }

    /**
     * Called when the user touches the view or release the view.
     *
     * @param view  The originating [View]
     * @param touch The view is being touched.
     */
    override fun onTouch(view: View?, touch: Boolean?) {
    }

    companion object {
        private const val ANIMATION_DURATION: Long = 500

        //展示时间
        const val SHOW_TIME: Long = 3000
    }
}
