package com.heart.heartmerge.socket

import com.google.gson.Gson
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.mmkv.MMKVBaseDataRep
import com.heart.heartmerge.mmkv.MMKVDataRep

object WebSocketMessageSender {

    private val gson = Gson()
    private const val TAG = "WebSocketMessageSender"

    /**
     * 通用方法，用于发送 WebSocket 消息
     * @param cmd 命令类型
     * @param extend 扩展数据，可以是 Map 或其他数据类
     */
    private fun sendWebSocketMessage(cmd: String, extend: Map<String, Any?>?) {
        if (MMKVDataRep.userInfo == null || MMKVDataRep.userInfo.id.isNullOrEmpty()) return
        val ownerUid = MMKVDataRep.userInfo.id.toLong()
        val token = MMKVBaseDataRep.token ?: ""
        val seq = System.currentTimeMillis().toString()

        val message = mutableMapOf<String, Any?>(
            "cmd" to cmd,
            "owner" to ownerUid,
            "token" to token,
            "seq" to seq
        )
        extend?.let {
            message["extend"] = gson.toJson(it) // 将 extend 对象序列化为 JSON 字符串
        }

        val jsonMessage = gson.toJson(message)
        WebSocketManager.getInstance().sendMessage(jsonMessage)
        LogX.d(TAG, "发送 $cmd 消息: $jsonMessage")
    }

    /**
     * 发送通话拒绝消息
     * @param peerUID 主叫的uid
     * @param callID 收到邀请的通话ID
     * @param callIDOther 用户主动点击拒绝则传收到邀请的频道id，如果被动拒绝则是其他频道或者其他邀请的频道id
     * @param reasonType 拒绝原因类型
     * @param reason 拒绝原因描述
     */
    fun sendCallRefuseMessage(
        peerUID: Long,
        callID: Int,
        callIDOther: Int,
        reasonType: Int,
        reason: String
    ) {
        val extendData = mapOf(
            "peerUID" to peerUID,
            "callID" to callID,
            "callIDOther" to callIDOther,
            "reasonType" to reasonType,
            "reason" to reason
        )
        sendWebSocketMessage("call_refuse", extendData)
    }

    /**
     * 发送进入频道消息
     * @param callID 通话id
     */
    fun sendCallJoinRoomMessage(callID: Int) {
        val extendData = mapOf(
            "callID" to callID
        )
        sendWebSocketMessage("call_join_room", extendData)
    }

    /**
     * 发送心跳包消息
     * @param callID 正在通话中的通话id，不在通话中传0
     * @param lastActionAt 上次app操作的毫秒时间戳
     */
    fun sendHeartbeatMessage(callID: Int, lastActionAt: String) {
        val extendData = mapOf(
            "callID" to callID,
            "last_action_at" to lastActionAt
        )
        sendWebSocketMessage("heartbeat", extendData)
    }

    /**
     * 发送通话中异常上报消息
     * @param callID 正在通话中的通话id，不在通话中传0
     * @param errMsg 错误信息
     */
    fun sendCallingExceptionMessage(callID: Int, errMsg: String) {
        val extendData = mapOf(
            "callID" to callID,
            "errMsg" to errMsg
        )
        sendWebSocketMessage("calling_exception", extendData)
    }

    /**
     * 发送挂断消息
     * @param faceDuration 露脸时长
     * @param faceTargetDuration 目标时长内的露脸时长
     * @param callID 正在通话中的通话id，不在通话中传0
     * @param reasonType 挂断原因类型
     */
    fun sendCallHangUpMessage(
        faceDuration: Int,
        faceTargetDuration: Int,
        callID: Int,
        reasonType: Int
    ) {
        val extendData = mapOf(
            "face_duration" to faceDuration,
            "face_target_duration" to faceTargetDuration,
            "callID" to callID,
            "reasonType" to reasonType
        )
        sendWebSocketMessage("call_hang_up", extendData)
    }

    /**
     * 发送通话过程中待机消息
     * @param callID 正在通话中的通话id，不在通话中传0
     * @param statType 状态类型 (1待机 2取消待机)
     */
    fun sendCallStatingUpdateMessage(callID: Int, statType: Int) {
        val extendData = mapOf(
            "callID" to callID,
            "statType" to statType
        )
        sendWebSocketMessage("call_stating_update", extendData)
    }

    /**
     * 发送主叫取消通话邀请消息
     * @param peerUID 被叫的uid
     * @param callID 通话id
     * @param reasonType 取消原因类型 (1 用户主动取消 2 主播未接听取消)
     */
    fun sendCallInviteCancelMessage(peerUID: Int, callID: Int, reasonType: Int) {
        val extendData = mapOf(
            "peerUID" to peerUID,
            "callID" to callID,
            "reasonType" to reasonType
        )
        sendWebSocketMessage("call_invite_cancel", extendData)
    }

    /**
     * 发送展示支付渠道弹窗或者下单
     */
    fun sendUserPayingMessage() {
        val extendData = emptyMap<String, Any?>()
        sendWebSocketMessage("user_paying", extendData)
    }
}