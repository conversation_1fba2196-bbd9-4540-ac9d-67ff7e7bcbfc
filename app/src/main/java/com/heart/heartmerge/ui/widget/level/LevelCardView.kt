package com.heart.heartmerge.ui.widget.level

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.extension.setTextCompatColor
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.ViewLevelCardBinding
import com.heart.heartmerge.mmkv.MMKVDataRep

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/8 16:07
 * @description :等级卡片View
 */
class LevelCardView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    val binding: ViewLevelCardBinding =
        ViewLevelCardBinding.inflate(LayoutInflater.from(this.context), this, true)

    var current: Int = 0
        set(value) {
            field = value.coerceAtMost(LevelConfig.LEVEL_MAX)

            val levelRange = LevelConfig.ranges.find { ints -> field in ints }
                ?.let { ints -> ints.start..ints.endInclusive }
                ?: run { LevelConfig.LEVEL_MAX..Int.MAX_VALUE }

            LevelConfig.getStyleForLevel(field)?.cardRes?.let { style ->
                binding.tvLevel.setTextCompatColor(style.first)
                binding.tvNextLevelValue.setTextCompatColor(style.first)
                binding.ivLevelLabel.setImageResource(style.second)
                binding.ivLevel.setImageResource(style.third)
                binding.progressBar.progressDrawable =
                    ContextCompat.getDrawable(this.context, style.fourth)
                binding.tvNotLocked.setTextCompatColor(style.first)
            }

            val userLevelConfig = MMKVDataRep.userInfo.level_config
            val userLevel = userLevelConfig?.level ?: 0
            binding.tvLevel.apply {
                binding.tvLevel.text =
                    if (userLevel in levelRange) "Lv$userLevel" else "Lv${levelRange.start}-${levelRange.endInclusive}"
            }
            binding.tvNotLocked.apply {
                makeVisible(userLevel !in levelRange)
                text = if (current < userLevel) {
                    context.getString(R.string.level_unlocked)
                } else {
                    context.getString(R.string.level_not_unlocked)
                }
            }
            binding.tvNextLevelValue.apply {
                makeVisible(userLevel in levelRange)
                text = context.getString(
                    R.string.to_next_level,
                    (userLevelConfig?.end ?: 0) / 100 - (userLevelConfig?.exp ?: 0) / 100
                )
            }
            binding.progressBar.apply {
                makeVisible((current <= userLevel) || (userLevel in levelRange))
                if ((userLevel == 0) || (current <= userLevel) && (userLevel in levelRange)) {
                    max = userLevelConfig?.end ?: 0
                    progress = userLevelConfig?.exp ?: 0
                } else {
                    max = 100
                    progress = 100
                }
            }
        }
}