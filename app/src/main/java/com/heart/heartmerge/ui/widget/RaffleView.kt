package com.heart.heartmerge.ui.widget

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.content.Context
import android.os.Bundle
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.heart.heartmerge.R
import com.heart.heartmerge.popup.raffle.RaffleExhaustedDialog
import com.heart.heartmerge.popup.raffle.RaffleWinDialog

class RaffleView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private val animator = ValueAnimator()
    private var targetIndex = 6
    private val adapter: RaffleAdapter
    private var lotteryStatus = 0//当前可以抽奖状态
    private var onStartClickListener: OnStartClickListener

    init {
        animator.duration = 5000
        animator.setIntValues(0, 2 * 8 + targetIndex)
        animator.interpolator = AccelerateDecelerateInterpolator()
        animator.addUpdateListener {
            val position = it.animatedValue as Int
            setCurrentPosition(position % 8)
        }
        animator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                setCurrentPosition(targetIndex)
                lotteryStatus = 0

                val dialog = RaffleWinDialog()
                val bundle = Bundle()
                bundle.putInt("index",targetIndex)
                dialog.arguments = bundle
                dialog.show((context as AppCompatActivity).supportFragmentManager, "dialog")
            }
        })
        onStartClickListener = object : OnStartClickListener {
            override fun onStart() {
                if (lotteryStatus == 0) {
                    animator.start()
                    lotteryStatus = 1
                    val dialog = RaffleExhaustedDialog()
                    dialog.show((context as AppCompatActivity).supportFragmentManager, "dialog")
                }
            }
        }
        adapter = RaffleAdapter(onStartClickListener)
        setAdapter(adapter)
        layoutManager = object : GridLayoutManager(context, 3) {
            override fun canScrollVertically(): Boolean {
                return false
            }
        }
    }

    private fun setCurrentPosition(position: Int) {
        //刷新当前所在位置
        adapter.setSelectionPosition(position)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        animator.cancel()
    }

    interface OnStartClickListener {
        fun onStart()
    }
}

class RaffleAdapter(private var onClick: RaffleView.OnStartClickListener) :
    RecyclerView.Adapter<RaffleAdapter.LotteryViewHolder>() {
    private var positionMap =
        mapOf<Int, Int>(0 to 0, 1 to 1, 2 to 2, 3 to 7, 4 to 8, 5 to 3, 6 to 6, 7 to 5, 8 to 4)
    private var selectPosition = -1//当前选中需要常亮的

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LotteryViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_raffle, parent, false)
        return LotteryViewHolder(view, onClick)
    }

    override fun onBindViewHolder(holder: LotteryViewHolder, position: Int) {
        holder.init(positionMap[position], selectPosition)
    }

    override fun getItemCount(): Int {
        return 9
    }

    fun setSelectionPosition(currentPosition: Int) {
        val lastPos = selectPosition
        selectPosition = currentPosition
        if (lastPos != -1) {
            notifyItemChanged(reversePosition(lastPos))
        } else {
            notifyDataSetChanged()
        }
        notifyItemChanged(reversePosition(currentPosition))
    }

    private fun reversePosition(selectPos: Int): Int {
        for ((key, value) in positionMap.entries) {
            if (value == selectPos) {
                return key
            }
        }
        return -1
    }

    class LotteryViewHolder(itemView: View, private var onClick: RaffleView.OnStartClickListener) :
        RecyclerView.ViewHolder(itemView) {
        private var ivBg: ImageView = itemView.findViewById(R.id.iv_bg)
        private var ivLottery: ImageView = itemView.findViewById(R.id.iv_lottery)
        private var tvName: TextView = itemView.findViewById(R.id.tv_lottery_name)
        private var viewShadow: View = itemView.findViewById(R.id.view_shadow)

        fun init(fakePos: Int?, selectPos: Int) {
            if (fakePos == 8) {
                //抽奖按钮
                ivBg.setBackgroundResource(R.mipmap.ic_raffle)
                ivLottery.visibility = View.GONE
                tvName.visibility = View.GONE
                viewShadow.visibility = View.GONE
                ivBg.setOnClickListener {
                    onClick.onStart()
                }
            } else {
                ivBg.setBackgroundResource(R.mipmap.bg_raffle_item)
                ivLottery.visibility = View.VISIBLE
                tvName.visibility = View.VISIBLE
                viewShadow.visibility =
                    if (selectPos == -1 || selectPos == fakePos) View.GONE else View.VISIBLE
                ivLottery.setImageResource(iconArray[fakePos!!])
//                tvName.text = "$fakePos"
            }
        }

        var iconArray = arrayOf(
            R.mipmap.ic_reward1,
            R.mipmap.ic_reward2,
            R.mipmap.ic_reward3,
            R.mipmap.ic_reward4,
            R.mipmap.ic_reward5,
            R.mipmap.ic_reward6,
            R.mipmap.ic_reward7,
            R.mipmap.ic_reward8
        )
    }
}
