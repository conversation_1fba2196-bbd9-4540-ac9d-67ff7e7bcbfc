package com.heart.heartmerge.ui.activities.mine


import androidx.core.net.toUri
import androidx.lifecycle.asLiveData
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.jump
import com.bdc.android.library.imageloader.ImageLoader
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.ActivityProfileCompletionBinding
import com.heart.heartmerge.extension.formatDate
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.popup.showAvatarPopup
import com.heart.heartmerge.popup.showBirthdayPopup
import com.heart.heartmerge.popup.showCountryPopup
import com.heart.heartmerge.popup.showLanguagePopup
import com.heart.heartmerge.ui.activities.MainActivity
import com.heart.heartmerge.viewmodes.UserViewModel
import com.luck.picture.lib.utils.PictureFileUtils

@Deprecated(
    "This class is deprecated, please use BuildProfileActivity instead.",
    replaceWith = ReplaceWith("BuildProfileActivity", "com.heart.heartmerge.ui.activities.mine")
)
class ProfileCompletionActivity :
    BaseCoreActivity<ActivityProfileCompletionBinding, UserViewModel>() {

    private val params = JsonObject()

    override fun getLayoutId(): Int = R.layout.activity_profile_completion

    override fun initView() {
        mBinding.rlAvatar.setOnClickListener {
            showAvatarPopup(this) {
                ImageLoader.with(this).load(it ?: "").asAvatar().into(mBinding.ivAvatar)
                mViewModel.upload(PictureFileUtils.getPath(this, it?.toUri())).asLiveData()
                    .observe(this) {
                        params.addProperty("avatar", it.objectKey)
                    }
            }
        }

        mBinding.tvChooseBirthday.setOnClickListener {
            showBirthdayPopup(this) {
                mBinding.tvChooseBirthday.text = it.formatDate()
                params.addProperty("birthday", it.formatDate())
            }
        }

        mBinding.tvChooseCountry.setOnClickListener {
            showCountryPopup(this) {
                mBinding.tvChooseCountry.text = it.title
                params.addProperty("country", it.title)
            }
        }

        mBinding.tvChooseLanguage.setOnClickListener {
            showLanguagePopup(this, MMKVDataRep.userInfo.languages) {
                mBinding.tvChooseLanguage.text = it.map { it.languageName }.joinToString()
                params.add("language", Gson().toJsonTree(it.map { it.languageCode }))
            }
        }

        mBinding.btnCommit.setOnClickListener {
            mViewModel.updateProfile(params.apply {
                addProperty("nickName", mBinding.etNickname.text.toString())
            }).asLiveData().observe(this) {
                jump(MainActivity::class.java)
            }
        }

        mBinding.tvSkip.setOnClickListener {
            jump(MainActivity::class.java)
        }
    }
}