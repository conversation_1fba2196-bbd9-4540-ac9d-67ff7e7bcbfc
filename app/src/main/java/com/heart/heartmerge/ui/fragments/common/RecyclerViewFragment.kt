package com.heart.heartmerge.ui.fragments.common

import androidx.lifecycle.ViewModel
import com.angcyo.dsladapter.DslAdapterItem
import com.bdc.android.library.base.fragment.BaseCoreFragment
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.FragmentRecyclerviewBinding

open class RecyclerViewFragment : BaseCoreFragment<FragmentRecyclerviewBinding, ViewModel>() {

    override fun getLayoutId(): Int = R.layout.fragment_recyclerview

    override fun initData() {
        super.initData()
        mBinding.refreshLayout.onRefresh()
    }

    protected inline fun <reified T : DslAdapterItem> append(
        refresh: Boolean = false, items: List<Any>
    ) {
        mBinding.refreshLayout.append<T>(refresh, items) { data ->
            itemData = data
        }
    }

    protected inline fun <reified T : DslAdapterItem> append(
        refresh: Boolean = false,
        items: List<Any>,
        crossinline initItem: T.(data: Any?) -> Unit = {}
    ) {
        mBinding.refreshLayout.append<T>(refresh, items, initItem)
    }
}