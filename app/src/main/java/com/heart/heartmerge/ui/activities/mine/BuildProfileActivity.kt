package com.heart.heartmerge.ui.activities.mine

import android.app.Activity
import android.icu.util.Calendar
import android.net.Uri
import android.os.Build
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.core.text.isDigitsOnly
import androidx.lifecycle.asLiveData
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter._dslAdapter
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jumpThenFinish
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.extension.observeOnce
import com.bdc.android.library.extension.toast
import com.bdc.android.library.imageloader.ImageLoader
import com.google.gson.JsonObject
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.ActivityBuildProfileBinding
import com.heart.heartmerge.extension.extractS3ObjectKey
import com.heart.heartmerge.extension.formatDate
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.manager.FileUploadManager
import com.heart.heartmerge.mmkv.MMKVBaseDataRep
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.mmkv.MMKVGuiYinDataRep
import com.heart.heartmerge.popup.showAvatarPopup
import com.heart.heartmerge.popup.showBirthdayPopup
import com.heart.heartmerge.popup.showCountryPopup
import com.heart.heartmerge.ui.activities.MainActivity
import com.heart.heartmerge.utils.AppsFlyerHelper
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.UCropUtil
import com.heart.heartmerge.viewmodes.UserViewModel
import com.yalantis.ucrop.UCrop
import top.zibin.luban.Luban
import top.zibin.luban.OnNewCompressListener
import java.io.File

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/5 17:46
 * @description :完善个人资料
 */
class BuildProfileActivity : BaseCoreActivity<ActivityBuildProfileBinding, UserViewModel>() {

    private val userViewModel by viewModels<UserViewModel>()
    private val maleAvatars = mutableListOf<String>()
    private val femaleAvatars = mutableListOf<String>()

    private val params = JsonObject()

    private val pickMedia = registerForActivityResult(ActivityResultContracts.PickVisualMedia()) {
        it?.let {
            startCrop(it)
        }
    }

    override fun getLayoutId(): Int = R.layout.activity_build_profile

    override fun initView() {
        super.initView()
        mViewModel.getDefaultAvatar().asLiveData().observeOnce(this) {
            maleAvatars.addAll(it.male_avatar_list.map { item -> "${it.cdn}$item" })
            femaleAvatars.addAll(it.female_avatar_list.map { item -> "${it.cdn}$item" })

            initDefaultGender(1)
        }

        setNavigationImage(R.mipmap.ic_x) {
            MMKVDataRep.clearAll()
            false
        }

        //设置随机昵称
        mBinding.etNickname.setText(MMKVDataRep.userInfo.nickname)

        //设置默认生日
        Calendar.getInstance().apply {
            set(2000, 0, 1)
        }.time.time.apply {
            mBinding.tvBirthday.text = this.formatDate()
            params.addProperty("birthday", this)
        }
        mBinding.ivGenderMale.isSelected = true

        MMKVDataRep.userInfo.userCountry.takeIf { it != null }?.apply {
            mBinding.tvCountry.text = title
            params.addProperty("country_id", this.id)
        }
    }

    override fun bindListener() {
        mBinding.ivGenderFemale.click {
            mBinding.ivGenderFemale.isSelected = true
            mBinding.ivGenderMale.isSelected = false
            mBinding.ivGenderFemale.setImageResource(R.mipmap.ic_gender_female_checked)
            mBinding.ivGenderMale.setImageResource(R.mipmap.ic_gender_male_normal)
            initDefaultGender(0)
        }

        mBinding.ivGenderMale.click {
            mBinding.ivGenderMale.isSelected = true
            mBinding.ivGenderFemale.isSelected = false
            mBinding.ivGenderFemale.setImageResource(R.mipmap.ic_gender_female_normal)
            mBinding.ivGenderMale.setImageResource(R.mipmap.ic_gender_male_checked)
            initDefaultGender(1)
        }

        mBinding.tvBirthday.click {
            showBirthdayPopup(this) {
                mBinding.tvBirthday.text = it.formatDate()
                params.addProperty("birthday", it)
            }
        }

        mBinding.tvCountry.click {
            showCountryPopup(this) {
                mBinding.tvCountry.text = it.title
                params.addProperty("country_id", it.id)
            }
        }

        mBinding.btnSubmit.click {
            if (!params.has("avatar") || params.get("avatar") == null || params.get("avatar").asString == "") {
                toast(getString(R.string.please_choose_your_avatar))
                return@click
            }

            if (mBinding.etNickname.text.isNullOrEmpty()) {
                toast(getString(R.string.label_title_nickname_hint))
                return@click
            }

            if (!params.has("country_id")) {
                toast(getString(R.string.label_title_country_hint))
                return@click
            }

            userViewModel.updateProfile(params.apply {
                addProperty(
                    "gender",
                    if (mBinding.ivGenderMale.isSelected) Constants.GENDER_MALE else Constants.GENDER_FEMALE
                )
                addProperty("nickname", mBinding.etNickname.text.toString())
//                addProperty("newUser", true)
                addProperty("register_update", 2)
                addProperty("updateType", 0)
                addProperty("enable_notify", true)
//                if (!has("avatar")) {
//                    addProperty("avatar", MMKVDataRep.userInfo.avatar)
//                }
//                if (!has("birthday")) {
//                    addProperty("birthday", System.currentTimeMillis())
//                }

//                if (!has("languages")) {
//                    add(
//                        "languages",
//                        Gson().toJsonTree(MMKVDataRep.userInfo.languages.map { it.languageCode })
//                    )
//                }
            }).asLiveData().observe(this) {
                MMKVBaseDataRep.token = it.accessToken
                MMKVDataRep.userInfo = it.user

                if (MMKVGuiYinDataRep.isOrganicUser) {
                    AppsFlyerHelper.initialize()
                }
                toast(getString(R.string.modify_successful))
                jumpThenFinish(MainActivity::class.java)
            }
        }
    }

    private fun startCrop(sourceUri: Uri) {
        // 启动裁剪
        cropImageLauncher.launch(UCropUtil.getIntent(this, sourceUri))
    }

    private val cropImageLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                val data = result.data
                val resultUri = UCrop.getOutput(data!!)
                resultUri?.let {
                    handleUpload(it.path)
                }
            } else if (result.resultCode == UCrop.RESULT_ERROR) {
                LogX.e("UCrop Error")
            }
        }

    private fun initDefaultGender(type: Int = 0/*0-女，1-男*/) {
        addDefaultView(buildList {
            addAll(if (type == 0) femaleAvatars else maleAvatars)
            add(R.mipmap.ic_upload_image)
        })
    }

    private fun addDefaultView(avatars: List<Any>) {
        mBinding.recyclerView.clearItems()
        mBinding.recyclerView.apply {
            applySingleSelectorModel(0)
            append<DslAdapterItem>(avatars) {
                itemLayoutId = R.layout.item_avatar_default
                itemBindOverride = { itemHolder, _, dslItem, _ ->
                    itemHolder.img(R.id.iv_avatar)?.apply {
                        if (itemData is FileUploadManager.FileUploadResult) {
                            ImageLoader.with(this)
                                .load((itemData as FileUploadManager.FileUploadResult).accessUrl)
                                .asAvatar().into(this)
                        } else if (itemData is String) {
                            ImageLoader.with(this).load(itemData.toString()).asAvatar().into(this)
                        } else {
                            if (itemData.toString().isDigitsOnly()) {
                                setImageResource(itemData.toString().toInt())
                            }
                        }
                    }

                    if (itemIsSelected) {
                        itemHolder.itemView.setBackgroundResource(R.drawable.shape_default_avatar_border)
                        itemHolder.img(R.id.iv_status)?.makeVisible()
                        params.addProperty(
                            "avatar", when (itemData) {
                                is FileUploadManager.FileUploadResult -> (itemData as FileUploadManager.FileUploadResult).objectKey
                                is String -> itemData.toString().extractS3ObjectKey()
                                else -> ""
                            }
                        )
                    } else {
                        itemHolder.itemView.setBackgroundResource(0)
                        itemHolder.img(R.id.iv_status)?.makeGone()
                    }
                    itemHolder.clickItem {
                        updateItemSelect(true)
                        if (itemHolder.layoutPosition == avatars.lastIndex) {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && ActivityResultContracts.PickVisualMedia.isPhotoPickerAvailable(
                                    this@BuildProfileActivity
                                )
                            ) {
                                pickMedia.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly))
                            } else {
                                showAvatarPopup(this@BuildProfileActivity) {
                                    ImageLoader.with(itemHolder.context).load(it).asAvatar()
                                        .into(itemHolder.img(R.id.iv_avatar)!!)
                                    handleUpload(it)
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private fun handleUpload(path: String?) {
        showLoading(getString(R.string.uploading))

        fun launchUpload(it: String) {
            userViewModel.upload(it).asLiveData().observeForever {
                stopLoading()
                if (it.success) {
                    mBinding.recyclerView._dslAdapter?.dataItems?.last()
                        ?.let { item -> item.itemData = it }
                    mBinding.recyclerView.notifyUpdateItem()
                    params.addProperty("avatar", it.objectKey)
                }
            }
        }

        Luban.with(this).load(path).ignoreBy(100)
            .setCompressListener(object : OnNewCompressListener {
                override fun onStart() {}

                override fun onSuccess(source: String?, compressFile: File?) {
                    compressFile?.let {
                        launchUpload(it.path)
                    }
                }

                override fun onError(source: String?, e: Throwable?) {
                    source?.let {
                        launchUpload(it)
                    }
                }
            }).launch()
    }
}