package com.heart.heartmerge.ui.fragments.message

import android.os.Bundle
import android.view.View
import androidx.activity.ComponentActivity
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.DslViewHolder
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.extension.buildImageUrl
import com.heart.heartmerge.extension.loadAvatar
import com.heart.heartmerge.ui.activities.anchor.AnchorVideoActivity
import com.heart.heartmerge.ui.activities.anchor.detail.AnchorDetailActivity
import com.heart.heartmerge.ui.activities.message.MyRongConversationActivity
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.PurchaseScene
import com.heart.heartmerge.utils.toJson
import io.rong.imlib.model.Conversation

/**
 * 作者：Lxf
 * 创建日期：2024/7/27 17:17
 * 描述：
 */
class FollowFansItem : DslAdapterItem() {
    init {
        itemLayoutId = R.layout.item_follow_or_fans
    }

    var anchorInfo: UserBean? = null

    override fun onItemBind(
        itemHolder: DslViewHolder,
        itemPosition: Int,
        adapterItem: DslAdapterItem,
        payloads: List<Any>
    ) {
        super.onItemBind(itemHolder, itemPosition, adapterItem, payloads)
        itemHolder.apply {
            clickItem {
                jump(
                    AnchorDetailActivity::class.java,
                    Bundle().apply {
                        putString(
                            Constants.INTENT_PARAM_KEY_ANCHOR_INFO,
                            anchorInfo?.toJson()
                        )
                    })
            }
            anchorInfo?.apply {
                val newId = anchorId.ifBlank { id }
                img(R.id.iv_avatar)?.loadAvatar(avatar.buildImageUrl())
                tv(R.id.tv_nickname)?.text = nickname
                tv(R.id.tv_age)?.text = "$age"
                tv(R.id.tv_country)?.apply {
                    val title = anchorCountry?.title
                    if (!title.isNullOrBlank()) {
                        text = title
                        visibility = View.VISIBLE
                    } else {
                        visibility = View.GONE
                    }
                }
                img(R.id.iv_msg)?.click {
                    jump(MyRongConversationActivity::class.java, Bundle().apply {
                        putString("targetId", newId)
                        putString(
                            "ConversationType", Conversation.ConversationType.PRIVATE.name
                        )
                    })
                }
                img(R.id.iv_video)?.click {
                    itemDslAdapter?._recyclerView?.context?.let {
                        AppUtil.checkoutDiamondAndShowPopup(
                            it as ComponentActivity, anchorLevel?.discountPrice ?: Constants.VIDEO_DEFAULT_PRICE,
                            PurchaseScene.VideoCallFollow(anchorId = newId)
                        ) {
                            AppUtil.requestVideoPermission(context, onGrantedCallBack = {
                                jump(
                                    AnchorVideoActivity::class.java,
                                    Bundle().apply {
                                        //TODO 这里用户的id是另一个字段 followUserId 需要做一下转换 建议后端统一一下字段
                                        val tmpAnchor = UserBean(id = newId)
                                        putString(
                                            Constants.INTENT_PARAM_KEY_ANCHOR_INFO,
                                            tmpAnchor.toJson()
                                        )
                                    })
                            }) {}

                        }
                    }
                }
                when (status) {
                    "1" -> {
                        img(R.id.iv_video)?.makeVisible()
                        view(R.id.online_dot)?.makeVisible()
                    }

                    else -> {
                        img(R.id.iv_video)?.makeGone()
                        view(R.id.online_dot)?.makeGone()
                    }
                }
            }
        }
    }
}