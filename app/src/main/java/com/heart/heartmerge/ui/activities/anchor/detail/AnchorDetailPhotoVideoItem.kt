package com.heart.heartmerge.ui.activities.anchor.detail

import android.content.Context
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import androidx.appcompat.app.AppCompatActivity
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.DslViewHolder
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.AnchorFileBean
import com.heart.heartmerge.extension.buildImageUrl
import com.heart.heartmerge.extension.loadAnchorImage
import com.heart.heartmerge.extension.loadAnchorImageWithBlur
import com.heart.heartmerge.extension.loadVideoFirstFrame
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.popup.showMembershipSubscribePopup
import com.heart.heartmerge.popup.showReportPopup
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.ContextHolder
import com.heart.heartmerge.utils.PurchaseScene
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.util.SmartGlideImageLoader


/**
 * 作者：Lxf
 * 创建日期：2024/8/14 10:52
 * 描述：
 */
class AnchorDetailPhotoVideoItem : DslAdapterItem() {
    init {
        itemLayoutId = R.layout.item_anchor_photo_video
    }

    var anchorFile: AnchorFileBean? = null
    var photos: MutableList<AnchorFileBean>? = null
    var anchorId: String? = null

    override fun onItemBind(
        itemHolder: DslViewHolder,
        itemPosition: Int,
        adapterItem: DslAdapterItem,
        payloads: List<Any>
    ) {
        super.onItemBind(itemHolder, itemPosition, adapterItem, payloads)
        itemHolder.apply {
            anchorFile?.apply {
                val isVip = MMKVDataRep.userInfo.isVIP
                clickItem {
                    if (!isVip && isLock == "2") {
                        showMembershipSubscribePopup(
                            context as AppCompatActivity, PurchaseScene.AlbumUnlock(anchorId)
                        )
                    } else {
                        img(R.id.file_thumbnail)?.let {
                            seePhotoOrVideo(isVip, fileType, context, it, fileUrl.buildImageUrl(highQuality = true))
                        }
                    }
                }

                if (!isVip && isLock == "2") {
                    img(R.id.file_lock)?.makeVisible()
                    if (fileType == Constants.ANCHOR_FILE_TYPE_VIDEO) {
                        if (thumbnail.isNotEmpty()) {
                            img(R.id.file_thumbnail)?.loadAnchorImageWithBlur(thumbnail.buildImageUrl())
                        } else {
                            img(R.id.file_thumbnail)?.loadVideoFirstFrame(fileUrl.buildImageUrl(), needBlur = true)
                        }
                    } else {
                        img(R.id.file_thumbnail)?.loadAnchorImageWithBlur(fileUrl.buildImageUrl())
                    }

                } else {
                    img(R.id.file_lock)?.makeGone()
                    if (fileType == Constants.ANCHOR_FILE_TYPE_VIDEO) {
                        if (thumbnail.isNotEmpty()) {
                            img(R.id.file_thumbnail)?.loadAnchorImage(thumbnail.buildImageUrl())
                        } else {
                            img(R.id.file_thumbnail)?.loadVideoFirstFrame(fileUrl.buildImageUrl(), needBlur = false)
                        }
                    } else {
                        img(R.id.file_thumbnail)?.loadAnchorImage(
                            fileUrl.buildImageUrl(), ContextHolder.context.resources.getDimension(
                                com.bdc.android.library.R.dimen.dp_8
                            )
                        )
                    }

                }
                img(R.id.file_play)?.makeVisible(fileType == Constants.ANCHOR_FILE_TYPE_VIDEO)
            }
        }
    }

    private fun seePhotoOrVideo(
        isVip: Boolean,
        fileType: String,
        context: Context,
        imageView: ImageView,
        url: String? = null
    ) {
        when (fileType) {
            Constants.ANCHOR_FILE_TYPE_PHOTO -> {
                val tmpPhotoList = mutableListOf<String>()
                var currentPos = 0
                photos?.forEachIndexed { _, anchorFileBean ->
                    if (isVip || anchorFileBean.isLock != "2") {
                        tmpPhotoList.add(anchorFileBean.fileUrl)
                        if (anchorFileBean.fileUrl == url) {
                            currentPos = tmpPhotoList.size - 1
                        }
                    }

                }
                if (tmpPhotoList.isEmpty()){
                    LogX.e("seePhotoOrVideo 没有图片")
                    return
                }
                val photoViewDialog = XPopup.Builder(context).isTouchThrough(true).asImageViewer(
                        imageView, currentPos, tmpPhotoList as List<Any>?, { _, _ -> // do nothing
                        }, SmartGlideImageLoader()
                    ).isShowSaveButton(false)
                val dialogBack = photoViewDialog.findViewById<View>(R.id.fl_back);
                dialogBack?.click { photoViewDialog.dismiss() }
                val dialogReport = photoViewDialog.findViewById<View>(R.id.fl_report);
                dialogReport?.click {
                    anchorId?.let {
                        showReportPopup(context as AppCompatActivity, it)
                    }
                }
                photoViewDialog.show()
            }

            Constants.ANCHOR_FILE_TYPE_VIDEO -> {
                jump(VideoPlayActivity::class.java, Bundle().apply {
                    putString("video_url", anchorFile?.fileUrl)
                    putString("thumb_url", anchorFile?.thumbnail)
                    putString(Constants.INTENT_PARAM_KEY_ANCHOR_UID, anchorId)
                })
            }
        }
    }
}