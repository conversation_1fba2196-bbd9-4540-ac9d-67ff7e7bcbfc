import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.TextView
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.http.request
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.AuthBean
import com.heart.heartmerge.http.RetrofitClient
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.ui.widget.DraggableLinearLayout
import com.heart.heartmerge.utils.FlexibleCountdownTimer
import java.text.SimpleDateFormat
import java.util.TimeZone

class RechargeActivityView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : DraggableLinearLayout(context, attrs, defStyleAttr) {

    private var flexibleCountdownTimer: FlexibleCountdownTimer? = null

    init {
        orientation = VERTICAL
        val contentView =
            LayoutInflater.from(context).inflate(R.layout.view_recharge_activity, this, false)
        addView(contentView)
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        checkFirstRecharge()
    }

    private fun checkFirstRecharge() {
        request<AuthBean> {
            call {
                RetrofitClient.aggregatedService.getUserDetail()
            }
            onSuccess {
                if (it?.user?.firstRechargeCountdown?.isNotEmpty() == true) {
                    startCountdown(it.user.firstRechargeCountdown)
                } else {
                    findViewById<TextView>(R.id.tv_countdown).makeGone()
                }
            }
            onFailure {
                findViewById<TextView>(R.id.tv_countdown).makeGone()
            }
        }
    }

    @SuppressLint("SimpleDateFormat")
    private fun startCountdown(times: List<Long>?) {
        val textView = findViewById<TextView>(R.id.tv_countdown)

        val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        sdf.timeZone = TimeZone.getDefault()

        val maxTimestamp =
            times?.filter { it > System.currentTimeMillis() }?.maxByOrNull { it } ?: 0

        flexibleCountdownTimer = FlexibleCountdownTimer(
            totalTimeInMillis = (maxTimestamp - System.currentTimeMillis()),
            onTick = {
                val minTimestamp =
                    times?.filter { it > System.currentTimeMillis() }?.minByOrNull { it } ?: 0

                if (minTimestamp <= 0) {
                    textView.makeGone()
                    flexibleCountdownTimer?.cancel()
                    return@FlexibleCountdownTimer
                }
                textView.makeVisible()

                val totalSeconds = (minTimestamp - System.currentTimeMillis()) / 1000
                val hours = totalSeconds / 3600
                val remainderSeconds = totalSeconds % 3600
                val minutes = remainderSeconds / 60
                val seconds = remainderSeconds % 60
                textView.text = String.format("%02d:%02d:%02d", hours, minutes, seconds)

                //倒计时如果用户完成充值，则隐藏倒计时
                if (MMKVDataRep.userInfo.alreadyFirstRecharge) {
                    textView.makeGone()
                    flexibleCountdownTimer?.cancel()
                }
            },
            onFinish = {
                textView.makeGone()
            })
        flexibleCountdownTimer?.start()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        flexibleCountdownTimer?.cancel()
    }
}
