package com.heart.heartmerge.ui.pages.mingle

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectHorizontalDragGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.rotate
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.heart.heartmerge.R
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

@Composable
fun MinglePage() {
    Scaffold(
        modifier = Modifier.fillMaxSize(),
        backgroundColor = Color(0xffff9b9c),
    ) { padding ->
        val checked = remember {
            mutableIntStateOf(0)
        }
        Column(
            modifier = Modifier
                .padding(padding)
                .statusBarsPadding(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                val items = listOf("Match", "Swipe")
                SegmentButton(items = items,
                    selected = checked.intValue,
                    modifier = Modifier.padding(start = 10.dp),
                    onSelectedChange = {
                        checked.intValue = it
                    })
                Spacer(modifier = Modifier.weight(1F))
                Text(
                    text = "匹配记录",
                    modifier = Modifier.padding(end = 10.dp),
                    color = Color.White,
                    fontSize = 20.sp
                )
            }
            if (checked.intValue == 0) {
                MatchContent(modifier = Modifier.padding(top = 80.dp))
            } else {
                SwipeContent(modifier = Modifier.padding(top = 10.dp))
            }
        }
    }
}

@Composable
fun SegmentButton(
    items: List<String>,
    selected: Int,
    onSelectedChange: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .background(
                Color(0xffffb1b1), shape = RoundedCornerShape(50)
            )
            .height(50.dp)
    ) {
        items.forEachIndexed { index, item ->
            Button(
                elevation = ButtonDefaults.elevation(defaultElevation = 0.dp),
                onClick = { onSelectedChange(index) },
                colors = ButtonDefaults.buttonColors(
                    backgroundColor = if (index == selected) Color(0xfff9473d) else Color.Transparent
                ),
                modifier = Modifier
                    .fillMaxHeight()
                    .clip(RoundedCornerShape(40.dp))
            ) {
                Text(item, color = Color.White)
            }
        }
    }
}

@Composable
fun MatchContent(modifier: Modifier = Modifier) {
    var isAnimating by remember { mutableStateOf(false) } // 控制动画是否执行
    val animatable = remember { Animatable(0f) }
    var job: Job? = null

    LaunchedEffect(key1 = isAnimating) {
        if (isAnimating) {
            job = launch {
                animatable.animateTo(
                    targetValue = 360f, animationSpec = infiniteRepeatable(
                        animation = tween(6000, easing = LinearEasing),
                        repeatMode = RepeatMode.Restart
                    )
                )
            }
        } else {
            job?.cancel() // 停止动画
            animatable.snapTo(0f) // 重置动画值
        }
    }

    val rotation = animatable.value

//    val infiniteTransition = rememberInfiniteTransition(label = "")
//    val rotation by infiniteTransition.animateFloat(
//        initialValue = 0f, targetValue = 360f, animationSpec = infiniteRepeatable(
//            animation = tween(6000, easing = LinearEasing), repeatMode = RepeatMode.Restart
//        ), label = ""
//    )

    Box(modifier = modifier.size(280.dp)) {
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .rotate(rotation)  // 整个Canvas旋转
        ) {
            val center = Offset(size.width / 2, size.height / 2)
            val maxRadius = size.width.coerceAtMost(size.height) / 2

            // 背景（不旋转）
            rotate(-rotation) {  // 抵消背景的旋转
                drawCircle(Color(0xFFF3D3D4), radius = maxRadius)
            }

            // 扇形扫描区域（随Canvas旋转）
            drawArc(
                brush = Brush.sweepGradient(
                    colors = listOf(Color(0xFFB06668), Color(0xFFB06668).copy(alpha = 0f)),
                    center = Offset(center.x, center.y),
                ),
                startAngle = -90f,
                sweepAngle = 90f,
                useCenter = true,
                topLeft = Offset(center.x - maxRadius, center.y - maxRadius),
                size = Size(maxRadius * 2, maxRadius * 2)
            )

            // 三个同心圆
            drawCircle(Color.White.copy(alpha = 0.2f), radius = maxRadius * 0.95f)
            drawCircle(Color.White.copy(alpha = 0.4f), radius = maxRadius * 0.7f)
            drawCircle(Color.White.copy(alpha = 0.6f), radius = maxRadius * 0.45f)

            // 中心小圆
            drawCircle(Color.White, radius = maxRadius * 0.2f)
        }

        // WiFi 图标（不旋转）
        Icon(
            imageVector = Icons.Default.Favorite,
            contentDescription = "WiFi Icon",
            modifier = Modifier
                .size(40.dp)
                .align(Alignment.Center),
            tint = Color(0xFFfe6e57)
        )
    }

    AnimatedVisibility(visible = !isAnimating) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Column(horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center,
                modifier = Modifier
                    .padding(top = 30.dp)
                    .clickable {
                        isAnimating = !isAnimating
                    }
                    .background(
                        brush = Brush.linearGradient(
                            colors = listOf(
                                Color(0xFFfe6e57), Color(0xFFf9463c)
                            )
                        ), shape = RoundedCornerShape(50)
                    )
                    .width(120.dp)
                    .height(50.dp)) {
                Text(text = "在线匹配", color = Color.White)
                Text(text = "(100钻石/次)", color = Color.White)
            }

            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(top = 10.dp)
            ) {
                Text(text = "当前剩余钻石：")
                Image(
                    painter = painterResource(id = R.mipmap.ic_diamond),
                    contentDescription = "diamand",
                    modifier = Modifier
                        .width(20.dp)
                        .height(20.dp)
                )
                Text(text = "999", color = Color(0xfffb677d))
            }
        }
    }

    AnimatedVisibility(visible = isAnimating) {
        Text(
            text = "正在为你匹配有缘人，请稍等~",
            color = Color(0xffff6861),
            fontSize = 20.sp,
            modifier = Modifier.padding(top = 30.dp)
        )
    }
}


@Composable
fun SwipeContent(modifier: Modifier = Modifier) {
    val images = listOf(
        R.mipmap.bg_login,
        R.mipmap.bg_login,
        R.mipmap.bg_login,
        R.mipmap.bg_login,
        R.mipmap.bg_login,
        R.mipmap.bg_login,
        R.mipmap.bg_login,
        R.mipmap.bg_login,
        R.mipmap.bg_login,
        R.mipmap.bg_login
    )

    var currentIndex by remember { mutableIntStateOf(0) }
    val scope = rememberCoroutineScope()
    val cardOffsetX = remember { Animatable(0f) }
    val rotation = remember { Animatable(0f) }

    Box(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        images.takeLast(3).forEachIndexed { index, imageRes ->
            val reverseIndex = 2 - index
            val scale = animateFloatAsState(
                targetValue = if (reverseIndex == 0) 1f else 1f - 0.07f * reverseIndex, label = ""
            )
            val translationY = animateFloatAsState(
                targetValue = 70.dp.value * reverseIndex, label = ""
            )

            Box(modifier = Modifier
                .fillMaxSize()
                .graphicsLayer(
                    translationX = if (reverseIndex == 0) cardOffsetX.value else 0f,
                    translationY = translationY.value,
                    rotationZ = if (reverseIndex == 0) rotation.value else 0f,
                    scaleX = scale.value,
                    scaleY = scale.value
                )
                .pointerInput(Unit) {
                    if (reverseIndex == 0) {
                        detectHorizontalDragGestures(onDragEnd = {
                            if (cardOffsetX.value > 300) {
                                scope.launch {
                                    cardOffsetX.animateTo(
                                        targetValue = 1000f,
                                        animationSpec = tween(durationMillis = 300)
                                    )
                                    currentIndex = (currentIndex + 1) % images.size
                                    cardOffsetX.snapTo(0f)
                                    rotation.snapTo(0f)
                                }
                            } else if (cardOffsetX.value < -300) {
                                scope.launch {
                                    cardOffsetX.animateTo(
                                        targetValue = -1000f,
                                        animationSpec = tween(durationMillis = 300)
                                    )
                                    currentIndex = (currentIndex + 1) % images.size
                                    cardOffsetX.snapTo(0f)
                                    rotation.snapTo(0f)
                                }
                            } else {
                                scope.launch {
                                    cardOffsetX.animateTo(
                                        targetValue = 0f,
                                        animationSpec = tween(durationMillis = 250)
                                    )
                                }
                                scope.launch {
                                    rotation.animateTo(
                                        targetValue = 0f,
                                        animationSpec = tween(durationMillis = 250)
                                    )
                                }
                            }
                        }) { change, dragAmount ->
                            change.consume()
                            scope.launch {
                                cardOffsetX.snapTo(cardOffsetX.value + dragAmount)
                                rotation.snapTo(cardOffsetX.value / 20)
                            }
                        }
                    }
                }) {
                Card(
                    shape = RoundedCornerShape(16.dp),
                    elevation = 8.dp,
                    modifier = Modifier
                        .padding(horizontal = 20.dp)
                        .fillMaxWidth()
                        .height(400.dp)
                        .border(
                            BorderStroke(
                                15.dp, brush = Brush.linearGradient(
                                    colors = listOf(
                                        Color(0xFFffe3e3), Color(0xFFffffff)
                                    )
                                )
                            ), shape = RoundedCornerShape(20.dp)
                        )
                ) {
                    Box(contentAlignment = Alignment.BottomStart, modifier = Modifier.clip(
                        RoundedCornerShape(20)
                    )) {
                        Image(
                            painter = painterResource(id = imageRes),
                            contentDescription = "Profile Picture",
                            contentScale = ContentScale.Crop,
                            modifier = Modifier
                                .fillMaxSize()
                                .clip(RoundedCornerShape(20))

                        )
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.padding(20.dp)
                        ) {
                            Image(
                                painter = painterResource(id = R.mipmap.bg_login),
                                contentDescription = "avatar",
                                contentScale = ContentScale.Crop,
                                modifier = Modifier
                                    .size(30.dp)
                                    .clip(RoundedCornerShape(100))
                            )
                            Text(
                                text = "Nickname",
                                color = Color.White,
                                modifier = Modifier.padding(start = 10.dp)
                            )
                        }
                    }
                }
            }
        }

        Row(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .padding(horizontal = 80.dp), horizontalArrangement = Arrangement.SpaceBetween
        ) {
            TextButton(
                onClick = {
                    scope.launch {
                        cardOffsetX.animateTo(
                            targetValue = -1000f, animationSpec = tween(durationMillis = 300)
                        )
                        currentIndex = (currentIndex + 1) % images.size
                        cardOffsetX.snapTo(0f)
                        rotation.snapTo(0f)
                    }
                }, modifier = Modifier
                    .background(
                        Color.White.copy(0.5f), shape = RoundedCornerShape(50)
                    )
                    .size(60.dp)
            ) {
                Text(
                    text = "Dislike",
                    textAlign = TextAlign.Center,
                )
            }
            TextButton(
                onClick = {
                    scope.launch {
                        cardOffsetX.animateTo(
                            targetValue = 1000f, animationSpec = tween(durationMillis = 300)
                        )
                        currentIndex = (currentIndex + 1) % images.size
                        cardOffsetX.snapTo(0f)
                        rotation.snapTo(0f)
                    }
                }, modifier = Modifier
                    .background(
                        Color.Red.copy(alpha = 0.5f), shape = RoundedCornerShape(50)
                    )
                    .size(60.dp)
            ) {
                Text(text = "Like", color = Color.White)
            }
        }
    }
}