package com.heart.heartmerge.ui.activities.login

import androidx.lifecycle.ViewModel
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.ActivityEmailBindBinding
import com.heart.heartmerge.extension.isEmail
import com.heart.heartmerge.utils.FlexibleCountdownTimer

class EmailBindActivity : BaseCoreActivity<ActivityEmailBindBinding, ViewModel>() {

    override fun getLayoutId(): Int = R.layout.activity_email_bind

    override fun initView() {
//        setCenterTitle(getString(R.string.email_bind))

        mBinding.tvCaptcha.setOnClickListener {

            if (!validate()) return@setOnClickListener

            FlexibleCountdownTimer(onTick = {
                mBinding.tvCaptcha.isEnabled = false
                mBinding.tvCaptcha.text = getString(R.string.few_seconds_retry, it.toString())
            }, onFinish = {
                mBinding.tvCaptcha.isEnabled = true
                mBinding.tvCaptcha.text = getString(R.string.get_code)
            }).start()
        }

        mBinding.btnLogin.setOnClickListener {
            if (!validate()) return@setOnClickListener
        }
    }

    private fun validate(): Boolean {
        if (mBinding.etEmail.text.isEmpty()) {
            ToastUtil.show(getString(R.string.login_placeholder_account))
            return false
        }

        if (!mBinding.etEmail.text.toString().isEmail()) {
            ToastUtil.show(getString(R.string.email_validate_error))
            return false
        }
        return true
    }
}