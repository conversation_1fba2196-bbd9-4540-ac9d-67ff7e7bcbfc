package com.heart.heartmerge.ui.activities.mine

import androidx.lifecycle.asLiveData
import com.angcyo.dsladapter.DslAdapterItem
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.GoodsBean
import com.heart.heartmerge.beans.GoodsQueryType
import com.heart.heartmerge.databinding.ActivityRechargeHistoryBinding
import com.heart.heartmerge.extension.formatDateTime
import com.heart.heartmerge.extension.openLoadMore
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond
import com.heart.heartmerge.viewmodes.UserViewModel

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/12 14:34
 * @description :充值历史记录
 */
class RechargeHistoryActivity : BaseCoreActivity<ActivityRechargeHistoryBinding, UserViewModel>() {
    override fun getLayoutId(): Int = R.layout.activity_recharge_history

    override fun initView() {
        mBinding.refreshLayout.recyclerView.openLoadMore { }
        mBinding.refreshLayout.setOnRefreshListener { b, i ->
            fetch(b, i)
        }
        mBinding.refreshLayout.onRefresh()
    }

    private fun fetch(refresh: Boolean, pageIndex: Int) {
        mViewModel.getRechargeList(refresh,GoodsQueryType.DIAMOND).asLiveData().observe(this) {
            mBinding.refreshLayout.append<DslAdapterItem>(refresh, it.records) {
                itemData = it
                itemLayoutId = R.layout.item_recharge_history
                itemBindOverride = { itemHolder, _, _, _ ->
                    val item = itemData as? GoodsBean
                    itemHolder.tv(R.id.tv_diamond)?.text = item?.gold?.toShowDiamond().toString()
                    itemHolder.tv(R.id.tv_order_id)?.text =
                        getString(R.string.order_id, item?.orderId)
                    itemHolder.tv(R.id.tv_order_time)?.text =
                        getString(R.string.order_time, item?.createdAt?.formatDateTime())
                    itemHolder.tv(R.id.tv_price)?.text = "${item?.real_price}"

                    itemHolder.tv(R.id.tv_status)?.text =
                        if (item?.stat == 2) "Success" else "Unfinished"
                }
            }
        }
    }
}