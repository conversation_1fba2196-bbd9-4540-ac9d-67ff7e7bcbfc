package com.heart.heartmerge.ui.fragments.message

import android.os.Bundle
import androidx.fragment.app.viewModels
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.extension.putIndex
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.beans.RemoteSocketStatusChangeExtend
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.extension.openLoadMore
import com.heart.heartmerge.ui.fragments.common.RecyclerViewFragment
import com.heart.heartmerge.ui.fragments.home.HomeAnchorItem
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.viewmodes.FollowFansRequestEvent
import com.heart.heartmerge.viewmodes.FollowFansViewModel

/**
 * Author:Lxf
 * Create on:2024/8/5
 * Description:
 */
enum class RelationType(val value: Int) {
    FOLLOW(0), FANS(1)
}

class FollowOrFansFragment : RecyclerViewFragment() {

    private val viewModel by viewModels<FollowFansViewModel>()
    private val pageSize = 20
    private var mCursor = ""

    companion object {
        fun getInstance(index: RelationType): FollowOrFansFragment {
            return FollowOrFansFragment().apply {
                arguments = Bundle().putIndex(index.value)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        mBinding.refreshLayout.onRefresh()
    }

    override fun initView() {
        mBinding.refreshLayout.recyclerView.apply {
            setOnRefreshListener { b: Boolean, _: Int ->
                if (b) {
                    mCursor = ""
                }
                viewModel.cancelLoadAnchorJob()
                viewModel.fetchFollowOrFansList(mCursor, pageSize)
            }
            openLoadMore {
                viewModel.fetchFollowOrFansList(mCursor, pageSize)
            }
        }
    }

    override fun initData() {
        FlowBus.with<String>(Constants.PUSH_TYPE_BLACK_ANCHOR).register(this) {
            mBinding.refreshLayout.recyclerView.dslAdapter.apply {
                dataItems.forEach { dslAdapterItem ->
                    val item = dslAdapterItem as FollowFansItem
                    if (item.anchorInfo?.anchorId == it) {
                        mBinding.refreshLayout.recyclerView.dslAdapter.removeItem(dslAdapterItem)
                        updateItemDepend()
                        return@forEach
                    }
                }
            }
        }

        FlowBus.with<RemoteSocketStatusChangeExtend>(Constants.PushCmd.PUSH_CMD_REMOTE_SOCKET_STATUS_CHANGE).register(this) {
            mBinding.refreshLayout.recyclerView.dslAdapter.apply {
                dataItems.forEach { dslAdapterItem ->
                    val item = dslAdapterItem as FollowFansItem
                    if (item.anchorInfo?.id == it.peerUID.toString()) {
                        item.anchorInfo?.status = it.status.toString()
                        mBinding.refreshLayout.recyclerView.dslAdapter.notifyItemChanged(dslAdapterItem)
                        updateItemDepend()
                        return@forEach
                    }
                }
            }
        }
//        mBinding.refreshLayout.onRefresh()
    }

    override fun initViewEvents() {
        viewModel.pageEvents.observeEvent(this) {
            when (it) {
                is FollowFansRequestEvent.GetFollowSuccess -> {
                    mBinding.refreshLayout.apply {
                        if (mCursor.isEmpty()) {
                            clearItems()
                        }

                        mCursor = it.cursor
                        append<FollowFansItem>(items = it.list) { data ->
                            anchorInfo = data as UserBean
                        }
                    }
                }

                is FollowFansRequestEvent.GetFollowFailed -> {
                    ToastUtil.show(it.msg)
                    mBinding.refreshLayout.loadFailedWithMore()
                }
            }
        }
    }
}