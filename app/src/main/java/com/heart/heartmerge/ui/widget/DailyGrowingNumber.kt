package com.heart.heartmerge.ui.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.heart.heartmerge.manager.GrowthNumberManager

@Composable
fun DailyGrowingNumber(
    fontSize: TextUnit = 12.sp,
    isFromMain: Boolean = false
) {
    val currentValue by GrowthNumberManager.currentValue

    // 确保只调用一次启动逻辑
    LaunchedEffect(Unit) {
        GrowthNumberManager.startTracking()
    }
    if (isFromMain) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFFF53D3D), RoundedCornerShape(6.dp))
        ) {
            Text(
                text = "$currentValue",
                fontSize = 10.sp, // 控制字体不要太大
                color = Color.White,
                textAlign = TextAlign.Center,
                lineHeight = 12.sp,
                modifier = Modifier.align(Alignment.Center)
            )
        }
    } else {
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .clip(CircleShape)
                .background(Color(0xFF9F2AF8))
                .border(1.dp, Color.White, CircleShape)

        ) {
            Text(
                text = "$currentValue",
                fontSize = fontSize,
                color = Color.White,
            )
        }
    }

}
