package com.heart.heartmerge.ui.pages.home

import android.content.Intent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ChipDefaults
import androidx.compose.material.Divider
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.FilterChip
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.lifecycle.viewmodel.compose.viewModel
import com.heart.heartmerge.R
import com.heart.heartmerge.ui.activities.login.LoginActivity
import com.heart.heartmerge.ui.activities.mine.ProfileCompletionActivity
import com.heart.heartmerge.viewmodes.HomeViewModel

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun HomePage() {
    Scaffold(
        modifier = Modifier
            .statusBarsPadding()
            .fillMaxSize(),
        backgroundColor = Color(0xff101321),
    ) { padding ->
        val context = LocalContext.current
        val viewModel = viewModel<HomeViewModel>()
        LaunchedEffect(Unit) {
//            viewModel.getAnchorList(1, 20)
        }
        val gridState = rememberLazyGridState()
        Column(Modifier.padding(padding)) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 10.dp)
                    .height(50.dp)
                    .background(Color(0xff1b1d39), shape = RoundedCornerShape(50))
                    .clickable {
                        context.startActivity(
                            Intent(
                                context,
                                ProfileCompletionActivity::class.java
                            )
                        )
                    }
            ) {
                Image(
                    painter = painterResource(id = R.mipmap.ic_search),
                    contentDescription = "Search",
                    modifier = Modifier
                        .padding(start = 10.dp, end = 10.dp)
                        .size(20.dp)
                )
                Text(text = "搜索主播姓名、id", color = Color(0xff687191))
            }
            Divider(
                color = Color(0xff262935), modifier = Modifier.padding(top = 20.dp)
            )
        }

        LazyVerticalGrid(
            columns = GridCells.Fixed(2),
            contentPadding = PaddingValues(5.dp),
            state = gridState,
            modifier = Modifier.padding(top = 70.dp)
        ) {
            item(span = { GridItemSpan(2) }) {
                Column {
                    FlowRow(
                        horizontalArrangement = Arrangement.spacedBy(10.dp),
                        verticalArrangement = Arrangement.spacedBy(0.dp),
                        modifier = Modifier.padding(0.dp)
                    ) {
                        BuildFilterClip() { index ->
                            print("------------>>>$index")
                        }
                    }

                    Image(
                        painter = painterResource(id = R.mipmap.ic_diamond),
                        contentDescription = "banner",
                        contentScale = ContentScale.FillWidth,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 20.dp)
                            .height(90.dp)
                    )
                }
            }
            val count = (0..20).toList()
            items(count = count.size) { BuildGridItem() }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun BuildFilterClip(onSelected: (Int) -> Unit) {
    val selected = remember {
        mutableIntStateOf(0)
    }

    val clips = arrayOf(
        "All", "English", "Francais", "PycckNN", "Espariol", "XXXXX", "Turkce", "IME"
    )

    clips.forEachIndexed { index, item ->
        FilterChip(selected = selected.intValue == index, colors = ChipDefaults.filterChipColors(
            backgroundColor = Color(0xff1b1d39),
            contentColor = Color(0xff8c91c4),
            selectedBackgroundColor = Color(0xff3a1a2a),
            selectedContentColor = Color(0xfffe3b5a),
        ), onClick = {
            selected.intValue = index
            onSelected(index)
        }) {
            Text(text = item)
        }
    }
}

@Composable
fun BuildGridItem() {
    val context = LocalContext.current
    ConstraintLayout(modifier = Modifier.padding(start = 10.dp, end = 10.dp, bottom = 20.dp)) {
        val (avatar, price, status, nickname, age, contact) = createRefs()
        Image(painter = painterResource(id = R.mipmap.bg_login),
            contentDescription = "avatar",
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .constrainAs(avatar) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
                .height(300.dp)
                .clip(RoundedCornerShape(8.dp)))

        Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier
            .constrainAs(price) {
                top.linkTo(avatar.top)
                start.linkTo(avatar.start)
            }
            .padding(10.dp)) {
            Text(text = "120", color = Color.White)
            Image(painter = painterResource(id = R.mipmap.ic_diamond), contentDescription = "price")
            Text(text = "/min", color = Color.White)
        }

        Text(text = "^在线", color = Color(0xff22c46d), modifier = Modifier
            .constrainAs(status) {
                top.linkTo(avatar.top)
                end.linkTo(avatar.end)
            }
            .padding(10.dp)
            .background(Color.Black.copy(alpha = 0.5f), shape = RoundedCornerShape(50.dp))
            .padding(vertical = 5.dp, horizontal = 10.dp))

        Text(text = "画图的蔡菜菜", modifier = Modifier
            .constrainAs(nickname) {
                bottom.linkTo(age.top)
                start.linkTo(age.start)
            }
            .padding(start = 10.dp, bottom = 10.dp), color = Color.White, fontSize = 20.sp)

        Row(modifier = Modifier
            .constrainAs(age) {
                start.linkTo(avatar.start)
                bottom.linkTo(avatar.bottom)
            }
            .padding(bottom = 10.dp), verticalAlignment = Alignment.CenterVertically) {
            Image(
                painter = painterResource(id = R.mipmap.ic_female),
                contentDescription = "gender",
                modifier = Modifier
                    .padding(end = 3.dp)
                    .size(20.dp)
            )
            Text(text = "25", color = Color.White, modifier = Modifier.padding(start = 3.dp))
            Text(text = "中国", color = Color.White, modifier = Modifier.padding(start = 5.dp))
        }

        Button(onClick = {
            context.startActivity(Intent(context, LoginActivity::class.java))
        }, modifier = Modifier
            .constrainAs(contact) {
                bottom.linkTo(parent.bottom)
                end.linkTo(parent.end)
            }
            .padding(10.dp)
            .background(Color.Cyan)
            .size(50.dp)
            .clip(RoundedCornerShape(50))) {

        }
    }
}

@Preview
@Composable
fun HomePreview() {
    HomePage()
}
