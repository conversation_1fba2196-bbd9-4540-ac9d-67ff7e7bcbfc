package com.heart.heartmerge.ui.widget.slide;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.view.View;
import android.view.animation.LinearInterpolator;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 创建日期   2023/8/28 15:12
 *
 * <AUTHOR>
 * @类说明
 */
public class CardStackItemTouchHelper extends ItemTouchHelper {

    RecyclerView mCardStackRecyclerView;
    CardStackCallback mCardStackCallback;

    protected ValueAnimator mValueAnimator;

    public CardStackItemTouchHelper(@NonNull CardStackCallback callback) {
        super(callback);
        mCardStackCallback = callback;
    }

    @Override
    public void attachToRecyclerView(@Nullable RecyclerView rv) {
        mCardStackRecyclerView = rv;
        super.attachToRecyclerView(rv);
    }

    //扩展实现:点击按钮实现左滑效果
    public void toLeft(RecyclerView.ViewHolder holder) {
        if (check()) {
            animTo(holder, mCardStackRecyclerView, ItemTouchHelper.LEFT);
        }
    }

    //扩展实现:点击按钮实现右滑效果
    public void toRight(RecyclerView.ViewHolder holder) {
        if (check()) {
            animTo(holder, mCardStackRecyclerView, ItemTouchHelper.RIGHT);
        }
    }

    @SuppressLint("LongLogTag")
    protected void animTo(RecyclerView.ViewHolder holder, RecyclerView recyclerView, int direction) {
        View view = holder.itemView;
        float distance = (float) recyclerView.getWidth() + (float) recyclerView.getWidth() * 0.2f;
        mValueAnimator = ValueAnimator.ofFloat(0f, direction == ItemTouchHelper.LEFT ? -distance : distance);
        mValueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                float dX = (float) valueAnimator.getAnimatedValue();
                view.setTranslationX(dX);

                if (mCardStackCallback != null){
                    mCardStackCallback.onChildDrawSwipeLogic(recyclerView, holder, dX);
                }
            }
        });
        mValueAnimator.addListener(new AnimatorListenerAdapter() {
            @SuppressLint("NotifyDataSetChanged")
            @Override
            public void onAnimationEnd(Animator animation) {
                recyclerView.removeView(view);
                if (mCardStackCallback != null){
                    mCardStackCallback.onSwiped(holder, direction);
                }
            }
        });

        mValueAnimator.setInterpolator(new LinearInterpolator());
        mValueAnimator.setDuration(ItemConfig.AUTO_SWIPED_ANIM_DURATION);
        mValueAnimator.start();
    }

    public boolean isAutoSwiping() {
        if (mValueAnimator != null) {
            return mValueAnimator.isRunning();
        }
        return false;
    }

    @SuppressLint("LongLogTag")
    public boolean check() {
        if (isAutoSwiping()) {
            return false;
        }
        if (mCardStackRecyclerView == null || mCardStackRecyclerView.getAdapter() == null) {
            return false;
        }
        if (mCardStackRecyclerView.getAdapter().getItemCount() == 0) {
            return false;
        }
        return true;
    }


    public abstract static class CardStackCallback extends Callback {

        public abstract void onChildDrawSwipeLogic(RecyclerView recyclerView, RecyclerView.ViewHolder holder, float dX);

    }
}
