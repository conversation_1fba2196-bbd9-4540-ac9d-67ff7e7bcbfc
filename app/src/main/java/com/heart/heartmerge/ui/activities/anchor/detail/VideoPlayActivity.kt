package com.heart.heartmerge.ui.activities.anchor.detail

import android.view.View
import android.widget.ImageView
import coil.load
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.click
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.BuildConfig
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.ActivityVideoPlayBinding
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond
import com.heart.heartmerge.popup.PayVideoTipPopup
import com.heart.heartmerge.popup.showPayVideoTipPopup
import com.heart.heartmerge.popup.showReportPopup
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.PurchaseScene
import com.heart.heartmerge.viewmodes.AnchorViewModel
import com.heart.heartmerge.viewmodes.SearchRequestEvent
import com.shuyu.gsyvideoplayer.GSYVideoManager
import com.shuyu.gsyvideoplayer.listener.GSYVideoProgressListener
import com.shuyu.gsyvideoplayer.utils.OrientationUtils
import io.rong.imkit.IMCenter
import io.rong.imlib.IRongCoreCallback
import io.rong.imlib.IRongCoreEnum
import io.rong.imlib.RongCoreClient
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Message

/**
 * Author:Lxf
 * Create on:2024/8/15
 * Description:
 */
class VideoPlayActivity : BaseCoreActivity<ActivityVideoPlayBinding, AnchorViewModel>() {
    private val playSource: String get() = intent.getStringExtra("video_url") ?: ""
    private val thumbUrl: String get() = intent.getStringExtra("thumb_url") ?: ""
    private val anchorId: String
        get() = intent.getStringExtra(Constants.INTENT_PARAM_KEY_ANCHOR_UID) ?: ""
    private val sightMessageUid: String get() = intent.getStringExtra("sight_message_uid") ?: ""
    private val anchorHeader: String get() = intent.getStringExtra("anchor_header") ?: ""
    private var updateMessage: Message? = null

    private var orientationUtil: OrientationUtils? = null
    private var mMsgExpansion: MutableMap<String, String>? = null
    private var mPayVideoTipPopup: PayVideoTipPopup? = null

    private var canPlayedDirectly = true  //是否可以直接播放


    override fun getLayoutId() = R.layout.activity_video_play
    override fun initView() {
        if (!BuildConfig.DEBUG) {
            AppUtil.screenSecure(window)
        }
        mBinding.videoPlayer.apply {
            setUp(playSource, true, "")
            val thumbImageView = ImageView(context).apply {
                setScaleType(ImageView.ScaleType.CENTER_CROP)
                load(thumbUrl)
            }
            setThumbImageView(thumbImageView) //增加封面
            titleTextView.visibility = View.VISIBLE //增加title
            backButton.visibility = View.VISIBLE//设置返回键
            orientationUtil = OrientationUtils(this@VideoPlayActivity, this) //设置旋转
            fullscreenButton.click { //设置全屏按键功能,这是使用的是选择屏幕，而不是全屏
                orientationUtil?.resolveByClick()
            }
            setIsTouchWiget(true) //是否可以滑动调整
            backButton.click {
                onBackPressed()
            }

            if (sightMessageUid.isNotEmpty()) {
                RongCoreClient.getInstance().getMessageByUid(
                    sightMessageUid, object : IRongCoreCallback.ResultCallback<Message?>() {
                        override fun onSuccess(t: Message?) {
                            t?.let {
                                updateMessage = t
                                mMsgExpansion = t.expansion
                                LogX.d("VideoPlayActivity  mMsgExpansion: ${t.expansion} , extra:${t.extra}, playSource:$playSource, messageId:$sightMessageUid, isCanIncludeExpansion: ${t.isCanIncludeExpansion}")
                                mMsgExpansion?.let {
                                    val isFree = it["free"]?.toBoolean() ?: true
                                    if (!isFree) {
                                        // 未付费，准备相关参数
                                        val diamond = (it["coin"] as? String)?.toIntOrNull() ?: 0
                                        val freeDurationMs = ((it["free_duration"] as? String)?.toIntOrNull() ?: 2) * 1000
                                        val videoId = (it["media_id"] as? String)?.toLongOrNull() ?: -1L
                                        canPlayedDirectly = false
                                        setGSYVideoProgressListener { progress, secProgress, currentPosition, duration ->
                                            if (currentPosition > (freeDurationMs) && duration > (freeDurationMs)) {
                                                mBinding.videoPlayer.onVideoPause()
                                                if (mPayVideoTipPopup == null) {
                                                    mPayVideoTipPopup =
                                                        showPayVideoTipPopup(
                                                            this@VideoPlayActivity,
                                                            image = anchorHeader,
                                                            diamond = diamond.toShowDiamond(),
                                                            block = {
                                                                AppUtil.checkoutDiamondAndShowPopup(
                                                                    this@VideoPlayActivity,
                                                                    diamond,
                                                                    purchaseScene = PurchaseScene.PaidVideo(
                                                                        anchorId = anchorId,
                                                                        videoId = videoId,
                                                                        url = playSource
                                                                    )
                                                                ) {
                                                                    //调用接口扣费
                                                                    mViewModel.userAlbumPay(sightMessageUid)
                                                                }
                                                            }) {
                                                            finish()
                                                        }
                                                } else {
                                                    mPayVideoTipPopup?.show()
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        override fun onError(e: IRongCoreEnum.CoreErrorCode?) {
                        }
                    })
            }

            if (playSource.isNotEmpty()) {
                startPlayLogic()
            }
        }
        mBinding.flReport.click {
            showReportPopup(this, anchorId)
        }
    }

    override fun initViewEvents() {
        mViewModel.pageEvents.observeEvent(this) {
            when (it) {
                is SearchRequestEvent.UserAlbumPayFailed -> ToastUtil.show(it.msg)

                is SearchRequestEvent.UserAlbumPaySuccess -> {
                    ToastUtil.show(getString(R.string.payment_suc))
                    mPayVideoTipPopup?.dismiss()
                    mBinding.videoPlayer.setGSYVideoProgressListener(null)
                    mBinding.videoPlayer.onVideoResume()
                    canPlayedDirectly = true
                    mMsgExpansion?.let { expansion ->
                        expansion["free"] = "true"
                        RongIMClient.getInstance().updateMessageExpansion(
                            expansion, sightMessageUid, object : RongIMClient.OperationCallback() {
                                override fun onSuccess() {
                                    LogX.e("updateMessageExpansion success")
                                    updateMessage?.let { message ->
                                        val javaHashMap = HashMap(mMsgExpansion ?: emptyMap())
                                        message.setExpansion(javaHashMap)
                                        IMCenter.getInstance().refreshMessage(message)
                                    }
                                }

                                override fun onError(errorCode: RongIMClient.ErrorCode?) {
                                    LogX.e("updateMessageExpansion error $errorCode")
                                }
                            })
                    }
                }

                else -> {}
            }
        }
    }

    override fun onBackPressed() {
        mBinding.videoPlayer.setVideoAllCallBack(null)
        super.onBackPressed()
    }

    override fun onPause() {
        super.onPause()
        mBinding.videoPlayer.onVideoPause()
    }

    override fun onResume() {
        super.onResume()
        if (canPlayedDirectly) {
            mBinding.videoPlayer.onVideoResume()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        GSYVideoManager.releaseAllVideos()
        orientationUtil?.releaseListener()
    }

}