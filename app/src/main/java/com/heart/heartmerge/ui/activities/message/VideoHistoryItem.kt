package com.heart.heartmerge.ui.activities.message

import android.os.Bundle
import androidx.core.content.ContextCompat
import coil.load
import coil.transform.CircleCropTransformation
import coil.transform.RoundedCornersTransformation
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.DslViewHolder
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.beans.VideoRecordInfo
import com.heart.heartmerge.ui.activities.anchor.AnchorVideoActivity
import com.heart.heartmerge.ui.activities.anchor.detail.AnchorDetailActivity
import com.heart.heartmerge.ui.activities.message.MyRongConversationActivity
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.Constants.GENDER_FEMALE
import com.heart.heartmerge.utils.toJson
import io.rong.imlib.model.Conversation
import java.util.Locale

/**
 * 作者：Lxf
 * 创建日期：2024/7/27 17:17
 * 描述：
 */
class VideoHistoryItem : DslAdapterItem() {
    init {
        itemLayoutId = R.layout.item_video_history
    }

    var recordInfo: VideoRecordInfo? = null

    override fun onItemBind(
        itemHolder: DslViewHolder,
        itemPosition: Int,
        adapterItem: DslAdapterItem,
        payloads: List<Any>
    ) {
        super.onItemBind(itemHolder, itemPosition, adapterItem, payloads)
        itemHolder.apply {
            clickItem {
                //ID需要做下转换
                val userBean = UserBean(id = recordInfo?.anchorUserId ?: "", avatar = recordInfo?.headFileName ?: "", nickname = recordInfo?.nickName ?: "")
                jump(
                    AnchorDetailActivity::class.java,
                    Bundle().apply { putString(Constants.INTENT_PARAM_KEY_ANCHOR_INFO, userBean.toJson()) })
            }
            recordInfo?.apply {
                img(R.id.iv_avatar)?.load(headFileName) {
                    error(R.mipmap.ic_pic_default_oval)
                    placeholder(R.mipmap.ic_pic_default_oval)
                    transformations(CircleCropTransformation())
                }
                tv(R.id.tv_nickname)?.text = nickName
                when (callStat) {//通话状态 1未接通 2已接通
                    "2" -> {
                        tv(R.id.videoStatus)?.text = context.getString(R.string.tip_call_done)
                        tv(R.id.videoStatus)?.setTextColor(ContextCompat.getColor(context, R.color.color_21C76E))
                        img(R.id.iv_status)?.setImageResource(R.mipmap.ic_call_history_suc)
                    }

                    else -> {
                        tv(R.id.videoStatus)?.text = context.getString(R.string.tip_call_cancel)
                        tv(R.id.videoStatus)?.setTextColor(ContextCompat.getColor(context, R.color.color_DE3953))
                        img(R.id.iv_status)?.setImageResource(R.mipmap.ic_call_history_failed)
                    }
                }
                tv(R.id.videoTime)?.text = AppUtil.translateDuration(createTime)
            }
        }
    }
}