package com.heart.heartmerge.ui.activities.mine

import android.app.Activity
import android.net.Uri
import android.os.Build
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.lifecycle.asLiveData
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.observeOnce
import com.bdc.android.library.extension.toast
import com.bdc.android.library.imageloader.ImageLoader
import com.bdc.android.library.utils.ToastUtil
import com.google.gson.JsonObject
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.ActivityProfileBinding
import com.heart.heartmerge.extension.buildImageUrl
import com.heart.heartmerge.extension.formatDate
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.mmkv.MMKVBaseDataRep
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.popup.showAvatarPopup
import com.heart.heartmerge.popup.showBirthdayPopup
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.UCropUtil
import com.heart.heartmerge.viewmodes.UserViewModel
import com.yalantis.ucrop.UCrop
import top.zibin.luban.Luban
import top.zibin.luban.OnNewCompressListener
import java.io.File

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/7 19:27
 * @description :个人资料
 */
class ProfileActivity : BaseCoreActivity<ActivityProfileBinding, UserViewModel>() {

    private val params = JsonObject()

    override fun getLayoutId(): Int = R.layout.activity_profile

    private val pickMedia = registerForActivityResult(ActivityResultContracts.PickVisualMedia()) {
        it?.let {
            startCrop(it)
        }
    }

    private fun startCrop(sourceUri: Uri) {
        // 启动裁剪
        cropImageLauncher.launch(UCropUtil.getIntent(this, sourceUri))
    }

    private val cropImageLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                val data = result.data
                val resultUri = UCrop.getOutput(data!!)
                resultUri?.let {
                    uploadAvatar(resultUri.path)
                }
            } else if (result.resultCode == UCrop.RESULT_ERROR) {
                LogX.e("UCrop Error")
            }
        }


    override fun initData() {
        super.initData()
        mViewModel.userBean.asLiveData().observeOnce(this) {
            ImageLoader.with(this).load(it.avatar.buildImageUrl()).asAvatar()
                .into(mBinding.ivAvatar)
            mBinding.etNickname.setText(it.nickname)
            mBinding.tvBirthday.text =
                it.birthdayAt.takeIf { it.isNotEmpty() }?.toLong()?.formatDate()
            mBinding.tvCountry.text = it.userCountry?.code
            mBinding.tvGender.text =
                if (it.gender == Constants.GENDER_FEMALE) getString(R.string.gender_female) else getString(
                    R.string.gender_male
                )
        }
    }

    private fun uploadAvatar(it: String?) {
        if (it == null) {
            ToastUtil.show("un select image")
        } else {
            showLoading("Uploading")
            fun upload(path: String) {
                mViewModel.upload(path).asLiveData().observe(this) {
                    if (it.success) {
                        ImageLoader.with(this).load(it.accessUrl).asAvatar().into(mBinding.ivAvatar)
                        params.addProperty("avatar", it.objectKey)
                    }
                    stopLoading()
                }
            }

            Luban.with(this).load(it).ignoreBy(100)
                .setCompressListener(object : OnNewCompressListener {
                    override fun onStart() {
                    }

                    override fun onSuccess(source: String?, compressFile: File?) {
                        compressFile?.let {
                            upload(it.path)
                        }
                    }

                    override fun onError(source: String?, e: Throwable?) {
                        source?.let {
                            upload(it)
                        }
                    }
                }).launch()
        }
    }

    override fun bindListener() {
        mBinding.llAvatar.click {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && ActivityResultContracts.PickVisualMedia.isPhotoPickerAvailable(
                    this
                )
            ) {
                pickMedia.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly))
            } else {
                showAvatarPopup(this) {
                    uploadAvatar(it)
                }
            }
        }

        mBinding.llBirthday.click {
            showBirthdayPopup(this) {
                mBinding.tvBirthday.text = it.formatDate()
                params.addProperty("birthday", it.toString())
            }
        }

        mBinding.btnSave.click {
            showLoading("Loading")
            mViewModel.updateProfile(params.apply {
                addProperty("nickname", mBinding.etNickname.text.toString())
                addProperty("register_update", 3)
                addProperty("update_type", 3)
                if (!has("birthday")) {
                    addProperty("birthday", MMKVDataRep.userInfo.birthdayAt)
                }
//                if (!has("languages")) {
//                    add(
//                        "languages",
//                        Gson().toJsonTree(MMKVDataRep.userInfo.languages.map { it.languageCode })
//                    )
//                }
//                if (!has("country_id")) {
//                    addProperty(
//                        "country_id", MMKVDataRep.userInfo.country
//                    )
//                }
            }).asLiveData().observe(this) {
                stopLoading()
                toast(getString(R.string.modify_successful))
                MMKVBaseDataRep.token = it.accessToken
                MMKVDataRep.userInfo = it.user
                MMKVDataRep.userConfig = it.config
                finish()
            }
        }
    }
}