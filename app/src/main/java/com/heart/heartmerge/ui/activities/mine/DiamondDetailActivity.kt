package com.heart.heartmerge.ui.activities.mine

import android.view.View
import androidx.core.content.ContextCompat
import androidx.lifecycle.asLiveData
import com.angcyo.dsladapter.DslAdapterItem
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.WalletTransactionBean
import com.heart.heartmerge.databinding.ActivityDiamondDetailBinding
import com.heart.heartmerge.extension.formatDateTime
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond
import com.heart.heartmerge.viewmodes.UserViewModel

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/12 15:41
 * @description :钻石明细列表
 */
class DiamondDetailActivity : BaseCoreActivity<ActivityDiamondDetailBinding, UserViewModel>() {
    override fun getLayoutId(): Int = R.layout.activity_diamond_detail

    override fun initView() {
        mBinding.refreshLayout.recyclerView.enableLoadMore(true)
        mBinding.refreshLayout.setOnRefreshListener { b, i ->
            fetch(b, i)
        }
        mBinding.refreshLayout.onRefresh()
    }

    private fun fetch(refresh: Boolean, pageIndex: Int) {
        val colors = listOf(R.color.color_9F2AF8, R.color.color_8F5CF4, R.color.color_28B8E4)
        mViewModel.fetchWalletTransactionList(refresh).asLiveData().observe(this) {
            if (it.isNullOrEmpty() || it.size < 20) {
                mBinding.refreshLayout.recyclerView.enableLoadMore(false)
            }
            mBinding.refreshLayout.append<DslAdapterItem>(refresh, it.orEmpty()) {
                itemLayoutId = R.layout.item_diamond_detail
                itemBindOverride = { itemHolder, _, _, _ ->
                    val item = itemData as? WalletTransactionBean
                    itemHolder.v<View>(R.id.indicator)?.setBackgroundColor(
                        ContextCompat.getColor(
                            itemHolder.context, colors.random()
                        )
                    )
                    itemHolder.tv(R.id.tv_title)?.text = item?.text
//                        DiamondChangeType.get(item?.changeType.toString())
                    itemHolder.tv(R.id.tv_date)?.text = item?.actionAt?.formatDateTime()
                    itemHolder.tv(R.id.tv_price)?.text =
                        "${if ((item?.coin ?: 0) >= (item?.coin?.toShowDiamond() ?: 0)) "+" else ""}${item?.coin?.toShowDiamond()}"
                }
            }
        }
    }
}