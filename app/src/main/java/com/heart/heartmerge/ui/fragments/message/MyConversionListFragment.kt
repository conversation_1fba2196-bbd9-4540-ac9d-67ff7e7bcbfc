package com.heart.heartmerge.ui.fragments.message

import android.os.Bundle
import android.view.View
import androidx.compose.ui.platform.ComposeView
import androidx.lifecycle.ViewModelProvider
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.bdc.android.library.mvi.observeEvent
import com.heart.heartmerge.R
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.ui.activities.WhoSeeMeActivity
import com.heart.heartmerge.ui.theme.HeartMergeTheme
import com.heart.heartmerge.ui.widget.DailyGrowingNumber
import com.heart.heartmerge.utils.RongMessageUtil
import com.heart.heartmerge.viewmodes.AnchorViewModel
import com.heart.heartmerge.viewmodes.SearchRequestEvent
import io.rong.imkit.conversationlist.ConversationListFragment
import io.rong.imkit.conversationlist.viewmodel.ConversationListViewModel
import io.rong.imkit.widget.refresh.constant.RefreshState


/**
 * Author:Lxf
 * Create on:2024/8/5
 * Description:
 */
class MyConversionListFragment : ConversationListFragment() {
    companion object {
        private const val TAG = "MyConversionListFragment"
    }

    private lateinit var anchorViewModel: AnchorViewModel
    private val cachedAnchorIds = mutableSetOf<String>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val whoSeeMe = view.findViewById<View>(R.id.cl_who_see_me)
        whoSeeMe.click {
//            if (MMKVDataRep.userInfo.isVIP) {
            jump(WhoSeeMeActivity::class.java)

//            } else {
//                context?.let { it1 ->
//                    showWhoSeeMePopup(
//                        it1 as Activity,
//                        block = {
//                            jump(MembershipCenterActivity::class.java)
//                        }
//                    )
//                }
//            }
        }
        val whoSeeMeLikeNum = view.findViewById<ComposeView>(R.id.tv_like_num)
        whoSeeMeLikeNum.setContent {
            HeartMergeTheme {
                DailyGrowingNumber()
            }
        }
    }

    override fun subscribeUi() {
        super.subscribeUi()
        mConversationListViewModel =
            (ViewModelProvider(this))[ConversationListViewModel::class.java]
        anchorViewModel = (ViewModelProvider(this))[AnchorViewModel::class.java]
        mConversationListViewModel.getConversationList(false, false, 0L)
        mConversationListViewModel.conversationListLiveData.observe(
            viewLifecycleOwner
        ) { conversationList ->
            if (mNewState == 0) {
                mAdapter.setDataCollection(conversationList)
                val anchorIdsToFetch = mutableListOf<String>()
                conversationList.forEach { item ->
                    val targetId = item.mCore.targetId
                    if (!cachedAnchorIds.contains(targetId)) {
                        anchorIdsToFetch.add(targetId)
                        cachedAnchorIds.add(targetId) // Add to cache immediately to avoid duplicate fetches
                    }

//                    RongMessageUtil.clearHistoryMessageBefore15Days(targetId)
                }
                if (anchorIdsToFetch.isNotEmpty()) {
                    LogX.e("Fetching anchor info for IDs: $anchorIdsToFetch")
                    anchorViewModel.fetchAnchorList(anchorIdsToFetch)
                }
            } else {
                delayRefresh = true
            }
        }
        mConversationListViewModel.noticeContentLiveData.observe(
            viewLifecycleOwner
        ) {
            if (mNoticeContainerView.visibility == View.GONE) {
                mHandler.postDelayed(Runnable {
                    updateNoticeContent(mConversationListViewModel.noticeContentLiveData.value)
                }, 4000L)
            } else {
                updateNoticeContent(it)
            }
        }
        mConversationListViewModel.refreshEventLiveData.observe(
            viewLifecycleOwner
        ) {
            if (it.state == RefreshState.LoadFinish) {
                if (mRefreshLayout != null) {
                    mRefreshLayout.finishLoadMore()
                } else {
                    LogX.e("onChanged finishLoadMore error")
                }
            } else if (it.state == RefreshState.RefreshFinish) {
                if (mRefreshLayout != null) {
                    mRefreshLayout.finishRefresh()
                } else {
                    LogX.e("onChanged finishRefresh error")
                }
            }
        }

        anchorViewModel.pageEvents.observeEvent(viewLifecycleOwner) { event ->
            when (event) {
                is SearchRequestEvent.FetchAnchorListFailed -> {
                    LogX.e("FetchAnchorListFailed: ${event.msg}, IDs: ${event.anchorIds}")
                    // If fetching fails, remove these IDs from cache so they can be retried
                    cachedAnchorIds.removeAll(event.anchorIds.toSet())
                }

                else -> {
                }
            }
        }
    }
}