package com.heart.heartmerge.ui.adapter

import android.annotation.SuppressLint
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import androidx.core.text.isDigitsOnly
import com.angcyo.dsladapter.DslAdapter
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.DslViewHolder
import com.angcyo.dsladapter.ItemSelectorHelper
import com.bdc.android.library.base.dslitem.BaseDslItem
import com.bdc.android.library.extension.makeVisible
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.GoodsBean
import com.heart.heartmerge.extension.formatPercent
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond
import com.heart.heartmerge.utils.AppUtil

class DiamondRechargeAdapter(
    items: List<DiamondRechargeItem>
) : DslAdapter(items) {

    init {
        itemSelectorHelper.selectorModel = ItemSelectorHelper.MODEL_SINGLE
    }

    class DiamondRechargeItem(
        bean: GoodsBean, val level: Int, val block: (GoodsBean) -> Unit = {}
    ) : BaseDslItem<GoodsBean>(R.layout.item_diamond_recharge) {

        init {
            itemData = bean
        }

        @SuppressLint("DefaultLocale")
        override fun onItemBindAfter(
            itemHolder: DslViewHolder,
            itemPosition: Int,
            adapterItem: DslAdapterItem,
            payloads: List<Any>
        ) {
            super.onItemBindAfter(itemHolder, itemPosition, adapterItem, payloads)
            val item = itemData as? GoodsBean

            if (item?.isSubscribe == true) {
                itemHolder.img(R.id.iv_icon)?.setBackgroundResource(
                    when (item.sku) {
                        "subweek" -> R.mipmap.ic_label_bronze_vip
                        "submonth" -> R.mipmap.ic_label_silver_vip
                        "sub3month" -> R.mipmap.ic_label_gold_vip
                        else -> R.mipmap.ic_vip_crown
                    }
                )
//                itemHolder.tv(R.id.tv_diamond_value)
//                    ?.setTextColor(ContextCompat.getColor(itemHolder.context, R.color.color_1B1207))
//                itemHolder.tv(R.id.tv_give_diamond)
//                    ?.setTextColor(ContextCompat.getColor(itemHolder.context, R.color.color_1B1207))
                itemHolder.v<TextView>(R.id.btn_amount)?.apply {
                    setBackgroundResource(R.drawable.shape_ff962d_radius50)
                    setTextColor(ContextCompat.getColor(itemHolder.context, R.color.color_white))
                }
            } else {
//                itemHolder.view(R.id.rl_container)?.setBackgroundResource(R.mipmap.bg_recharge_item)
                itemHolder.img(R.id.iv_icon)?.apply {
                    val resId =
                        AppUtil.getDrawableByName("ic_diamond_recharge_level${if (itemPosition < 6) itemPosition + 1 else 6}")
                    resId?.let {
                        setBackgroundResource(resId)
                    } ?: run { setBackgroundResource(R.mipmap.ic_diamond_recharge_level6) }
                }
                itemHolder.tv(R.id.tv_diamond_value)
                    ?.setTextColor(ContextCompat.getColor(itemHolder.context, R.color.color_white))
                itemHolder.tv(R.id.tv_give_diamond)
                    ?.setTextColor(ContextCompat.getColor(itemHolder.context, R.color.color_white))
//                itemHolder.v<Button>(R.id.btn_amount)?.apply {
//                    setBackgroundResource(R.drawable.selector_primary_button)
//                    setTextColor(ContextCompat.getColor(itemHolder.context, R.color.color_white))
//                }
            }

            itemHolder.tv(R.id.tv_label)?.apply {
                makeVisible(
                    item?.hasDiscount == true || item?.hasCountdown == true
                )
                text = if (item?.hasCountdown == true) {
                    itemHolder.v<CardView>(R.id.container_card_view)?.setCardBackgroundColor(
                        ContextCompat.getColor(
                            itemHolder.context, R.color.color_4F3B78
                        )
                    )
                    item?.countdown
                } else {
                    itemHolder.v<CardView>(R.id.container_card_view)?.setCardBackgroundColor(
                        ContextCompat.getColor(
                            itemHolder.context, R.color.color_card_background
                        )
                    )
                    itemHolder.context.getString(
                        R.string.recharge_offer_percent,
                        (item?.discount ?: 0.0).formatPercent()
                    )
                }
            }

            itemHolder.tv(R.id.tv_diamond_value)?.text =
                if (item?.isSubscribe == true) "${item.title}"
                else {
                    if (item?.coin_1?.isNotEmpty() == true && item.coin_1.isDigitsOnly()) {
                        "${
                            item.coin_1.toInt().toShowDiamond()
                        }"
                    } else {
                        "${
                            item?.coin?.toInt()?.toShowDiamond()
                        }"
                    }
                }

            itemHolder.tv(R.id.tv_sub_title)?.apply {
                makeVisible(item?.coin_2?.isNotEmpty() == true && item.coin_2.isDigitsOnly() && item.coin_2.toInt() > 0)
                text = item?.coin_2?.toInt()?.toShowDiamond().toString()
            }

            itemHolder.view(R.id.ll_give_container)?.makeVisible(
                item?.level_bonus?.isDigitsOnly() == true && (item?.level_bonus?.toInt() ?: 0) > 0
            )
            itemHolder.tv(R.id.tv_give_diamond)?.text = item?.level_bonus?.isDigitsOnly().let {
                item?.level_bonus?.toInt()?.toShowDiamond().toString()
            }

            itemHolder.tv(R.id.tv_level)?.text = itemHolder.context.getString(
                if (item?.isSubscribe == true) R.string.recharge_level_bonus_vip else R.string.recharge_level_bonus,
                "$level"
            )

            itemHolder.v<TextView>(R.id.btn_amount)?.apply {
                text =
                    item?.googleExtras?.subscriptionOfferDetails?.firstOrNull()?.pricingPhases?.pricingPhaseList?.firstOrNull()?.formattedPrice
                        ?: item?.googleExtras?.oneTimePurchaseOfferDetails?.formattedPrice
                                ?: "${item?.formattedPrice}"
            }
            itemHolder.clickItem {
                updateItemSelect(true)
                item?.let { bean -> block.invoke(bean) }
            }
        }
    }
}
