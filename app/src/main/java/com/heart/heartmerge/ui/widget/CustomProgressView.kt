package com.heart.heartmerge.ui.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Path
import android.graphics.RectF
import android.graphics.Shader
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.view.View
import com.heart.heartmerge.R
import com.heart.heartmerge.utils.AppUtil

/**
 * Author:Lxf
 * Create on:2024/8/28
 * Description:
 */


class CustomProgressView(context: Context, attrs: AttributeSet?) : View(context, attrs) {
    private val progressPaint = Paint()
    private val backgroundPaint = Paint()
    private var progress = 1f
    private var startColor = Color.parseColor("#F53D3D")
    private var endColor = Color.parseColor("#F53D3D")
    private var colors = intArrayOf(startColor, endColor)
    private var progressRect: RectF
    private var radio: Int = 0
    private var x = 0f
    private var progressCallback: ProgressChange? = null

    //绘制Path，限定Canvas边框
    private val path = Path()

    init {
        // 初始化进度条画笔
        progressPaint.isAntiAlias = true
        progressPaint.style = Paint.Style.FILL

        // 初始化背景画笔
        backgroundPaint.isAntiAlias = true
        backgroundPaint.style = Paint.Style.FILL
        backgroundPaint.color = Color.GRAY

        if (attrs != null) {
            val typedArray = context.obtainStyledAttributes(attrs, R.styleable.CustomProgressView)
            startColor = typedArray.getColor(R.styleable.CustomProgressView_startColor, startColor)
            endColor = typedArray.getColor(R.styleable.CustomProgressView_endColor, endColor)
            progress = typedArray.getFloat(R.styleable.CustomProgressView_progress, progress)
            colors = intArrayOf(startColor, endColor)
            typedArray.recycle()
        }

        progressRect = RectF()
        radio = context.resources.getDimensionPixelSize(com.bdc.android.library.R.dimen.dp_10)
    }

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        val width = width.toFloat()
        val height = height.toFloat()
        path.addRoundRect(0f, 0f, width, height, radio.toFloat(), radio.toFloat(), Path.Direction.CW)
        canvas.clipPath(path)
        //绘制进度条
        if (AppUtil.isRtl()){
            progressRect.set(width, 0f, width - width * progress / 100f, height)
            val shader = LinearGradient(width, 0f, width - width * progress / 100f, height, colors, null, Shader.TileMode.CLAMP)
            progressPaint.shader = shader
        }else{
            progressRect.set(0f, 0f, width * progress / 100f, height)
            val shader = LinearGradient(0f, 0f, width * progress / 100f, height, colors, null, Shader.TileMode.CLAMP)
            progressPaint.shader = shader
        }
        canvas.drawRect(progressRect, progressPaint)
    }

    fun setProgress(progress: Float) {
        this.progress = progress
        invalidate()
    }

    // 新增方法：启动自动增长动画
    fun startProgressAnimation(duration: Long) {
        val startTime = System.currentTimeMillis()
        val handler = Handler(Looper.getMainLooper())
        val updateTask = object : Runnable {
            override fun run() {
                val elapsed = System.currentTimeMillis() - startTime
                val fraction = elapsed.toFloat() / duration

                // 更新进度
                setProgress(fraction * 100)

                // 如果未到达目标时间，继续更新
                if (fraction < 1f) {
                    handler.postDelayed(this, 16) // 大约60fps
                }
            }
        }

        handler.post(updateTask)
    }

    fun setOnProgressChangeListener(callback: ProgressChange) {
        progressCallback = callback
    }

    interface ProgressChange {
        fun onProgressChange(progress: Float)
    }
}
