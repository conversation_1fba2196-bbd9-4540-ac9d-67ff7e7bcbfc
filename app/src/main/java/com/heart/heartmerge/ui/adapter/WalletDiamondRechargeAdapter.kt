package com.heart.heartmerge.ui.adapter

import android.annotation.SuppressLint
import android.view.View
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import androidx.core.text.isDigitsOnly
import com.angcyo.dsladapter.DslAdapter
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.DslViewHolder
import com.angcyo.dsladapter.ItemSelectorHelper
import com.bdc.android.library.base.dslitem.BaseDslItem
import com.bdc.android.library.extension.makeVisible
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.GoodsBean
import com.heart.heartmerge.extension.formatPercent
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond
import com.heart.heartmerge.popup.showChoosePaymentPopup
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.PurchaseScene

class WalletDiamondRechargeAdapter(items: List<DiamondRechargeItem>) : DslAdapter(items) {
    init {
        itemSelectorHelper.selectorModel = ItemSelectorHelper.MODEL_SINGLE
    }

    class DiamondRechargeItem(
        bean: GoodsBean, val purchaseScene: PurchaseScene? = null, val level: Int = 0
    ) : BaseDslItem<GoodsBean>(R.layout.item_wallet_diamond_recharge) {

        init {
            itemData = bean
        }

        @SuppressLint("SetTextI18n", "DefaultLocale")
        override fun onItemBindAfter(
            itemHolder: DslViewHolder,
            itemPosition: Int,
            adapterItem: DslAdapterItem,
            payloads: List<Any>
        ) {
            super.onItemBindAfter(itemHolder, itemPosition, adapterItem, payloads)
            val item = itemData as? GoodsBean

            itemHolder.img(R.id.iv_icon)?.apply {
                val resId =
                    AppUtil.getDrawableByName("ic_diamond_recharge_level${if (itemPosition < 6) itemPosition + 1 else 6}")
                resId?.let {
                    setBackgroundResource(resId)
                } ?: run { setBackgroundResource(R.mipmap.ic_diamond_recharge_level6) }
            }

            itemHolder.tv(R.id.tv_label)?.apply {
                makeVisible(item?.hasDiscount == true || item?.hasCountdown == true)
                text = if (item?.hasCountdown == true) {
                    itemHolder.v<CardView>(R.id.container_card_view)?.setCardBackgroundColor(
                        ContextCompat.getColor(
                            itemHolder.context, R.color.color_4F3B78
                        )
                    )
                    item?.countdown
                } else {
                    itemHolder.v<CardView>(R.id.container_card_view)?.setCardBackgroundColor(
                        ContextCompat.getColor(
                            itemHolder.context, R.color.color_card_background
                        )
                    )
                    itemHolder.context.getString(
                        R.string.recharge_offer_percent,
                        (item?.discount ?: 0.0).formatPercent()
                    )
                }
            }
            itemHolder.tv(R.id.tv_title)?.apply {
                text =
                    if (item?.coin_1?.isNotEmpty() == true && item?.coin_1?.isDigitsOnly() == true) {
                        "${item.coin_1.toInt().toShowDiamond()}"
                    } else {
                        "${item?.coin?.toInt()?.toShowDiamond()}"
                    }
            }

            itemHolder.tv(R.id.tv_sub_title)?.apply {
                //首充不显示
                makeVisible(item?.coin_2?.isNotEmpty() == true && item.coin_2.isDigitsOnly() && item.coin_2.toInt() > 0)
                text = item?.coin_2?.toInt()?.toShowDiamond().toString()
            }

            itemHolder.v<View>(R.id.ll_give_container)
                ?.makeVisible(item?.level_bonus?.isDigitsOnly() == true && item.level_bonus.toInt() > 0)
            itemHolder.tv(R.id.tv_give_diamond)?.apply {
                text = item?.level_bonus?.isDigitsOnly().let {
                    item?.level_bonus?.toInt()?.toShowDiamond().toString()
                }
            }
            itemHolder.tv(R.id.tv_level)?.text = itemHolder.context.getString(
                if (item?.isSubscribe == true) R.string.recharge_level_bonus_vip else R.string.recharge_level_bonus,
                "$level"
            )
            itemHolder.v<TextView>(R.id.btn_recharge)?.apply {
                text = item?.googleExtras?.oneTimePurchaseOfferDetails?.formattedPrice
                    ?: "${item?.show_price}"
            }

            itemHolder.clickItem {
                updateItemSelect(true)
                showChoosePaymentPopup(
                    itemHolder.context as AppCompatActivity, item, purchaseScene = purchaseScene
                ) {}
            }
        }
    }
}
