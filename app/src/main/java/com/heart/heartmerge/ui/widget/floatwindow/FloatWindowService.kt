package com.heart.heartmerge.ui.widget.floatwindow

import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Binder
import android.os.Build
import android.os.IBinder
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.View.OnTouchListener
import android.view.WindowManager
import android.widget.ImageView
import android.widget.RelativeLayout
import com.heart.heartmerge.R
import kotlin.math.abs


/**
 * Author:Lxf
 * Create on:2024/8/1
 * Description:
 */
/**
 * 视频悬浮窗服务
 */
class FloatWindowService : Service(), OnTouchListener {
    private lateinit var mWindowManager: WindowManager
    private var inflater: LayoutInflater? = null

    //浮动布局view
    private var mFloatingLayout: View? = null

    //容器父布局
    private val mMainVIew: View? = null

    //开始触控的坐标，移动时的坐标（相对于屏幕左上角的坐标）
    private var mTouchStartX = 0
    private var mTouchStartY = 0
    private var mTouchCurrentX = 0
    private var mTouchCurrentY = 0

    //开始时的坐标和结束时的坐标（相对于自身控件的坐标）
    private var mStartX = 0
    private var mStartY = 0
    private var mStopX = 0
    private var mStopY = 0

    //判断悬浮窗口是否移动，这里做个标记，防止移动后松手触发了点击事件
    private var isMove = false
    override fun onCreate() {
        super.onCreate()
        initWindow() //设置悬浮窗基本参数（位置、宽高等）
    }

    override fun onBind(intent: Intent): IBinder {
//  currentBigUserId = intent.getStringExtra("localUserId")
//  remoteUserId = intent.getStringExtra("remoteUserId")
        initFloating() //悬浮框点击事件的处理
        return MyBinder()
    }

    inner class MyBinder : Binder() {
        val service: FloatWindowService
            get() = this@FloatWindowService
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        return super.onStartCommand(intent, flags, startId)
    }

    override fun onDestroy() {
        super.onDestroy()
        if (mFloatingLayout != null) {
// 移除悬浮窗口
            mWindowManager.removeView(mFloatingLayout)
            mFloatingLayout = null
        }
    }

    /**
     * 设置悬浮框基本参数（位置、宽高等）
     */
    private fun initWindow() {
        mWindowManager = applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        //设置好悬浮窗的参数
        // 悬浮窗默认显示以左上角为起始坐标
        wmParams.gravity = Gravity.END or Gravity.TOP
        //悬浮窗的开始位置，因为设置的是从右上角开始，所以屏幕左上角是x=屏幕最大值;y=0
        wmParams.x = 10
        wmParams.y = 120
        //得到容器，通过这个inflater来获得悬浮窗控件
        inflater = LayoutInflater.from(applicationContext)
        // 获取浮动窗口视图所在布局
        mFloatingLayout = inflater?.inflate(R.layout.activity_float_window, null)
        // 添加悬浮窗的视图
        mWindowManager.addView(mFloatingLayout, wmParams)
    }

    private val wmParams: WindowManager.LayoutParams
        get() {
            return WindowManager.LayoutParams().apply {
                if (Build.VERSION_CODES.O == Build.VERSION.SDK_INT) {
                    type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    type = WindowManager.LayoutParams.TYPE_PHONE
                }
                //设置可以显示在状态栏上
                flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or WindowManager.LayoutParams.FLAG_LAYOUT_INSET_DECOR or WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH
                //设置悬浮窗口长宽数据
                width = WindowManager.LayoutParams.WRAP_CONTENT
                height = WindowManager.LayoutParams.WRAP_CONTENT
            }
        }

    //加载远端视屏：在这对悬浮窗内内容做操作
    private fun initFloating() {
        //将子View加载进悬浮窗View
        val mMainView = mFloatingLayout!!.findViewById<RelativeLayout>(R.id.layout_call_video) //悬浮窗父布局
        val mChildView: ImageView = ImageView(applicationContext) //加载进悬浮窗的子View，这个VIew来自天转过来的那个Activity里面的那个需要加载的View
        // 设置图片资源
        mChildView.setImageResource(R.mipmap.ic_tab_mine_normal)
        mMainView.addView(mChildView) //将需要悬浮显示的Viewadd到mTXCloudVideoView中
        //悬浮框触摸事件，设置悬浮框可拖动
//        mTXCloudVideoView.setOnTouchListener(this::onTouch)
//        //悬浮框点击事件
//        mTXCloudVideoView.setOnClickListener(View.OnClickListener { //在这里实现点击重新回到Activity
//            val intent: Intent =
//                Intent(this@FloatWindowService, RtcActivity::class.java) //从该service跳转至该activity会将该activity从后台唤醒，所以activity会走onReStart（）
//            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK) //从Service跳转至RTCActivity，需要Intent.FLAG_ACTIVITY_NEW_TASK，不然会崩溃
//            startActivity(intent)
//        })
    }

    //触摸事件
    override fun onTouch(v: View, event: MotionEvent): Boolean {
        val action = event.action
        when (action) {
            MotionEvent.ACTION_DOWN -> {
                isMove = false
                mTouchStartX = event.rawX.toInt()
                mTouchStartY = event.rawY.toInt()
                mStartX = event.x.toInt()
                mStartY = event.y.toInt()
            }

            MotionEvent.ACTION_MOVE -> {
                mTouchCurrentX = event.rawX.toInt()
                mTouchCurrentY = event.rawY.toInt()
                wmParams.x += mTouchStartX - mTouchCurrentX
                wmParams.y += mTouchCurrentY - mTouchStartY
//                ALog.dTag("FloatingListener() onTouch", mTouchCurrentX, mTouchStartX, mTouchCurrentY, mTouchStartY)
                mWindowManager.updateViewLayout(mFloatingLayout, wmParams)
                mTouchStartX = mTouchCurrentX
                mTouchStartY = mTouchCurrentY
            }

            MotionEvent.ACTION_UP -> {
                mStopX = event.x.toInt()
                mStopY = event.y.toInt()
                if (abs(mStartX - mStopX) == 1 || abs(mStartY - mStopY) == 1) {
                    isMove = true
                }
            }

            else -> {}
        }
        //如果是移动事件不触发OnClick事件，防止移动的时候一放手形成点击事件
        return isMove
    }
}