package com.heart.heartmerge.ui.pages.message

import android.os.Build
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.slideIn
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Scaffold
import androidx.compose.material.ScrollableTabRow
import androidx.compose.material.Tab
import androidx.compose.material.TabRow
import androidx.compose.material.TabRowDefaults
import androidx.compose.material.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Call
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.SubcomposeLayout
import androidx.compose.ui.layout.layout
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import com.heart.heartmerge.R

@Composable
fun MessagePage() {
    Scaffold(
        modifier = Modifier.fillMaxSize(), backgroundColor = Color(0xff101321)
    ) { padding ->
        val tabs = listOf("消息", "我关注的", "关注我的")
        var selectedTabIndex by remember { mutableStateOf(0) }
        Box {
            ScrollableTabRow(
                selectedTabIndex = selectedTabIndex,
                modifier = Modifier
                    .padding(padding)
                    .statusBarsPadding()
                    .height(30.dp),
                backgroundColor = Color(0xff101321),
                edgePadding = 0.dp,
                indicator = { tabPositions ->
                    TabRowDefaults.Indicator(
                        modifier = Modifier
                            .tabIndicatorOffset(tabPositions[selectedTabIndex])
                            .padding(horizontal = 30.dp), height = 3.dp, color = Color.Red
                    )
                },
                contentColor = Color.Red
            ) {
                tabs.forEachIndexed { index, tab ->
                    Tab(selectedTabIndex == index,
                        onClick = {
                            selectedTabIndex = index
                        },
                        text = { Text(text = tab) },
                        selectedContentColor = Color.Red,
                        unselectedContentColor = Color.White,
                        modifier = Modifier.padding(horizontal = 0.dp, vertical = 0.dp)
                    )
                }
            }

            IconButton(
                onClick = { /*TODO*/ },
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(end = 5.dp)
                    .statusBarsPadding()
                    .size(30.dp)
            ) {
                Icon(
                    imageVector = Icons.Filled.Call,
                    contentDescription = "Video call",
                    tint = Color.White
                )
            }
        }

        AnimatedVisibility(
            visible = selectedTabIndex == 0,
            modifier = Modifier.padding(top = 45.dp),
            enter = slideInVertically()
        ) {
            BuildMessageContent(modifier = Modifier.padding(top = 30.dp))
//            BuildFollowContent(modifier = Modifier.padding(top = 30.dp))
        }
        AnimatedVisibility(
            visible = selectedTabIndex > 0,
            modifier = Modifier.padding(top = 45.dp),
            enter = slideInVertically()
        ) {
            BuildFollowContent(modifier = Modifier.padding(top = 30.dp))
        }
    }
}

@Composable
fun BuildMessageContent(modifier: Modifier = Modifier) {
    LazyColumn(modifier = modifier) {
        //系统消息
        items(count = 2) {
            BuildMessageItemContent(avatarRadiusPercent = 30)
        }

        item {
            Divider(
                Modifier
                    .padding(vertical = 10.dp)
                    .height(10.dp), color = Color(0xff1d2031)
            )
        }

        item {
            Text(
                text = "置顶聊天",
                color = Color.White,
                modifier = Modifier.padding(start = 20.dp, top = 5.dp, bottom = 5.dp)
            )
        }
        //置顶聊天
        items(count = 2) {
            BuildMessageItemContent()
        }

        item {
            Column {
                Divider(
                    color = Color(0xff161928),
                    modifier = Modifier.padding(top = 10.dp, bottom = 10.dp)
                )
                Text(
                    text = "聊天",
                    color = Color.White,
                    modifier = Modifier.padding(start = 20.dp, top = 5.dp, bottom = 5.dp)
                )
            }
        }
        //普通聊天消息
        items(count = 10) {
            BuildMessageItemContent()
        }
    }
}

@Composable
fun BuildMessageItemContent(avatarRadiusPercent: Int = 100, modifier: Modifier = Modifier) {
    ConstraintLayout(
        modifier = modifier
            .padding(vertical = 10.dp)
            .fillMaxWidth()
    ) {
        val (avatar, name, msg, time) = createRefs()

        Box(modifier = Modifier.constrainAs(avatar) {
            top.linkTo(parent.top)
            start.linkTo(parent.start)
            bottom.linkTo(parent.bottom)
        }) {
            Image(
                painter = painterResource(id = R.mipmap.bg_login),
                contentDescription = "avatar",
                contentScale = ContentScale.Crop,
                modifier = Modifier
                    .padding(start = 20.dp)
                    .size(50.dp)
                    .clip(RoundedCornerShape(avatarRadiusPercent))
            )
            Box(
                modifier = Modifier
                    .offset(x = 3.dp, y = (-3).dp)
                    .align(Alignment.TopEnd)
                    .background(Color(0xffff4d4f), shape = RoundedCornerShape(100))
                    .border(1.dp, color = Color.White, shape = RoundedCornerShape(100))
                    .size(width = 20.dp, height = 20.dp)
            ) {
                Text(
                    text = "9",
                    color = Color.White,
                    fontSize = 14.sp,
                    modifier = Modifier.align(Alignment.Center),
                    textAlign = TextAlign.Center,
                )
            }
            Box(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(end = 2.dp, bottom = 2.dp)
                    .background(Color(0xff20c66e), shape = RoundedCornerShape(100))
                    .size(8.dp)
            )
        }

        Text(text = "客户机器人", color = Color.White, modifier = Modifier.constrainAs(name) {
            top.linkTo(avatar.top)
            start.linkTo(avatar.end, margin = 10.dp)
        })

        Text(text = "您有一条新短信息，请注意查收",
            color = Color(0xff666666),
            fontSize = 10.sp,
            modifier = Modifier.constrainAs(msg) {
                bottom.linkTo(avatar.bottom, margin = 3.dp)
                start.linkTo(name.start)
            })

        Text(text = "2024.6.20",
            color = Color(0xff999999),
            fontSize = 10.sp,
            modifier = Modifier.constrainAs(time) {
                top.linkTo(name.top)
                bottom.linkTo(name.bottom)
                end.linkTo(parent.end, margin = 10.dp)
            })
    }
}

@Composable
fun BuildFollowContent(modifier: Modifier = Modifier) {
    LazyColumn(modifier = modifier) {
        items(10) {
            BuildFollowItemContent()
            Divider(color = Color(0xff151827))
        }
    }
}

@Composable
fun BuildFollowItemContent(modifier: Modifier = Modifier) {

    var isFollowed by remember {
        mutableStateOf(false)
    }

    ConstraintLayout(
        modifier = modifier
            .padding(vertical = 10.dp)
            .fillMaxWidth()
    ) {
        val (avatar, name, status, gender, contact) = createRefs()
        Box(modifier = Modifier.constrainAs(avatar) {
            top.linkTo(parent.top)
            start.linkTo(parent.start)
            bottom.linkTo(parent.bottom)
        }) {
            Image(
                painter = painterResource(id = R.mipmap.bg_login),
                contentDescription = "avatar",
                contentScale = ContentScale.Crop,
                modifier = Modifier
                    .padding(start = 20.dp)
                    .size(50.dp)
                    .clip(RoundedCornerShape(100))
            )
            Box(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(end = 2.dp, bottom = 2.dp)
                    .background(Color(0xff20c66e), shape = RoundedCornerShape(100))
                    .size(8.dp)
            )
        }

        Text(text = "用户A", color = Color.White, modifier = Modifier.constrainAs(name) {
            top.linkTo(avatar.top)
            start.linkTo(avatar.end, margin = 10.dp)
        })

        Text(text = if (isFollowed) "取消关注" else "关注",
            modifier = Modifier
                .constrainAs(status) {
                    start.linkTo(name.end, margin = 5.dp)
                    top.linkTo(name.top)
                    bottom.linkTo(name.bottom)
                }
                .clickable {
                    isFollowed = !isFollowed
                }
                .background(
                    brush = Brush.linearGradient(
                        colors = if (isFollowed) arrayListOf(
                            Color(0xff26293c), Color(0xff26293c)
                        ) else arrayListOf(
                            Color(0xfffd6652), Color(0xfff9453c)
                        )
                    ), shape = RoundedCornerShape(50)
                )
                .padding(horizontal = 10.dp, vertical = 2.dp),
            color = if (isFollowed) Color(0xff585b79) else Color.White,
            fontSize = 10.sp)

        Row(verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.constrainAs(gender) {
                bottom.linkTo(avatar.bottom, margin = 3.dp)
                start.linkTo(name.start)
            }) {
            Image(
                painter = painterResource(id = R.mipmap.ic_male),
                contentDescription = "gender",
                modifier = Modifier.size(15.dp)
            )
            Text(
                text = "25",
                color = Color(0xffbbbbbb),
                fontSize = 13.sp,
                modifier = Modifier.padding(start = 3.dp)
            )
            Text(
                text = "-中国",
                color = Color(0xffbbbbbb),
                fontSize = 13.sp,
                modifier = Modifier.padding(start = 3.dp)
            )
        }

        Row(modifier = Modifier.constrainAs(contact) {
            top.linkTo(avatar.top)
            bottom.linkTo(avatar.bottom)
            end.linkTo(parent.end, margin = 5.dp)
        }) {
            IconButton(
                onClick = { /*TODO*/ }, modifier = Modifier
                    .background(
                        color = Color(0xff1d2031), shape = RoundedCornerShape(100)
                    )
                    .size(40.dp)
            ) {
                Icon(imageVector = Icons.Filled.Edit, contentDescription = "", tint = Color.White)
            }

            IconButton(
                onClick = { /*TODO*/ }, modifier = Modifier
                    .padding(start = 10.dp)
                    .background(
                        color = Color(0xff1d2031), shape = RoundedCornerShape(100)
                    )
                    .size(40.dp)
            ) {
                Icon(
                    imageVector = Icons.Filled.PlayArrow,
                    contentDescription = "",
                    tint = Color.White
                )
            }
        }
    }
}


@Preview
@Composable
fun MessagePagePreview() {
    MessagePage()
}
