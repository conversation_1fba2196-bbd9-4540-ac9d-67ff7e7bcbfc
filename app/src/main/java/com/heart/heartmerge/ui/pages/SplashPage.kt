package com.heart.heartmerge.ui.pages

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.heart.heartmerge.extension.LocalNavHostController

@Composable
fun SplashPage() {
    val navController = LocalNavHostController.current
    Scaffold { padding ->
        Column(modifier = Modifier.padding(padding)) {
            Text(text = "SplashPage")
        }
    }
}