package com.heart.heartmerge.ui.activities.anchor.search

import android.os.Bundle
import androidx.core.content.ContextCompat
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.DslViewHolder
import com.bdc.android.library.extension.jump
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.extension.loadAvatar
import com.heart.heartmerge.ui.activities.anchor.detail.AnchorDetailActivity
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.toJson

/**
 * 作者：Lxf
 * 创建日期：2024/7/27 17:17
 * 描述：
 */
class SearchAnchorItem : DslAdapterItem() {
    init {
        itemLayoutId = R.layout.item_search_anchor
        itemSpanCount = 1
    }

    var anchorInfo: UserBean? = null

    override fun onItemBind(
        itemHolder: DslViewHolder,
        itemPosition: Int,
        adapterItem: DslAdapterItem,
        payloads: List<Any>
    ) {
        super.onItemBind(itemHolder, itemPosition, adapterItem, payloads)
        itemHolder.apply {
            clickItem {
                jump(
                    AnchorDetailActivity::class.java,
                    Bundle().apply { putString(Constants.INTENT_PARAM_KEY_ANCHOR_INFO, anchorInfo?.toJson()) })
            }
            anchorInfo?.apply {
                img(R.id.anchor_image)?.loadAvatar(avatar)
                tv(R.id.anchor_name)?.text = nickname
                //本地load国家图片
                if (userCountry?.title?.isNotEmpty()==true) {
                    AppUtil.setLocalCountryImage(userCountry?.code, img(R.id.anchor_country))
                }

                tv(R.id.online_status)?.apply {
                    when (onlineStatus) {
                        "0" -> {
                            text = context.resources.getString(R.string.off_line)
                            setTextColor(ContextCompat.getColor(context, R.color.color_white60))
                        }

                        "1" -> {
                            text = context.resources.getString(R.string.on_line)
                            setTextColor(ContextCompat.getColor(context, R.color.color_21C76E))
                        }

                        "2" -> {
                            text = context.resources.getString(R.string.status_busy)
                            setTextColor(ContextCompat.getColor(context, R.color.color_21C76E))
                        }
                    }
                }
            }
        }
    }
}