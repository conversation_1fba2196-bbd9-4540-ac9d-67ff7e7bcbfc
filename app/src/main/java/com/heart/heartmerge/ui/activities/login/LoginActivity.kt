package com.heart.heartmerge.ui.activities.login

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.TextPaint
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.lifecycle.asLiveData
import com.android.installreferrer.api.InstallReferrerClient
import com.android.installreferrer.api.InstallReferrerStateListener
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.bdc.android.library.extension.jumpThenFinish
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeInVisible
import com.bdc.android.library.extension.makeVisible
import com.facebook.CallbackManager
import com.facebook.FacebookCallback
import com.facebook.FacebookException
import com.facebook.login.LoginManager
import com.facebook.login.LoginResult
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.heart.heartmerge.BuildConfig
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.ActivityLoginBinding
import com.heart.heartmerge.firebase.auth.AuthManager
import com.heart.heartmerge.firebase.report.ReportManager
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.mmkv.MMKVBaseDataRep
import com.heart.heartmerge.mmkv.MMKVGuiYinDataRep
import com.heart.heartmerge.popup.showLoginConfirmAgreementPopup
import com.heart.heartmerge.ui.activities.MainActivity
import com.heart.heartmerge.ui.activities.WebViewActivity
import com.heart.heartmerge.ui.activities.mine.BuildProfileActivity
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.UniqueIDUtil
import com.heart.heartmerge.viewmodes.UserViewModel
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


enum class LOGIN_TYPE(val value: Int) {
    GUEST(1), EMAIL(2), GOOGLE(4), FACEBOOK(2)
}

class LoginActivity : BaseCoreActivity<ActivityLoginBinding, UserViewModel>() {

    private lateinit var signInLauncher: ActivityResultLauncher<Intent>
    private lateinit var callbackManager: CallbackManager
    private var referrerClient: InstallReferrerClient? = null
    private val INSTALL_REFERRER_RETRY_COUNT = 3
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        signInLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            LogX.i("google login result: $result")
            if (result.resultCode == Activity.RESULT_OK) {
                val task = GoogleSignIn.getSignedInAccountFromIntent(result.data)
                LogX.e("login result: ${task.result.id}")
                if (MMKVBaseDataRep.token != null) {
                    mViewModel.bindGoogle(task.result.email ?: "", task.result.idToken ?: "")
                        .asLiveData().observe(this) {
                            finish()
                        }
                } else {
                    handleLogin(
                        LOGIN_TYPE.GOOGLE, task.result.email ?: "", task.result.idToken ?: ""
                    )
                }
            }
        }

        //如果是游客登录、点击Sign in再次进入登录页面（绑定谷歌帐号）
        if (MMKVBaseDataRep.token != null) {
            mBinding.llGuestLogin.makeGone()
            mBinding.llBottomAgreement.makeInVisible()
            mBinding.ivFinish.makeVisible()
        }

        if (BuildConfig.DEBUG) {
            mBinding.llGoogleLogin.makeGone()
        }

        mBinding.tvUserAgreement.paintFlags =
            mBinding.tvUserAgreement.paintFlags or TextPaint.UNDERLINE_TEXT_FLAG

        mBinding.tvPrivacyPolicy.paintFlags =
            mBinding.tvPrivacyPolicy.paintFlags or TextPaint.UNDERLINE_TEXT_FLAG

        initInstallReferrer()
    }

    override fun isImmerse(): Boolean = true

    override fun getLayoutId(): Int = R.layout.activity_login

    override fun bindListener() {
        mBinding.llEmailLogin.click {
            login(LOGIN_TYPE.EMAIL)
        }

        mBinding.llGoogleLogin.click {
            login(LOGIN_TYPE.GOOGLE)
        }

        mBinding.llGuestLogin.click {
            login(LOGIN_TYPE.GUEST)
        }

        mBinding.llFacebookLogin.click {
            login(LOGIN_TYPE.FACEBOOK)
        }

        mBinding.tvCheckboxText.click {
            mBinding.cbAgreement.isChecked = !mBinding.cbAgreement.isChecked
        }

        mBinding.tvUserAgreement.click {
            WebViewActivity.jump(
                this,
                getString(R.string.user_agreement).replace("《", "").replace("》", ""),
                Constants.Agreement.REGISTRATION_URL
            )
        }

        mBinding.tvPrivacyPolicy.click {
            WebViewActivity.jump(
                this,
                getString(R.string.privacy_policy).replace("《", "").replace("》", ""),
                Constants.Agreement.PRIVACY_URL
            )
        }
        mBinding.ivFinish.click {
            finish()
        }
    }

    private fun login(type: LOGIN_TYPE/*0-游客登录，1-邮箱登录，2-Google登录，3-Facebook登录 */) {
        if (mBinding.llBottomAgreement.isVisible && !mBinding.cbAgreement.isChecked) {
            showLoginConfirmAgreementPopup(this) {
                mBinding.cbAgreement.isChecked = true
                login(type)
            }
            return
        }

        when (type) {
            LOGIN_TYPE.GUEST -> {
                handleLogin(
                    LOGIN_TYPE.GUEST,
                    authId = UniqueIDUtil.getUniqueID(this),
                    authToken = UniqueIDUtil.getUniqueID(this)
                )
            }

            LOGIN_TYPE.EMAIL -> {
                jump(EmailLoginActivity::class.java)
            }

            LOGIN_TYPE.GOOGLE -> {
                signInLauncher.launch(AuthManager.authGoogle(this))
            }

            LOGIN_TYPE.FACEBOOK -> {
                callbackManager = CallbackManager.Factory.create()
                LoginManager.getInstance()
                    .registerCallback(callbackManager, object : FacebookCallback<LoginResult> {
                        override fun onCancel() {
                            LogX.d("facebook login callback: cancel")
                        }

                        override fun onError(error: FacebookException) {
                            LogX.e("facebook login callback: onError $error")
                        }

                        override fun onSuccess(result: LoginResult) {
                            LogX.d("facebook login callback: onSuccess ${result.accessToken.userId}")
                            handleLogin(LOGIN_TYPE.FACEBOOK, result.accessToken.token, "")
                        }
                    })
                LoginManager.getInstance()
                    .logInWithReadPermissions(this, listOf("email", "public_profile"))
            }
        }
    }

    private fun initInstallReferrer() {
        referrerClient = InstallReferrerClient.newBuilder(this).build()
        referrerClient?.startConnection(object : InstallReferrerStateListener {
            override fun onInstallReferrerSetupFinished(responseCode: Int) {
                LogX.d("onInstallReferrerSetupFinished $responseCode")
                when (responseCode) {
                    InstallReferrerClient.InstallReferrerResponse.OK -> {
                        runCatching {
                            referrerClient?.installReferrer?.let { details ->
                                MMKVGuiYinDataRep.googleInstallReferrer = details.installReferrer
                                val referrerClickTime = details.referrerClickTimestampSeconds
                                val appInstallTime = details.installBeginTimestampSeconds
                                val instantExperienceLaunched = details.googlePlayInstantParam
                                LogX.d("InstallReferrer referrerUrl $${MMKVGuiYinDataRep.googleInstallReferrer} , $referrerClickTime , $appInstallTime , $instantExperienceLaunched")

                                mViewModel.reportGoogleReferrer(details.installReferrer)
                                    .asLiveData().observe(this@LoginActivity) {
                                        MMKVGuiYinDataRep.isOrganicUser = !it.is_ad
                                    }
                            }
                            // 确保关闭连接
                            referrerClient?.endConnection()
                        }.onFailure { error ->
                            ReportManager.logException(error)
                            try {
                                referrerClient?.endConnection()
                            } catch (e: Exception) {
                                ReportManager.logException(e)
                            }
                        }
                    }

                    InstallReferrerClient.InstallReferrerResponse.FEATURE_NOT_SUPPORTED -> {}
                    InstallReferrerClient.InstallReferrerResponse.SERVICE_UNAVAILABLE -> {
                        MainScope().launch {
                            for (i in 0 until INSTALL_REFERRER_RETRY_COUNT) {
                                delay(250)
                                initInstallReferrer()
                            }
                            cancel()
                        }
                    }
                }
            }

            override fun onInstallReferrerServiceDisconnected() {
                LogX.d("onInstallReferrerServiceDisconnected")
                // Try to restart the connection on the next request to
                // Google Play by calling the startConnection() method.
            }
        })
    }

    fun handleLogin(type: LOGIN_TYPE, authId: String, authToken: String) {
        showLoading(getString(R.string.label_loading))
        mViewModel.login(type, authId, authToken) {
            stopLoading()
        }.asLiveData().observe(this) {
            stopLoading()
            //游客登录绑定帐号
            if (mBinding.llGuestLogin.isGone) {
                finish()
                return@observe
            }
            if (it.birthdayAt.isEmpty() && it.avatar.isEmpty()) {
                jumpThenFinish(BuildProfileActivity::class.java)
            } else {
                jumpThenFinish(MainActivity::class.java)
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (::callbackManager.isInitialized) {
            callbackManager.onActivityResult(requestCode, resultCode, data)
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    override fun onDestroy() {
        super.onDestroy()
        //firebase后台有个崩溃onInstallReferrerSetupFinished 回调时抛出的 DeadObjectException
        referrerClient?.endConnection()
    }
}