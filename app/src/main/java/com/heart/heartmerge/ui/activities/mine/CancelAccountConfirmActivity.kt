package com.heart.heartmerge.ui.activities.mine

import androidx.lifecycle.asLiveData
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.ActivityCancelAccountConfirmBinding
import com.heart.heartmerge.popup.showNormalNewPopup
import com.heart.heartmerge.viewmodes.UserViewModel

class CancelAccountConfirmActivity :
    BaseCoreActivity<ActivityCancelAccountConfirmBinding, UserViewModel>() {
    override fun getLayoutId(): Int = R.layout.activity_cancel_account_confirm

    override fun bindListener() {
        mBinding.btnConfirm.click {
            showNormalNewPopup(
                this,
                R.mipmap.ic_dialog_warning,
                btnSure = getString(R.string.confirm),
                btnCancel = getString(R.string.cancel),
                title = getString(R.string.remind),
                content = getString(R.string.cancel_account_confirm_again),
                block = {
                    mViewModel.cancelAccount().asLiveData().observe(this) {
                        jump(CancelAccountResultActivity::class.java)
                    }
                })
        }
        mBinding.btnBack.click { finish() }
    }
}