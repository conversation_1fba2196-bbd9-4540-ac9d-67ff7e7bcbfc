package com.heart.heartmerge.ui.pages.mine

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color

@Composable
fun MinePage() {
    Scaffold(backgroundColor = Color(0xff101321)) { padding ->
        Column(modifier = Modifier.padding(padding)) {
            Text(text = "MinePage")
        }
    }
}