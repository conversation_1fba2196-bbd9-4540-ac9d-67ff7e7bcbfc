package com.heart.heartmerge.ui.activities.message

import android.content.Context
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import android.view.View
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import com.angcyo.dsladapter.isVisible
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.mvi.observeState
import com.bdc.android.library.utils.ToastUtil
import com.gyf.immersionbar.ImmersionBar
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.GiftItemBean
import com.heart.heartmerge.beans.RemoteSocketStatusChangeExtend
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.extension.whatsAppAnimation
import com.heart.heartmerge.lce.BaseRequestEvent
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.manager.DiamondChangeManager
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond
import com.heart.heartmerge.manager.GiftManager
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.popup.showDiamondRechargePopup
import com.heart.heartmerge.popup.showNormalNewPopup
import com.heart.heartmerge.popup.showReportBlockPopup
import com.heart.heartmerge.ui.activities.anchor.AnchorVideoActivity
import com.heart.heartmerge.ui.activities.anchor.detail.AnchorDetailActivity
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.PurchaseScene
import com.heart.heartmerge.utils.RongMessageUtil
import com.heart.heartmerge.utils.toJson
import com.heart.heartmerge.viewmodes.AnchorVideoPageState
import com.heart.heartmerge.viewmodes.AnchorVideoStatus
import com.heart.heartmerge.viewmodes.AnchorVideoViewModel
import com.heart.heartmerge.viewmodes.AnchorViewModel
import com.heart.heartmerge.viewmodes.SearchRequestEvent
import io.rong.imkit.IMCenter
import io.rong.imkit.MessageItemLongClickActionManager
import io.rong.imkit.R.id
import io.rong.imkit.R.string
import io.rong.imkit.RongIM
import io.rong.imkit.activity.RongBaseActivity
import io.rong.imkit.config.ConversationClickListener
import io.rong.imkit.conversation.ConversationViewModel
import io.rong.imkit.model.TypingInfo.TypingUserInfo
import io.rong.imkit.picture.entity.LocalMedia
import io.rong.imkit.userinfo.RongUserInfoManager
import io.rong.imkit.userinfo.RongUserInfoManager.UserDataObserver
import io.rong.imkit.userinfo.model.GroupUserInfo
import io.rong.imlib.IRongCoreCallback
import io.rong.imlib.IRongCoreEnum
import io.rong.imlib.RongCoreClient
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.ConversationIdentifier
import io.rong.imlib.model.Group
import io.rong.imlib.model.Message
import io.rong.imlib.model.UserInfo
import java.lang.ref.WeakReference

/**
 * Author:Lxf
 * Create on:2024/8/6
 * Description:
 */
open class MyRongConversationActivity : RongBaseActivity() {
    private val viewModel by viewModels<AnchorVideoViewModel>()
    private val anchorViewModel by viewModels<AnchorViewModel>()
    private var anchorInfo: UserBean? = null
    protected var mTargetId: String? = null
    private var mConversationType: Conversation.ConversationType? = null
    private var mConversationFragment: MyConversationFragment? = null
    private var conversationViewModel: ConversationViewModel? = null
    private var selectImageCallBack: (List<LocalMedia>) -> Unit = {}

    // 使用静态内部类和弱引用持有回调，避免内存泄漏
    private var rongCoreCallback: IRongCoreCallback.ResultCallback<Boolean>? = null
    private var rongIMClientCallback: RongIMClient.ResultCallback<Boolean>? = null

    // 静态内部类实现回调，避免内存泄漏
    private class StaticRongCoreCallback(activity: MyRongConversationActivity) :
        IRongCoreCallback.ResultCallback<Boolean>() {
        private val weakActivity = WeakReference(activity)

        override fun onSuccess(t: Boolean) {
            val activity = weakActivity.get() ?: return
            if (!activity.isFinishing) {
                activity.mTitleBar.findViewById<View>(R.id.fl_sticky_top).makeVisible(!t)
            }
        }

        override fun onError(e: IRongCoreEnum.CoreErrorCode?) {
            // 错误处理
        }
    }

    private class StaticRongIMClientCallback(
        activity: MyRongConversationActivity,
        private val stickyTop: View
    ) : RongIMClient.ResultCallback<Boolean>() {
        private val weakActivity = WeakReference(activity)

        override fun onSuccess(t: Boolean) {
            val activity = weakActivity.get() ?: return
            if (!activity.isFinishing) {
                activity.runOnUiThread {
                    stickyTop.makeGone()
                    ToastUtil.show(activity.getString(R.string.sticky_suc))
                }
            }
        }

        override fun onError(e: RongIMClient.ErrorCode?) {
            val activity = weakActivity.get() ?: return
            if (!activity.isFinishing) {
                ToastUtil.show(activity.getString(R.string.sticky_failed))
            }
        }
    }

    private val pickMedia =
        registerForActivityResult(ActivityResultContracts.PickMultipleVisualMedia(9)) {
            val localMediaList = ArrayList<LocalMedia>()
            it.forEach { selMedia ->
                localMediaList.add(LocalMedia().apply {
                    path = AppUtil.getPathFromUri(this@MyRongConversationActivity, selMedia)
                    LogX.e("selPath $path")
                })
            }
            selectImageCallBack.invoke(localMediaList)
        }

    fun pickImages(callBack: (List<LocalMedia>) -> Unit) {
        pickMedia.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly))
        selectImageCallBack = callBack
    }

    private val mUserDataObserver: UserDataObserver = object : UserDataObserver {
        override fun onUserUpdate(info: UserInfo) {
            if (TextUtils.equals(mTargetId, info.userId)) {
                runOnUiThread { setTitle() }
            }
        }

        override fun onGroupUpdate(group: Group) {
            if (TextUtils.equals(mTargetId, group.id)) {
                runOnUiThread { setTitle() }
            }
        }

        override fun onGroupUserInfoUpdate(groupUserInfo: GroupUserInfo) {
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (this.intent != null) {
            this.mTargetId = this.intent.getStringExtra("targetId")
            val type = this.intent.getStringExtra("ConversationType")
            if (TextUtils.isEmpty(type)) {
                return
            }

            this.mConversationType = Conversation.ConversationType.valueOf(type!!.uppercase())
        }

        this.setContentView(R.layout.rc_conversation_activity)
        this.setTitle()
        this.mConversationFragment =
            this.supportFragmentManager.findFragmentById(id.conversation) as MyConversationFragment?
        mTitleBar.setOnBackClickListener {
            if (mConversationFragment?.onBackPressed() == false) {
                finish()
            }
        }
        mTitleBar.rightView.visibility = View.GONE
        this.initViewModel()
        this.observeUserInfoChange()
        ImmersionBar.with(this).statusBarDarkFont(false)
            .navigationBarColor(com.bdc.android.library.R.color.black).init()
        mTitleBar.setBackgroundColor(
            ContextCompat.getColor(
                this, com.bdc.android.library.R.color.transparent
            )
        )
        val stickyTop = mTitleBar.findViewById<View>(R.id.fl_sticky_top)
        val titleMore = mTitleBar.findViewById<View>(R.id.fl_more)
        if (mTargetId != Constants.RONG_YUN_ID_SYSTEM && mTargetId != Constants.RONG_YUN_ID_CUSTOM_SERVICE) {
            // 使用静态内部类回调避免内存泄漏
            rongCoreCallback = StaticRongCoreCallback(this)
            RongCoreClient.getInstance().getConversationTopStatus(
                mTargetId, Conversation.ConversationType.PRIVATE, rongCoreCallback
            )

            stickyTop.click {
                val conversationIdentifier =
                    ConversationIdentifier(Conversation.ConversationType.PRIVATE, mTargetId)
                // 使用静态内部类回调避免内存泄漏
                rongIMClientCallback = StaticRongIMClientCallback(this, stickyTop)
                IMCenter.getInstance().setConversationToTop(
                    conversationIdentifier, true, rongIMClientCallback
                )
            }
            titleMore.click {
                showReportBlockPopup(this, anchorInfo, true) {
                    showNormalNewPopup(
                        this,
                        R.mipmap.ic_dialog_warning,
                        title = getString(R.string.clear_msg),
                        content = getString(R.string.dialog_content_clear_msg),
                        btnSure = getString(R.string.string_ok),
                        btnCancel = getString(R.string.cancel),
                        mainColor = R.color.color_F53D3D,
                        block = {
                            if (mConversationFragment?.isAdded == true) {
                                mConversationFragment?.clearMsg(mTargetId)
                            }
//                            RongCoreClient.getInstance().deleteMessages(
//                                Conversation.ConversationType.PRIVATE,
//                                mTargetId,
//                                object :
//                                    IRongCoreCallback.ResultCallback<Boolean>() {
//                                    override fun onSuccess(t: Boolean?) {
//                                        LogX.i("clear message suc")
//                                        mConversationFragment?.clearMsg(mTargetId)
//                                    }
//
//                                    override fun onError(e: IRongCoreEnum.CoreErrorCode?) {
//                                        ToastUtil.show(getString(R.string.clear_msg_failed))
//                                    }
//
//                                })
                        },
                    )
                }
            }
        }

        setupPortraitClickEvent()
        initViewStates()
        initViewEvents()
        deleteLongClickItem()
        mTargetId?.let {
            if (it == Constants.RONG_YUN_ID_SYSTEM || it == Constants.RONG_YUN_ID_CUSTOM_SERVICE) {
                stickyTop?.makeGone()
                titleMore?.makeGone()

            } else {
                anchorViewModel.fetchAnchorDetail(it)
                val whatsApp = mTitleBar.findViewById<View>(R.id.fl_whatsapp)
                whatsApp.apply {
                    makeVisible(
                        anchorInfo?.show_whats_app == true
                    )
                    if (isVisible()) {
                        this.whatsAppAnimation()
                    }
                    click {
                        showNormalNewPopup(
                            this@MyRongConversationActivity,
                            title = getString(R.string.whatsapp_hint_title),
                            content = getString(
                                R.string.whatsapp_hint_content,
                                MMKVDataRep.userConfig?.whats_app_recharge_min_coins?.toShowDiamond()
                            ),
                            btnSure = getString(R.string.whatsapp_hint_button),
                            block = {
                                showDiamondRechargePopup(
                                    this@MyRongConversationActivity,
                                    purchaseScene = PurchaseScene.WhatsApp(
                                        anchorId = mTargetId ?: ""
                                    )
                                ) {}
                            })
                    }
                }
            }
        }
        if (mTargetId == Constants.RONG_YUN_ID_CUSTOM_SERVICE) {
            AppUtil.cacheCustomServiceInfo()
        }
        if (mTargetId == Constants.RONG_YUN_ID_SYSTEM) {
            AppUtil.cacheSystemNoticeInfo()
        }
        FlowBus.with<String>(Constants.PUSH_TYPE_BLACK_ANCHOR).register(this) {
            if (mTargetId == it) {
                finish()
            }
        }

        FlowBus.with<RemoteSocketStatusChangeExtend>(Constants.PushCmd.PUSH_CMD_REMOTE_SOCKET_STATUS_CHANGE)
            .register(this) {
                anchorInfo?.let { info ->
                    if (info.id == it.peerUID.toString()) {
                        info.status = it.status.toString()
                    }
                }
            }

        FlowBus.with<Boolean>(Constants.PAYMENT_SUCCESS).register(this) {
            checkWhatsAppStatus()
        }
    }

    private fun checkWhatsAppStatus() {
        if (mTargetId == Constants.RONG_YUN_ID_SYSTEM || mTargetId == Constants.RONG_YUN_ID_CUSTOM_SERVICE) return
        mTitleBar.findViewById<View>(R.id.fl_whatsapp).makeVisible(
            anchorInfo?.show_whats_app == true
        )
    }

    private fun observeUserInfoChange() {
        if (!TextUtils.isEmpty(this.mTargetId)) {
            RongUserInfoManager.getInstance().addUserDataObserver(this.mUserDataObserver)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (!TextUtils.isEmpty(this.mTargetId)) {
            RongUserInfoManager.getInstance().removeUserDataObserver(this.mUserDataObserver)
            // 清空回调对象
            rongCoreCallback = null
            rongIMClientCallback = null

            // 尝试释放资源以避免内存泄漏
            try {
                // 融云SDK没有直接的取消请求方法
                // 这里我们使用其他方式清理资源
                System.gc()
            } catch (e: Exception) {
                LogX.e("Error cleaning up resources: ${e.message}")
            }
        }

        // 注销FlowBus
        FlowBus.with<String>(Constants.PUSH_TYPE_BLACK_ANCHOR).onDestroy()

        // 移除ViewModel观察者
        conversationViewModel?.let { viewModel ->
            viewModel.typingStatusInfo.removeObservers(this)
            viewModel.typingStatusInfo.value = null
        }
        viewModel.viewStates.removeObservers(this)

        // 清除会话监听器
        RongIM.setConversationClickListener(null)
        IMCenter.setConversationClickListener(null)

        // 取消所有融云相关的回调和监听器
        try {
            // 尝试取消所有可能的监听器
            // 根据融云SDK的实际API调整这里的代码
            try {
                // 如果有消息监听器，尝试取消注册
                val method =
                    RongCoreClient::class.java.getDeclaredMethod("removeOnReceiveMessageListener")
                method.invoke(RongCoreClient.getInstance())
            } catch (e: Exception) {
                // 如果方法不存在或调用失败，忽略异常
                LogX.e("Cannot remove message listener: ${e.message}")
            }

            // 清除任何可能的内部回调引用
            System.gc()
        } catch (e: Exception) {
            LogX.e("Error cleaning up Rong callbacks: ${e.message}")
        }

        // 取消ActivityResultLauncher注册
        try {
            pickMedia.unregister()
        } catch (e: Exception) {
            LogX.e("Error unregistering pickMedia: ${e.message}")
        }

        // 清除回调引用
        selectImageCallBack = {}

        // 清除Fragment引用
        mConversationFragment?.onDestroy()
        mConversationFragment = null

        // 清除其他引用
        anchorInfo = null
        mTargetId = null
        mConversationType = null
        conversationViewModel = null
    }

    private fun setTitle() {
        if (!TextUtils.isEmpty(this.mTargetId) && this.mConversationType == Conversation.ConversationType.GROUP) {
            val group = RongUserInfoManager.getInstance().getGroupInfo(this.mTargetId)
            mTitleBar.setTitle(if (group == null) this.mTargetId else group.name)
        } else {
            val userInfo = RongUserInfoManager.getInstance().getUserInfo(this.mTargetId)
            mTitleBar.setTitle(if (userInfo == null) this.mTargetId else userInfo.name)
        }

        if (this.mConversationType == Conversation.ConversationType.CUSTOMER_SERVICE || (this.mConversationType == Conversation.ConversationType.CHATROOM)) {
            mTitleBar.setRightVisible(false)
        }
    }

    private fun initViewModel() {
        this.conversationViewModel = ViewModelProvider(this)[ConversationViewModel::class.java]
        conversationViewModel?.typingStatusInfo?.observe(this) { typingInfo ->
            if (typingInfo != null) {
                if ((typingInfo.conversationType == mConversationType) && mTargetId == typingInfo.targetId) {
                    if (typingInfo.typingList == null) {
                        mTitleBar.middleView.visibility = View.VISIBLE
                        mTitleBar.typingView.visibility = View.GONE
                    } else {
                        mTitleBar.middleView.visibility = View.GONE
                        mTitleBar.typingView.visibility = View.VISIBLE
                        val typing =
                            typingInfo.typingList[typingInfo.typingList.size - 1] as TypingUserInfo
                        if (typing.type == TypingUserInfo.Type.text) {
                            mTitleBar.setTyping(string.rc_conversation_remote_side_is_typing)
                        } else if (typing.type == TypingUserInfo.Type.voice) {
                            mTitleBar.setTyping(string.rc_conversation_remote_side_speaking)
                        }
                    }
                }
            }
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (4 == event.keyCode && this.mConversationFragment != null && !mConversationFragment!!.onBackPressed()) {
            this.finish()
        }

        return false
    }

    private fun gotoDetail() {
        val tmpUserInfo = if (anchorInfo != null) {
            anchorInfo
        } else {
            val userInfo = RongUserInfoManager.getInstance().getUserInfo(mTargetId)
            if (userInfo != null && userInfo.portraitUri != null) {
                UserBean(id = mTargetId ?: "", avatar = userInfo.portraitUri.toString())
            } else null
        }
        if (tmpUserInfo == null) {
            return
        }
        jump(
            AnchorDetailActivity::class.java, Bundle().apply {
                putString(
                    Constants.INTENT_PARAM_KEY_ANCHOR_INFO, tmpUserInfo.toJson()
                )
            })
    }

    // 静态内部类实现ConversationClickListener避免内存泄漏
    private class StaticConversationClickListener(activity: MyRongConversationActivity) :
        ConversationClickListener {
        private val weakActivity = WeakReference(activity)

        override fun onUserPortraitClick(
            context: Context,
            conversationType: Conversation.ConversationType,
            user: UserInfo,
            targetId: String
        ): Boolean {
            val activity = weakActivity.get() ?: return false
            if (user.userId == targetId && targetId != Constants.RONG_YUN_ID_CUSTOM_SERVICE) {
                activity.gotoDetail()
                return true
            }
            return false
        }

        override fun onUserPortraitLongClick(
            context: Context?,
            conversationType: Conversation.ConversationType?,
            user: UserInfo?,
            targetId: String?
        ): Boolean {
            return false
        }

        override fun onMessageClick(
            context: Context?, view: View?, message: Message?
        ): Boolean {
            return false
        }

        override fun onMessageLongClick(
            context: Context?, view: View?, message: Message?
        ): Boolean {
            return false
        }

        override fun onMessageLinkClick(
            context: Context?, link: String?, message: Message?
        ): Boolean {
            return false
        }

        override fun onReadReceiptStateClick(context: Context?, message: Message?): Boolean {
            return false
        }
    }

    private fun setupPortraitClickEvent() {
        RongIM.setConversationClickListener(StaticConversationClickListener(this))
    }

    private fun deleteLongClickItem() {
        val clickActions =
            MessageItemLongClickActionManager.getInstance().messageItemLongClickActions
        val iterator = clickActions.iterator()
        val delActionTitle = getString(io.rong.imkit.R.string.rc_dialog_item_message_reference)
        while (iterator.hasNext()) {
            val clickAction = iterator.next()
            val isDelAction = delActionTitle == clickAction.getTitle(this)
            if (isDelAction) {
                iterator.remove()
                break
            }
        }
    }

    fun sendGift(gift: GiftItemBean) {
        AppUtil.checkoutDiamondAndShowPopup(
            this,
            gift.coin,
            PurchaseScene.Gift(anchorId = mTargetId ?: "")
        ) {
            viewModel.giftGive(anchorId = mTargetId ?: "", giftBean = gift)
        }
    }

    // 静态内部类实现IMCenter的ConversationClickListener
    private class StaticIMCenterClickListener : ConversationClickListener {
        override fun onUserPortraitClick(
            context: Context,
            conversationType: Conversation.ConversationType?,
            user: UserInfo?,
            targetId: String?
        ): Boolean {
            return false
        }

        override fun onUserPortraitLongClick(
            context: Context,
            conversationType: Conversation.ConversationType?,
            user: UserInfo?,
            targetId: String?
        ): Boolean {
            return false
        }

        override fun onMessageClick(
            context: Context, view: View, message: Message
        ): Boolean {
            return false
        }

        override fun onMessageLongClick(
            context: Context, view: View, message: Message
        ): Boolean {
            return true
        }

        override fun onMessageLinkClick(
            context: Context?, link: String?, message: Message?
        ): Boolean {
            return false
        }

        override fun onReadReceiptStateClick(
            context: Context?, message: Message?
        ): Boolean {
            return false
        }
    }

    private fun setupMessageLongClick() {
        IMCenter.setConversationClickListener(StaticIMCenterClickListener())
    }

    private fun initViewStates() {
        viewModel.viewStates.let { states ->
            states.observeState(this, AnchorVideoPageState::anchorVideoStatus) {
                when (it) {
                    is AnchorVideoStatus.GiftGiveSuccess -> {
//                        DiamondChangeManager.reduceDiamond(it.giftBean.coin)
//                        mTargetId?.let { it1 ->
//                            mConversationType?.let { it2 ->
//                                RongMessageUtil.sendGiftMessage(
//                                    it1, it.giftBean, "0"
//                                )
//                            }
//                        }

                        LogX.e("MyRongConversationActivity, playGiftAnimation  ${it.giftBean.giftSvgaUrl}")
                        GiftManager.getInstance(this)
                            .playGiftAnimation(this, this, it.giftBean.giftSvgaUrl)
                    }

                    else -> {}
                }
            }
        }
    }

    private fun initViewEvents() {
        viewModel.viewEvents.observeEvent(this) {
            when (it) {
                is BaseRequestEvent.ShowToast -> ToastUtil.show(it.message)
                else -> {}
            }
        }

        anchorViewModel.pageEvents.observeEvent(this) {
            when (it) {
                is SearchRequestEvent.GetAnchorDetailFailed -> ToastUtil.show(it.msg)
                is SearchRequestEvent.GetAnchorDetailSuccess -> {
                    anchorInfo = it.userBean
                    checkWhatsAppStatus()
                    mConversationFragment?.refreshHeaderView(it.userBean)
                    //用户信息有修改 更新数据IM用户信息
                    RongMessageUtil.refreshCacheUserInfo(it.userBean)
                }

                else -> {}
            }
        }
    }

    fun startVideo() {
        if (anchorInfo == null) {
            anchorInfo = UserBean(id = mTargetId ?: "")
//            mTargetId?.let { viewModel.getAnchorLitDetailWithCallBack(it, true) }
//        } else {
            gotoVideo()
        }
        gotoVideo()
    }

    private fun gotoVideo() {
        anchorInfo?.apply {
            AppUtil.checkoutDiamondAndShowPopup(
                this@MyRongConversationActivity,
                anchorLevel?.discountPrice ?: Constants.VIDEO_DEFAULT_PRICE,
                purchaseScene = PurchaseScene.Chat(anchorId = this.id)
            ) {
                when (status) {
                    "1" -> {
                        AppUtil.requestVideoPermission(
                            this@MyRongConversationActivity, onGrantedCallBack = {
                                jump(
                                    AnchorVideoActivity::class.java, Bundle().apply {
                                        putString(
                                            Constants.INTENT_PARAM_KEY_ANCHOR_INFO,
                                            anchorInfo?.toJson()
                                        )
                                    })
                            }) {}

                    }

                    "2" -> {
                        ToastUtil.show(getString(R.string.anchor_status_busy))
                    }

                    else -> {
                        ToastUtil.show(getString(R.string.anchor_status_offline))
                    }
                }
            }
        }
    }

    fun getTargetId(): String? {
        return mTargetId
    }
}