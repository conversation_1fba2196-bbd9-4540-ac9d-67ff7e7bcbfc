package com.heart.heartmerge.ui.widget.floatwindow


/**
 * Author:Lxf
 * Create on:2024/8/1
 * Description:
 */
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.graphics.Point
import android.os.Build
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.View.OnTouchListener
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.TextView
import com.heart.heartmerge.R
import com.heart.heartmerge.ui.activities.anchor.AnchorVideoActivity
import com.heart.heartmerge.utils.ContextHolder
import io.agora.rtc2.video.VideoCanvas
import kotlin.math.abs


class CallFloatWindow private constructor() {
    private lateinit var windowManager: WindowManager
    private lateinit var mLayoutParams: WindowManager.LayoutParams

    private var floatView: View? = null
    private var tvContent: TextView? = null
    private var screenWidth = 0
    private var floatViewWidth = 0

    // 计时器
    private var mIsCameraSelf = true
    private val supportedWindowType: Int
        get() = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
        }

    init {
        initFloatWindow()
    }

    private fun initFloatWindow() {
        windowManager = ContextHolder.context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val point = Point()
        windowManager.defaultDisplay.getSize(point)
        screenWidth = point.x
    }

    /**
     * add float window
     */
    fun show() {
        if (floatView != null) {
            return
        }
        mLayoutParams = WindowManager.LayoutParams().apply {
            gravity = Gravity.END or Gravity.TOP
            width = WindowManager.LayoutParams.WRAP_CONTENT
            height = WindowManager.LayoutParams.WRAP_CONTENT
            format = PixelFormat.TRANSPARENT
            type = supportedWindowType
            flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM
            //显示的位置
            y = 300
        }

        floatView = LayoutInflater.from(ContextHolder.context).inflate(R.layout.activity_float_window, null)
        floatView?.run {
            tvContent = findViewById<View>(R.id.tv_content) as TextView
            isFocusableInTouchMode = true
            floatViewWidth = width
            setOnClickListener {
                val intent = Intent(ContextHolder.context, AnchorVideoActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                intent.putExtra("IsCameraSelf", mIsCameraSelf)
                ContextHolder.context.startActivity(intent)
                dismiss();
            }

            setOnTouchListener(object : OnTouchListener {
                var result: Boolean = false

                var left: Int = 0
                var top: Int = 0
                var startX: Float = 0f
                var startY: Float = 0f

                override fun onTouch(v: View, event: MotionEvent): Boolean {
                    when (event.action) {
                        MotionEvent.ACTION_DOWN -> {
                            result = false
                            startX = event.rawX
                            startY = event.rawY

                            left = mLayoutParams.x
                            top = mLayoutParams.y
                        }

                        MotionEvent.ACTION_MOVE -> {
                            if (abs((event.rawX - startX).toDouble()) > 20 || abs((event.rawY - startY).toDouble()) > 20) {
                                result = true
                            }

                            val deltaX = (startX - event.rawX).toInt()

                            mLayoutParams.x = left + deltaX
                            mLayoutParams.y = (top + event.rawY - startY).toInt()
                            windowManager.updateViewLayout(floatView, layoutParams)
                        }

                        MotionEvent.ACTION_UP -> smoothScrollToBorder()
                    }
                    return result
                }
            })
        }
        windowManager.addView(floatView, mLayoutParams)
    }

    /**
     * @param isCalling 是否在拨打中， 拨打中显示对方视频，未拨打显示自己的视图
     * @param isCameraSelf 显示浮窗之前 当前的摄像头是否为自己 用来控制浮窗view从那个canvas获取，以及恢复到activity
     * @param localSelVideoCanvas 本地视图
     * @param remoteVideoCanvas 远程视图
     */
    fun update(isCalling: Boolean, isCameraSelf: Boolean, localSelVideoCanvas: VideoCanvas?, remoteVideoCanvas: VideoCanvas?, time: Int = 0) {
        mIsCameraSelf = isCameraSelf
        floatView?.run {
            val contentView = findViewById<FrameLayout>(R.id.layout_call_video)
            contentView.removeAllViews()

            if (isCalling) {
                if (isCameraSelf) {
                    localSelVideoCanvas?.let {
                        removeFromParent(it)
                        contentView.addView(it.view)
                    }
                } else {
                    remoteVideoCanvas?.let {
                        removeFromParent(it)
                        contentView.addView(it.view)
                    }
                }

            } else {
                localSelVideoCanvas?.let {
                    removeFromParent(it)
                    contentView.addView(it.view)
                }
            }
        }
    }

    private fun removeFromParent(canvas: VideoCanvas?): ViewGroup? {
        if (canvas != null) {
            val parent = canvas.view.parent
            if (parent != null) {
                val group = parent as ViewGroup
                group.removeView(canvas.view)
                return group
            }
        }
        return null
    }


    val isShowing: Boolean
        get() {
            return floatView != null
        }

    /**
     * 停止悬浮窗
     */
    fun dismiss() {
        Log.i(TAG, "dismiss: ")
        if (floatView != null) {
            windowManager.removeView(floatView)
        }
        floatView = null
    }

    private fun smoothScrollToBorder() {
        Log.i(TAG, "screenWidth: $screenWidth, floatViewWidth: $floatViewWidth")
        val splitLine = screenWidth / 2 - floatViewWidth / 2
        val left = mLayoutParams.x
        val top = mLayoutParams.y

        val targetX: Int = if (left < splitLine) {
            // 滑动到最左边
            0
        } else {
            // 滑动到最右边
            screenWidth - floatViewWidth
        }

        val animator = ValueAnimator.ofInt(left, targetX)
        animator.setDuration(100)
            .addUpdateListener(ValueAnimator.AnimatorUpdateListener { animation ->
                if (floatView == null) return@AnimatorUpdateListener
                val value = animation.animatedValue as Int
                Log.i(TAG, "onAnimationUpdate, value: $value")
                mLayoutParams.x = value
                mLayoutParams.y = top
                windowManager.updateViewLayout(floatView, mLayoutParams)
            })
        animator.start()
    }

    fun setTimeContent(timeString: String) {
        tvContent?.text = timeString
    }

    companion object {
        private const val TAG = "EaseCallFloatWindow"

        val instance: CallFloatWindow by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            CallFloatWindow()
        }

    }
}