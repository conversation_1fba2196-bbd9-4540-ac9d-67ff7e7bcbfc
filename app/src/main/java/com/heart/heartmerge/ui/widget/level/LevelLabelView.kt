package com.heart.heartmerge.ui.widget.level

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.bdc.android.library.extension.setTextCompatColor
import com.heart.heartmerge.databinding.ViewLevelLabelBinding

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2024/11/8 16:47
 * @description :等级标签View
 */

class LevelLabelView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    val binding: ViewLevelLabelBinding =
        ViewLevelLabelBinding.inflate(LayoutInflater.from(this.context), this, true)

    var current: Int = 0
        set(value) {
            field = value.coerceAtMost(LevelConfig.LEVEL_MAX)
            binding.tvLevel.text = "Lv$field"

            LevelConfig.getStyleForLevel(field)?.labelRes?.let { style ->
                binding.tvLevel.setTextCompatColor(style.first)
                binding.tvLevel.setBackgroundResource(style.second)
            }
        }
}