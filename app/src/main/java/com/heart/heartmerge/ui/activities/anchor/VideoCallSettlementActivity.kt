package com.heart.heartmerge.ui.activities.anchor

import android.app.Activity
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import coil.transform.CircleCropTransformation
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.BuildConfig
import com.heart.heartmerge.R
import com.heart.heartmerge.lce.BaseRequestEvent
import com.heart.heartmerge.ui.theme.HeartMergeTheme
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.viewmodes.VideoCallSettlementModel

/**
 * Author:Lxf
 * Create on:2024/8/3
 * Description:
 */
class VideoCallSettlementActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (!BuildConfig.DEBUG) {
            AppUtil.screenSecure(window)
        }
        val channelId = intent.getStringExtra(Constants.INTENT_PARAM_KEY_CHANNEL_ID)
        setContent {
            HeartMergeTheme() {
                VideoCallSettlementPage(channelId ?: "")
            }
        }
    }
}

@Composable
fun VideoCallSettlementPage(channelId: String) {
    val viewModel = viewModel<VideoCallSettlementModel>()
    val lifecycleOwner = LocalLifecycleOwner.current
    LaunchedEffect(Unit) {
        viewModel.getCallSettlement(channelId)
        viewModel.viewEvents.observeEvent(lifecycleOwner) {
            when (it) {
                is BaseRequestEvent.ShowToast -> ToastUtil.show(it.message)
                else -> {
                }
            }
        }
    }
    VideoCallSettlementContent()
}


@Composable
fun VideoCallSettlementContent() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .navigationBarsPadding(), horizontalAlignment = Alignment.CenterHorizontally, verticalArrangement = Arrangement.Center
    ) {
        val viewModel = viewModel<VideoCallSettlementModel>()
        val cellSettlement by remember { viewModel.callSettlement }

        Box {
            Image(painter = painterResource(id = R.mipmap.call_cettlement_bg), modifier = Modifier.width(280.dp), contentDescription = null)
            Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier.width(280.dp)) {
                Image(
                    painter = rememberAsyncImagePainter(
                        ImageRequest.Builder(LocalContext.current).data(data = cellSettlement.headFileName)
                            .apply(block = fun ImageRequest.Builder.() {
                                crossfade(true)
                                transformations(CircleCropTransformation())
                            }).build()
                    ),
                    modifier = Modifier
                        .padding(top = 24.dp)
                        .size(50.dp),
                    contentDescription = null,
                )

                Text(
                    text = cellSettlement.nickName,
                    fontSize = 16.sp,
                    color = Color.White,
                    modifier = Modifier.padding(top = 12.dp, bottom = 6.dp),
                    fontWeight = FontWeight.Medium
                )
                Row(
                    Modifier
                        .background(Color(0XFF010101).copy(alpha = 0.2f), shape = RoundedCornerShape(11.dp))
                        .padding(horizontal = 8.dp, vertical = 3.dp),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(id = if (cellSettlement.gender == Constants.GENDER_FEMALE) R.mipmap.ic_gender_female else R.mipmap.ic_gender_male),
                        contentDescription = null,
                        modifier = Modifier.size(12.dp)
                    )
                    Text(
                        text = "${cellSettlement.age}·  ${cellSettlement.country}",
                        fontSize = 12.sp,
                        color = Color.White,
                        modifier = Modifier.padding(start = 3.dp)
                    )
                }

//                if (cellSettlement.freeTime > 0) {
//                    TimeDiamondItem(stringResource(id = R.string.match_video_time), cellSettlement.freeTimeFormat)
//                }
                TimeDiamondItem(stringResource(id = R.string.video_time), cellSettlement.videoTime)
//                TimeDiamondItem(stringResource(id = R.string.consume_diamonds), cellSettlement.diamondConsumption.toString(), true)
                if (cellSettlement.freeFlag == "1") {
                    TimeDiamondItem(stringResource(id = R.string.consume_match_tickets), cellSettlement.freeFlag)
                }
            }
        }

        BottomOK()

    }
}

@Composable
fun TimeDiamondItem(leftLabel: String, rightValue: String, isDiamond: Boolean = false) {
    Row(
        Modifier
            .fillMaxWidth()
            .padding(horizontal = 24.dp, vertical = 6.dp),

        ) {
        Text(text = leftLabel, fontSize = 14.sp, color = Color.White, modifier = Modifier.weight(1f))
        Box(
            modifier = Modifier
                .weight(1f),
            contentAlignment = Alignment.CenterEnd
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                if (isDiamond) {
                    Image(
                        painter = painterResource(id = R.mipmap.ic_diamond), contentDescription = null, modifier = Modifier
                            .padding(end = 4.dp)
                            .size(12.dp)
                    )
                }
                Text(
                    text = rightValue, color = Color.White, fontSize = 14.sp,
                )
            }
        }
    }
}

@Composable
fun BottomOK() {
    val context = LocalContext.current
    Box(
        modifier = Modifier
            .padding(top = 20.dp)
            .width(200.dp)
            .height(40.dp)
            .background(
                brush = Brush.linearGradient(colors = listOf(Color(0XFFFC87B9), Color(0XFFFB24A3))),
                shape = RoundedCornerShape(22.dp),
            )
            .clickable {
                val activity: Activity = context as Activity
                activity.finish()
            },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = stringResource(id = R.string.string_ok), color = Color.White, fontSize = 14.sp
        )
    }
}
