package com.heart.heartmerge.ui.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Path
import android.util.AttributeSet
import android.view.SurfaceView

/**
 * Author:Lxf
 * Create on:2024/8/10
 * Description:
 */
class RoundedCornerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : SurfaceView(context, attrs, defStyleAttr) {

    private val cornerRadius = context.resources.getDimension(com.bdc.android.library.R.dimen.dp_16) // Helper function to convert dp to px
    private val path = Path()

    override fun onDraw(canvas: Canvas) {
        val width = width.toFloat()
        val height = height.toFloat()
        path.reset()
        path.addRoundRect(0f, 0f, width, height, cornerRadius, cornerRadius, Path.Direction.CW)
        canvas.clipPath(path)
        super.onDraw(canvas)
    }
}
