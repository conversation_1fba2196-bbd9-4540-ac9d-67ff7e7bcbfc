package com.heart.heartmerge.ui.activities.mine

import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.asLiveData
import com.angcyo.dsladapter.DslAdapterItem
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.getIndex
import com.bdc.android.library.extension.putIndex
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.DiamondFlowBean
import com.heart.heartmerge.databinding.ActivityMatchingCardRecordBinding
import com.heart.heartmerge.extension.attach
import com.heart.heartmerge.extension.checkSelected
import com.heart.heartmerge.ui.activities.mine.WalletTransactionActivity.ChildFragment
import com.heart.heartmerge.ui.fragments.common.RecyclerViewFragment
import com.heart.heartmerge.viewmodes.UserViewModel

class MatchingCardRecordActivity :
    BaseCoreActivity<ActivityMatchingCardRecordBinding, UserViewModel>() {

    override fun getLayoutId(): Int = R.layout.activity_matching_card_record

    override fun initView() {
        val titles: Array<String> = arrayOf(getString(R.string.obtain), getString(R.string.consume))
        mBinding.tab.setTitles(titles)
        initViewPager(titles)
        mBinding.tab.setViewPager(mBinding.viewPager)
    }

    private fun initViewPager(titles: Array<String>) {
        val fragments = buildList<Fragment> {
            titles.forEachIndexed { index, s ->
                add(ChildFragment.getInstance(index))
            }
        }

        mBinding.viewPager.attach(supportFragmentManager, lifecycle, fragments) { position ->
            mBinding.tab.setCurrentTab(position)
        }
        mBinding.viewPager.checkSelected(0)
    }

    class ChildFragment : RecyclerViewFragment() {
        private val viewModel by viewModels<UserViewModel>()

        companion object {
            fun getInstance(index: Int): ChildFragment {
                return ChildFragment().apply {
                    arguments = Bundle().apply { putIndex(index) }
                }
            }
        }

        override fun initData() {
            super.initData()
            mBinding.refreshLayout.setOnRefreshListener { b, i ->
                fetch(b, i)
            }
        }

        private fun fetch(refresh: Boolean, pageIndex: Int) {
            val type = if (arguments?.getIndex() == 0) 1 else 0
            viewModel.fetchMatchingRecordList(type, pageIndex)
                .asLiveData()
                .observe(this) {
                    append<DslAdapterItem>(refresh, it) {
                        itemLayoutId = R.layout.item_matching_card_record
                        itemBindOverride = { itemHolder, _, _, _ ->
                            val item = itemData as DiamondFlowBean
                            itemHolder.tv(R.id.tv_name)?.text =
                                if (arguments?.getIndex() == 1) when (item.sourceType) {
                                    "1" -> "First login"
                                    "2" -> "Sign in"
//                                    3 -> "转盘抽奖"
                                    else -> ""
                                } else {
//                                    "与主播${item.anchorUsername}匹配消耗"
                                    "With ${item.anchorUsername} Video call"
                                }
                            itemHolder.tv(R.id.tv_time)?.text = item.createTime
                            itemHolder.tv(R.id.tv_money)?.text =
                                "${if (type == 1) "+" else "-"} ${item.num}"
                        }
                    }
                }
        }
    }
}