package com.heart.heartmerge.ui.widget

import android.animation.ValueAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.widget.FrameLayout
import android.widget.LinearLayout
import com.heart.heartmerge.extension.getDimen
import kotlin.math.max
import kotlin.math.min

open class DraggableLinearLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : LinearLayout(context, attrs, defStyleAttr) {

    private var lastX = 0f
    private var lastY = 0f
    private var isDragging = false

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        post {
            val parentView = parent as android.view.View
            val layoutParams = layoutParams as FrameLayout.LayoutParams
            layoutParams.leftMargin = 0
            layoutParams.topMargin = parentView.height - height - getTopMargin()
            setLayoutParams(layoutParams)
        }
    }

    open fun getTopMargin(): Int = context.getDimen(com.bdc.android.library.R.dimen.dp_100)

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                lastX = event.rawX
                lastY = event.rawY
                isDragging = false
            }

            MotionEvent.ACTION_MOVE -> {
                val dx = event.rawX - lastX
                val dy = event.rawY - lastY

                if (dx * dx + dy * dy > 25) {
                    isDragging = true
                }

                val layoutParams = layoutParams as FrameLayout.LayoutParams
                layoutParams.leftMargin = (layoutParams.leftMargin + dx).toInt()
                layoutParams.topMargin = (layoutParams.topMargin + dy).toInt()

                layoutParams.leftMargin =
                    max(0, min(layoutParams.leftMargin, (parent as View).width - width))
                layoutParams.topMargin =
                    max(0, min(layoutParams.topMargin, (parent as View).height - height))

                setLayoutParams(layoutParams)

                lastX = event.rawX
                lastY = event.rawY
            }

            MotionEvent.ACTION_UP -> {
                if (isDragging) {
                    smoothAutoAlignToEdge()
                } else {
                    performClick()
                }
            }
        }
        return true
    }

    private fun smoothAutoAlignToEdge() {
        val parentView = parent as View
        val layoutParams = layoutParams as FrameLayout.LayoutParams

        val halfParentWidth = parentView.width / 2
        val viewCenterX = layoutParams.leftMargin + width / 2

        val targetLeftMargin = if (viewCenterX < halfParentWidth) {
            0
        } else {
            parentView.width - width
        }

        val animator = ValueAnimator.ofInt(layoutParams.leftMargin, targetLeftMargin)
        animator.addUpdateListener { animation ->
            layoutParams.leftMargin = animation.animatedValue as Int
            setLayoutParams(layoutParams)
        }
        animator.duration = 300
        animator.start()

        layoutParams.topMargin = max(0, min(layoutParams.topMargin, parentView.height - height))
    }
}
