package com.heart.heartmerge.ui.activities.mine

import androidx.lifecycle.asLiveData
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.bdc.android.library.extension.toast
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.utils.DataCleanManager
import com.bdc.android.library.utils.DateUtil
import com.heart.heartmerge.BuildConfig
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.VersionBean
import com.heart.heartmerge.databinding.ActivitySettingBinding
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.manager.DoNotDisturbManager
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.popup.NormalNewPopup
import com.heart.heartmerge.popup.showAppUpgradePopup
import com.heart.heartmerge.popup.showNormalNewPopup
import com.heart.heartmerge.ui.activities.WebViewActivity
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.viewmodes.SettingRequestEvent
import com.heart.heartmerge.viewmodes.UserViewModel

class SettingActivity : BaseCoreActivity<ActivitySettingBinding, UserViewModel>() {
    private var matchTipDialog: NormalNewPopup? = null
    private var versionBean: VersionBean? = null

    override fun getLayoutId(): Int = R.layout.activity_setting

    override fun initData() {
        super.initData()
        mBinding.tvVersion.text = "v${BuildConfig.VERSION_NAME}-${BuildConfig.VERSION_CODE}"
        cacheDir?.let {
            DataCleanManager.getCacheSize(it).let {
                mBinding.llClearCache.rightText = it
            }
        }
        mBinding.busySwitch.isChecked = MMKVDataRep.doNotDisturb
        DoNotDisturbManager.checkDoNotDisturbMode()
        checkDoNotDisturbCountdown()
//        mViewModel.checkVersion().asLiveData().observe(this) {
//            val hasUpdate = BuildConfig.VERSION_CODE < it.versionCode
//            if (hasUpdate) {
//                mBinding.tvNewVersionLabel.makeVisible()
//                versionBean = it
//            }
//        }
    }

    override fun bindListener() {
        super.bindListener()
        mBinding.tvCancellation.click {
            jump(CancelAccountActivity::class.java)
        }
        mBinding.tvBlackList.click {
            jump(BlacklistActivity::class.java)
        }
        mBinding.tvHelp.click {
            jump(FeedbackActivity::class.java)
        }

        mBinding.tvComplaints.click {
            WebViewActivity.jump(
                this, title = getString(R.string.customer_complaints), Constants.Agreement.FAQ_URL
            )
        }

        mBinding.tvAbout.click {
            jump(AboutActivity::class.java)
        }
        mBinding.tvRegistration.click {
            WebViewActivity.jump(
                this,
                getString(R.string.setting_user_agreement),
                Constants.Agreement.REGISTRATION_URL
            )
        }
        mBinding.tvPrivacyPolicy.click {
            WebViewActivity.jump(
                this, getString(R.string.setting_privacy_policy), Constants.Agreement.PRIVACY_URL
            )
        }
        mBinding.tvUseTerms.click {
            WebViewActivity.jump(
                this, getString(R.string.setting_use_terms), Constants.Agreement.TERMS_USE_URL
            )
        }

        mBinding.tvChildProtection.click {
            WebViewActivity.jump(
                this,
                getString(R.string.setting_child_protection),
                Constants.Agreement.CHILD_PROTECTION_AGREEMENT_URL
            )
        }

        mBinding.tvPermission.click {
            jump(PermissionManagerActivity::class.java)
        }

        mBinding.llUpdates.click {
            versionBean?.let { showAppUpgradePopup(this, versionBean) } ?: run {
                toast(getString(R.string.already_new_version))
            }
        }
        mBinding.llClearCache.click {
            DataCleanManager.cleanCustomCache(cacheDir.absolutePath)
            mBinding.llClearCache.rightText = "0KB"
        }

        mBinding.llLogout.click {
            showNormalNewPopup(
                this,
                R.mipmap.ic_dialog_warning,
                title = getString(R.string.remind),
                content = getString(R.string.logout_tips),
                btnSure = getString(R.string.confirm),
                btnCancel = getString(R.string.cancel),
                mainColor = R.color.color_F53D3D,
                block = {
                    mViewModel.logout(MMKVDataRep.userInfo.id).asLiveData().observe(this) {
                        LogX.i("logout suc")
                    }
                    AppUtil.forceReLogin(this)
                },
            )
        }
        mBinding.busySwitch.click {
            if (!mBinding.busySwitch.isChecked) {
                mViewModel.updateDoNotDisturb(mBinding.busySwitch.isChecked)
            } else {
                if (matchTipDialog == null) {
                    matchTipDialog = showNormalNewPopup(
                        this,
                        R.mipmap.ic_dialog_warning,
                        title = getString(R.string.remind),
                        content = getString(R.string.busy_open_tip),
                        btnSure = getString(R.string.confirm),
                        btnCancel = getString(R.string.cancel),
                        mainColor = R.color.color_F53D3D,
                        dismissOnTouchOutside = false,
                        dismissOnBackPressed = false,
                        block = {
                            mViewModel.updateDoNotDisturb(mBinding.busySwitch.isChecked)
                        },
                    ) {
                        mBinding.busySwitch.isChecked = false
                    }
                } else {
                    matchTipDialog?.show()
                }
            }
        }
    }

    private fun checkDoNotDisturbCountdown() {
        DoNotDisturbManager.remainingTime.observe(this) { value ->
            mBinding.tvDoNotDisturbCountdown.text = DateUtil.formatCountdown(value)
            if (value <= 0) {
                mBinding.tvDoNotDisturbCountdown.text = ""
                mBinding.busySwitch.isChecked = false
            }
        }
    }

    override fun initViewEvents() {
        mViewModel.pageEvents.observeEvent(this) {
            when (it) {
                is SettingRequestEvent.UpdateDoNotDisturbFailed -> {
                    mBinding.busySwitch.isChecked = it.orgStatus
                }

                else -> {}
            }
        }
    }
}