package com.heart.heartmerge.ui.activities.mine

import androidx.lifecycle.asLiveData
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.SwipeMenuHelper.Companion.SWIPE_MENU_TYPE_FLOWING
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.databinding.ActivityBlacklistBinding
import com.heart.heartmerge.extension.buildImageUrl
import com.heart.heartmerge.extension.loadAvatar
import com.heart.heartmerge.extension.openLoadMore
import com.heart.heartmerge.viewmodes.UserViewModel

class BlacklistActivity : BaseCoreActivity<ActivityBlacklistBinding, UserViewModel>() {
    override fun getLayoutId(): Int = R.layout.activity_blacklist

    override fun initView() {
        super.initView()
        mBinding.refreshLayout.recyclerView.openLoadMore { }
    }

    override fun initData() {
        mBinding.refreshLayout.onRefresh()
        mBinding.refreshLayout.setOnRefreshListener { b, i ->
            mViewModel.fetchBlackList(b).asLiveData().observe(this) {
                mBinding.refreshLayout.onFinishRefresh()
                mBinding.refreshLayout.append<DslAdapterItem>(b, it) {
                    itemSwipeMenuType = SWIPE_MENU_TYPE_FLOWING
                    itemLayoutId = R.layout.item_blacklist
                    itemBindOverride = { itemHolder, _, adapterItem, _ ->
                        val item = itemData as UserBean
                        itemHolder.apply {
                            img(R.id.iv_avatar)?.loadAvatar(item.avatar.buildImageUrl())
                            tv(R.id.tv_nickname)?.text = item.nickname
                            tv(R.id.tv_age)?.text = "${item.age}"
                            tv(R.id.tv_country)?.text = item.anchorCountry?.code
                            click(R.id.tv_remove) {
                                mViewModel.removeBlack(item.id).asLiveData()
                                    .observe(this@BlacklistActivity) {
                                        itemDslAdapter?.removeItem(adapterItem)
                                        updateItemDepend()
                                    }
                            }
                        }
                    }
                }
            }
        }
    }

//    class BlacklistAdapterItem(val block: (Int, String) -> Unit) : DslAdapterItem() {
//        init {
//            itemLayoutId = R.layout.item_blacklist
//            itemSwipeMenuType = SWIPE_MENU_TYPE_FLOWING
//        }
//
//        override fun onItemBind(
//            itemHolder: DslViewHolder,
//            itemPosition: Int,
//            adapterItem: DslAdapterItem,
//            payloads: List<Any>
//        ) {
//            super.onItemBind(itemHolder, itemPosition, adapterItem, payloads)
//            val item = itemData as BlacklistBean
//            itemHolder.v<ImageView>(R.id.iv_avatar)?.apply {
//                ImageLoader.with(this).load(item.blackHeadFileName).asAvatar().into(this)
//            }
//            itemHolder.v<TextView>(R.id.tv_name)?.text = item.blackNickName
//            itemHolder.v<TextView>(R.id.tv_id)?.text = "ID:${item.blackUserId}"
//            itemHolder.v<TextView>(R.id.tv_gender_age_area)?.text =
//                "${item.blackLevel} ${item.country}"
//
//            itemHolder.v<View>(R.id.ll_delete)?.click {
//                _itemSwipeMenuHelper?.closeSwipeMenu(itemHolder)
//                block.invoke(itemPosition, item.blackUserId)
//            }
//        }
//    }
}