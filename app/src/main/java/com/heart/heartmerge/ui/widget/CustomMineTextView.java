package com.heart.heartmerge.ui.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.ColorRes;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.heart.heartmerge.R;
import com.heart.heartmerge.i18n.I18nTextView;
import com.heart.heartmerge.i18n.I18nViewHelper;


/**
 * Created by lenovo on 2018/5/11.
 */

public class CustomMineTextView extends LinearLayout {
    private I18nTextView tvLeft;
    private TextView tvRight;
    private ImageView ivIcon;
    private ImageView ivNext;
    private EditText editText;
    private String sLeft, sRightHint, sRight;
    private boolean isNext;
    private boolean isShowEdiText;
    private boolean isBold;
    private int icon, nextIcon;
    private int leftColor, rightColor, rightHintColor;
    private float leftTextSize, rightTextSize;
    private int rightMarginHorizontal;
    private int rightMarginLeft;
    private int rightMarginRight;
    private int leftMarginLeft;
    private int leftMarginRight;
    private int rightGravity;


    public CustomMineTextView(Context context) {
        super(context);
        initAttrs(context, null);
    }

    public CustomMineTextView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initAttrs(context, attrs);
    }

    public CustomMineTextView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initAttrs(context, attrs);
    }

    private void initAttrs(Context context, AttributeSet attrs) {
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.CustomMineTextView);
        icon = typedArray.getResourceId(R.styleable.CustomMineTextView_ctv_left_icon, -1);
        sLeft = I18nViewHelper.getStringFromTypedArray(context, typedArray, R.styleable.CustomMineTextView_ctv_left);
        leftTextSize = typedArray.getDimension(R.styleable.CustomMineTextView_ctv_left_textSize, getResources().getDimension(com.bdc.android.library.R.dimen.sp_15));
        isBold = typedArray.getBoolean(R.styleable.CustomMineTextView_ctv_left_is_bold, true);
        leftColor = typedArray.getResourceId(R.styleable.CustomMineTextView_ctv_left_color, R.color.text_title1);
        leftMarginLeft = typedArray.getDimensionPixelSize(R.styleable.CustomMineTextView_ctv_left_marginLeft, -1);
        leftMarginRight = typedArray.getDimensionPixelSize(R.styleable.CustomMineTextView_ctv_left_marginRight, -1);

        isShowEdiText = typedArray.getBoolean(R.styleable.CustomMineTextView_ctv_right_isCanInput, false);

        nextIcon = typedArray.getResourceId(R.styleable.CustomMineTextView_ctv_next_icon, 0);
        isNext = typedArray.getBoolean(R.styleable.CustomMineTextView_ctv_next, true);

        sRight = I18nViewHelper.getStringFromTypedArray(context, typedArray, R.styleable.CustomMineTextView_ctv_right);
        sRightHint = I18nViewHelper.getStringFromTypedArray(context, typedArray, R.styleable.CustomMineTextView_ctv_right_hint);
        rightColor = typedArray.getResourceId(R.styleable.CustomMineTextView_ctv_right_color, R.color.color_C8C8C8);
        rightHintColor = typedArray.getResourceId(R.styleable.CustomMineTextView_ctv_right_hint_color, R.color.color_BFBFBF);
        rightTextSize = typedArray.getDimension(R.styleable.CustomMineTextView_ctv_right_textSize, 0);
        rightMarginHorizontal = typedArray.getDimensionPixelSize(R.styleable.CustomMineTextView_ctv_right_marginHorizontal, -1);
        rightMarginLeft = typedArray.getDimensionPixelSize(R.styleable.CustomMineTextView_ctv_right_marginLeft, -1);
        rightMarginRight = typedArray.getDimensionPixelSize(R.styleable.CustomMineTextView_ctv_right_marginRight, -1);
        rightGravity = typedArray.getInt(R.styleable.CustomMineTextView_ctv_right_gravity, 2);
        typedArray.recycle();
        init(context);
    }

    private void init(Context context) {
        LayoutInflater.from(getContext()).inflate(R.layout.view_mine_text_view, this, true);
        tvLeft = findViewById(R.id.tv_left);
        tvRight = findViewById(R.id.tv_right);
        ivNext = findViewById(R.id.iv_next);
        ivIcon = findViewById(R.id.iv_icon);
        editText = findViewById(R.id.tv_right_ed);
        showLeftView(context);
        showRightView(context);
    }

    private void showRightView(Context context) {
        editText.setVisibility(isShowEdiText ? VISIBLE : GONE);
        tvRight.setVisibility(!isShowEdiText ? VISIBLE : GONE);
        tvRight.setGravity(rightGravity == 2 ? Gravity.RIGHT : Gravity.LEFT);
        editText.setGravity(rightGravity == 2 ? Gravity.RIGHT : Gravity.LEFT);

        if (isShowEdiText) {
            editText.setText(sRight);
            editText.setHint(sRightHint);
            if (rightTextSize != 0) editText.setTextSize(TypedValue.COMPLEX_UNIT_PX, rightTextSize);
            tvRight.setTextColor(ContextCompat.getColor(context, rightColor));
            editText.setHintTextColor(ContextCompat.getColor(getContext(), rightHintColor));
            showRightMargin(editText);

        } else {
            tvRight.setText(sRight);
            tvRight.setHint(sRightHint);
            if (rightTextSize != 0) tvRight.setTextSize(TypedValue.COMPLEX_UNIT_PX, rightTextSize);
            tvRight.setTextColor(ContextCompat.getColor(context, rightColor));
            tvRight.setHintTextColor(ContextCompat.getColor(context, rightHintColor));
            showRightMargin(tvRight);
        }


        ivNext.setVisibility(isNext ? VISIBLE : GONE);
        if (nextIcon != 0) {
            ivNext.setImageResource(nextIcon);
        }
    }

    private void showRightMargin(View editText) {
        LayoutParams layoutParams = (LayoutParams) tvLeft.getLayoutParams();
        if (rightMarginLeft != -1) layoutParams.leftMargin = leftMarginLeft;
        if (rightMarginRight != -1) layoutParams.rightMargin = leftMarginRight;
        if (rightMarginHorizontal != -1) {
            layoutParams.leftMargin = rightMarginHorizontal;
            layoutParams.rightMargin = rightMarginHorizontal;
        }
        editText.setLayoutParams(layoutParams);
    }

    private void showLeftView(Context context) {
        //加粗
//        tvLeft.setTypeface(Typeface.defaultFromStyle(isBold ? Typeface.BOLD : Typeface.NORMAL));
        if (icon != -1) ivIcon.setImageResource(icon);
        ivIcon.setVisibility(icon == -1 ? GONE : VISIBLE);
        LayoutParams layoutParams = (LayoutParams) tvLeft.getLayoutParams();
        if (leftMarginLeft != -1) layoutParams.leftMargin = leftMarginLeft;
        if (leftMarginRight != -1) layoutParams.rightMargin = leftMarginRight;
        tvLeft.setLayoutParams(layoutParams);
        tvLeft.setTextColor(ContextCompat.getColor(context, leftColor));
        // 使用安全设置方法，避免触发动态文本检测
        if (sLeft != null) {
            tvLeft.setTextSafely(sLeft);
        }
        if (leftTextSize != 0) tvLeft.setTextSize(TypedValue.COMPLEX_UNIT_PX, leftTextSize);
    }


    public void setRightText(String s) {
        if (isShowEdiText) {
            editText.setText(s);
        } else this.tvRight.setText(s);
    }

    public void setRightTextColor(@ColorRes int color) {
        if (isShowEdiText) {
            editText.setTextColor(ContextCompat.getColor(this.getContext(),color));
        } else this.tvRight.setTextColor(ContextCompat.getColor(this.getContext(),color));
    }

    public void setRightHintText(String s) {
        if (isShowEdiText) {
            editText.setHint(s);
        } else this.tvRight.setHint(s);
    }

    public void setLeftText(String s) {
        if (tvLeft != null) {
            tvLeft.setText(s);
        }
    }

    public String getRightText() {
        return isShowEdiText ? editText.getText().toString() : this.tvRight.getText().toString();
    }

}
