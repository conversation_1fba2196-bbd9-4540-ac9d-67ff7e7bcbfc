package com.heart.heartmerge.ui.widget

import android.content.Context
import android.util.AttributeSet
import com.bdc.android.library.extension.makeInVisible
import com.bdc.android.library.extension.makeVisible
import com.heart.heartmerge.R
import com.heart.heartmerge.extension.getDimen

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2025/5/29 17:22
 * @description :
 */
class SpecialOfferView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : DraggableLinearLayout(context, attrs, defStyleAttr) {

    init {
        orientation = VERTICAL
        inflate(context, R.layout.view_special_offer, this)
        makeInVisible()
    }

    override fun getTopMargin(): Int = context.getDimen(com.bdc.android.library.R.dimen.dp_200)

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        postDelayed({
            makeVisible()
        }, 0)
    }
}