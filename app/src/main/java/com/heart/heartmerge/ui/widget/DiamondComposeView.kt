package com.heart.heartmerge.ui.widget

import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp
import com.heart.heartmerge.R
import com.heart.heartmerge.manager.DiamondChangeManager
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond

/**
 * Author:Lxf
 * Create on:2024/8/20
 * Description:
 */
@Composable
fun DiamondComposeView(
    modifier: Modifier = Modifier,
    fontSize: TextUnit = 14.sp,
    color: Color = Color.White,
    fontFamily: FontFamily = FontFamily(
        Font(R.font.baloochettan2),
    ),
) {
    // 从 StateFlow 获取状态
    val sharedValue by DiamondChangeManager.sharedValueFlow.collectAsState()
    Text(
        text = "${sharedValue.toShowDiamond()}",
        fontSize = fontSize,
        fontFamily = fontFamily,
        color = color,
        modifier = modifier,
    )
}