package com.heart.heartmerge.ui.widget.slide;

/**
 * Created by 钉某人
 * github: https://github.com/DingMouRen
 * email: nailding<PERSON><PERSON>@gmail.com
 */

public final class ItemConfig {
    public static final int SLIDING_NONE = 1;
    public static final int SLIDING_LEFT = 1 << 2;
    public static final int SLIDING_RIGHT = 1 << 3;
    public static final int SLIDED_LEFT = 1;
    public static final int SLIDED_RIGHT = 1 << 2;
    public static final int DEFAULT_SHOW_ITEM = 3;
    public static final float DEFAULT_SCALE = 0.1f;
    public static final int DEFAULT_TRANSLATE_Y = 15;
    public static final float DEFAULT_ROTATE_DEGREE = 15f;
    public static final Long AUTO_SWIPED_ANIM_DURATION = 300L;
}
