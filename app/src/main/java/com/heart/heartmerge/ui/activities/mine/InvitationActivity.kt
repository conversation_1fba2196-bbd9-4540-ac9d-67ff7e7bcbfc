package com.heart.heartmerge.ui.activities.mine

import android.content.Intent
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.click
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.ActivityInvitationBinding
import com.heart.heartmerge.firebase.report.DT_EVENT_CONSTANTS
import com.heart.heartmerge.firebase.report.ReportManager
import com.heart.heartmerge.viewmodes.UserViewModel


class InvitationActivity : BaseCoreActivity<ActivityInvitationBinding, UserViewModel>() {

    override fun getLayoutId(): Int = R.layout.activity_invitation

    override fun bindListener() {
        super.bindListener()
        mBinding.btnShare.click {
            ReportManager.logEvent(DT_EVENT_CONSTANTS.EVENT_SHARE)
            val i = Intent(Intent.ACTION_SEND)
            i.setType("text/plain")
            i.putExtra(Intent.EXTRA_SUBJECT, "HeartMerge")
            i.putExtra(
                Intent.EXTRA_TEXT,
                "https://play.google.com/store/apps/details?id=com.heart.heartmerge&pcampaignid=web_share"
            )
            startActivity(Intent.createChooser(i, "Share HeartMerge"))
        }
    }
}