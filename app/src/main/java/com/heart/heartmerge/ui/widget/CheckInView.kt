import android.animation.ValueAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.widget.FrameLayout
import android.widget.LinearLayout
import com.bdc.android.library.utils.DisplayUtil
import com.heart.heartmerge.R
import com.heart.heartmerge.extension.getDimen
import kotlin.math.max
import kotlin.math.min

class CheckInView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : LinearLayout(context, attrs, defStyleAttr) {

    private var lastX = 0f
    private var lastY = 0f
    private var isDragging = false

    init {
        orientation = VERTICAL
//        setBackgroundColor(0x88FF0000.toInt()) // 半透明红色背景
        // 如果传递了有效的布局资源 ID，则自动加载该布局
        val contentView = LayoutInflater.from(context).inflate(R.layout.view_checkin, this, false)
        addView(contentView)
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        // 在视图加载完成后，动态设置初始位置为右下角
        post {
            val parentView = parent as View
            val layoutParams = layoutParams as FrameLayout.LayoutParams
            layoutParams.leftMargin =
                DisplayUtil.getScreenWidth(context) - this.right // 设置右边距为0，确保视图在右侧
            layoutParams.topMargin =
                parentView.height - height - context.getDimen(com.bdc.android.library.R.dimen.dp_100)
            setLayoutParams(layoutParams)
        }
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                lastX = event.rawX
                lastY = event.rawY
                isDragging = false
            }

            MotionEvent.ACTION_MOVE -> {
                val dx = event.rawX - lastX
                val dy = event.rawY - lastY

                if (dx * dx + dy * dy > 25) {
                    isDragging = true
                }

                val layoutParams = layoutParams as FrameLayout.LayoutParams
                layoutParams.leftMargin = (layoutParams.leftMargin + dx).toInt()
                layoutParams.topMargin = (layoutParams.topMargin + dy).toInt()

                // 修复边界限制，确保视图可以向左拖动
                layoutParams.leftMargin =
                    max(0, min(layoutParams.leftMargin, (parent as View).width - width))

                layoutParams.topMargin =
                    max(0, min(layoutParams.topMargin, (parent as View).height - height))

                setLayoutParams(layoutParams)

                lastX = event.rawX
                lastY = event.rawY
            }

            MotionEvent.ACTION_UP -> {
                if (isDragging) {
                    // 拖动结束时自动贴边，带动画
                    smoothAutoAlignToEdge()
                } else {
                    performClick()
                }
            }
        }
        return true
    }

    private fun smoothAutoAlignToEdge() {
        val parentView = parent as View
        val layoutParams = layoutParams as FrameLayout.LayoutParams

        val halfParentWidth = parentView.width / 2
        val viewCenterX = layoutParams.leftMargin + width / 2

        // 自动贴边：如果视图在屏幕左侧一半，贴左边；否则贴右边
        val targetLeftMargin = if (viewCenterX < halfParentWidth) {
            0
        } else {
            parentView.width - width
        }

        // 动画平滑移动到边缘
        val animator = ValueAnimator.ofInt(layoutParams.leftMargin, targetLeftMargin)
        animator.addUpdateListener { animation ->
            layoutParams.leftMargin = animation.animatedValue as Int
            setLayoutParams(layoutParams)
        }
        animator.duration = 300 // 动画持续时间
        animator.start()

        // 同时，确保顶部和底部边界不超出屏幕
        layoutParams.topMargin = max(0, min(layoutParams.topMargin, parentView.height - height))
    }


}
