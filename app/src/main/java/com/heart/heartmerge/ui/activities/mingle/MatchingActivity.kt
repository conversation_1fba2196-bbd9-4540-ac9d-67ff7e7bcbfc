package com.heart.heartmerge.ui.activities.mingle

import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.VideoRecordInfo
import com.heart.heartmerge.databinding.ActivityMatchingRecordBinding
import com.heart.heartmerge.extension.openLoadMore
import com.heart.heartmerge.ui.activities.message.MatchHistoryItem
import com.heart.heartmerge.viewmodes.MatchRequestEvent
import com.heart.heartmerge.viewmodes.MingleViewModel

class MatchingActivity : BaseCoreActivity<ActivityMatchingRecordBinding, MingleViewModel>() {
    private var loadPage = 1
    private val pageSize = 20
    override fun getLayoutId(): Int = R.layout.activity_matching_record

    override fun initView() {
        mBinding.refreshLayout.recyclerView.apply {
            setOnRefreshListener { b: Boolean, _: Int ->
                if (b) {
                    loadPage = 1
                }
                mViewModel.fetchMatchingHistory(loadPage, pageSize)
            }
            openLoadMore {
                loadPage++
                mViewModel.fetchMatchingHistory(loadPage, pageSize)
            }
        }
    }

    override fun initData() {
        mBinding.refreshLayout.onRefresh()
    }

    override fun initViewEvents() {
        mViewModel.pageEvents.observeEvent(this) {
            when (it) {
                is MatchRequestEvent.MatchHistorySuccess -> {
                    mBinding.refreshLayout.apply {
                        if (loadPage == 1) {
                            clearAllItems()
                        }
                        append<MatchHistoryItem>(items = it.list) { data ->
                            recordInfo = data as VideoRecordInfo
                        }
                    }
                }

                is MatchRequestEvent.MatchHistoryFailed -> {
                    ToastUtil.show(it.msg)
                    val tmpPage = loadPage--
                    loadPage = if (tmpPage >= 1) tmpPage else 1
                    mBinding.refreshLayout.loadFailedWithMore()
                }

                else -> {}
            }
        }
    }

//    override fun initView() {
//        mBinding.refreshLayout.onRefresh()
//        mBinding.refreshLayout.setOnRefreshListener { refresh, i ->
//            mViewModel.fetchMatchingHistory(i).asLiveData().observe(this) {
//                it?.let {
//                    mBinding.refreshLayout.append<DslAdapterItem>(it) {
//                        itemLayoutId = R.layout.item_matching_record
//                        itemBindOverride = { itemHolder, _, _, _ ->
//                            val item = itemData as? MatchingRecordBean
//                            itemHolder.tv(R.id.tv_title)?.text = "与主播  ${item?.nickName}匹配"
//                            itemHolder.tv(R.id.tv_time)?.text = item?.videoTimeShow
//                            itemHolder.tv(R.id.tv_diamond)?.text = item?.diamond.toString()
//                            itemHolder.tv(R.id.tv_juan)?.text = item?.freeFlag.toString()
//                        }
//                    }
//                }
//            }
//        }
//    }
}