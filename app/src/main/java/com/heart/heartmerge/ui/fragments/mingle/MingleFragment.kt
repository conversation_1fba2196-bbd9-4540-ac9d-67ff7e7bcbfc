package com.heart.heartmerge.ui.fragments.mingle

import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import androidx.viewpager.widget.ViewPager
import com.bdc.android.library.base.fragment.BaseCoreFragment
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.FragmentMingleBinding
import com.heart.heartmerge.ui.activities.mingle.MatchingRecordActivity
import com.heart.heartmerge.viewmodes.MingleViewModel

class MingleFragment : BaseCoreFragment<FragmentMingleBinding, MingleViewModel>() {

    private var fragmentList: ArrayList<Fragment> = ArrayList()

    override fun getLayoutId(): Int = R.layout.fragment_mingle

    override fun initView() {
        super.initView()

        val tagList: List<String> = java.util.ArrayList()
        fragmentList.add(MatchFragment.newInstance())
//        fragmentList.add(SwipeFragment.newInstance())

        val tabFragmentPagerAdapter =
            TabFragmentAdapter(childFragmentManager, fragmentList, tagList)
        mBinding.viewPager.adapter = tabFragmentPagerAdapter
        mBinding.viewPager.offscreenPageLimit = fragmentList.size

        mBinding.viewPager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrolled(
                position: Int, positionOffset: Float, positionOffsetPixels: Int
            ) {
            }

            override fun onPageSelected(position: Int) {
                if (position == 0) {
                    mBinding.tvTab1.setBackgroundResource(R.drawable.shape_gradient_f84139_ff765c_50radius)
                    mBinding.tvTab2.setBackgroundResource(0)
                } else {
                    mBinding.tvTab1.setBackgroundResource(0)
                    mBinding.tvTab2.setBackgroundResource(R.drawable.shape_gradient_f84139_ff765c_50radius)
                }
            }

            override fun onPageScrollStateChanged(state: Int) {
            }
        })


        mBinding.tvTab1.click {
            mBinding.viewPager.setCurrentItem(0)
        }
        mBinding.tvTab2.click {
            mBinding.viewPager.setCurrentItem(1)
        }

        mBinding.tvMatchRecord.click {
            jump(MatchingRecordActivity::class.java)
        }
    }

    class TabFragmentAdapter(
        private val fragmentManager: FragmentManager,
        private val fragments: List<Fragment>,
        private val list_Title: List<String>
    ) :
        FragmentPagerAdapter(fragmentManager) {
        private val tags: MutableList<String> =
            java.util.ArrayList() //标示fragment的tag

        override fun getItem(position: Int): Fragment {
            return fragments[position]
        }

        override fun getCount(): Int {
            return fragments.size
        }

        override fun getItemPosition(`object`: Any): Int {
            return POSITION_NONE
        }

        //这个就不说了
        private fun makeFragmentName(viewId: Int, id: Long): String {
            return "android:switcher:$viewId:$id"
        }

        override fun getPageTitle(position: Int): CharSequence? {
            return list_Title[position]
        }

        override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
            super.destroyItem(container, position, `object`)
        }

        //必须重写此方法，添加tag一一做记录
        override fun instantiateItem(container: ViewGroup, position: Int): Any {
            tags.add(makeFragmentName(container.id, getItemId(position)))
            val fragment = super.instantiateItem(container, position) as Fragment
            fragmentManager.beginTransaction().show(fragment).commitAllowingStateLoss()
            return fragment
        }

        //根据tag查找缓存的fragment，移除缓存的fragment，替换成新的
        fun setNewFragments() {
            val fragmentTransaction = fragmentManager.beginTransaction()
            for (i in tags.indices) {
                fragmentTransaction.remove(fragmentManager.findFragmentByTag(tags[i])!!)
            }
            fragmentTransaction.commitAllowingStateLoss()
            fragmentManager.executePendingTransactions()
            tags.clear()
            notifyDataSetChanged()
        }
    }
}
