package com.heart.heartmerge.ui.activities.mine

import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.ActivityCancelAccountSubmitBinding
import com.heart.heartmerge.viewmodes.UserViewModel

class CancelAccountSubmitActivity :
    BaseCoreActivity<ActivityCancelAccountSubmitBinding, UserViewModel>() {
    override fun getLayoutId(): Int = R.layout.activity_cancel_account_submit

    override fun bindListener() {
        mBinding.btnSubmit.click {
            if (mBinding.radioReason.checkedRadioButtonId < 0) {
                ToastUtil.show(getString(R.string.cancel_account_choose_reason_toast))
                return@click
            }
            if (mBinding.radioReason.checkedRadioButtonId == R.id.radio_other && mBinding.etReason.text.toString()
                    .isEmpty()
            ) {
                ToastUtil.show(getString(R.string.cancel_account_reason_empty_toast))
                return@click
            }
            jump(CancelAccountConfirmActivity::class.java)
        }
    }
}