package com.heart.heartmerge.ui.activities.anchor.search

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.bdc.android.library.flowlayout.FlowLayout
import com.bdc.android.library.flowlayout.FlowLayoutAdapter
import com.heart.heartmerge.R

/**
 * Author:Lxf
 * Create on:2024/7/27
 * Description:
 */
class SearchHistoryFlowAdapter() : FlowLayoutAdapter() {
    private var dataList: MutableSet<String> = mutableSetOf()
    private var mCheckedPosition = -1

    fun setCheckedPosition(checkedPosition: Int) {
        if (this.mCheckedPosition == checkedPosition) {
            this.mCheckedPosition = -1
        } else {
            this.mCheckedPosition = checkedPosition
        }
        notifyChange()
    }

    fun setNewData(datas: Set<String>) {
        dataList.apply {
            clear()
            addAll(datas)
        }
        notifyChange()
    }

    fun addData(datas: Set<String>) {
        dataList.addAll(datas)
        notifyChange()
    }

    fun addData(data: String) {
        dataList.add(data)
        notifyChange()
    }


    override fun createView(context: Context, flowLayout: FlowLayout, position: Int): View {
        val textView = TextView(context)
        textView.text = dataList.elementAt(position)
        textView.textSize = 14f
        if (position == mCheckedPosition) {
            textView.setTextColor(ContextCompat.getColor(context, R.color.color_white))
            textView.setBackgroundResource(R.drawable.shape_home_language_item_sel)
        } else {
            textView.setTextColor(ContextCompat.getColor(context, R.color.color_8F94C7))
            textView.setBackgroundResource(R.drawable.shape_home_language_item_normal)
        }
        val params =
            MarginLayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
        textView.layoutParams = params
        val horizontalMargin = dip2px(context, 16f)
        val vertical = dip2px(context, 6f)
        textView.setPadding(horizontalMargin, vertical, horizontalMargin, vertical)
        return textView
    }

    override fun getItemCount(): Int = dataList.size

    override fun getItem(position: Int): Any = dataList.elementAt(position)

    private fun dip2px(context: Context, dpValue: Float): Int {
        val scale = context.resources.displayMetrics.density
        return (dpValue * scale + 0.5f).toInt()
    }
}