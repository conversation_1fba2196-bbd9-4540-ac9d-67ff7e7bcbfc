package com.heart.heartmerge.ui.activities.mine

import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.ActivityCancelAccountBinding
import com.heart.heartmerge.viewmodes.UserViewModel

class CancelAccountActivity : BaseCoreActivity<ActivityCancelAccountBinding, UserViewModel>() {
    override fun getLayoutId(): Int = R.layout.activity_cancel_account

    override fun bindListener() {
        mBinding.tvSubmit.click { jump(CancelAccountSubmitActivity::class.java) }
    }
}