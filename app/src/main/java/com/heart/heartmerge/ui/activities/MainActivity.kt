package com.heart.heartmerge.ui.activities

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import androidx.activity.ComponentActivity
import androidx.activity.viewModels
import androidx.fragment.app.Fragment
import androidx.lifecycle.asLiveData
import androidx.lifecycle.lifecycleScope
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.extension.jumpThenFinish
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.utils.ActivityManager
import com.bdc.android.library.utils.Logger
import com.bdc.android.library.utils.ToastUtil
import com.flyco.tablayout.listener.CustomTabEntity
import com.flyco.tablayout.listener.OnTabSelectListener
import com.heart.heartmerge.BuildConfig
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.CheckOrderBody
import com.heart.heartmerge.databinding.ActivityMainBinding
import com.heart.heartmerge.extension.attach
import com.heart.heartmerge.extension.checkSelected
import com.heart.heartmerge.extension.formatDate
import com.heart.heartmerge.firebase.report.ReportManager
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.manager.DoNotDisturbManager
import com.heart.heartmerge.manager.FloatGiftManager
import com.heart.heartmerge.manager.GiftManager
import com.heart.heartmerge.manager.MainPopupPriority
import com.heart.heartmerge.manager.OrderManager
import com.heart.heartmerge.manager.PriorityDialogManager
import com.heart.heartmerge.mmkv.MMKVBaseDataRep
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.popup.PlatformRulePopup
import com.heart.heartmerge.popup.UserFirstLoginRewardPopup
import com.heart.heartmerge.popup.UserSignPopup
import com.heart.heartmerge.popup.showAppUpgradePopup
import com.heart.heartmerge.popup.showBecomeMembershipPopup
import com.heart.heartmerge.popup.showPaymentSuccessPopup
import com.heart.heartmerge.socket.WebSocketManager
import com.heart.heartmerge.ui.activities.mine.BuildProfileActivity
import com.heart.heartmerge.ui.fragments.home.HomeFragment
import com.heart.heartmerge.ui.fragments.message.MessageFragment
import com.heart.heartmerge.ui.fragments.mine.MineFragment
import com.heart.heartmerge.ui.fragments.mingle.MingleFragment
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.EmulatorDetector
import com.heart.heartmerge.viewmodes.GetRongYunTokenEvent
import com.heart.heartmerge.viewmodes.MainViewModel
import com.heart.heartmerge.viewmodes.UserViewModel
import com.scottyab.rootbeer.RootBeer
import io.rong.imkit.RongIM
import io.rong.imlib.RongIMClient
import io.rong.imlib.RongIMClient.ConnectCallback
import io.rong.imlib.translation.TranslationClient
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch


/**
 * 作者：Lxf
 * 创建日期：2024/7/27 14:11
 * 描述：
 */
class MainActivity : BaseCoreActivity<ActivityMainBinding, MainViewModel>() {

    private lateinit var homeFragment: HomeFragment
    private lateinit var mingleFragment: MingleFragment
    private lateinit var messageFragment: MessageFragment
    private lateinit var mineFragment: MineFragment
    private lateinit var mTitles: Array<String>

    private val userViewModel by viewModels<UserViewModel>()
    private val mIconUnselectIds = intArrayOf(
        R.mipmap.ic_tab_home_normal,
        R.mipmap.ic_tab_mingle_normal,
        R.mipmap.ic_tab_msg_normal,
        R.mipmap.ic_tab_mine_normal
    )
    private val mIconSelectIds = intArrayOf(
        R.mipmap.ic_tab_home_checked,
        R.mipmap.ic_tab_mingle_checked,
        R.mipmap.ic_tab_msg_checked,
        R.mipmap.ic_tab_mine_checked
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        handleDeepLink(intent)
    }

    override fun isImmerse(): Boolean = true

    override fun isDarkStatusBar(): Boolean = false

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        val selTab = intent.getIntExtra(Constants.INTENT_PARAM_TAB_INDEX, -1)
        if (selTab >= 0) {
            mBinding.viewPager.checkSelected(selTab)
        }
        handleDeepLink(intent)
    }

    override fun onResume() {
        super.onResume()
        AppUtil.canAutoPopupVideoCallingPage = true
        mViewModel.refreshUnreadMsg()
    }

    override fun initData() {
        super.initData()
        // 强制重新初始化 WebSocketManager，确保使用最新的token和用户信息
        WebSocketManager.getInstance().forceReinit(
            applicationContext, BuildConfig.SOCKET_HOST
        )

        mViewModel.clearCacheVideos()
        mViewModel.fetchConfiguration()

        userViewModel.refreshUser()
        mViewModel.getVirtualVideos()
        userViewModel.userBean.asLiveData().observeForever {
            if (it.reLogin) {
                LogX.d("reLogin")
                AppUtil.forceReLogin(this)
                return@observeForever
            }
            MMKVDataRep.userInfo = it
            ReportManager.setUserProperty(it.nickname, it.id)

            if (it.birthdayAt.isEmpty() && it.avatar.isEmpty()) {
                if (ActivityManager.current is BuildProfileActivity) {
                    return@observeForever
                }
                jumpThenFinish(BuildProfileActivity::class.java)
            }
        }

        LogX.i("rongYunToken  ${MMKVBaseDataRep.rongYunToken}")
        //获取融云token并连接
        if (MMKVBaseDataRep.rongYunToken.isNullOrEmpty()) {
//            mViewModel.getRongYunToken()
        } else {
            connectRongYunIM(MMKVBaseDataRep.rongYunToken ?: "")
        }
        //获取融云翻译token
        if (MMKVBaseDataRep.rongYunTransitionToken.isNullOrEmpty()) {
            mViewModel.getRongYunTranslateToken()
        } else {
            TranslationClient.getInstance().updateAuthToken(MMKVBaseDataRep.rongYunTransitionToken)
        }
        setIMStatusListener()
        //activity刚初始化
//        lifecycleScope.launch {
//            delay(2000)
//            mViewModel.refreshUnreadMsg()
//            //开启AIB
//            mViewModel.startAutoAnchorCalling()
//        }

        FlowBus.with<Boolean>(Constants.PAYMENT_SUCCESS).register(this) { //支付成功
            if (it) {
                //充值成功 隐藏充值提醒
                userViewModel.refreshUser()
            }
        }

        FlowBus.with<Boolean>(Constants.SUBSCRIBE_SUCCESS).register(this) { //订阅成功
            if (it) {
                //充值成功 隐藏充值提醒
                userViewModel.refreshUser()
            }
        }

        delayTask()
    }

    private fun handleDeepLink(intent: Intent?) {
        LogX.d("处理DeepLink ${intent?.data}")
        val data = intent?.data ?: return
        when (data.host) {
            "payment-success" -> {
                LogX.d("处理DeepLink payment-success ${intent?.data}")
                val orderId = data.getQueryParameter("order_id")
                // 跳转到支付成功页
                if (orderId != null && orderId.isNotEmpty()) {
                    handlePaymentResult(orderId)
                }
            }
        }
    }

    private fun handlePaymentResult(orderId: String) {
        LogX.d("处理DeepLink payment-success orderId: $orderId")
        showLoading("Loading...")
        OrderManager.checkOrderStatus(
            body = CheckOrderBody(
                payOrderId = orderId, payType = null
            )
        ) {
            stopLoading()
            it?.let { result ->
                if (result.isPaid) {
                    userViewModel.refreshUser()
                    if (result.isSubscribe) {
                        showBecomeMembershipPopup(this) {
                            lifecycleScope.launch {
                                FlowBus.with<Boolean>(Constants.SUBSCRIBE_SUCCESS)
                                    .post(result.isPaid)
                            }
                        }
                    } else {
                        showPaymentSuccessPopup(this) {
                            lifecycleScope.launch {
                                FlowBus.with<Boolean>(Constants.PAYMENT_SUCCESS).post(result.isPaid)
                            }
                        }
                    }
                }
            } ?: run {
                ToastUtil.show(getString(R.string.payment_processing))
            }
        }
    }

    private fun delayTask() {
        lifecycleScope.launch {
            delay(2000)
            mViewModel.refreshUnreadMsg()
            //开启AIB
//            mViewModel.startAutoAnchorCalling()

            DoNotDisturbManager.checkDoNotDisturbMode()

            FloatGiftManager.start(this@MainActivity)
            GiftManager.getInstance(this@MainActivity).initializeGifts { success, failedList ->
                LogX.d("gift svg download result $success")
            }

//            fetchVersion()
        }
    }

    private var versionCheckJob: Job? = null
    private var isUpgradePopupShown = false

    private fun fetchVersion() {
        // 启动定时检查版本更新（只启动一次）
        if (versionCheckJob == null || versionCheckJob?.isActive != true) {
            versionCheckJob = MainScope().launch {
                while (true) {
                    try {
                        // 使用first()只获取第一个值，然后继续循环
                        val versionBean = userViewModel.checkVersion().first()
                        val hasUpdate = BuildConfig.VERSION_CODE < versionBean.versionCode

                        if (hasUpdate) {
                            MMKVDataRep.hasVersionUpdate = true
                            // 如果今天还没显示过弹框且当前没有显示弹框，则显示弹框
                            if (!isUpgradePopupShown) {
                                // 获取当前活跃的Activity来显示弹框
                                ActivityManager.current?.let { activity ->
                                    if (activity is ComponentActivity) {
                                        isUpgradePopupShown = true
                                        showAppUpgradePopup(
                                            activity, versionBean
                                        ) {
                                            isUpgradePopupShown = false
                                        }
                                    }
                                }
                            }
                        }
                    } catch (e: Exception) {
                        Logger.e("Version check failed: ${e.message}")
                        // 网络请求失败，继续下一次循环
                    }

                    delay(10000) // 每10秒检查一次
                    Logger.d("Version check loop continuing...")

                    // 检查MainActivity是否还存在，如果已销毁则退出循环
                    if (isDestroyed || isFinishing) {
                        Logger.d("MainActivity destroyed, stop version check")
                        break
                    }
                }
            }
        }
    }

    override fun initView() {
        if (!BuildConfig.DEBUG) {
            AppUtil.screenSecure(window)
        }
//        val rootBeer = RootBeer(this)
//        LogX.e("vvvvvvvvvvvvvvvvvv, 是否被root： ${rootBeer.isRooted} ")
//        if (rootBeer.isRooted) {
            //we found indication of root
//        } else {
            //we didn't find indication of root
//        }
//        ToastUtil.show("是否被root： ${rootBeer.isRooted}  是否为模拟器：  ${EmulatorDetector.isEmulator(this)}"  )
//        LogX.e("vvvvvvvvvvvvvvvvvv11111, 是否为模拟器：  ${EmulatorDetector.isEmulator(this)} ")

        mTitles = arrayOf(
            getString(R.string.main_tabs_home),
            getString(R.string.main_tabs_match),
            getString(R.string.main_tabs_message),
            getString(R.string.main_tabs_mine)
        )
        initBottomBar()
        initViewPager()

//        checkFirstLoginRewardShowed()
        checkPlatformRuleRead()
        checkSign()
    }

    override fun getLayoutId(): Int = R.layout.activity_main

    override fun initViewEvents() {
        mViewModel.pageEvents.observeEvent(this) {
            when (it) {
//                is GetRongYunTokenEvent.GetRongYunTokenSuccess -> connectRongYunIM(it.token)
                is GetRongYunTokenEvent.ShowToast -> ToastUtil.show(it.message)
                is GetRongYunTokenEvent.RefreshBottomBarUnReadMsg -> {
                    if (it.count > 0) {
                        mBinding.bottomBar.showMsg(2, it.count)
                        mBinding.bottomBar.setMsgMargin(2, -10f, 5f)
                    } else {
                        mBinding.bottomBar.hideMsg(2)
                    }
                }

                is GetRongYunTokenEvent.VipChangeRefresh -> {
                    userViewModel.refreshUser()
                }

                else -> {}
            }
        }
    }

    //连接融云
    private fun connectRongYunIM(token: String) {
        RongIM.connect(token, 0, object : ConnectCallback() {
            override fun onSuccess(userId: String) {
                LogX.e("RongYun connect success: $userId")
            }

            override fun onError(e: RongIMClient.ConnectionErrorCode) {
                LogX.e("RongYun connect error: ${e.value}")
                //当提示token失效或者获取时重新连接
//                when (e) {
//                    RongIMClient.ConnectionErrorCode.RC_CONN_TOKEN_INCORRECT, RongIMClient.ConnectionErrorCode.RC_CONN_TOKEN_EXPIRE -> mViewModel.getRongYunToken()
//                    else -> {}
//                }
            }

            override fun onDatabaseOpened(code: RongIMClient.DatabaseOpenStatus) {
                if (RongIMClient.DatabaseOpenStatus.DATABASE_OPEN_SUCCESS == code) {
                    //本地数据库打开，跳转到会话列表页面
                } else {
                    //数据库打开失败，可以弹出 toast 提示。
                    LogX.e("RongYun connect onDatabaseOpened Failed: $code")
                }
            }
        })
    }

    private fun setIMStatusListener() {
        RongIM.setConnectionStatusListener { status -> LogX.e("rongyun connect status: $status") }
    }

    private fun initViewPager() {
        val fragments = mutableListOf<Fragment>()
        homeFragment = ensureFragment(fragments) { HomeFragment() }
        mingleFragment = ensureFragment(fragments) { MingleFragment() }
        messageFragment = ensureFragment(fragments) { MessageFragment() }
        mineFragment = ensureFragment(fragments) { MineFragment() }
        mBinding.viewPager.attach(
            supportFragmentManager, lifecycle, fragments, isInputEnabled = false, isLimitAll = true
        ) { position ->
            mBinding.bottomBar.setCurrentTab(position)
        }
        mBinding.viewPager.checkSelected(0)
    }

    private fun initBottomBar() {
        val mTabEntities = ArrayList<CustomTabEntity>()
        for (i in mTitles.indices) {
            mTabEntities.add(TabEntity(mTitles[i], mIconSelectIds[i], mIconUnselectIds[i]))
        }
        mBinding.bottomBar.run {
            setTabData(mTabEntities)
            setOnTabSelectListener(object : OnTabSelectListener {
                override fun onTabSelect(position: Int) {
                    mBinding.viewPager.checkSelected(position)
                }

                override fun onTabReselect(position: Int) {
                }
            })
            getMsgView(2)?.run {
                strokeWidth = 0
            }
        }
    }

    // ViewPager2的fragment初始化时需要先检查对应tag是否已经存在（app被系统杀死重启时已存在），
    // 不存在时调用function创建，否则直接使用系统已经创建的实例，inline函数使得泛型参数T不被擦除。
    // 重要信息：调用循序必须严格对应ViewPager从左到右显示的页面。
    private inline fun <reified T : Fragment> ensureFragment(
        fragments: MutableList<Fragment>, function: () -> T
    ): T {
        val f = supportFragmentManager.findFragmentByTag("f${fragments.size}")
        val result = if (f is T) f else function()
        fragments.add(result)
        return result
    }

    /**
     * 检查平台规则是否读取
     */
    private fun checkPlatformRuleRead() {
        if (MMKVDataRep.isPlatformRuleRead || isDestroyed || isFinishing) {
            return
        }
        PriorityDialogManager.addPopup(
            MainPopupPriority.FIRST_LAUNCHER_PLATFORM_RULE, PlatformRulePopup(this)
        )
    }

    /**
     * 检查首次登录奖励弹框是否弹出
     */
    private fun checkFirstLoginRewardShowed() {
        runCatching {
            val diamond = MMKVDataRep.userInfo.initialFreeDiamondGive
            if (MMKVDataRep.isFirstLoginRewardShowed || diamond <= 0 || isDestroyed || isFinishing) {
                return
            }
            PriorityDialogManager.addPopup(
                MainPopupPriority.FIRST_USER_LOGIN_REWARD, UserFirstLoginRewardPopup(this, null)
            )
        }.onFailure {
            it.printStackTrace()
        }
    }

    /**
     * 检查签到
     */
    private fun checkSign() {
        //非vip 当天已显示，再次打开app,不再显示签到弹框 (暂时调整为非vip用户不弹）
        if (!MMKVDataRep.userInfo.isVIP) {
            return
        }

        val nowString = System.currentTimeMillis().formatDate()

        //vip 当天已签到，再次打开app，不再显示签到弹框
        if (MMKVDataRep.userInfo.isVIP && TextUtils.equals(
                nowString, MMKVDataRep.lastSignDate
            )
        ) {
            return
        }

        PriorityDialogManager.addPopup(
            MainPopupPriority.USER_SIGN, UserSignPopup(this)
        )
    }

    override fun onDestroy() {
        super.onDestroy()
        // 取消版本检查协程
        versionCheckJob?.cancel()
//        lifecycleScope.launch(Dispatchers.IO) {
//            AppUtil.deleteFolder(File(ContextHolder.context.filesDir, saveVideoFolder))
//        }
        userViewModel.userBean.asLiveData().removeObserver { value -> }
    }
}