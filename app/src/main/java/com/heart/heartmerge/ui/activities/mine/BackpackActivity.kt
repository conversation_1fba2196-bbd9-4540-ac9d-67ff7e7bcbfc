package com.heart.heartmerge.ui.activities.mine

import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.asLiveData
import com.angcyo.dsladapter.DslAdapterItem
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.putIndex
import com.bdc.android.library.imageloader.ImageLoader
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.BackpackBean
import com.heart.heartmerge.databinding.ActivityBackpackBinding
import com.heart.heartmerge.extension.attach
import com.heart.heartmerge.extension.checkSelected
import com.heart.heartmerge.popup.showActionSheetPopup
import com.heart.heartmerge.ui.fragments.common.RecyclerViewFragment
import com.heart.heartmerge.viewmodes.UserViewModel

class BackpackActivity : BaseCoreActivity<ActivityBackpackBinding, UserViewModel>() {

    override fun getLayoutId(): Int = R.layout.activity_backpack

    private var currentFilterIndex = 0
    override fun initView() {
        super.initView()
        val titles =
            arrayOf(getString(R.string.backpack_tabs_prop), getString(R.string.backpack_tabs_gift))
        mBinding.tab.setTitles(titles)
        initViewPager(titles)
        mBinding.tab.setViewPager(mBinding.viewPager)
    }

    private fun initViewPager(titles: Array<String>) {
        val fragments = buildList<Fragment> {
            titles.forEachIndexed { index, s ->
                add(ChildFragment.getInstance(index))
            }
        }
        mBinding.viewPager.attach(supportFragmentManager, lifecycle, fragments) { position ->
            mBinding.tab.setCurrentTab(position)
        }
        mBinding.viewPager.checkSelected(0)
    }

    override fun bindListener() {
        val options = arrayOf(
            getString(R.string.backpack_filter_options_all),
            getString(R.string.backpack_filter_options_available),
            getString(R.string.backpack_filter_options_expired)
        )
        mBinding.tvFilter.click {
            showActionSheetPopup(
                this, options.toList(), currentFilterIndex
            ) {
                currentFilterIndex = it
                mBinding.tvFilter.text = options[it]
            }
        }
    }

    class ChildFragment : RecyclerViewFragment() {
        private val viewModel by viewModels<UserViewModel>()

        companion object {
            fun getInstance(index: Int): ChildFragment {
                return ChildFragment().apply {
                    arguments = Bundle().putIndex(index)
                }
            }
        }

        override fun initData() {
            super.initData()
            mBinding.refreshLayout.setOnRefreshListener { b, i ->
                fetch(b, i)
            }
        }

        private fun fetch(refresh: Boolean, pageIndex: Int) {
            viewModel.fetchBackpackList().asLiveData().observe(this) {
                append<DslAdapterItem>(refresh, it) {
                    itemData = it
                    itemLayoutId = R.layout.item_backpack
                    itemBindOverride = { itemHolder, _, _, _ ->
                        val item = itemData as? BackpackBean
                        itemHolder.tv(R.id.tv_name)?.text = item?.name
                        itemHolder.img(R.id.iv_image)?.apply {
                            ImageLoader.with(this).load(item?.giftUrl).into(this)
                        }
                        itemHolder.tv(R.id.tv_time)?.text = item?.createTime
                        itemHolder.visible(R.id.tv_type, item?.status == 0)
                        itemHolder.visible(R.id.tv_user_service, item?.status == 1)
                    }
                }
            }
        }
    }
}