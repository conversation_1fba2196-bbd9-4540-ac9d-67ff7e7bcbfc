package com.heart.heartmerge.ui.activities.mine

import androidx.lifecycle.asLiveData
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jumpThenFinish
import com.bdc.android.library.utils.ActivityManager
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.ActivityCancelAccountResultBinding
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.popup.showConfirmPopup
import com.heart.heartmerge.ui.activities.login.LoginActivity
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.viewmodes.UserViewModel

class CancelAccountResultActivity : BaseCoreActivity<ActivityCancelAccountResultBinding, UserViewModel>() {
    override fun getLayoutId(): Int = R.layout.activity_cancel_account_result

    override fun bindListener() {
        mBinding.btnLogout.click {
            showConfirmPopup(this,
                title = getString(R.string.remind),
                content = getString(R.string.logout_tips),
                confirmBlock = {
                    mViewModel.logout(MMKVDataRep.userInfo.id).asLiveData().observe(this) {
                        LogX.i("logout suc")
                    }
                    AppUtil.clearCache()
                    jumpThenFinish(LoginActivity::class.java)
                    ActivityManager.clearAll()
                })
        }
    }
}