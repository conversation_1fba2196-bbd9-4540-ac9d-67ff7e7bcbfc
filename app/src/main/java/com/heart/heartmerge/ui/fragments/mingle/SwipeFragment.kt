package com.heart.heartmerge.ui.fragments.mingle

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.asLiveData
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import androidx.recyclerview.widget.SimpleItemAnimator
import coil.load
import coil.transform.CircleCropTransformation
import coil.transform.RoundedCornersTransformation
import com.bdc.android.library.base.fragment.BaseCoreFragment
import com.bdc.android.library.extension.click
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.databinding.FragmentSwipeBinding
import com.heart.heartmerge.ui.widget.slide.CardStackItemTouchHelper
import com.heart.heartmerge.ui.widget.slide.ItemTouchHelperCallback
import com.heart.heartmerge.ui.widget.slide.OnSlideListener
import com.heart.heartmerge.ui.widget.slide.SlideLayoutManager
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.viewmodes.MingleViewModel

class SwipeFragment : BaseCoreFragment<FragmentSwipeBinding, MingleViewModel>() {

    companion object {
        fun newInstance(): SwipeFragment {
            val fragment = SwipeFragment()
            val args = Bundle()
            fragment.arguments = args
            return fragment
        }
    }

    private var mItems = mutableListOf<UserBean>()
    private lateinit var mSlideLayoutManager: SlideLayoutManager
    private lateinit var mItemTouchHelper: CardStackItemTouchHelper
    private lateinit var mItemTouchHelperCallback: ItemTouchHelperCallback<UserBean>
    private var pageIndex = 1

    override fun getLayoutId(): Int = R.layout.fragment_swipe

    override fun initView() {
        super.initView()
        (mBinding.recyclerView.itemAnimator as SimpleItemAnimator).supportsChangeAnimations = false
        mBinding.recyclerView.adapter = SwipeAdapter(mItems)
        mItemTouchHelperCallback =
            ItemTouchHelperCallback<UserBean>(mBinding.recyclerView.adapter!!)
        mItemTouchHelper = CardStackItemTouchHelper(mItemTouchHelperCallback)
        mItemTouchHelper.attachToRecyclerView(mBinding.recyclerView)
        mSlideLayoutManager = SlideLayoutManager(mBinding.recyclerView, mItemTouchHelper)
        mBinding.recyclerView.setLayoutManager(mSlideLayoutManager)
        mItemTouchHelperCallback.setOnSlideListener(object : OnSlideListener<UserBean> {
            override fun onSliding(
                viewHolder: ViewHolder?, ratio: Float, direction: Int
            ) {
            }

            override fun onClear() {}

            @SuppressLint("NotifyDataSetChanged")
            override fun onSlided(
                viewHolder: ViewHolder, t: UserBean?, direction: Int
            ) {
                mItems.remove(t)
                mBinding.recyclerView.adapter?.notifyDataSetChanged()
                if (mItems.size <= 5) {
                    pageIndex++
                    fetchData(pageIndex)
                }
            }
        })
    }

    override fun initData() {
        fetchData(pageIndex)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun fetchData(pageIndex: Int) {
        mViewModel.fetchSwipeList(pageIndex).asLiveData().observe(this) {
            mItems.addAll(it)
            mItemTouchHelperCallback.addItems(mItems)
            mBinding.recyclerView.adapter?.notifyDataSetChanged()
        }
    }

    override fun bindListener() {
        mBinding.ivSwipeClose.click {
            initData()
            if (mItems.isEmpty()) return@click
            var itemPosition = 0
            itemPosition = if (mItems.size > 3) {
                3
            } else {
                mItems.size - 1
            }
            mItemTouchHelper.toLeft(
                mBinding.recyclerView.getChildViewHolder(
                    mBinding.recyclerView.getChildAt(itemPosition)
                )
            )
        }
        mBinding.ivSwipePhone.click {
            if (mItems.isEmpty()) return@click
            var itemPosition = 0
            itemPosition = if (mItems.size > 3) {
                3
            } else {
                mItems.size - 1
            }
            mItemTouchHelper.toRight(
                mBinding.recyclerView.getChildViewHolder(
                    mBinding.recyclerView.getChildAt(itemPosition)
                )
            )
        }
    }

    inner class SwipeAdapter(val items: List<UserBean>) : RecyclerView.Adapter<ViewHolder>() {
        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_swipe_child, parent, false)
            return ViewHolder(view)
        }

        override fun getItemCount(): Int = items.size

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            val item = items[position]
            item.anchorFileList?.forEach {
                if (it.fileType == Constants.ANCHOR_FILE_TYPE_PHOTO) {
                    holder.itemView.findViewById<ImageView>(R.id.iv_background)?.load(it.fileUrl) {
                        transformations(RoundedCornersTransformation(16f))
                    }
                    return@forEach
                }
            }
            holder.itemView.findViewById<ImageView>(R.id.iv_avatar)?.load(item.avatar) {
                transformations(CircleCropTransformation())
            }
            holder.itemView.findViewById<TextView>(R.id.tv_nickname)?.text = item.nickname
        }
    }
}