package com.heart.heartmerge.ui.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Typeface

import android.graphics.drawable.ColorDrawable
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.widget.TextView

class SideBar @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    interface OnTouchingLetterChangedListener {
        fun onTouchingLetterChanged(s: String)
    }

    companion object {
        val LETTERS = arrayOf(
            "A",
            "B",
            "C",
            "D",
            "E",
            "F",
            "G",
            "H",
            "I",
            "J",
            "K",
            "L",
            "M",
            "N",
            "O",
            "P",
            "Q",
            "R",
            "S",
            "T",
            "U",
            "V",
            "W",
            "X",
            "Y",
            "Z",
            "#"
        )
    }

    private var onTouchingLetterChangedListener: OnTouchingLetterChangedListener? = null
    private var choose = -1
    private val paint = Paint()
    private var mTextDialog: TextView? = null

    fun setTextView(textDialog: TextView) {
        this.mTextDialog = textDialog
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        val height = height
        val width = width
        val singleHeight = height / LETTERS.size

        for (i in LETTERS.indices) {
            paint.color = Color.parseColor("#999999")
            paint.typeface = Typeface.DEFAULT_BOLD
            paint.isAntiAlias = true
            paint.textSize = 30f
            if (i == choose) {
                paint.color = Color.parseColor("#EC12E2")
                paint.isFakeBoldText = true
            }
            val xPos = width / 2 - paint.measureText(LETTERS[i]) / 2
            val yPos = singleHeight * i + singleHeight.toFloat()
            canvas.drawText(LETTERS[i], xPos, yPos, paint)
            paint.reset()
        }
    }

    override fun dispatchTouchEvent(event: MotionEvent): Boolean {
        val action = event.action
        val y = event.y
        val oldChoose = choose
        val listener = onTouchingLetterChangedListener
        val c = (y / height * LETTERS.size).toInt()

        when (action) {
            MotionEvent.ACTION_UP -> {
                background = ColorDrawable(0x00000000)
                choose = -1
                invalidate()
                mTextDialog?.visibility = View.INVISIBLE
            }

            else -> {
//                setBackgroundResource(R.drawable.shape_sidebar) // 替换为你实际的 drawable 资源
                if (oldChoose != c) {
                    if (c in LETTERS.indices) {
                        listener?.onTouchingLetterChanged(LETTERS[c])
                        mTextDialog?.text = LETTERS[c]
                        mTextDialog?.visibility = View.VISIBLE
                        choose = c
                        invalidate()
                    }
                }
            }
        }
        return true
    }

    fun setOnTouchingLetterChangedListener(listener: OnTouchingLetterChangedListener) {
        this.onTouchingLetterChangedListener = listener
    }
}