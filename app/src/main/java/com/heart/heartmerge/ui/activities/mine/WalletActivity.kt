package com.heart.heartmerge.ui.activities.mine

import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import androidx.lifecycle.asLiveData
import androidx.lifecycle.lifecycleScope
import com.angcyo.dsladapter._dslAdapter
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.GoodsBean
import com.heart.heartmerge.beans.GoodsQueryType
import com.heart.heartmerge.databinding.ActivityWalletBinding
import com.heart.heartmerge.extension.applyGooglePrice
import com.heart.heartmerge.extension.applyRechargeCountdown
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond
import com.heart.heartmerge.payment.BillingService
import com.heart.heartmerge.popup.showChoosePaymentPopup
import com.heart.heartmerge.ui.activities.WebViewActivity
import com.heart.heartmerge.ui.activities.mine.MembershipCenterActivity.Companion.SCENE
import com.heart.heartmerge.ui.adapter.WalletDiamondRechargeAdapter
import com.heart.heartmerge.ui.theme.HeartMergeTheme
import com.heart.heartmerge.ui.theme.white
import com.heart.heartmerge.ui.widget.DiamondComposeView
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.FlexibleCountdownTimer
import com.heart.heartmerge.utils.PurchaseScene
import com.heart.heartmerge.viewmodes.UserViewModel
import com.lxj.xpopup.core.BottomPopupView.VISIBLE
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class WalletActivity : BaseCoreActivity<ActivityWalletBinding, UserViewModel>() {

    private val purchaseScene by lazy {
        intent.getParcelableExtra<PurchaseScene>(SCENE)
    }

    private var billingService: BillingService? = null
    private var flexibleCountdownTimer: FlexibleCountdownTimer? = null

    override fun getLayoutId(): Int = R.layout.activity_wallet

    override fun initView() {
        super.initView()
        billingService = BillingService(this)
        mBinding.tvDiamonds.setContent {
            HeartMergeTheme {
                DiamondComposeView(
                    color = white,
                    fontSize = 35.sp,
//                    fontFamily = FontFamily(Font(R.font.baloochettan2))
                )
            }
        }
//        buildAgreementText()
    }

    override fun bindListener() {
        mBinding.toolbar.setActionOnClickListener {
            jump(RechargeHistoryActivity::class.java)
        }
        mBinding.btnRecharge.click {
            val item =
                mBinding.recyclerView._dslAdapter?.dataItems?.find { it.itemIsSelected }?.itemData as? GoodsBean
            showChoosePaymentPopup(this, item, purchaseScene) {}
        }
        mBinding.refreshLayout.setOnRefreshListener {
            mViewModel.refreshUser()
            lifecycleScope.launch {
                delay(350)
                mBinding.refreshLayout.onFinishRefresh()
            }
        }
    }

    private fun buildAgreementText() {
        val agreementText = getString(R.string.recharge_agreement_hint)
        val policyText = getString(R.string.recharge_agreement)
        val fullText = agreementText + policyText
        val spannableString = SpannableString(fullText)

        fun setClickableSpan(
            spannableString: SpannableString,
            fullText: String,
            clickableText: String,
            onClick: () -> Unit
        ) {
            val startIndex = fullText.indexOf(clickableText)
            val endIndex = startIndex + clickableText.length

            spannableString.setSpan(
                object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        onClick()
                    }

                    override fun updateDrawState(ds: TextPaint) {
                        super.updateDrawState(ds)
                        ds.color = ContextCompat.getColor(
                            this@WalletActivity, R.color.color_FF69C0
                        ) // 设置链接颜色
                        ds.isUnderlineText = false
                    }
                }, startIndex, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        setClickableSpan(spannableString, fullText, policyText) {
            WebViewActivity.jump(
                this, getString(R.string.recharge_agreement), Constants.Agreement.RECHARGE_URL
            )
        }

        mBinding.tvAgreement.text = spannableString
        mBinding.tvAgreement.movementMethod = LinkMovementMethod.getInstance()
    }

    override fun initData() {
        super.initData()
        mBinding.progressBar.apply {
            visibility = VISIBLE
            isIndeterminate = true
        }
        mViewModel.fetchGoodsList(goodsType = GoodsQueryType.DIAMOND).asLiveData()
            .observe(this) { result ->
                val firstRechargeList = result?.goldGoods?.filter { it.hasCountdown }
                val list = buildList {
                    val current =
                        firstRechargeList?.filter { it.hasCountdown }?.minByOrNull { it.deadline }
                    current?.let { add(it) }
                    result?.goldGoods?.let {
                        addAll(it.filter { !it.hasCountdown })
                    }
                }

                lifecycleScope.launch {
                    billingService?.let {
                        firstRechargeList?.applyGooglePrice(it)
                        list.applyGooglePrice(it)
                    }
                    mBinding.progressBar.makeGone()
                    mBinding.refreshLayout.onFinishRefresh()

                    list.map {
                        WalletDiamondRechargeAdapter.DiamondRechargeItem(
                            it, purchaseScene, result?.level_config?.level ?: 1
                        )
                    }.let {
                        mBinding.recyclerView.adapter = WalletDiamondRechargeAdapter(it).apply {
                            itemSelectorHelper.selector(0)
                        }
                    }

                    buildFirstRechargeText()

                    if (firstRechargeList?.isNotEmpty() == true) {
                        flexibleCountdownTimer?.cancel()
                        flexibleCountdownTimer =
                            mBinding.recyclerView.applyRechargeCountdown(firstRechargeList) {
                                buildFirstRechargeText()
                            }
                    }
                }
            }
    }

    private fun buildFirstRechargeText() {
        val items =
            mBinding.recyclerView._dslAdapter?.dataItems?.mapNotNull { (it.itemData as? GoodsBean)?.takeIf { bean -> bean.hasCountdown } }
        mBinding.llFirstRechargeContainer.makeVisible(items?.isNotEmpty() == true)
        items?.takeIf { beans -> beans.isNotEmpty() && beans.size > 0 }?.let { beans ->
            beans.first()
        }?.apply {
            mBinding.tvFirstRecharge.text = getString(
                R.string.first_recharge_text_tips,
                googleExtras?.oneTimePurchaseOfferDetails?.formattedPrice ?: formattedPrice,
                coin.toInt().toShowDiamond()
            )
        }
    }

    override fun onResume() {
        super.onResume()
        AppUtil.canAutoPopupVideoCallingPage = false
    }

    override fun onDestroy() {
        super.onDestroy()
        AppUtil.canAutoPopupVideoCallingPage = true
        flexibleCountdownTimer?.cancel()
        billingService?.onDestroy()
    }
}