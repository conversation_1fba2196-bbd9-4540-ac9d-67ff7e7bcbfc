package com.heart.heartmerge.ui.activities.anchor

import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.RateLabelList
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.popup.VideoRatePopup
import com.heart.heartmerge.popup.showVideoRatePopup
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.fromJson
import com.heart.heartmerge.viewmodes.AnchorViewModel
import com.heart.heartmerge.viewmodes.SearchRequestEvent

class AnchorRateStarActivity : AppCompatActivity() {
    private val anchorInfo: UserBean?
        get() = intent.getStringExtra(Constants.INTENT_PARAM_KEY_ANCHOR_INFO)?.fromJson<UserBean>()
    private val channelId: String?
        get() = intent.getStringExtra(Constants.INTENT_PARAM_KEY_CHANNEL_ID)

    private var rateLabels = mutableListOf<RateLabelList>()
    private var popup: VideoRatePopup? = null
    private val mViewModel: AnchorViewModel by viewModels()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_anchor_rate_star)
        initViewEvents()
        initData()
    }

    fun initData() {
        anchorInfo?.let {
            popup = showVideoRatePopup(this, it, mutableListOf()) { selRate, selLabelIds ->
                channelId?.let { it1 ->
                    mViewModel.anchorRate(
                        selRate,
                        it1, selLabelIds
                    )
                }
            }
        }

        mViewModel.getRateLabels()

    }

    fun initViewEvents() {
        mViewModel.pageEvents.observeEvent(this) {
            when (it) {
                is SearchRequestEvent.GetRateLabelsSuccess -> {
                    rateLabels = it.rateLabelBeans
                    popup?.refreshLabels(rateLabels)
                }


                is SearchRequestEvent.AnchorRateFailed -> {
                    ToastUtil.show(it.msg)
                    LogX.e("AnchorRateFailed", it.msg)
                    finish()
                }

                is SearchRequestEvent.AnchorRateSuccess -> {
                    ToastUtil.show(getString(R.string.rate_suc))
                    popup?.dismissWith {
                        finish()
                    }
                }

                else -> {}
            }
        }
    }
}