package com.heart.heartmerge.ui.activities.mine

import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.ActivityRaffleBinding
import com.heart.heartmerge.popup.raffle.RaffleRulesDialog
import com.heart.heartmerge.popup.raffle.LotteryTimesPurchaseDialog
import com.heart.heartmerge.viewmodes.BaseViewModel

class RaffleActivity : BaseCoreActivity<ActivityRaffleBinding, BaseViewModel>() {

    override fun getLayoutId(): Int = R.layout.activity_raffle

    override fun isImmerse(): Boolean = true

    override fun getNavigationBarColor(): Int = R.color.color_E83B35

    override fun initView() {
        setToolbarTitle("幸运大转盘")
    }

    override fun bindListener() {
        mBinding.btnPurchase.click {
            val dialog = LotteryTimesPurchaseDialog()
            dialog.show(supportFragmentManager, "dialog")
        }

        mBinding.btnRules.click {
            val dialog = RaffleRulesDialog()
            dialog.show(supportFragmentManager, "dialog")
        }

        mBinding.btnRecord.click {
            jump(WinningRecordActivity::class.java)
        }
    }

//    override fun onCreate(savedInstanceState: Bundle?) {
//        super.onCreate(savedInstanceState)
//        enableEdgeToEdge()
//        setContentView(R.layout.activity_main)
////        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
////            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
////            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
////            insets
////        }
//
//        findViewById<View>(R.id.btn_purchase).setOnClickListener {
//            val dialog = LotteryTimesPurchaseDialog()
//            dialog.show(supportFragmentManager, "dialog")
//        }
//
//        findViewById<View>(R.id.btn_rules).setOnClickListener {
//            val dialog = LotteryRulesDialog()
//            dialog.show(supportFragmentManager, "dialog")
//        }
//
//        findViewById<View>(R.id.btn_record).setOnClickListener {
//           startActivity(Intent(this, WinningRecordActivity::class.java))
//        }
//    }
}