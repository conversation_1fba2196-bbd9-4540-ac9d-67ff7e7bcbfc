package com.heart.heartmerge.ui.activities

import android.os.Bundle
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jumpThenFinish
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.mvi.observeEvent
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.databinding.ActivityWhoSeeMeBinding
import com.heart.heartmerge.extension.buildImageUrl
import com.heart.heartmerge.extension.loadAvatar
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.ui.activities.mine.MembershipCenterActivity
import com.heart.heartmerge.ui.fragments.message.FollowFansItem
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.PurchaseScene
import com.heart.heartmerge.viewmodes.SettingRequestEvent
import com.heart.heartmerge.viewmodes.UserViewModel

class WhoSeeMeActivity : BaseCoreActivity<ActivityWhoSeeMeBinding, UserViewModel>() {
    override fun getLayoutId(): Int = R.layout.activity_who_see_me
    override fun initData() {
        FlowBus.with<String>(Constants.PUSH_TYPE_BLACK_ANCHOR).register(this) {
            mBinding.refreshLayout.recyclerView.dslAdapter.apply {
                dataItems.forEach { dslAdapterItem ->
                    val item = dslAdapterItem as FollowFansItem
                    if (item.anchorInfo?.anchorId == it) {
                        mBinding.refreshLayout.recyclerView.dslAdapter.removeItem(dslAdapterItem)
                        updateItemDepend()
                        return@forEach
                    }
                }
            }
        }
        mBinding.ivMyAvatar.loadAvatar(MMKVDataRep.userInfo.avatar.buildImageUrl())
        mBinding.refreshLayout.onRefresh()
    }

    override fun initView() {
        if (MMKVDataRep.userInfo.isVIP) {
            mBinding.clVipTip.makeGone()
            mBinding.cropImage.makeGone()
        }else {
            mBinding.clVipTip.makeVisible()
            mBinding.cropImage.makeVisible()
        }
        mBinding.refreshLayout.recyclerView.apply {
            setOnRefreshListener { _: Boolean, _: Int ->
                mViewModel.getWhoSeeMe()
            }
        }

        mBinding.clVipTip.click {
            jumpThenFinish(MembershipCenterActivity::class.java, Bundle().apply {
                putParcelable(MembershipCenterActivity.SCENE, PurchaseScene.WhoLikeMe)
            })
        }
        mBinding.cropImage.click {
            jumpThenFinish(MembershipCenterActivity::class.java, Bundle().apply {
                putParcelable(MembershipCenterActivity.SCENE, PurchaseScene.WhoLikeMe)
            })
        }
    }

    override fun initViewEvents() {
        mViewModel.pageEvents.observeEvent(this) {
            when (it) {
                is SettingRequestEvent.GetWhoSeeMeSuccess -> {
                    mBinding.refreshLayout.apply {
                        clearAllItems()
                        append<FollowFansItem>(items = it.users) { data ->
                            anchorInfo = data as UserBean
                        }
                    }
                }

                else -> {}
            }
        }
    }
}