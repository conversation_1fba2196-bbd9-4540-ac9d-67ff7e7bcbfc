package com.heart.heartmerge.ui.activities.mine

import android.graphics.Color
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.click
import com.google.android.material.tabs.TabLayout
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.ActivityWinningRecordBinding
import com.heart.heartmerge.popup.BottomActionSheetDialog
import com.heart.heartmerge.popup.BottomActionSheetItem
import com.heart.heartmerge.popup.raffle.RaffleRewardUseDialog
import com.heart.heartmerge.viewmodes.BaseViewModel

data class LotteryRecordItem(
    val name: String, val duration: Int, val status: Int = 0, var createTime: Long
)

class WinningRecordActivity : BaseCoreActivity<ActivityWinningRecordBinding, BaseViewModel>() {

    override fun getLayoutId(): Int = R.layout.activity_winning_record

    override fun initView() {
        super.initView()
        setToolbarTitle("抽奖记录")
        initialTab()
        initialRecyclerView()
    }

    override fun bindListener() {
        super.bindListener()
        mBinding.tvFilter.click {
            val dialog = BottomActionSheetDialog(buildList {
                add("全部")
                add("已使用")
                add("未使用")
            }.map { BottomActionSheetItem(it) },
                listener = object : BottomActionSheetDialog.BottomSheetAdapter.OnItemClickListener {
                    override fun onItemClick(position: Int) {

                    }
                })
            dialog.show(supportFragmentManager, "dialog")
        }
    }

    private fun initialTab() {
        val tabLayout = findViewById<TabLayout>(R.id.tabLayout)
        val tabs = arrayOf("全部", "已中奖", "未中奖")

        for (tab in tabs) {
            tabLayout.newTab().apply {
                customView = LayoutInflater.from(this@WinningRecordActivity)
                    .inflate(R.layout.item_custom_tab_view, null)
                customView?.findViewById<TextView>(R.id.tab)?.text = tab
                tabLayout.addTab(this)
            }
        }

        tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.customView?.apply {
                    setBackgroundResource(R.drawable.bg_tablayout_item_shape)
                    findViewById<TextView>(R.id.tab).setTextColor(Color.WHITE)
                }
                findViewById<View>(R.id.tv_filter).visibility =
                    if (tab?.position == 0) View.GONE else View.VISIBLE
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                tab?.customView?.apply {
                    setBackgroundResource(0)
                    findViewById<TextView>(R.id.tab).setTextColor(Color.parseColor("#81839a"))
                }
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
                tab?.customView?.apply {
                    setBackgroundResource(R.drawable.bg_tablayout_item_shape)
                    findViewById<TextView>(R.id.tab).setTextColor(Color.WHITE)
                }
            }
        })

        tabLayout.getTabAt(0)?.select()
    }

    private fun initialRecyclerView() {
        val items = listOf(
            LotteryRecordItem(
                name = "对话框", duration = 0, createTime = System.currentTimeMillis()
            ),
            LotteryRecordItem(
                name = "对话框", duration = 0, createTime = System.currentTimeMillis()
            ),
            LotteryRecordItem(
                name = "动态特效礼物",
                duration = 1,
                status = 1,
                createTime = System.currentTimeMillis()
            ),
            LotteryRecordItem(
                name = "谢谢惠顾", duration = -1, createTime = System.currentTimeMillis()
            ),
        )

        findViewById<RecyclerView>(R.id.recyclerView).apply {
            adapter =
                WinningRecordAdapter(items, object : WinningRecordAdapter.OnItemUseClickListener {
                    override fun onItemClick(position: Int) {
                        val dialog = RaffleRewardUseDialog()
                        dialog.show(supportFragmentManager, "dialog")
                    }
                })
        }
    }

    class WinningRecordAdapter(
        private val items: List<LotteryRecordItem>, private val listener: OnItemUseClickListener
    ) : RecyclerView.Adapter<WinningRecordAdapter.ViewHolder>() {

        class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

        interface OnItemUseClickListener {
            fun onItemClick(position: Int)
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_raffle_winning_record, parent, false)
            return ViewHolder(view)
        }

        override fun getItemCount(): Int {
            return items.size
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val item = items[position]
            holder.itemView.findViewById<TextView>(R.id.tv_reward_name).text = item.name
            holder.itemView.findViewById<TextView>(R.id.tv_duration).apply {
                visibility = if (item.duration < 0) {
                    View.GONE
                } else {
                    View.VISIBLE
                }
                val prefix = "有效时长："
                val duration = if (item.duration == 0) "终身有效" else "${item.duration}个月"
                val formattedDuration = SpannableString("$prefix$duration")
                formattedDuration.setSpan(
                    ForegroundColorSpan(Color.parseColor("#fdaa49")),
                    prefix.length,
                    prefix.length + duration.length,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
                text = formattedDuration
            }
            holder.itemView.findViewById<TextView>(R.id.tv_lottery_time).text =
                "抽奖时间：2024-11-12 19:10"
            holder.itemView.findViewById<View>(R.id.iv_status).apply {
                visibility = if (item.status == 1) {
                    View.VISIBLE
                } else {
                    View.GONE
                }
            }
            holder.itemView.findViewById<View>(R.id.tv_use).apply {
                visibility = if (item.status == 0) {
                    View.VISIBLE
                } else {
                    View.GONE
                }
                setOnClickListener { listener.onItemClick(position) }
            }

        }
    }
}