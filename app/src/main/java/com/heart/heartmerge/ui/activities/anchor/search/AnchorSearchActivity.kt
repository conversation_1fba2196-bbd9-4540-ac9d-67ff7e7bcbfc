package com.heart.heartmerge.ui.activities.anchor.search

import android.graphics.Bitmap
import android.graphics.drawable.BitmapDrawable
import android.view.View
import android.view.inputmethod.EditorInfo
import androidx.core.content.ContextCompat
import androidx.core.widget.doAfterTextChanged
import com.angcyo.dsladapter.DslLoadMoreItem.Companion.LOAD_MORE_ERROR
import com.angcyo.dsladapter.DslLoadMoreItem.Companion.LOAD_MORE_NORMAL
import com.angcyo.dsladapter.DslLoadMoreItem.Companion.LOAD_MORE_NO_MORE
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.extension.click
import com.bdc.android.library.flowlayout.FlowLayout
import com.bdc.android.library.flowlayout.FlowLayoutAdapter
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.BuildConfig
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.databinding.ActivitySearchBinding
import com.heart.heartmerge.extension.openLoadMore
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.popup.showConfirmPopup
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.viewmodes.AnchorViewModel
import com.heart.heartmerge.viewmodes.SearchRequestEvent


/**
 * Author:Lxf
 * Create on:2024/8/12
 * Description:
 */
class AnchorSearchActivity : BaseCoreActivity<ActivitySearchBinding, AnchorViewModel>() {
    override fun getLayoutId(): Int = R.layout.activity_search
    private var loadPage = 1
    private val pageSize = 20
    private var mCountId = ""
    private var historySet: MutableSet<String> = mutableSetOf()
    private lateinit var historyFlowAdapter: SearchHistoryFlowAdapter
    private var mSearchKeyWord: String = ""
    override fun initView() {
        if (!BuildConfig.DEBUG) {
            AppUtil.screenSecure(window)
        }
        mBinding.topView.apply {
            val drawable = ContextCompat.getDrawable(this@AnchorSearchActivity, R.mipmap.ic_search)
            val bitmap = (drawable as BitmapDrawable).bitmap
            val scaledDrawable =
                BitmapDrawable(resources, Bitmap.createScaledBitmap(bitmap, 48, 48, true))
            if (AppUtil.isRtl()) {
                setCompoundDrawablesWithIntrinsicBounds(null, null, scaledDrawable, null)
            } else {
                setCompoundDrawablesWithIntrinsicBounds(scaledDrawable, null, null, null)
            }

            doAfterTextChanged {
                if (it.isNullOrEmpty()) {
                    mBinding.messageList.visibility = View.GONE
                    mBinding.delHistory.visibility = View.VISIBLE
                    mBinding.tvHistoryTitle.visibility = View.VISIBLE
                    mBinding.flowLayout.visibility = View.VISIBLE
                }
            }

            setOnEditorActionListener { _, i, _ ->
                when (i) {
                    EditorInfo.IME_ACTION_SEARCH -> doSearch()
                    else -> {}
                }
                false
            }
        }
        mBinding.btnSearch.click {
            doSearch()
        }
        mBinding.delHistory.click {
            showConfirmPopup(
                this, content = getString(R.string.tip_clear_search_history), confirmBlock = {
                    historySet.clear()
                    MMKVDataRep.searchHistory = historySet
                    historyFlowAdapter.setNewData(historySet)
                })
        }
        mBinding.back.click {
            finish()
        }
        mBinding.flowLayout.setOnItemClickListener(object : FlowLayout.OnItemClickListener {
            override fun onItemClick(
                flowLayout: FlowLayout?, adapter: FlowLayoutAdapter?, rowNumber: Int, position: Int
            ) {
                adapter?.apply {
                    val item = getItem(position)
                    item?.let {
                        val inputStr = it as String
                        mSearchKeyWord = inputStr
                        mBinding.topView.setText(inputStr)
                        gotoSearchAnchor(inputStr)
                    }
                }
            }
        })

        mBinding.messageList.openLoadMore {
            loadPage++
            mViewModel.searchAnchor(loadPage, pageSize, mCountId, mSearchKeyWord)
        }
    }

    private fun doSearch() {
        mBinding.topView.text.let {
            if (it.isNullOrEmpty()) {
                ToastUtil.show(getString(R.string.tip_input_anchor_name))
            } else {
                it.toString().let { inputStr ->
                    mSearchKeyWord = inputStr
                    historySet.add(inputStr)
                    historyFlowAdapter.addData(inputStr)
                    //缓存历史记录
                    MMKVDataRep.searchHistory = historySet
                    gotoSearchAnchor(inputStr)
                }
            }
        }
    }

    override fun initData() {
        historyFlowAdapter = SearchHistoryFlowAdapter()
        mBinding.flowLayout.setAdapter(historyFlowAdapter)
        MMKVDataRep.searchHistory?.let {
            historySet.addAll(it)
            historyFlowAdapter.addData(it)
        }

        FlowBus.with<String>(Constants.PUSH_TYPE_BLACK_ANCHOR).register(this) {
            mBinding.messageList.dslAdapter.apply {
                dataItems.forEach { dslAdapterItem ->
                    val item = dslAdapterItem as SearchAnchorItem
                    if (item.anchorInfo?.id == it) {
                        mBinding.messageList.dslAdapter.removeItem(dslAdapterItem)
                        updateItemDepend()
                        return@forEach
                    }
                }
            }
        }
    }

    private fun gotoSearchAnchor(inputStr: String) {
        loadPage = 1
        mBinding.messageList.clearAllItems()
        mBinding.messageList.visibility = View.VISIBLE
        mBinding.delHistory.visibility = View.GONE
        mBinding.tvHistoryTitle.visibility = View.GONE
        mBinding.flowLayout.visibility = View.GONE
        mViewModel.searchAnchor(loadPage, pageSize, mCountId, inputStr)
    }

    override fun initViewEvents() {
        mViewModel.pageEvents.observeEvent(this) {
            when (it) {
                is SearchRequestEvent.AnchorSearchSuccess -> {
                    mBinding.messageList.visibility = View.VISIBLE
                    mBinding.delHistory.visibility = View.GONE
                    mBinding.tvHistoryTitle.visibility = View.GONE
                    if (loadPage == 1) {
                        mBinding.messageList.clearAllItems()
                    }
                    mBinding.messageList.apply {
                        append<SearchAnchorItem>(it.list) { data ->
                            anchorInfo = data as UserBean
                        }
                        if (it.list.isNullOrEmpty()) {
                            dslAdapter.setLoadMore(LOAD_MORE_NO_MORE)
                        } else {
                            dslAdapter.setLoadMore(LOAD_MORE_NORMAL)
                        }
                    }
                }

                is SearchRequestEvent.AnchorSearchFailed -> {
                    ToastUtil.show(it.msg)
                    val tmpPage = loadPage--
                    loadPage = if (tmpPage >= 1) tmpPage else 1
                    mBinding.messageList.dslAdapter.setLoadMore(
                        LOAD_MORE_ERROR
                    )
                }

                else -> {}
            }
        }
    }
}