package com.heart.heartmerge.ui.activities.mine

import com.bdc.android.library.base.activity.BaseCoreActivity
import com.heart.heartmerge.BuildConfig
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.ActivityAboutBinding
import com.heart.heartmerge.viewmodes.UserViewModel

class AboutActivity : BaseCoreActivity<ActivityAboutBinding, UserViewModel>() {

    override fun getLayoutId(): Int = R.layout.activity_about

    override fun initData() {
        super.initData()
        mBinding.tvVer.text = getString(R.string.current_ver, BuildConfig.VERSION_NAME)
    }
}