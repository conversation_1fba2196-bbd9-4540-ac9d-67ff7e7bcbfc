package com.heart.heartmerge.ui.activities

import android.content.Intent
import android.os.Build
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.splashscreen.SplashScreen
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import com.bdc.android.library.extension.jumpThenFinish
import com.heart.heartmerge.mmkv.MMKVBaseDataRep
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.utils.AppUtil

/**
 * 作者：Lxf
 * 创建日期：2024/8/12 14:58
 * 描述：
 */
class WelcomeActivity : AppCompatActivity() {
    var splashScreen: SplashScreen? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            splashScreen = installSplashScreen()
            splashScreen?.setKeepOnScreenCondition { true }
        }
//        val splashScreen = installSplashScreen()
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
//            // Disable the Android splash screen fade out animation to avoid
//            // a flicker before the similar frame is drawn in Flutter.
//            splashScreen.setOnExitAnimationListener { splashScreenView -> splashScreenView.remove() }
//        }
        super.onCreate(savedInstanceState)

        gotoActivity()
    }

    private fun gotoActivity() {
        if (MMKVBaseDataRep.token.isNullOrEmpty() || MMKVDataRep.userInfo.id.isEmpty() || MMKVDataRep.userInfo.reLogin) {
            AppUtil.forceReLogin(this)
        } else {
            // DeepLink 数据
            val deepLinkUri = intent?.data
            jumpThenFinish(Intent(this, MainActivity::class.java).apply {
                data = deepLinkUri
            })
        }
    }
}
