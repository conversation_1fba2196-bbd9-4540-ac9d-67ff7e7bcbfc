package com.heart.heartmerge.ui.activities.anchor.detail

import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.AnchorFileBean
import com.heart.heartmerge.extension.buildImageUrl
import com.heart.heartmerge.extension.loadAnchorImage
import com.heart.heartmerge.extension.loadAnchorImageWithBlur
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.popup.showMembershipSubscribePopup
import com.heart.heartmerge.utils.PurchaseScene
import com.zhpan.bannerview.BaseBannerAdapter
import com.zhpan.bannerview.BaseViewHolder

/**
 * Author:Lxf
 * Create on:2024/11/7
 * Description:
 */
class ImageBannerAdapter : BaseBannerAdapter<AnchorFileBean>() {
    override fun bindData(
        holder: BaseViewHolder<AnchorFileBean>,
        data: AnchorFileBean,
        position: Int,
        pageSize: Int
    ) {
        val image = holder.findViewById<ImageView>(R.id.banner_image)
        val unlock = holder.findViewById<TextView>(R.id.tv_vip_unlock)
        image.loadAnchorImage(data.fileUrl.buildImageUrl())
        data.apply {
            val isVip = MMKVDataRep.userInfo.isVIP
            unlock.click {
                showMembershipSubscribePopup(
                    holder.itemView.context as AppCompatActivity,
                    PurchaseScene.CoverUnlock(anchorId)
                )
            }

            if (!isVip && isLock == "2") {
                unlock.makeVisible()
                image.loadAnchorImageWithBlur(fileUrl.buildImageUrl())
            } else {
                unlock.makeGone()
                image.loadAnchorImage(fileUrl.buildImageUrl(highQuality = true), 8f)
            }
        }
    }

    override fun getLayoutId(viewType: Int): Int {
        return R.layout.detail_banner_image_item
    }
}