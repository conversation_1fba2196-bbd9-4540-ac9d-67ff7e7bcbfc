package com.heart.heartmerge.ui.fragments.mingle

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.app.Activity
import android.graphics.Paint
import android.os.Bundle
import android.text.SpannableString
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.view.ViewGroup
import android.widget.ImageView
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.lifecycleScope
import com.angcyo.dsladapter.className
import com.bdc.android.library.base.fragment.BaseCoreFragment
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.extension.performSimulatedClick
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.utils.ActivityManager
import com.bdc.android.library.utils.DisplayUtil
import com.bdc.android.library.utils.SpannableUtil
import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.RemoteUserCleanMatchExtend
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.databinding.FragmentMatchBinding
import com.heart.heartmerge.extension.formatCountdown
import com.heart.heartmerge.extension.formatDate
import com.heart.heartmerge.firebase.report.DT_EVENT_CONSTANTS
import com.heart.heartmerge.firebase.report.ReportManager
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.popup.showDiamondRechargePopup
import com.heart.heartmerge.popup.showMatchChanceCountdownPopup
import com.heart.heartmerge.popup.showMatchChancePopup
import com.heart.heartmerge.ui.activities.anchor.AnchorVideoActivity
import com.heart.heartmerge.ui.activities.mine.MembershipCenterActivity
import com.heart.heartmerge.ui.activities.mine.WalletActivity
import com.heart.heartmerge.ui.theme.HeartMergeTheme
import com.heart.heartmerge.ui.theme.white
import com.heart.heartmerge.ui.widget.DiamondComposeView
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.FlexibleCountdownTimer
import com.heart.heartmerge.utils.PurchaseScene
import com.heart.heartmerge.utils.toJson
import com.heart.heartmerge.viewmodes.MatchRequestEvent
import com.heart.heartmerge.viewmodes.MingleViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class MatchFragment : BaseCoreFragment<FragmentMatchBinding, MingleViewModel>() {

    private var matchCardCount = 0
    private val animatorSetList = mutableListOf<AnimatorSet>()
    private var animationJob: Job? = null
    private var randomAnimator: ValueAnimator? = null

    private val images = listOf(
        R.mipmap.random_header1,
        R.mipmap.random_header2,
        R.mipmap.random_header3,
        R.mipmap.random_header4,
        R.mipmap.random_header5,
        R.mipmap.random_header6,
    )

    companion object {
        fun newInstance(): MatchFragment {
            val fragment = MatchFragment()
            val args = Bundle()
            fragment.arguments = args
            return fragment
        }
    }

    private lateinit var flexibleCountdownTimer: FlexibleCountdownTimer

    private var currentMatchObject: UserBean? = null

    override fun getLayoutId(): Int = R.layout.fragment_match

    override fun initView() {
        mBinding.tvMatchBalance.setContent {
            HeartMergeTheme {
                DiamondComposeView(
                    color = white,
                    fontFamily = FontFamily(
                        Font(R.font.baloochettan2regular),
                    ),
                )
            }
        }

        runCatching {
            if (!DisplayUtil.isLongScreen(requireContext())) {
                val screenHeight = DisplayUtil.getScreenHeight(requireContext())
                val screenWidth = DisplayUtil.getScreenWidth(requireContext())
                val marginTop = (screenHeight * 0.05).toInt() // 5% 的屏幕高度
                mBinding.centerContainer.updateLayoutParams<ViewGroup.MarginLayoutParams> {
                    topMargin = marginTop
                }
                mBinding.sizeLayout.updateLayoutParams {
                    width = (screenWidth * 0.83).toInt()
                    height = (screenWidth * 0.83).toInt()
                }
            }
        }

        FlowBus.with<RemoteUserCleanMatchExtend>(Constants.PushCmd.PUSH_CMD_REMOTE_USER_CLEAN_MATCH)
            .register(this) {
                MMKVDataRep.userConfig = MMKVDataRep.userConfig?.copy(
                    match_times = it.times, match_recover_duration = it.matchRecoverDuration
                )
//                userViewModel.refreshUser()
                checkFreeMatchChance(false)
            }
    }

    override fun initData() {
        lifecycleScope.launch {
//            userViewModel.userBean.collect {
//                checkFreeMatchChance(false)
//            }
            delay(2000)
            startSlotAnimation()
        }
    }

    override fun onResume() {
        super.onResume()
        if (userVisibleHint) {
            getMatchingCardCount()
            checkFreeMatchChance(false) {
                if (it) {
                    mBinding.btnMatch.performSimulatedClick()
                }
            }
        }
    }

    private fun getMatchingCardCount() {
        fun update() {
            matchCardCount = MMKVDataRep.userConfig?.match_times ?: 0
            mBinding.llVipPriceLabel?.apply {
//                makeVisible(!MMKVDataRep.userInfo.isVIP)
//                mBinding.tvVipPrice.text = String.format(
//                    getString(R.string.label_match_every_time),
//                    MMKVDataRep.userConfig?.vip_match_price?.toShowDiamond()
//                )
//                click {
//                    jump(MembershipCenterActivity::class.java, Bundle().apply {
//                        putParcelable(
//                            MembershipCenterActivity.SCENE,
//                            PurchaseScene.Match(anchorId = "", videoId = 0L)
//                        )
//                    })
//                }
            }
            if (matchCardCount > 0) {
                mBinding.icDiamond.makeGone()
                mBinding.tvMatchPrice.text = getString(R.string.label_match_card)
            } else {
                mBinding.icDiamond.makeVisible()
                if (MMKVDataRep.userInfo.isVIP) {
                    mBinding.tvMatchPrice.text = getString(
                        R.string.match_price,
                        MMKVDataRep.userConfig?.vip_match_price?.toShowDiamond()
                    )
                } else {
                    mBinding.tvMatchPrice.text = getString(
                        R.string.match_price, MMKVDataRep.userConfig?.match_price?.toShowDiamond()
                    )
                    mBinding.tvVipPrice.text = String.format(
                        getString(R.string.label_match_every_time),
                        MMKVDataRep.userConfig?.vip_match_price?.toShowDiamond()
                    )
                }
                mBinding.tvPriceOrg.makeVisible(MMKVDataRep.userInfo.isVIP)
                mBinding.tvPriceOrg.paintFlags =
                    mBinding.tvPriceOrg.paintFlags or Paint.STRIKE_THRU_TEXT_FLAG
                mBinding.tvPriceOrg.text = getString(
                    R.string.match_price, MMKVDataRep.userConfig?.match_price?.toShowDiamond()
                )
            }
        }

        update()
    }

    private fun checkFreeMatchChance(
        showCountdownPopup: Boolean = true, block: (Boolean/*是否自动执行匹配操作*/) -> Unit = {}
    ) {
        MMKVDataRep.userConfig?.let {
            //异常情况处理
            if (it.match_times <= 0 && it.match_recover_duration <= 0) {
                mBinding.tvMatchCardCountdown.makeGone()
                if (::flexibleCountdownTimer.isInitialized) {
                    flexibleCountdownTimer.cancel()
                }

                block.invoke(false)
                return
            }
            if (it.match_times > 0 && userVisibleHint) {
                if (TextUtils.equals(
                        MMKVDataRep.freeRandomMatchPopupShowDate,
                        System.currentTimeMillis().formatDate()
                    )
                ) {
                    block.invoke(false)
                    return
                }

                showMatchChancePopup(requireActivity(), MMKVDataRep.userConfig?.match_times ?: 0) {
                    //开始匹配
                    block.invoke(true)
                }
            }
            mBinding.tvMatchCardCountdown.makeVisible(it.match_times <= 0 && it.match_recover_duration > System.currentTimeMillis())
            if (it.match_recover_duration <= 0 && ::flexibleCountdownTimer.isInitialized) {
                flexibleCountdownTimer.cancel()
            }

            if (mBinding.tvMatchCardCountdown.isVisible && it.match_recover_duration > System.currentTimeMillis() && userVisibleHint) {
                if (!::flexibleCountdownTimer.isInitialized) {
                    val colorSpan = ForegroundColorSpan(
                        ContextCompat.getColor(
                            requireActivity(), R.color.color_F53D3D
                        )
                    )
                    flexibleCountdownTimer =
                        FlexibleCountdownTimer(
                            (MMKVDataRep.userConfig?.match_recover_duration
                                ?: 0) - System.currentTimeMillis(), onTick = { t ->
                                if (MMKVDataRep.userInfo.diamond > 0) {
                                    flexibleCountdownTimer.cancel()
                                    mBinding.tvMatchCardCountdown.makeGone()
                                    return@FlexibleCountdownTimer
                                }
                                val countdown = t.formatCountdown()
                                val content = getString(
                                    R.string.free_match_countdown, countdown
                                )
                                mBinding.tvMatchCardCountdown.text =
                                    SpannableUtil(requireContext(), content).apply {
                                        val start = this.indexOf("${countdown}")
                                        val end = start + countdown.toString().length
                                        setSpan(
                                            colorSpan,
                                            start,
                                            end,
                                            SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE
                                        )
                                    }
                            }, onFinish = {
                                mBinding.tvMatchCardCountdown.makeGone()
                                checkFreeMatchChance(false) {
                                    if (it) {
                                        mBinding.btnMatch.performSimulatedClick()
                                    }
                                }
                            })
                    flexibleCountdownTimer.start()
                }
                if (showCountdownPopup) {
                    showMatchChanceCountdownPopup(
                        requireActivity(), MMKVDataRep.userConfig?.match_recover_duration ?: 0
                    ) {
                        showDiamondRechargePopup(
                            requireActivity(), purchaseScene = PurchaseScene.Match(
                                anchorId = currentMatchObject?.id ?: "",
                                videoId = currentMatchObject?.virVideoId
                            )
                        )
                    }
                }
            }
        } ?: run {
            //异常情况处理
            block.invoke(false)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        stopAnimation(true)
        if (::flexibleCountdownTimer.isInitialized) {
            flexibleCountdownTimer.cancel()
        }
    }

    private fun setViewVisible(isMatching: Boolean) {
        mBinding.cancelMatching.makeVisible(isMatching)
        mBinding.btnMatch.makeVisible(!isMatching)
        mBinding.llDiamond.makeVisible(!isMatching)
        if (mBinding.btnMatch.isVisible) {
//            mBinding.llVipPriceLabel.makeVisible(!MMKVDataRep.userInfo.isVIP)
        } else {
//            mBinding.llVipPriceLabel.makeGone()
        }
    }

    private fun resetView() {
        stopAnimation()
        setViewVisible(false)
    }

    private fun stopAnimation(isDestroy: Boolean = false) {
        animationJob?.cancel()
        mBinding.radar.stop()
        if (isDestroy) {
            randomAnimator?.removeAllUpdateListeners()
            randomAnimator?.cancel()
        }
        animatorSetList.forEach {
            it.cancel()
        }
        animatorSetList.clear()
    }

    override fun bindListener() {
        mBinding.btnMatch.click {
            //模拟点击
            val isSimulatedClick =
                it.getTag(com.bdc.android.library.R.id.tag_is_simulated_click) == true
            LogX.i("isSimulatedClick: $isSimulatedClick")

            fun startMatching() {
                val userDiamond = MMKVDataRep.userInfo.balance
                val matchDiamond = if (MMKVDataRep.userInfo.isVIP) {
                    MMKVDataRep.userConfig?.vip_match_price ?: 0
                } else MMKVDataRep.userConfig?.match_price ?: 0

                val isDiamondEnough = userDiamond >= matchDiamond
                if ((matchCardCount < 1) && !isDiamondEnough) {
                    AppUtil.checkoutDiamondAndShowPopup(
                        activity = requireActivity(),
                        videoPrice = matchDiamond,
                        purchaseScene = PurchaseScene.Match(
                            anchorId = currentMatchObject?.id ?: "",
                            videoId = currentMatchObject?.virVideoId
                        )
                    ) {}
                    return
                }
                context?.let { it1 ->
                    AppUtil.requestVideoPermission(it1, onGrantedCallBack = {
                        mBinding.radar.start()
                        setViewVisible(true)
                        animationJob = lifecycleScope.launch {
                            startIconAnimation(mBinding.icMatchIcon4)
                            delay(800)
                            startIconAnimation(mBinding.icMatchIcon5)
                            delay(1200)
                            startIconAnimation(mBinding.icMatchIcon1)
                            delay(800)
                            startIconAnimation(mBinding.icMatchIcon2)
                            delay(800)
                            startIconAnimation(mBinding.icMatchIcon3)
                        }
                        AppUtil.canAutoPopupVideoCallingPage = false //匹配过程中 不允许自动弹出视频通话页面
                        mViewModel.fetchRandomAnchor(it1)
                    }) {}
                }
            }

            if (!isSimulatedClick) {
                checkFreeMatchChance {
                    startMatching()
                }
            } else {
                startMatching()
            }
        }

        mBinding.cancelMatching.click {
            resetView()
            mViewModel.fetchRandomAnchor?.cancel()
        }
        mBinding.llVip.click {
            jump(MembershipCenterActivity::class.java, Bundle().apply {
                putParcelable(
                    MembershipCenterActivity.SCENE, PurchaseScene.Match(anchorId = "", videoId = 0L)
                )
            })
        }
        mBinding.llBalance.click {
            jump(WalletActivity::class.java, Bundle().apply {
                putParcelable(
                    MembershipCenterActivity.SCENE, PurchaseScene.Match(anchorId = "", videoId = 0L)
                )
            })
        }
    }

    private fun startIconAnimation(imageView: ImageView) {
        // 等待图片加载完成后获取图片的宽度和高度
        imageView.post {
            val originalWidth = imageView.drawable.intrinsicWidth.toFloat()
            val originalHeight = imageView.drawable.intrinsicHeight.toFloat()

            // 获取原始宽度和高度的最大值作为放大倍数
            val maxScale = maxOf(
                originalWidth / imageView.width.toFloat(),
                originalHeight / imageView.height.toFloat()
            )
            val minScale = 0.5f // 最小缩放比例

            // 创建平滑缩小动画
            val scaleDownX = ObjectAnimator.ofFloat(imageView, "scaleX", 1f, minScale)
            val scaleDownY = ObjectAnimator.ofFloat(imageView, "scaleY", 1f, minScale)

            // 创建平滑放大动画
            val scaleUpX = ObjectAnimator.ofFloat(imageView, "scaleX", minScale, maxScale)
            val scaleUpY = ObjectAnimator.ofFloat(imageView, "scaleY", minScale, maxScale)

            // 设置每个动画的重复模式和次数
            scaleDownX.repeatCount = ValueAnimator.INFINITE // 无限循环
            scaleDownY.repeatCount = ValueAnimator.INFINITE // 无限循环
            scaleUpX.repeatCount = ValueAnimator.INFINITE // 无限循环
            scaleUpY.repeatCount = ValueAnimator.INFINITE // 无限循环

            // 设置动画的重复模式（从头开始）
            scaleDownX.repeatMode = ValueAnimator.REVERSE
            scaleDownY.repeatMode = ValueAnimator.REVERSE
            scaleUpX.repeatMode = ValueAnimator.REVERSE
            scaleUpY.repeatMode = ValueAnimator.REVERSE

            // 创建AnimatorSet来同时执行缩小和放大的动画
            val animatorSet = AnimatorSet()
            animatorSetList.add(animatorSet)
            animatorSet.play(scaleDownX).with(scaleDownY) // 执行缩小
            animatorSet.play(scaleUpX).with(scaleUpY).after(scaleDownX) // 执行放大

            // 设置每个动画的时长
            animatorSet.duration = 1500 // 每次动画的时长
            animatorSet.start() // 启动动画
        }
    }

    private fun startSlotAnimation() {
        randomAnimator = ValueAnimator.ofInt(0, images.size - 1).apply {
            duration = 5000 // 设置动画持续时间
            repeatCount = ValueAnimator.INFINITE // 无限循环，或者根据需求设置具体循环次数
            interpolator = android.view.animation.AccelerateDecelerateInterpolator() // 插值器，让滚动效果更顺滑

            addUpdateListener { animation ->
                val index = animation.animatedValue as Int
                mBinding.randomHeader.setImageResource(images[index])
            }
        }
        randomAnimator?.start()
    }

    private fun gotoVideo(anchorInfo: UserBean) {
        ReportManager.logEvent(DT_EVENT_CONSTANTS.EVENT_VIDEO_CALL, buildMap {
            put("userId", MMKVDataRep.userInfo.id)
            put("anchor", anchorInfo.toJson())
            put("useMatchCard", matchCardCount > 0)
            put("videoSource", Constants.VIDEO_SOURCE_MATCH)
        })
        jump(AnchorVideoActivity::class.java, Bundle().apply {
            putString(Constants.INTENT_PARAM_KEY_ANCHOR_INFO, anchorInfo.toJson())
            putInt(Constants.INTENT_PARAM_VIDEO_SOURCE, Constants.VIDEO_SOURCE_MATCH) //来源匹配
        })
    }

    override fun initViewEvents() {
        mViewModel.pageEvents.observeEvent(this) {
            when (it) {
                is MatchRequestEvent.MatchRandomRestView -> {
                    resetView()
                    val currentActivity = ActivityManager.current as? Activity
                    AppUtil.canAutoPopupVideoCallingPage = currentActivity == null ||
                            currentActivity.className() !in AppUtil.purchaseWhiteList
                }

                is MatchRequestEvent.MatchRandomFailed -> {
                    ToastUtil.show(getString(R.string.random_matching_failed))
                    resetView()
                }

//                is MatchRequestEvent.MatchRandomSuccess -> {
//                    getMatchingCardCount()
//                    currentMatchObject = it.anchorInfo
//                    if (it.anchorInfo.isVirVideo == "1") {//虚拟视频
//                        val cacheFolder = File(ContextHolder.context.filesDir, SAVE_VIDEO_FOLDER)
//                        if (cacheFolder.exists()) {
//                            val saveFile = File(cacheFolder, "${it.anchorInfo.virVideoId}.mp4")
//                            if (saveFile.exists()) {
//                                LogX.i("VideoPlaybackController playVideo ${saveFile.path}")
//                                // 通过 Intent 启动播放器页面，并传递视频的 URI
//                                val intent =
//                                    Intent(ContextHolder.context, IncomingVideoActivity::class.java)
//                                intent.putExtra(
//                                    Constants.INTENT_PARAM_KEY_ANCHOR_INFO, it.anchorInfo.toJson()
//                                )
//                                intent.putExtra(Constants.INTENT_PARAM_VIDEO_URL, saveFile.path)
//                                intent.putExtra(Constants.INTENT_PARAM_FROM_MATCH, true)
//                                intent.putExtra(
//                                    Constants.INTENT_PARAM_RECORD_ID, it.anchorInfo.recordId
//                                )
//                                intent.putExtra(
//                                    Constants.INTENT_PARAM_ISNEEDMUTE, it.anchorInfo.isNeedMute
//                                )
//                                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
//                                ContextHolder.context.startActivity(intent)
//                            } else {
//                                ToastUtil.show(getString(R.string.random_matching_failed))
//                            }
//                        } else {
//                            ToastUtil.show(getString(R.string.random_matching_failed))
//                        }
//
//                    } else {
//                        gotoVideo(it.anchorInfo)
//                    }
//                }

                is MatchRequestEvent.MatchRandomRealPeople -> {
                    if (it.anchorInfo == null) {
                        ToastUtil.show(getString(R.string.random_matching_failed))
                    } else {
                        gotoVideo(it.anchorInfo)
                    }
                }

                else -> {}
            }
        }
    }
}