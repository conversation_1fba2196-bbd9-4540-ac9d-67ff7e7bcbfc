package com.heart.heartmerge.ui.activities.mine

import android.content.Intent
import android.graphics.Paint
import android.text.SpannableString
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.UnderlineSpan
import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import androidx.core.text.HtmlCompat
import androidx.lifecycle.asLiveData
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.PagerSnapHelper
import com.angcyo.dsladapter._dslAdapter
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.getIndex
import com.bdc.android.library.extension.getString
import com.bdc.android.library.extension.jump
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.extension.observeOnce
import com.bdc.android.library.extension.toast
import com.heart.heartmerge.BuildConfig
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.GoodsBean
import com.heart.heartmerge.beans.GoodsQueryType
import com.heart.heartmerge.databinding.ActivityMembershipCenterBinding
import com.heart.heartmerge.extension.applyGooglePrice
import com.heart.heartmerge.extension.applyMembershipRights
import com.heart.heartmerge.extension.formatDateTime
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.payment.BillingService
import com.heart.heartmerge.popup.showChoosePaymentPopup
import com.heart.heartmerge.ui.activities.WebViewActivity
import com.heart.heartmerge.ui.adapter.MembershipSubscribeAdapter
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.PurchaseScene
import com.heart.heartmerge.viewmodes.UserViewModel
import com.lxj.xpopup.core.BottomPopupView.VISIBLE
import kotlinx.coroutines.launch


class MembershipCenterActivity :
    BaseCoreActivity<ActivityMembershipCenterBinding, UserViewModel>() {

    companion object {
        const val SCENE = "scene"
    }

    private val purchaseScene by lazy {
        intent.getParcelableExtra<PurchaseScene>(SCENE)
    }

    private var billingService: BillingService? = null

    override fun getLayoutId(): Int = R.layout.activity_membership_center

    override fun initView() {
        super.initView()
        billingService = BillingService(this)
        mBinding.toolbar.setActionOnClickListener {
            jump(SubscribeHistoryActivity::class.java)
        }
        mBinding.btnBuy.click {
            val item =
                mBinding.recyclerView._dslAdapter?.dataItems?.find { it.itemIsSelected }?.itemData as? GoodsBean
            showChoosePaymentPopup(this, item, purchaseScene)
        }

        mBinding.tvRestorePurchase.apply {
            paintFlags = mBinding.tvRestorePurchase.paintFlags or Paint.UNDERLINE_TEXT_FLAG
            click {
                showLoading("Loading...")
                billingService?.checkRestorePurchases {
                    mBinding.tvRestorePurchase.postDelayed({
                        stopLoading()
                    }, 500)
                }
            }
        }

        mBinding.tvCancelUnsubscribe.click {
            if (MMKVDataRep.userInfo.isGoogleUser) {
                val intent = Intent(Intent.ACTION_VIEW)
                intent.data =
                    "https://play.google.com/store/account/subscriptions?package=${BuildConfig.APPLICATION_ID}".toUri()
                intent.setPackage("com.android.vending")
                startActivity(intent)
            } else {
                toast(getString(R.string.please_contact_customer_service))
            }
        }

        PagerSnapHelper().attachToRecyclerView(mBinding.recyclerViewRights)

        buildAgreementText()

        mBinding.btnBuy.apply {
            isEnabled = !MMKVDataRep.userInfo.isVIP
            text = if (MMKVDataRep.userInfo.isVIP) {
                getString(R.string.btn_subscribed)
            } else {
                getString(R.string.btn_subscribe)
            }
        }

        fillExpiredText()

        FlowBus.with<Boolean>(Constants.SUBSCRIBE_SUCCESS).register(this) { //订阅成功
            fillExpiredText()
        }
    }

    private fun fillExpiredText() {
        if (MMKVDataRep.userInfo.isVIP && (MMKVDataRep.userInfo.user_vip?.expire_at ?: 0) > 0) {
            val spannableString = SpannableString(
                getString(
                    R.string.membership_expired_on,
                    MMKVDataRep.userInfo.user_vip?.expire_at?.formatDateTime()
                )
            )
            spannableString.setSpan(
                ForegroundColorSpan(ContextCompat.getColor(this, R.color.color_524B60)),
                0,
                getString(R.string.membership_expired_on, "").length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            mBinding.tvExpiredDay.makeVisible()
            mBinding.tvExpiredDay.text = spannableString
        }
    }

    private fun buildAgreementText() {
        val cancelSubscriptionHtml =
            "<font color='#FF5722'><b>${getString(R.string.subscribe_cancel_subscription)}</b></font><b>"
        val termsOfUseHtml =
            "<font color='#2196F3'><b>${getString(R.string.subscribe_terms_of_use)}</b></font>"
        val privacyPolicyHtml =
            "<font color='#4CAF50'><b>${getString(R.string.subscribe_privacy_policy)}</b></font>"

        val formattedText = getString(
            R.string.subscribe_agreement, cancelSubscriptionHtml, termsOfUseHtml, privacyPolicyHtml
        )

        mBinding.tvAgreement.text = HtmlCompat.fromHtml(
            formattedText.replace("\n", "<br>"), HtmlCompat.FROM_HTML_MODE_LEGACY
        )
        // 启用点击功能
        mBinding.tvAgreement.movementMethod = LinkMovementMethod.getInstance()

        fun setClickableSpans(textView: TextView) {
            val spannable = SpannableString(textView.text)

            val cancelSubscriptionSpan = object : ClickableSpan() {
                override fun onClick(widget: View) {
                    if (MMKVDataRep.userInfo.isGoogleUser) {
                        runCatching {
                            val intent = Intent(Intent.ACTION_VIEW)
                            intent.setData("https://play.google.com/store/account/subscriptions?package=${BuildConfig.APPLICATION_ID}".toUri())
                            intent.setPackage("com.android.vending")
                            startActivity(intent)
                        }.onFailure {
                            toast(getString(R.string.please_contact_customer_service))
                        }
                    } else {
                        toast(getString(R.string.please_contact_customer_service))
                    }
                }
            }
            val termsOfUseSpan = object : ClickableSpan() {
                override fun onClick(widget: View) {
                    WebViewActivity.jump(
                        this@MembershipCenterActivity,
                        getString(R.string.subscribe_terms_of_use),
                        Constants.Agreement.TERMS_USE_URL
                    )
                }
            }
            val privacyPolicySpan = object : ClickableSpan() {
                override fun onClick(widget: View) {
                    WebViewActivity.jump(
                        this@MembershipCenterActivity,
                        getString(R.string.subscribe_privacy_policy),
                        Constants.Agreement.PRIVACY_URL
                    )
                }
            }

            // 设置 "Cancel subscription" 的范围和样式
            val cancelSubscriptionText = getString(R.string.subscribe_cancel_subscription)
            val start1 = spannable.toString().indexOf(cancelSubscriptionText)
            val end1 = start1 + cancelSubscriptionText.length
            spannable.setSpan(
                cancelSubscriptionSpan, start1, end1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            spannable.setSpan(
                ForegroundColorSpan(ContextCompat.getColor(this, R.color.color_F53D3D)),
                start1,
                end1,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            spannable.setSpan(UnderlineSpan(), start1, end1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)

            // 设置 "Terms of use" 的范围和样式
            val termsOfUseText = getString(R.string.subscribe_terms_of_use)
            val start2 = spannable.toString().indexOf(termsOfUseText)
            val end2 = start2 + termsOfUseText.length
            spannable.setSpan(termsOfUseSpan, start2, end2, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            spannable.setSpan(
                ForegroundColorSpan(ContextCompat.getColor(this, R.color.color_9F2AF8)),
                start2,
                end2,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            spannable.setSpan(UnderlineSpan(), start2, end2, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)

            // 设置 "Privacy Policy" 的范围和样式
            val privacyPolicyText = getString(R.string.subscribe_privacy_policy)
            val start3 = spannable.toString().indexOf(privacyPolicyText)
            val end3 = start3 + privacyPolicyText.length
            spannable.setSpan(privacyPolicySpan, start3, end3, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            spannable.setSpan(
                ForegroundColorSpan(ContextCompat.getColor(this, R.color.color_9F2AF8)),
                start3,
                end3,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            spannable.setSpan(UnderlineSpan(), start3, end3, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            textView.text = spannable
        }

        // 设置点击事件
        setClickableSpans(mBinding.tvAgreement)
    }

    override fun initData() {
        mBinding.progressBar.apply {
            visibility = VISIBLE
            isIndeterminate = true
        }
        mViewModel.fetchGoodsList(goodsType = GoodsQueryType.SUBSCRIBE).asLiveData()
            .observeOnce(this@MembershipCenterActivity) { subscribeList ->
                lifecycleScope.launch {
//                    mBinding.tvPrivilege.text = getString(
//                        R.string.seven_exclusive_privilege, subscribeList?.vip_privileges?.size ?: 0
//                    )
                    subscribeList?.goldGoods?.apply {
                        billingService?.let {
                            this.applyGooglePrice(billingService = it)
                        }
                        this.map {
                            MembershipSubscribeAdapter.MembershipSubscribeItem(it, purchaseScene) {
//                                fetchBenefit(it)
                                mBinding.tvPrivilege.text =
                                    getString(R.string.for_week, it.formattedPrice)

                                val index = this.indexOfFirst { item ->
                                    it.product_id == item.product_id
                                }.takeIf { it > 0 } ?: 0
                                mBinding.recyclerViewRights.applyMembershipRights(
                                    privileges = subscribeList?.vip_privileges, index = index
                                )
                            }
                        }?.let {
                            mBinding.progressBar.makeGone()
                            val productId = intent.getString()
                            val index = this.indexOfFirst {
                                it.product_id == productId
                            }.takeIf { it > 0 } ?: intent.getIndex()
                                .takeIf { it >= 0 && it < this.size } ?: 0
                            mBinding.recyclerView.adapter = MembershipSubscribeAdapter(it).apply {
                                itemSelectorHelper.selector(index)
                                if (subscribeList != null && subscribeList.goldGoods.isNotEmpty()) {
//                                    fetchBenefit(subscribeList.goldGoods[index].product_id)
                                    mBinding.tvPrivilege.text = getString(
                                        R.string.for_week,
                                        subscribeList.goldGoods[index].formattedPrice
                                    )
                                }
                            }
                        }
                    }

                    mBinding.recyclerViewRights.applyMembershipRights(
                        privileges = subscribeList?.vip_privileges, index = 0
                    )
                }
            }
    }

    override fun onResume() {
        super.onResume()
        AppUtil.canAutoPopupVideoCallingPage = false
    }

    override fun onDestroy() {
        super.onDestroy()
        AppUtil.canAutoPopupVideoCallingPage = true
        billingService?.onDestroy()
    }
}