package com.heart.heartmerge.ui.activities.mine

import android.annotation.SuppressLint
import android.widget.ProgressBar
import androidx.lifecycle.asLiveData
import com.angcyo.dsladapter.DslAdapterItem
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.imageloader.ImageLoader
import com.bdc.android.library.refreshlayout.XRecyclerView
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.TaskBean
import com.heart.heartmerge.databinding.ActivityTaskBinding
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond
import com.heart.heartmerge.popup.showNormalNewPopup
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.ContextHolder
import com.heart.heartmerge.utils.PurchaseScene
import com.heart.heartmerge.utils.RewardTaskTypeText
import com.heart.heartmerge.viewmodes.UserViewModel

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/20 19:22
 * @description :任务列表
 */
class TaskActivity : BaseCoreActivity<ActivityTaskBinding, UserViewModel>() {
    override fun getLayoutId(): Int = R.layout.activity_task

    override fun initView() {
        super.initView()
        mBinding.refreshLayout.onRefresh()
        mBinding.refreshLayout.setOnRefreshListener { fetchData() }
    }

    private fun fetchData() {
        mViewModel.getLevelTaskList().asLiveData().observe(this) { value ->
            mBinding.refreshLayout.onFinishRefresh()

            val (category1, category2) = value.category_list?.let {
                it.firstOrNull() to it.getOrNull(1)
            } ?: Pair(null, null)

            mBinding.tvCareerTask.text = category1?.title
            mBinding.rvCareerTask.makeVisible(category1?.list?.any { it.isUserTask } == true)
            category1?.list?.filter { it.isUserTask }?.let {
                bindTaskAdapter(mBinding.rvCareerTask, it)
            }

            mBinding.tvDailyTask.text = category2?.title
            mBinding.rvDailyTask.makeVisible(category2?.list?.any { it.isUserTask } == true)
            category2?.list?.filter { it.isUserTask }?.let {
                bindTaskAdapter(mBinding.rvDailyTask, it)
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun bindTaskAdapter(
        recyclerView: XRecyclerView, tasks: List<TaskBean>
    ) {
        recyclerView.clearAllItems()
        recyclerView.append<DslAdapterItem>(tasks) {
            itemData = it
            itemLayoutId = R.layout.item_task
            itemBindOverride = { itemHolder, _, _, _ ->
                val item = itemData as TaskBean

                itemHolder.img(R.id.iv_task)?.apply {
                    ImageLoader.with(itemHolder.context).load(item.icon)
                        .error(R.mipmap.ic_diamond_big).placeholder(R.mipmap.ic_diamond_big)
                        .into(this)
                }
                val (taskName, taskDescription) = RewardTaskTypeText.get(
                    item.missionId, item.current, item.standardValue
                )
                itemHolder.tv(R.id.tv_task_name)?.text = item.title
                itemHolder.tv(R.id.tv_task_desc)?.text = item.sub_title
                itemHolder.tv(R.id.tv_task_value)?.text =
                    "+${item.reward?.gold_num?.toShowDiamond()}"
                itemHolder.v<ProgressBar>(R.id.progress_bar)?.apply {
                    makeVisible(item.hasProgress)
                    max = item.step.coerceAtLeast(item.complete_config_json?.total ?: 0)
                    progress = item.step
                }

                itemHolder.tv(R.id.tv_go)?.apply {
                    makeVisible(item.task_status >= -1)
                    isEnabled = !item.isClaimed
                    text = when {
                        item.isCompleted -> ContextHolder.context.getString(R.string.task_action_claim)
                        item.isClaimed -> ContextHolder.context.getString(R.string.claimed)
                        else -> context.getString(R.string.task_action_go)
                    }
                    click {
                        if (item.isCompleted) {
                            handleClaim(item.task_id, item.reward?.gold_num?.toShowDiamond() ?: 0)
                        } else {
                            AppUtil.jumpWithType(
                                this@TaskActivity, item.jump_url, PurchaseScene.Task
                            )
                        }
                    }
                }
            }
        }
    }

    private fun handleClaim(id: Int, diamond: Int = 0) {
        mViewModel.taskClaim(id).asLiveData().observe(this) {
            showNormalNewPopup(
                this,
                R.mipmap.ic_payment_success,
                title = getString(R.string.remind),
                content = "${if (diamond > 0) diamond else ""} ${getString(R.string.bonus_claimed_successful)}",
                btnSure = getString(R.string.confirm),
                block = {
                    fetchData()
                })
        }
    }
}