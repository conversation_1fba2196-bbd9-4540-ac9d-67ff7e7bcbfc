package com.heart.heartmerge.ui.activities.mine

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View.VISIBLE
import androidx.lifecycle.asLiveData
import androidx.viewpager2.widget.ViewPager2
import androidx.viewpager2.widget.ViewPager2.SCROLL_STATE_SETTLING
import com.angcyo.dsladapter.DslAdapterItem
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.base.fragment.BaseCoreFragment
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.extension.setTextCompatColor
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.LevelBean
import com.heart.heartmerge.beans.LevelWrapperBean
import com.heart.heartmerge.databinding.ActivityLevelBinding
import com.heart.heartmerge.databinding.ItemLevelChildBinding
import com.heart.heartmerge.extension.attach
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.popup.showNormalNewPopup
import com.heart.heartmerge.ui.widget.level.LevelCardView
import com.heart.heartmerge.ui.widget.level.LevelConfig
import com.heart.heartmerge.viewmodes.UserViewModel
import com.zhpan.bannerview.BaseBannerAdapter
import com.zhpan.bannerview.BaseViewHolder
import com.zhpan.bannerview.constants.PageStyle

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/8 15:38
 * @description :用户等级
 */
class LevelActivity : BaseCoreActivity<ActivityLevelBinding, UserViewModel>() {

    private var levelBean: LevelWrapperBean? = null

    override fun getLayoutId(): Int = R.layout.activity_level

    @SuppressLint("NotifyDataSetChanged")
    override fun initView() {
        super.initView()
        mBinding.bannerView.apply {
            setPageStyle(PageStyle.MULTI_PAGE_SCALE, 0.90f)
            setPageMargin(resources.getDimensionPixelOffset(com.bdc.android.library.R.dimen.dp_10))
            setRevealWidth(resources.getDimensionPixelOffset(com.bdc.android.library.R.dimen.dp_10))
            setAdapter(LevelCardAdapter())
            setLifecycleRegistry(lifecycle)
        }.create()

        val childViewPage2: ViewPager2? =
            mBinding.bannerView.findViewById<ViewPager2>(R.id.vp_main) as? ViewPager2

        mBinding.bannerView.registerOnPageChangeCallback(object :
            ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                if ((childViewPage2?.scrollState == SCROLL_STATE_SETTLING) || (position == 0 && childViewPage2?.scrollState == 0)) {
                    mBinding.levelStepView.current(position + 1)
                }
                mBinding.viewPager.currentItem = position
            }
        })

        mBinding.viewPager.attach(
            supportFragmentManager, lifecycle, listOf(LevelFragment(), LevelFragment())
        ) { position ->
            mBinding.bannerView.currentItem = position
        }
    }

    override fun initData() {
        super.initData()
        mBinding.progressBar.apply {
            visibility = VISIBLE
            isIndeterminate = true
        }
        mViewModel.getLevelConfig().asLiveData().observe(this) { value ->
            levelBean = value
            mBinding.progressBar.makeGone()
            mBinding.levelStepView.makeVisible()
            mBinding.bannerView.refreshData(value.level_ranges)
            LevelConfig.LEVEL_STEP = value.level_ranges.firstOrNull()?.let {
                it.level_end - it.level_start + 1
            } ?: 5
            LevelConfig.LEVEL_MAX = value.configs.maxBy { it.level }.level

            mBinding.levelStepView.setupLevel(value.level_ranges.map { bean -> "Lv${bean.level_start}-${bean.level_end}" })

            val fragments = buildList {
                value.level_ranges.forEachIndexed { index, levelBean ->
                    add(LevelFragment.getInstance(index, value.level_ranges.get(index).permissions))
                }
            }
            mBinding.viewPager.attach(
                supportFragmentManager, lifecycle, fragments
            ) { position ->
                mBinding.bannerView.currentItem = position
            }

            mBinding.viewPager.postDelayed({
                MMKVDataRep.userInfo?.level_config?.apply {
                    if (level > 0) {
                        val index = (level - 1) / LevelConfig.LEVEL_STEP
                        mBinding.bannerView.currentItem = index
                        mBinding.levelStepView.current(index + 1)
                    }
                }
            }, 100)
        }
    }

    class LevelFragment : BaseCoreFragment<ItemLevelChildBinding, UserViewModel>() {

        companion object {
            fun getInstance(
                index: Int, privileges: List<LevelBean.PrivilegeBean>?
            ): LevelFragment {
                return LevelFragment().apply {
                    arguments = Bundle().apply {
                        putInt("index", index)
                        putParcelableArray("privileges", privileges?.toTypedArray())
                    }
                }
            }
        }

        override fun getLayoutId(): Int = R.layout.item_level_child

        override fun initData() {
            super.initData()
            arguments?.getInt("index")?.let { position ->
                val start = position * LevelConfig.LEVEL_STEP + if (position == 0) 0 else 1
                val end = position * LevelConfig.LEVEL_STEP + LevelConfig.LEVEL_STEP
                mBinding.tvPrivilege.text = getString(
                    R.string.lv_privilege, "${start}-${end}"
                )
            }

            arguments?.getParcelableArray("privileges")?.let {
                fetchPrivilege(it.map { it as LevelBean.PrivilegeBean })
            }
        }

        private fun fetchPrivilege(privileges: List<LevelBean.PrivilegeBean>? = null) {
            mBinding.recyclerView.makeVisible()
            mBinding.recyclerView.clearAllItems()
            mBinding.recyclerView.append<DslAdapterItem>(
                privileges
            ) {
                itemLayoutId = R.layout.item_level_task
                itemBindOverride = { itemHolder, _, _, _ ->
                    val item = itemData as LevelBean.PrivilegeBean

                    val (icon, taskName, taskDescription) = when (item.permission) {
                        "recharge_bonus" -> Triple(
                            R.mipmap.ic_level_task_recharge_bonus,
                            R.string.recharge_bonus,
                            R.string.recharge_bonus_desc
                        )

                        "weekly_bonus" -> Triple(
                            R.mipmap.ic_level_task_recharge_bonus2,
                            R.string.weekly_bonus,
                            R.string.weekly_bonus_desc
                        )

                        else -> Triple(
                            R.mipmap.ic_level_task_recharge_bonus3,
                            R.string.level_identify,
                            R.string.level_identify_desc
                        )
                    }

                    itemHolder.img(R.id.iv_task)?.setImageResource(icon)
                    itemHolder.tv(R.id.tv_task_name)?.text = getString(taskName)
                    itemHolder.tv(R.id.tv_task_desc)?.apply {
                        text = getString(taskDescription)
                    }

                    itemHolder.tv(R.id.tv_progress)?.apply {
                        makeVisible(item.value.isNotEmpty() && item.stat == 0)
                        text = item.value
                    }

                    itemHolder.tv(R.id.tv_get)?.apply {
                        makeVisible(item.isAvailable || item.isReceived)
                        text =
                            if (item.isAvailable) getString(R.string.get) else getString(R.string.claimed)
                        setBackgroundResource(if (item.isAvailable) R.drawable.shape_9f2af8_50 else R.drawable.shape_3d374a_50)
                        setTextCompatColor(if (item.isAvailable) com.allen.library.R.color.white else R.color.color_9F9CA6)
                        click {
                            if (item.isAvailable) {
                                handleClaim(item.record_id) {
                                    item.stat = 2
                                    mBinding.recyclerView.notifyUpdateItem()
                                }
                            }
                        }
                    }
                }
            }
        }

        private fun handleClaim(recordId: Int, block: () -> Unit) {
            mViewModel.signIn(recordId.toString()).asLiveData().observe(this) { value ->
                showNormalNewPopup(
                    requireActivity(),
                    R.mipmap.ic_payment_success,
                    title = getString(R.string.remind),
                    content = getString(R.string.bonus_claimed_successful),
                    btnSure = getString(R.string.confirm),
                    block = {
                        block.invoke()
                    })
            }
        }
    }

    inner class LevelCardAdapter : BaseBannerAdapter<LevelBean>() {
        override fun bindData(
            holder: BaseViewHolder<LevelBean>, data: LevelBean, position: Int, pageSize: Int
        ) {
            holder.findViewById<LevelCardView>(R.id.level_cardView)?.apply {
                current = position * LevelConfig.LEVEL_STEP + 1
            }
        }

        override fun getLayoutId(viewType: Int): Int = R.layout.item_banner_level_card
    }
}