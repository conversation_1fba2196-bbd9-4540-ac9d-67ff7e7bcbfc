package com.heart.heartmerge.ui.widget

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.TypedArray
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Shader
import android.graphics.Typeface
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import com.heart.heartmerge.R


/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2025/5/29 16:23
 * @description :
 */
class GradientTextView : AppCompatTextView {
    private var gradientColors: IntArray = intArrayOf(Color.RED, Color.YELLOW, Color.BLUE) // 默认渐变色
    private var strokeColor: Int = Color.WHITE // 默认描边颜色
    private var strokeWidth = 4f // 默认描边宽度

    private var strokePaint: Paint? = null

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context, attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    ) {
        init(context, attrs)
    }

    private fun init(context: Context, attrs: AttributeSet?) {
        val ta: TypedArray = context.obtainStyledAttributes(attrs, R.styleable.GradientTextView)
        try {
            val gradientResId = ta.getResourceId(R.styleable.GradientTextView_gradientColors, 0)
            if (gradientResId != 0) {
                gradientColors = context.resources.getIntArray(gradientResId)
            }
            strokeColor = ta.getColor(R.styleable.GradientTextView_strokeColor, Color.WHITE)
            strokeWidth = ta.getDimension(R.styleable.GradientTextView_strokeWidth, 4f)
        } finally {
            ta.recycle()
        }

        strokePaint = Paint()
        strokePaint?.isAntiAlias = true
        strokePaint?.style = Paint.Style.STROKE
        strokePaint?.strokeWidth = strokeWidth
        strokePaint?.color = strokeColor
        strokePaint?.textAlign = Paint.Align.LEFT
    }

    @SuppressLint("DrawAllocation")
    protected override fun onDraw(canvas: Canvas) {
        val text = getText().toString()
        val x = paddingLeft.toFloat()
        val y = baseline.toFloat()

        strokePaint?.textSize = textSize
        val tf: Typeface? = typeface
        if (tf != null) {
            strokePaint?.typeface = tf
        }

        // 1️⃣ 绘制描边
        strokePaint?.let {
            canvas.drawText(text, x, y, it)
        }

        // 2️⃣ 绘制渐变文字
        val shader: Shader = LinearGradient(
            0f, 0f, width.toFloat(), height.toFloat(), gradientColors, null, Shader.TileMode.CLAMP
        )
        paint.shader = shader

        super.onDraw(canvas)
    }

    /** 动态设置渐变颜色 */
    fun setGradientColors(colors: IntArray) {
        gradientColors = colors
        invalidate()
    }

    /** 动态设置描边颜色 */
    fun setStrokeColor(color: Int) {
        strokeColor = color
        strokePaint?.color = strokeColor
        invalidate()
    }

    /** 动态设置描边宽度 */
    fun setStrokeWidth(width: Float) {
        strokeWidth = width
        strokePaint?.strokeWidth = strokeWidth
        invalidate()
    }
}
