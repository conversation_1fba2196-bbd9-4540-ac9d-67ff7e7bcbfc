package com.heart.heartmerge.ui.activities.login

import android.graphics.Color
import androidx.lifecycle.ViewModel
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.ActivityAgreementBinding

class AgreementActivity : BaseCoreActivity<ActivityAgreementBinding, ViewModel>() {

    val agreementContent =
        "用户注册协议\n" + "\n" + "欢迎使用我们的服务。请仔细阅读以下条款，如果您注册成为用户，即表示您已经同意以下所有条款。\n" + "\n" + "1. 接受条款\n" + "   通过使用我们的服务，您同意本协议的所有条款。如果您不同意这些条款，请不要使用我们的服务。\n" + "\n" + "2. 服务说明\n" + "   我们提供[简要描述您的服务]。我们保留随时修改或终止服务的权利，恕不另行通知。\n" + "\n" + "3. 用户注册\n" + "   3.1 您必须提供准确、完整、最新的个人信息。\n" + "   3.2 您有责任保护您的账户安全，包括保护您的密码。\n" + "   3.3 您同意对您账户下的所有活动负责。\n" + "\n" + "4. 用户行为\n" + "   4.1 您同意不会使用我们的服务进行任何非法或未经授权的活动。\n" + "   4.2 您不得骚扰、威胁或伤害其他用户。\n" + "   4.3 您不得上传、发布任何侵犯他人知识产权的内容。\n" + "\n" + "5. 隐私政策\n" + "   我们重视您的隐私。请参阅我们的隐私政策，了解我们如何收集、使用和保护您的个人信息。\n" + "\n" + "6. 内容所有权\n" + "   6.1 您保留您在我们服务上发布内容的所有权。\n" + "   6.2 您授予我们全球范围内的许可，允许我们使用、复制、修改、发布您的内容。\n" + "\n" + "7. 免责声明\n" + "   我们的服务按\"原样\"提供，不提供任何明示或暗示的保证。\n" + "\n" + "8. 责任限制\n" + "   在法律允许的最大范围内，我们对任何直接、间接、偶然、特殊或后果性损害不承担责任。\n" + "\n" + "9. 修改条款\n" + "   我们保留随时修改这些条款的权利。我们会在网站上发布修改后的条款。\n" + "\n" + "10. 终止\n" + "    我们保留因任何原因终止您使用我们服务的权利，恕不另行通知。\n" + "\n" + "11. 适用法律\n" + "    本协议受[您所在国家/地区]法律管辖。\n" + "\n" + "如果您对本协议有任何疑问，请联系我们：[联系方式]\n" + "\n" + "最后更新日期：[日期]"

    override fun getLayoutId(): Int = R.layout.activity_agreement

    override fun initView() {
        val type = intent.getIntExtra("type", 0)
        val agreement =
            if (type == 1) getString(R.string.user_agreement) else getString(R.string.privacy_policy)
//        setCenterTitle(agreement.replace("《", "").replace("》", ""))

        mBinding.webView.apply {
            setBackgroundColor(Color.parseColor("#101321"))
            var htmlContent = "<html><body>${agreementContent}</html>"
            htmlContent = htmlContent.replace("<body>", "<body style='color: #6D7096;'>")
            loadDataWithBaseURL(null, htmlContent, "text/html", "utf-8", null)
        }
    }
}