package com.heart.heartmerge.ui.fragments.message;

import android.app.Application;

import com.heart.heartmerge.utils.Constants;

import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

import io.rong.imkit.conversationlist.model.BaseUiConversation;
import io.rong.imkit.conversationlist.viewmodel.ConversationListViewModel;

public class MyConversationListViewModel extends ConversationListViewModel {
    public MyConversationListViewModel(Application application) {
        super(application);
    }

    /**
     * 强制重新排序并刷新会话列表
     * 在每次数据变化时调用，确保特殊会话始终保持在固定位置
     */
    private void forceRefreshList() {
        sort();
    }

    /**
     * 重写此方法以确保在获取会话列表时调用排序逻辑
     */
    @Override
    public void getConversationList(boolean isLoadMore, boolean isFilterConversation, long timestamp) {
        super.getConversationList(isLoadMore, isFilterConversation, timestamp);
        // 在获取会话列表后强制排序
        forceRefreshList();
    }

    @Override
    protected void sort() {
        List temp = Arrays.asList(mUiConversationList.toArray());
        Collections.sort(temp, new Comparator<BaseUiConversation>() {
            public int compare(BaseUiConversation o1, BaseUiConversation o2) {
                String id1 = o1.mCore.getTargetId();
                String id2 = o2.mCore.getTargetId();

                // 特殊ID的优先级排序 - 始终先检查优先级
                int priority1 = getPriority(id1);
                int priority2 = getPriority(id2);

                // 始终按优先级排序，不再考虑是否为0
                // 注意：这里是比较priority2和priority1，因为我们希望较小的优先级值排在前面
                if (priority1 != priority2) {
                    return Integer.compare(priority1, priority2);
                }

                // 其他消息保持原有排序逻辑
                if ((!o1.mCore.isTop() || !o2.mCore.isTop()) && (o1.mCore.isTop() || o2.mCore.isTop())) {
                    if (o1.mCore.isTop() && !o2.mCore.isTop()) {
                        return -1;
                    } else {
                        return !o1.mCore.isTop() && o2.mCore.isTop() ? 1 : 0;
                    }
                } else if (o1.mCore.getSentTime() > o2.mCore.getSentTime()) {
                    return -1;
                } else {
                    return o1.mCore.getSentTime() < o2.mCore.getSentTime() ? 1 : 0;
                }
            }

            /**
             * 获取会话的优先级
             * 返回值越小，优先级越高
             * 0表示没有特殊优先级
             * 注意：在排序时，我们使用Integer.compare(priority2, priority1)来确保较小的值排在前面
             */
            private int getPriority(String targetId) {
                if (targetId == null) {
                    return 100; // 空值排在最后
                }

                if (Constants.RONG_YUN_ID_SYSTEM.equals(targetId)) {
                    return 1; // 系统消息优先级最高
                } else if (Constants.RONG_YUN_ID_CUSTOM_SERVICE.equals(targetId)) {
                    return 2; // 客服消息优先级第二
                } else {
                    return 99; // 其他消息没有特殊优先级，设置为较大的值
                }
            }
        });
        mUiConversationList.clear();
        mUiConversationList.addAll(temp);
    }
}
