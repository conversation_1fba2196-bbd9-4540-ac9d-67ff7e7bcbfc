package com.heart.heartmerge.ui.activities.anchor

import androidx.core.text.isDigitsOnly
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.click
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.ActivityAnchorReportBinding
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.viewmodes.SettingRequestEvent
import com.heart.heartmerge.viewmodes.UserViewModel

class AnchorReportActivity : BaseCoreActivity<ActivityAnchorReportBinding, UserViewModel>() {
    override fun getLayoutId(): Int = R.layout.activity_anchor_report

    override fun bindListener() {
        mBinding.btnSubmit.click {
            if (mBinding.etReason.text.isEmpty()) {
                ToastUtil.show(getString(R.string.tip_report_reason_empty_toast))
            } else {
                val anchorId = intent.getStringExtra(Constants.INTENT_PARAM_KEY_ANCHOR_INFO) ?: ""
                if (anchorId?.isNotEmpty() == true && anchorId.isDigitsOnly() == true) {
                    mViewModel.anchorReport(anchorId.toInt(), mBinding.etReason.text.toString())
                }
            }
        }
    }

    override fun initViewEvents() {
        mViewModel.pageEvents.observeEvent(this) {
            when (it) {
                is SettingRequestEvent.AnchorReportFailed -> {
                    ToastUtil.show(it.msg)
                }

                is SettingRequestEvent.AnchorReportSuccess -> {
                    ToastUtil.show(getString(R.string.tip_report_suc))
                    finish()
                }

                else -> {}
            }
        }
    }
}