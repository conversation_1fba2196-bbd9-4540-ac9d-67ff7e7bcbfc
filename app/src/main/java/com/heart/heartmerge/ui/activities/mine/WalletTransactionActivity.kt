package com.heart.heartmerge.ui.activities.mine

import android.os.Bundle
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.asLiveData
import com.angcyo.dsladapter.DslAdapterItem
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.putIndex
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.WalletTransactionBean
import com.heart.heartmerge.databinding.ActivityWalletTransactionBinding
import com.heart.heartmerge.extension.attach
import com.heart.heartmerge.extension.checkSelected
import com.heart.heartmerge.extension.openLoadMore
import com.heart.heartmerge.ui.fragments.common.RecyclerViewFragment
import com.heart.heartmerge.viewmodes.UserViewModel

class WalletTransactionActivity :
    BaseCoreActivity<ActivityWalletTransactionBinding, UserViewModel>() {

    override fun getLayoutId(): Int = R.layout.activity_wallet_transaction

    override fun initView() {
        super.initView()
        val titles = arrayOf(
            getString(R.string.wallet_tabs_all),
            getString(R.string.wallet_tabs_income),
            getString(R.string.wallet_tabs_consume)
        )
        mBinding.tab.setTitles(titles)
        initViewPager(titles)
        mBinding.tab.setViewPager(mBinding.viewPager)
    }

    private fun initViewPager(titles: Array<String>) {
        val fragments = buildList<Fragment> {
            titles.forEachIndexed { index, s ->
                add(ChildFragment.getInstance(index))
            }
        }
        mBinding.viewPager.attach(supportFragmentManager, lifecycle, fragments) { position ->
            mBinding.tab.setCurrentTab(position)
        }
        mBinding.viewPager.checkSelected(0)
    }

    class ChildFragment : RecyclerViewFragment() {
        private val viewModel by viewModels<UserViewModel>()

        companion object {
            fun getInstance(index: Int): ChildFragment {
                return ChildFragment().apply {
                    arguments = Bundle().putIndex(index)
                }
            }
        }

        override fun initData() {
            super.initData()
            mBinding.refreshLayout.recyclerView.openLoadMore { }
            mBinding.refreshLayout.setOnRefreshListener { b, i ->
                fetch(b, i)
            }
        }

        private fun fetch(refresh: Boolean, pageIndex: Int) {
            viewModel.fetchWalletTransactionList(refresh).asLiveData()
                .observe(this) {
                    append<DslAdapterItem>(refresh, it.orEmpty()) {
                        itemData = it
                        itemLayoutId = R.layout.item_wallet_transaction
                        itemBindOverride = { itemHolder, _, _, _ ->
                            val item = itemData as? WalletTransactionBean
                            itemHolder.tv(R.id.tv_name)?.text = when (item?.changeType) {
                                0 -> itemHolder.context.getString(R.string.transaction_type_recharge)
                                1 -> itemHolder.context.getString(
                                    R.string.transaction_type_video_chat,
                                    "${item.anchorNickName}(${item.userCode})"
                                )

                                2 -> itemHolder.context.getString(R.string.transaction_type_private_picture)
                                3 -> itemHolder.context.getString(
                                    R.string.transaction_type_gift,
                                    "${item.anchorNickName}(${item.userCode})"
                                )

                                4 -> itemHolder.context.getString(R.string.transaction_type_withdraw)
                                5 -> itemHolder.context.getString(R.string.transaction_type_manual_recharge)
                                7 -> itemHolder.context.getString(R.string.transaction_type_subscribe)
                                8 -> itemHolder.context.getString(R.string.transaction_type_manual_signin)
                                15 -> itemHolder.context.getString(R.string.transaction_type_first_login)
                                else -> ""
                            }
                            itemHolder.tv(R.id.tv_time)?.text = item?.updateTime
                            itemHolder.tv(R.id.tv_money)?.apply {
                                setTextColor(
                                    ContextCompat.getColor(
                                        itemHolder.context,
                                        if ((item?.afterNum ?: 0) > (item?.beforeNum
                                                ?: 0)
                                        ) R.color.color_30A35C else com.bdc.android.library.R.color.color_999999
                                    )
                                )
                                text =
                                    "${if ((item?.afterNum ?: 0) > (item?.beforeNum ?: 0)) '+' else '-'}${item?.changeNum}"
                            }
                        }
                    }
                }
        }
    }
}