package com.heart.heartmerge.ui.fragments.message

import androidx.fragment.app.Fragment
import com.bdc.android.library.base.fragment.BaseCoreFragment
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.FragmentMessageBinding
import com.heart.heartmerge.extension.attach
import com.heart.heartmerge.extension.checkSelected
import com.heart.heartmerge.viewmodes.UserViewModel

class MessageFragment : BaseCoreFragment<FragmentMessageBinding, UserViewModel>() {

    override fun getLayoutId(): Int = R.layout.fragment_message
    override fun initView() {
        super.initView()
        val titles = arrayOf(
            getString(R.string.message_tabs_messages),
            getString(R.string.message_tabs_call),
            getString(R.string.message_tabs_followed)
        )
        mBinding.tab.setTitles(titles)
        initViewPager()
        mBinding.tab.setViewPager(mBinding.viewPager)
    }

    private fun initViewPager() {
        val fragments = mutableListOf<Fragment>()
        fragments.add(MyConversionListFragment())
        fragments.add(CallHistoryFragment.getInstance())
        fragments.add(FollowOrFansFragment.getInstance(RelationType.FOLLOW))
        mBinding.viewPager.attach(childFragmentManager, lifecycle, fragments) { position ->
            mBinding.tab.setCurrentTab(position)
        }
        mBinding.viewPager.checkSelected(0)
    }

    override fun bindListener() {
//        mBinding.flVideoHistory.click {
//            jump(VideoHistoryActivity::class.java)
//        }
    }

}