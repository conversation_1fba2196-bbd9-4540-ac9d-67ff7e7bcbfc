package com.heart.heartmerge.ui.activities.anchor.detail

import coil.load
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.DslViewHolder
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.GiftItemBean


/**
 * 作者：Lxf
 * 创建日期：2024/8/14 10:52
 * 描述：
 */
class AnchorGiftItem : DslAdapterItem() {
    init {
        itemLayoutId = R.layout.item_anchor_gifts
    }

    var giftItem: GiftItemBean? = null

    override fun onItemBind(
        itemHolder: DslViewHolder,
        itemPosition: Int,
        adapterItem: DslAdapterItem,
        payloads: List<Any>
    ) {
        super.onItemBind(itemHolder, itemPosition, adapterItem, payloads)
        itemHolder.apply {
            giftItem?.apply {
                img(R.id.gift_img)?.load(icon)
                tv(R.id.gift_name)?.text = "$title $total"
            }
        }
    }
}