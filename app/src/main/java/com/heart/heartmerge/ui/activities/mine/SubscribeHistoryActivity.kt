package com.heart.heartmerge.ui.activities.mine

import android.view.View
import androidx.core.content.ContextCompat
import androidx.lifecycle.asLiveData
import com.angcyo.dsladapter.DslAdapterItem
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.GoodsBean
import com.heart.heartmerge.beans.GoodsQueryType
import com.heart.heartmerge.databinding.ActivitySubscribeHistoryBinding
import com.heart.heartmerge.extension.formatDateTime
import com.heart.heartmerge.extension.openLoadMore
import com.heart.heartmerge.viewmodes.UserViewModel

class SubscribeHistoryActivity :
    BaseCoreActivity<ActivitySubscribeHistoryBinding, UserViewModel>() {
    override fun getLayoutId(): Int = R.layout.activity_subscribe_history

    override fun initView() {
        mBinding.refreshLayout.recyclerView.openLoadMore { }
        mBinding.refreshLayout.setOnRefreshListener { b, i ->
            fetch(b, i)
        }
        mBinding.refreshLayout.onRefresh()
    }

    private fun fetch(refresh: Boolean, pageIndex: Int) {
        val colors = listOf(R.color.color_9F2AF8, R.color.color_8F5CF4, R.color.color_28B8E4)
        mViewModel.getRechargeList(refresh, GoodsQueryType.SUBSCRIBE).asLiveData().observe(this) {
            mBinding.refreshLayout.append<DslAdapterItem>(refresh, it.records) {
                itemData = it
                itemLayoutId = R.layout.item_subscribe_history
                itemBindOverride = { itemHolder, _, _, _ ->
                    val item = itemData as? GoodsBean
                    itemHolder.v<View>(R.id.indicator)?.setBackgroundColor(
                        ContextCompat.getColor(
                            itemHolder.context, colors.random()
                        )
                    )
                    itemHolder.tv(R.id.tv_diamond)?.text = item?.desc
                    itemHolder.tv(R.id.tv_order_date)?.text = item?.createdAt?.formatDateTime()
                    itemHolder.tv(R.id.tv_price)?.text = "${item?.real_price}"

                    itemHolder.tv(R.id.tv_status)?.text =
                        if (item?.stat == 2) "Success" else "Unfinished"
                }
            }
        }
    }
}