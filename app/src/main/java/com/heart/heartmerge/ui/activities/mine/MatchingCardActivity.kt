package com.heart.heartmerge.ui.activities.mine

import android.os.Bundle
import androidx.lifecycle.asLiveData
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.bdc.android.library.extension.makeVisible
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.ActivityMatchingCardBinding
import com.heart.heartmerge.ui.activities.MainActivity
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.viewmodes.UserViewModel

class MatchingCardActivity : BaseCoreActivity<ActivityMatchingCardBinding, UserViewModel>() {

    override fun getLayoutId(): Int = R.layout.activity_matching_card

    override fun initView() {
        super.initView()
        mBinding.toolbar.setActionOnClickListener {
            jump(MatchingCardRecordActivity::class.java)
        }
        mBinding.tvMatch.click {
            jump(MainActivity::class.java, Bundle().apply {
                putInt(Constants.INTENT_PARAM_TAB_INDEX, 1)
            })
        }
    }

    override fun initData() {
        super.initData()
        mViewModel.fetchMatchingCardCount().asLiveData().observe(this) {
            mBinding.tvResidueNum.text = getString(R.string.matching_card_count, it)
            mBinding.ivMatchingCard.makeVisible(it > 0)
            mBinding.tvLabelMatchCard.makeVisible(it > 0)
            mBinding.ivMatchingCardNo.makeVisible(it <= 0)
            mBinding.tvMatchingCardNo.makeVisible(it <= 0)
        }
    }
}