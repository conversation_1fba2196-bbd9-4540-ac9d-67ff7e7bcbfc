package com.heart.heartmerge.ui.fragments.message

import androidx.fragment.app.viewModels
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.beans.VideoRecordInfo
import com.heart.heartmerge.extension.openLoadMore
import com.heart.heartmerge.ui.activities.message.VideoHistoryItem
import com.heart.heartmerge.ui.fragments.common.RecyclerViewFragment
import com.heart.heartmerge.viewmodes.VideoHistoryRequestEvent
import com.heart.heartmerge.viewmodes.VideoHistoryViewModel

/**
 * Author:Lxf
 * Create on:2024/8/5
 * Description:
 */
class CallHistoryFragment : RecyclerViewFragment() {

    private val viewModel by viewModels<VideoHistoryViewModel>()
    private val pageSize = 20
    private var mCursor: String = ""

    companion object {
        fun getInstance(): CallHistoryFragment {
            return CallHistoryFragment()
        }
    }

    override fun initView() {
        mBinding.refreshLayout.recyclerView.apply {
            setOnRefreshListener { b: <PERSON><PERSON><PERSON>, _: Int ->
                if (b) {
                    mCursor = ""
                }
                viewModel.cancelLoadAnchorJob()
                viewModel.videoHistoryList(mCursor, pageSize)
            }
            openLoadMore {
                viewModel.videoHistoryList(mCursor, pageSize)
            }
        }
    }

    override fun initData() {
        mBinding.refreshLayout.onRefresh()
    }

    override fun initViewEvents() {
        viewModel.pageEvents.observeEvent(this) {
            when (it) {
                is VideoHistoryRequestEvent.GetVideoHistorySuccess -> {
                    mBinding.refreshLayout.apply {
                        if (mCursor.isEmpty()) {
                            clearItems()
                        }
                        mCursor = it.cursor
                        append<VideoHistoryItem>(items = it.list) { data ->
                            recordInfo = data as VideoRecordInfo
                        }
                    }
                }

                is VideoHistoryRequestEvent.GetVideoHistoryFailed -> {
                    ToastUtil.show(it.msg)
                    mBinding.refreshLayout.loadFailedWithMore()
                }
            }
        }
    }
}