package com.heart.heartmerge.ui.activities.message

import coil.load
import coil.transform.CircleCropTransformation
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.DslViewHolder
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.VideoRecordInfo
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond
import com.heart.heartmerge.utils.AppUtil

/**
 * 作者：Lxf
 * 创建日期：2024/7/27 17:17
 * 描述：
 */
class MatchHistoryItem : DslAdapterItem() {
    init {
        itemLayoutId = R.layout.item_matching_record
    }

    var recordInfo: VideoRecordInfo? = null

    override fun onItemBind(
        itemHolder: DslViewHolder,
        itemPosition: Int,
        adapterItem: DslAdapterItem,
        payloads: List<Any>
    ) {
        super.onItemBind(itemHolder, itemPosition, adapterItem, payloads)
        itemHolder.apply {
            recordInfo?.apply {
                img(R.id.iv_header)?.load(headFileName) {
                    error(R.mipmap.ic_pic_default_oval)
                    placeholder(R.mipmap.ic_pic_default_oval)
                    transformations(CircleCropTransformation())
                }
                tv(R.id.tv_title)?.text = context.getString(R.string.title_match_with_anchor, nickName)
                tv(R.id.tv_time)?.text = AppUtil.translateCallDuration(videoTime)
                tv(R.id.tv_diamond)?.text = coilTotal.toShowDiamond().toString()
//                tv(R.id.tv_juan)?.text =
                tv(R.id.tv_create_time)?.text = AppUtil.translateDuration(createTime)
            }
        }
    }
}