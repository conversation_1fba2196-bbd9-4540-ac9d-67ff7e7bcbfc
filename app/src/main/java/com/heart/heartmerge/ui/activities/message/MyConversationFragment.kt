package com.heart.heartmerge.ui.activities.message

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.View.OnTouchListener
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.extension.buildImageUrl
import com.heart.heartmerge.extension.loadAnchorImage
import com.heart.heartmerge.extension.loadAvatar
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.popup.showMembershipSubscribePopup
import com.heart.heartmerge.ui.activities.anchor.detail.AnchorDetailActivity
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.ContextHolder
import com.heart.heartmerge.utils.PurchaseScene
import com.heart.heartmerge.utils.toJson
import com.heart.heartmerge.viewmodes.AnchorViewModel
import com.heart.heartmerge.viewmodes.SearchRequestEvent
import io.rong.imkit.IMCenter
import io.rong.imkit.conversation.ConversationFragment
import io.rong.imkit.conversation.extension.InputMode
import io.rong.imkit.model.UiMessage
import io.rong.imkit.utils.RouteUtils
import io.rong.imkit.widget.adapter.BaseAdapter
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation

/**
 * Author:Lxf
 * Create on:2024/9/14
 * Description:
 */
class MyConversationFragment : ConversationFragment() {
    private var isAddedHeader = false
    private var headerView: View? = null
    private var followed: Boolean = false //是否关注
    private var llConversationTip: View? = null;
    private val viewModel by viewModels<AnchorViewModel>()
    var mTargetId: String? = ""
    fun notifyList() {
        mAdapter.notifyItemChanged(0)
        mList.scrollToPosition(mAdapter.itemCount - 1)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initViewEvents()
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mRefreshLayout.setOnTouchListener { v, event ->
            closeExtensionPanel()
            false
        }
        mAdapter.setItemClickListener(object : BaseAdapter.OnItemClickListener {
            override fun onItemClick(view: View?, holder: ViewHolder?, position: Int) {
                closeExtensionPanel()
            }

            override fun onItemLongClick(view: View?, holder: ViewHolder?, position: Int): Boolean {
                return false
            }
        })
        mMessageViewModel?.isScrollToBottom = true
        llConversationTip = view.findViewById(R.id.ll_conversation_tip);
        llConversationTip?.click {
            activity?.let {
                showMembershipSubscribePopup(it, PurchaseScene.Chat(anchorId = mTargetId ?: ""));
            }
        }
        mTargetId = activity?.intent?.getStringExtra(RouteUtils.TARGET_ID).toString()
        if (mTargetId == Constants.RONG_YUN_ID_SYSTEM) {
            mRongExtension.makeGone()
            llConversationTip?.makeGone()
        } else if (mTargetId == Constants.RONG_YUN_ID_CUSTOM_SERVICE) {
            llConversationTip?.makeGone()
        } else {
            mMessageViewModel?.uiMessageLiveData?.observeForever(this.mListObserver)

            llConversationTip?.makeVisible(!MMKVDataRep.userInfo.isVIP)
        }

        if (!MMKVDataRep.userInfo.isVIP) {
            startVipTipAnimation()
        }

        FlowBus.with<Boolean>(Constants.SUBSCRIBE_SUCCESS).register(this) { //订阅成功
            if (it) {
                //充值成功 隐藏充值提醒
                llConversationTip?.makeGone()
            }
        }
    }

    private fun closeExtensionPanel() {
        mRongExtensionViewModel?.collapseExtensionBoard()
        val inputMode = mRongExtensionViewModel.inputModeLiveData.value
        if (inputMode == InputMode.EmoticonMode) {
            mRongExtensionViewModel.inputModeLiveData.postValue(InputMode.TextInput)
        }
    }

    private var mListObserver: Observer<List<UiMessage>> = Observer { uiMessages ->
        refreshList(uiMessages)
    }

    private fun refreshList(data: List<UiMessage>) {
        if (!isAddedHeader) {
            addHeaderView()
        }
        if (!mList.isComputingLayout && mList.scrollState == 0) {
            mAdapter.setDataCollection(data)
        } else {
            this.onScrollStopRefreshList = true
        }
    }

    private fun addHeaderView() {
        isAddedHeader = true
        headerView = LayoutInflater.from(activity)
            .inflate(R.layout.item_message_top_anchor_info, view as ViewGroup, false)
        headerView?.apply {
            val tmpLayoutParams = FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,  // 设置宽度为 match_parent
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            tmpLayoutParams.leftMargin =
                resources.getDimensionPixelSize(com.bdc.android.library.R.dimen.dp_16)
            tmpLayoutParams.rightMargin =
                resources.getDimensionPixelSize(com.bdc.android.library.R.dimen.dp_16)
            layoutParams = tmpLayoutParams

//            val targetId: String = activity?.intent?.getStringExtra(RouteUtils.TARGET_ID).toString()
//            val userInfo = RongUserInfoManager.getInstance().getUserInfo(targetId)
//            LogX.d("vvvvvvvvvvvvvvvvvvvvvvuserInfo: ${userInfo.extra}")
//            userInfo?.extra?.let {
//                val anchorInfo = it.fromJson<UserBean>()
//                refreshHeaderView(anchorInfo)
//            }

            addHeaderView(headerView)
            mAdapter.notifyItemChanged(0)
        }
//        notifyList()
    }

    fun refreshHeaderView(anchorInfo: UserBean?) {
        anchorInfo?.apply {
            headerView?.apply {
                click {
                    jump(
                        AnchorDetailActivity::class.java,
                        Bundle().apply {
                            putString(
                                Constants.INTENT_PARAM_KEY_ANCHOR_INFO,
                                anchorInfo.toJson()
                            )
                        })
                }
                findViewById<View>(R.id.ll_top_follow).click {
                    if (followed) {
                        followed = false
                        setFollowView()
                        viewModel.anchorUnfollow(anchorInfo.id)
                    } else {
                        followed = true
                        setFollowView()
                        anchorId.let {
                            viewModel.anchorFollow(anchorInfo.id)
                        }
                    }
                }
                findViewById<ImageView>(R.id.anchor_header).loadAvatar(avatar.buildImageUrl())
                findViewById<TextView>(R.id.anchor_name).text = nickname
                findViewById<TextView>(R.id.tv_age).text = "$age"
                anchorCountry?.title?.takeIf { it.isNotEmpty() }?.let {
                    findViewById<TextView>(R.id.tv_country).makeGone()
                    findViewById<TextView>(R.id.tv_country).text = it
                } ?: run {
                    findViewById<TextView>(R.id.tv_country).makeGone()
                }
                if (signature.isNotEmpty()) {
                    findViewById<TextView>(R.id.anchor_signature).makeVisible()
                    findViewById<TextView>(R.id.anchor_signature).text = signature
                }

                followed = showRelation == Constants.FOLLOW_FLAG_FOLLOWED
                findViewById<View>(R.id.ll_top_follow).makeVisible()
                setFollowView()

                val radio =
                    ContextHolder.context.resources.getDimension(com.bdc.android.library.R.dimen.dp_6)
                //图片
                val imageViews = listOf(
                    R.id.anchor_image1,
                    R.id.anchor_image2,
                    R.id.anchor_image3,
                    R.id.anchor_image4,
                    R.id.anchor_image5
                )
                var tmpIndex = 0
                anchorFileList?.forEach {
                    if (tmpIndex >= imageViews.size) return@forEach
                    if (it.fileType == Constants.ANCHOR_FILE_TYPE_PHOTO && it.isLock != "2") {
                        findViewById<ImageView>(imageViews[tmpIndex]).loadAnchorImage(it.fileUrl.buildImageUrl(), radio)
                        tmpIndex++
                    }

                }

                findViewById<LinearLayout>(R.id.ll_images).makeVisible(tmpIndex != 0)
            }
        }
    }

    private fun setFollowView() {
        if (followed) {
            val labelCancelFollow = getString(R.string.label_cancel_follow)
            headerView?.apply {
                findViewById<TextView>(R.id.tv_top_follow).text = labelCancelFollow
                findViewById<ImageView>(R.id.iv_top_follow).setImageResource(R.mipmap.ic_call_top_unfollow)
                findViewById<View>(R.id.ll_top_follow).setBackgroundResource(R.drawable.shape_call_top_unfollow_btn_bg)
            }
        } else {
            val labelFollow = getString(R.string.off_attention)
            headerView?.apply {
                findViewById<TextView>(R.id.tv_top_follow).text = labelFollow
                findViewById<ImageView>(R.id.iv_top_follow).setImageResource(R.mipmap.ic_call_top_follow)
                findViewById<View>(R.id.ll_top_follow).setBackgroundResource(R.drawable.shape_call_top_follow_btn_bg)
            }
        }
    }

    private fun initViewEvents() {
        viewModel.pageEvents.observeEvent(this) {
            when (it) {
                is SearchRequestEvent.AnchorFollowFailed -> {
                    ToastUtil.show(it.msg)
                    followed = false
                    setFollowView()
                }

                is SearchRequestEvent.AnchorUnFollowFailed -> {
                    ToastUtil.show(it.msg)
                    followed = true
                    setFollowView()
                }

                else -> {}
            }
        }
    }

    fun clearMsg(targetId: String?) {
        IMCenter.getInstance().cleanHistoryMessages(
            Conversation.ConversationType.PRIVATE,
            targetId,
            0,
            true,
            object :
                RongIMClient.OperationCallback() {
                override fun onSuccess() {
                    LogX.i("clear message suc")
                }

                override fun onError(errorCode: RongIMClient.ErrorCode?) {
                    ToastUtil.show(getString(R.string.clear_msg_failed))
                }

            })
        mAdapter?.setDataCollection(emptyList())
    }

    fun startVipTipAnimation() {
        llConversationTip?.let {
            // 缩放动画（放大到1.2倍再恢复）
            val scaleUpX = ObjectAnimator.ofFloat(it, "scaleX", 1f, 1.2f)
            val scaleUpY = ObjectAnimator.ofFloat(it, "scaleY", 1f, 1.2f)
            val scaleDownX = ObjectAnimator.ofFloat(it, "scaleX", 1.2f, 1f)
            val scaleDownY = ObjectAnimator.ofFloat(it, "scaleY", 1.2f, 1f)

            // 透明度动画（增强视觉效果）
//            val fadeOut = ObjectAnimator.ofFloat(it, "alpha", 1f, 0.8f)
//            val fadeIn = ObjectAnimator.ofFloat(it, "alpha", 0.8f, 1f)
            // 动画时长
            scaleUpX.duration = 300
            scaleUpY.duration = 300
            scaleDownX.duration = 300
            scaleDownY.duration = 300
//            fadeOut.duration = 300
//            fadeIn.duration = 300
            // 动画集合
            val animatorSet = AnimatorSet()
            animatorSet.play(scaleUpX).with(scaleUpY)
            animatorSet.play(scaleDownX).with(scaleDownY).after(scaleUpX)

            // 循环2次逻辑
            var repeatCount = 0 // 循环次数计数

            animatorSet.addListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {}
                override fun onAnimationEnd(animation: Animator) {
                    repeatCount++
                    if (repeatCount < 2) { // 只循环2次
                        animatorSet.start()
                    }
                }

                override fun onAnimationCancel(animation: Animator) {}
                override fun onAnimationRepeat(animation: Animator) {}
            })

            animatorSet.start()
        }
    }
}