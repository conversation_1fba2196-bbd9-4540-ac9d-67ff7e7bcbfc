package com.heart.heartmerge.ui.widget.floatwindow

import android.app.Activity
import android.app.Application.ActivityLifecycleCallbacks
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.MotionEvent
import android.view.Window
import androidx.appcompat.app.AppCompatActivity
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.extension.jumpThenFinish
import com.bdc.android.library.utils.ActivityManager
import com.bdc.android.library.utils.ToastManager
import com.heart.heartmerge.R
import com.heart.heartmerge.popup.showNormalNewPopup
import com.heart.heartmerge.socket.WebSocketManager
import com.heart.heartmerge.ui.activities.login.LoginActivity
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.Constants
import java.util.concurrent.atomic.AtomicBoolean


/**
 * Author:Lxf
 * Create on:2024/8/1
 * Description:
 */

class BaseActivityLifecycleCallbacks : ActivityLifecycleCallbacks, ActivityState {
    //默认被初始化状态，被系统回收(强杀)状态
    var mAppStatus: Int = STATUS_FORCE_KILLED

    var activityList: MutableList<Activity> = mutableListOf()
    var resumeActivity: MutableList<Activity> = mutableListOf()
    var tokenExpiredStatus = AtomicBoolean(false)
    private val lock = Any()
    private var lastHandledTime = 0L

    override fun onActivityCreated(activity: Activity, bundle: Bundle?) {
        val simpleName = activity::class.java.simpleName
        Log.i("ActivityLifecycle", "onActivityCreated  $simpleName")

        WebSocketManager.getInstance().updateHeartbeatDataPage(simpleName)

        activityList.add(0, activity)
        ActivityManager.addActivity(activity)
        // 为每个Activity的Window设置一个自定义的Callback，以拦截触摸事件
        val originalCallback = activity.window.callback
        activity.window.callback = object : Window.Callback by originalCallback {
            override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
                if (event?.action == MotionEvent.ACTION_DOWN) {
                    // 在触摸事件发生时更新全局时间戳
                    WebSocketManager.getInstance().updateLastAppActionTimestamp(System.currentTimeMillis())
                }
                return originalCallback.dispatchTouchEvent(event)
            }
        }
    }

    override fun onActivityStarted(activity: Activity) {
        Log.i("ActivityLifecycle", "onActivityStarted " + activity.localClassName)

        activity.takeIf { it is AppCompatActivity }?.let { it as AppCompatActivity }?.let {
            FlowBus.with<Int>(Constants.TOKEN_EXPIRED).register(it) {
                synchronized(lock) {
                    val currentTime = System.currentTimeMillis()
                    if (tokenExpiredStatus.compareAndSet(
                            false, true
                        ) && currentTime - lastHandledTime > 1000
                    ) {
                        if (activity !is LoginActivity) {
                            lastHandledTime = currentTime
                            showNormalNewPopup(
                                ActivityManager.current as? Activity ?: activity,
                                R.mipmap.ic_dialog_warning,
                                title = activity.getString(R.string.remind),
                                content = activity.getString(R.string.token_expired),
                                btnSure = activity.getString(R.string.confirm),
                                btnCancel = "",
                                dismissOnTouchOutside = false,
                                dismissOnBackPressed = false,
                                block = {
                                    AppUtil.clearCache()
                                    activity.jumpThenFinish(LoginActivity::class.java)
                                    ActivityManager.clearAll()
                                })
                        }
                    }
                }
            }
        }
    }

    override fun onActivityResumed(activity: Activity) {
        Log.i(
            "ActivityLifecycle",
            "onActivityResumed ${activity.localClassName}, ${resumeActivity.size}"
        )
        if (!resumeActivity.contains(activity)) {
            resumeActivity.add(activity)
            if (resumeActivity.size == 1) {
                //回到前台 从新开启循环
            }
            restartSingleInstanceActivity(activity)
        }
    }

    override fun onActivityPaused(activity: Activity) {
        Log.i("ActivityLifecycle", "onActivityPaused " + activity.localClassName)
    }

    override fun onActivityStopped(activity: Activity) {
        Log.i("ActivityLifecycle", "onActivityStopped " + activity.localClassName)
        resumeActivity.remove(activity)
        if (resumeActivity.isEmpty()) {
            Log.e("ActivityLifecycle", "在后台了")
        }
    }

    override fun onActivitySaveInstanceState(activity: Activity, bundle: Bundle) {
        Log.e("ActivityLifecycle", "onActivitySaveInstanceState " + activity.localClassName)
    }

    override fun onActivityDestroyed(activity: Activity) {
        Log.e("ActivityLifecycle", "onActivityDestroyed " + activity.localClassName)
        activityList.remove(activity)
        ActivityManager.removeActivity(activity)
        tokenExpiredStatus.set(false)
        ToastManager.cancelAll()
    }

    override fun current(): Activity? {
        return if (activityList.size > 0) activityList[0] else null
    }

    override fun activityList(): List<Activity> {
        return activityList
    }

    override fun count(): Int {
        return activityList.size
    }

    override fun isFront(): Boolean {
        return resumeActivity.size > 0
    }

    /**
     * 跳转到目标activity
     *
     * @param cls
     */
    fun skipToTarget(cls: Class<*>?) {
        if (activityList.size > 0) {
            current()!!.startActivity(Intent(current(), cls))
            for (activity in activityList) {
                activity.finish()
            }
        }
    }

    /**
     * finish target activity
     *
     * @param cls
     */
    fun finishTarget(cls: Class<*>) {
        if (activityList.isNotEmpty()) {
            for (activity in activityList) {
                if (activity.javaClass == cls) {
                    activity.finish()
                }
            }
        }
    }

    /**
     * 判断app是否在前台
     *
     * @return
     */
    fun isOnForeground(): Boolean {
        return resumeActivity.isNotEmpty()
    }


    /**
     * 用于按下home键，点击图标，检查启动模式是singleInstance，且在activity列表中首位的Activity
     * 下面的方法，专用于解决启动模式是singleInstance, 为开启悬浮框的情况
     *
     * @param activity
     */
    private fun restartSingleInstanceActivity(activity: Activity) {
        val isClickByFloat = activity.intent.getBooleanExtra("isClickByFloat", false)
        if (isClickByFloat) {
            return
        }
        //刚启动，或者从桌面返回app
        if (resumeActivity.size == 1) {
            return
        }
        //至少需要activityList中至少两个activity
        if (resumeActivity.size >= 1 && activityList.size > 1) {
            val a = getOtherTaskSingleInstanceActivity(resumeActivity[0].taskId)
            if (//当前activity和列表中首个activity不相同
                a != null && !a.isFinishing && a !== activity && a.taskId != activity.taskId) {
                Log.e("ActivityLifecycle", "启动了activity = " + a.javaClass.name)
                activity.startActivity(Intent(activity, a.javaClass))
            }
        }
    }

    private fun getOtherTaskSingleInstanceActivity(taskId: Int): Activity? {
        if (taskId != 0 && activityList.size > 1) {
            for (activity in activityList) {
                if (activity.taskId != taskId) {
                    if (isTargetSingleInstance(activity)) {
                        return activity
                    }
                }
            }
        }
        return null
    }

    private fun isTargetSingleInstance(activity: Activity?): Boolean {
        if (activity == null) {
            return false
        }
        val title = activity.title/* if(TextUtils.equals(title, activity.getString(R.string.demo_activity_label_video_call))
                      || TextUtils.equals(title, activity.getString(R.string.demo_activity_label_multi_call))) {
                  return true;
              }*/
        return false
    }

    /**
     * 此方法用于设置启动模式为singleInstance的activity调用
     * 用于解决点击悬浮框后，然后finish当前的activity，app回到桌面的问题
     * 需要如下两个权限：
     * <uses-permission android:name="android.permission.GET_TASKS"></uses-permission>
     * <uses-permission android:name="android.permission.REORDER_TASKS"></uses-permission>
     *
     * @param activity
     */
    fun makeMainTaskToFront(activity: Activity) {
        //当前activity正在finish，且可见的activity列表中只有这个正在finish的activity,且没有销毁的activity个数大于等于2
        if (activity.isFinishing && resumeActivity.size == 1 && resumeActivity[0] === activity && activityList.size > 1) {
            val manager: android.app.ActivityManager =
                activity.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
            val runningTasks: List<android.app.ActivityManager.RunningTaskInfo> =
                manager.getRunningTasks(20)
            for (i in runningTasks.indices) {
                val taskInfo: android.app.ActivityManager.RunningTaskInfo = runningTasks[i]
                val topActivity: ComponentName? = taskInfo.topActivity
                //判断是否是相同的包名
                if (topActivity != null && topActivity.packageName == activity.packageName) {
                    val taskId: Int = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        taskInfo.taskId
                    } else {
                        taskInfo.id
                    }
                    //将任务栈置于前台
                    Log.e(
                        "ActivityLifecycle",
                        "执行moveTaskToFront，current activity:" + activity.javaClass.name
                    )
                    manager.moveTaskToFront(taskId, android.app.ActivityManager.MOVE_TASK_WITH_HOME)
                }
            }
        }
    }

    companion object {
        const val STATUS_FORCE_KILLED: Int = -1 //应用放在后台被强杀了
        const val STATUS_NORMAL: Int = 1 //APP正常态
    }
}

interface ActivityState {
    /**
     * 得到当前Activity
     *
     * @return
     */
    fun current(): Activity?

    /**
     * 得到Activity集合
     *
     * @return
     */
    fun activityList(): List<Activity>

    /**
     * 任务栈中Activity的总数
     *
     * @return
     */
    fun count(): Int

    /**
     * 判断应用是否处于前台，即是否可见
     *
     * @return
     */
    fun isFront(): Boolean
}