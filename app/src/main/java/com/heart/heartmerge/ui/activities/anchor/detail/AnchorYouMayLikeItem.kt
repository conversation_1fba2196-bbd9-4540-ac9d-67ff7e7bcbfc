package com.heart.heartmerge.ui.activities.anchor.detail

import android.os.Bundle
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.DslViewHolder
import com.bdc.android.library.extension.jump
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.extension.loadAnchorImage
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.toJson


/**
 * 作者：Lxf
 * 创建日期：2024/8/14 10:52
 * 描述：
 */
class AnchorYouMayLikeItem : DslAdapterItem() {
    init {
        itemLayoutId = R.layout.item_anchor_you_may_like
    }

    var mayLikeItem: UserBean? = null

    override fun onItemBind(
        itemHolder: DslViewHolder,
        itemPosition: Int,
        adapterItem: DslAdapterItem,
        payloads: List<Any>
    ) {
        super.onItemBind(itemHolder, itemPosition, adapterItem, payloads)
        itemHolder.apply {
            mayLikeItem?.let {item->
                clickItem {
                    jump(AnchorDetailActivity::class.java, Bundle().apply {
                        putString(
                            Constants.INTENT_PARAM_KEY_ANCHOR_INFO,
                            item.toJson()
                        )
                    })
                }
                img(R.id.anchor_header)?.loadAnchorImage(item.avatar)
                tv(R.id.anchor_name)?.text = item.nickname
                tv(R.id.anchor_country)?.text = item.anchorCountry?.title
            }
        }
    }
}