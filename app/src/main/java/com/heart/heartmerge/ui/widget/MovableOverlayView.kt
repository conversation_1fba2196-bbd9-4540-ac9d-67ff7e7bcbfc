package com.heart.heartmerge.ui.widget

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2024/11/30 11:05
 * @description :
 */

import android.animation.ObjectAnimator
import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.FrameLayout
import com.bdc.android.library.imageloader.ImageLoader
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.GiftItemBean
import com.heart.heartmerge.databinding.ViewMovableOverlayBinding

class MovableOverlayView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    val binding = ViewMovableOverlayBinding.inflate(LayoutInflater.from(this.context), this, true)

    /**
     * 设置显示的文字内容
     */
    fun setGift(gift: GiftItemBean) {
        binding.tvTitle.text =
            context.getString(R.string.gave_a_gift_to, gift.userNickName, gift.anchorNickName)
        ImageLoader.with(this).load(gift.icon).into(binding.ivGift)
        binding.tvGiftNum.text = "x${gift.giftNum}"
    }

    /**
     * 显示并执行动画
     * @param activity 当前 Activity
     * @param direction 动画方向: "rightToLeft", "leftToRight", "topToBottom"
     * @param duration 动画时长（毫秒）
     */
    fun show(
        activity: Activity,
        gift: GiftItemBean,
        direction: String = "rightToLeft",
        duration: Long = 10000
    ) {
        setGift(gift)
        // 动态获取根布局
        val rootLayout = activity.window.decorView.findViewById<ViewGroup>(android.R.id.content)
            ?: throw IllegalStateException("Cannot find root layout in the current activity.")

        // 确保布局已完成
        rootLayout.post {
            // 动态设置初始位置
            var startX = 0f
            var startY = 0f
            when (direction) {
                "rightToLeft" -> {
                    startX = rootLayout.width.toFloat() // 屏幕右侧外
//                    startY = (rootLayout.height / 2f) - (height / 2f) // 垂直居中
                    startY = 200f // 垂直居中
                }

                "leftToRight" -> {
                    startX = -width.toFloat() // 屏幕左侧外
                    startY = (rootLayout.height / 2f) - (height / 2f) // 垂直居中
                }

                "topToBottom" -> {
                    startX = (rootLayout.width / 2f) - (width / 2f) // 水平居中
                    startY = -height.toFloat() // 屏幕顶部外
                }
            }

            // 设置初始位置
            this.x = startX
            this.y = startY

            // 将 View 添加到根布局
            rootLayout.addView(this)

            // 执行动画
            val animator: ObjectAnimator? = when (direction) {
                "rightToLeft" -> ObjectAnimator.ofFloat(
                    this, "translationX", startX, -width.toFloat() - rootLayout.width.toFloat()
                )

                "leftToRight" -> ObjectAnimator.ofFloat(
                    this, "translationX", startX, rootLayout.width.toFloat()
                )

                "topToBottom" -> ObjectAnimator.ofFloat(
                    this, "translationY", startY, rootLayout.height.toFloat()
                )

                else -> null
            }

            animator?.apply {
                this.duration = duration
                start()
            }

            // 动画结束后移除 View
            postDelayed({ removeFromParent() }, duration)
        }
    }

    /**
     * 从父布局中移除自己
     */
    private fun removeFromParent() {
        (parent as? ViewGroup)?.removeView(this)
    }
}

