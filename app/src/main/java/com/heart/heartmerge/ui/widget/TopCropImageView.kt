package com.heart.heartmerge.ui.widget

import android.content.Context
import android.graphics.Matrix
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView

class TopCropImageView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {

    override fun setFrame(l: Int, t: Int, r: Int, b: Int): Boolean {
        val drawable = drawable ?: return super.setFrame(l, t, r, b)

        val viewWidth = width.toFloat()
        val viewHeight = height.toFloat()

        val drawableWidth = drawable.intrinsicWidth.toFloat()
        val drawableHeight = drawable.intrinsicHeight.toFloat()

        val scale = viewWidth / drawableWidth
        val scaledHeight = drawableHeight * scale

        val dx = 0f
        val dy = 0f // 从顶部对齐

        val matrix = Matrix().apply {
            setScale(scale, scale)
            postTranslate(dx, dy)
        }

        imageMatrix = matrix
        scaleType = ScaleType.MATRIX

        return super.setFrame(l, t, r, b)
    }
}

