package com.heart.heartmerge.ui.widget.level

import com.heart.heartmerge.R
import com.heart.heartmerge.beans.Quadruple

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/7/4 10:45
 * @description :
 */

data class LevelStyle(
    val range: IntRange,
    val labelRes: Pair<Int, Int>? = null,
    val cardRes: Quadruple<Int, Int, Int, Int>? = null
)

object LevelConfig {

    var LEVEL_STEP = 5//等级步长
    var LEVEL_MAX = 35

    val ranges = generateLevelRanges(LEVEL_MAX, LEVEL_STEP)

    val styles: List<LevelStyle> by lazy {
        ranges.mapIndexed { index, range ->
            LevelStyle(
                range = range, labelRes = when (index) {
                    0 -> Pair(R.color.color_level_1, R.mipmap.bg_level_label_1)
                    1 -> Pair(R.color.color_level_11, R.mipmap.bg_level_label_11)
                    2 -> Pair(R.color.color_level_21, R.mipmap.bg_level_label_21)
                    3 -> Pair(R.color.color_level_31, R.mipmap.bg_level_label_31)
                    4 -> Pair(R.color.color_level_41, R.mipmap.bg_level_label_41)
                    5 -> Pair(R.color.color_level_51, R.mipmap.bg_level_label_51)
                    else -> Pair(R.color.color_level_61, R.mipmap.bg_level_label_61)
                }, cardRes = when (index) {
                    0 -> Quadruple(
                        R.color.color_level_1,
                        R.mipmap.ic_label_level_1,
                        R.mipmap.bg_level_1,
                        R.drawable.progress_drawable_level_1
                    )

                    1 -> Quadruple(
                        R.color.color_level_11,
                        R.mipmap.ic_label_level_11,
                        R.mipmap.bg_level_11,
                        R.drawable.progress_drawable_level_11
                    )

                    2 -> Quadruple(
                        R.color.color_level_21,
                        R.mipmap.ic_label_level_21,
                        R.mipmap.bg_level_21,
                        R.drawable.progress_drawable_level_21
                    )

                    3 -> Quadruple(
                        R.color.color_level_31,
                        R.mipmap.ic_label_level_31,
                        R.mipmap.bg_level_31,
                        R.drawable.progress_drawable_level_31
                    )

                    4 -> Quadruple(
                        R.color.color_level_41,
                        R.mipmap.ic_label_level_41,
                        R.mipmap.bg_level_41,
                        R.drawable.progress_drawable_level_41
                    )

                    5 -> Quadruple(
                        R.color.color_level_51,
                        R.mipmap.ic_label_level_51,
                        R.mipmap.bg_level_51,
                        R.drawable.progress_drawable_level_51
                    )

                    else -> Quadruple(
                        R.color.color_level_61,
                        R.mipmap.ic_label_level_61,
                        R.mipmap.bg_level_61,
                        R.drawable.progress_drawable_level_61
                    )
                }
            )
        }
    }

    fun getStyleForLevel(level: Int): LevelStyle? {
        return styles.firstOrNull { level in it.range }
    }

    private fun generateLevelRanges(maxLevel: Int, step: Int): List<IntRange> {
        val ranges = mutableListOf<IntRange>()
        var start = 0
        while (start <= maxLevel) {
            val end = if (start == 0) {
                step.coerceAtMost(maxLevel)
            } else {
                (start + step - 1).coerceAtMost(maxLevel)
            }
            ranges.add(start..end)
            start = end + 1
        }
        return ranges
    }
}