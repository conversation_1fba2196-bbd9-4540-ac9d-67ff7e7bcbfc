package com.heart.heartmerge.ui.fragments.home

import androidx.core.content.ContextCompat
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.DslViewHolder
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.LanguageBean

/**
 * 作者：Lxf
 * 创建日期：2024/7/27 17:17
 * 描述：
 */
class HomeLanguagesItem : DslAdapterItem() {
    init {
        itemLayoutId = R.layout.item_home_language_filter
    }

    var language: LanguageBean? = null

    override fun onItemBind(
        itemHolder: DslViewHolder,
        itemPosition: Int,
        adapterItem: DslAdapterItem,
        payloads: List<Any>
    ) {
        super.onItemBind(itemHolder, itemPosition, adapterItem, payloads)
        itemHolder.apply {
            clickItem {
                updateItemSelect(true)
            }
            language?.apply {
                tv(R.id.label_language)?.text = languageName
                if (itemIsSelected) {
                    tv(R.id.label_language)?.setTextColor(ContextCompat.getColor(context, R.color.color_language_sel))
                    tv(R.id.label_language)?.background = (ContextCompat.getDrawable(context, R.drawable.shape_home_language_item_sel))
                } else {
                    tv(R.id.label_language)?.setTextColor(ContextCompat.getColor(context, R.color.color_8F94C7))
                    tv(R.id.label_language)?.background = (ContextCompat.getDrawable(context, R.drawable.shape_home_language_item_normal))
                }
            }
        }
    }
}