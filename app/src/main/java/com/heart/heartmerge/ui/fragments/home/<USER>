package com.heart.heartmerge.ui.fragments.home

import android.graphics.Paint
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import coil.load
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.DslViewHolder
import com.angcyo.dsladapter.isVisible
import com.bdc.android.library.extension.jump
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.extension.setDrawableLeft
import com.bdc.android.library.extension.setTextCompatColor
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.extension.buildImageUrl
import com.heart.heartmerge.extension.loadAnchorImageWithBlur
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.popup.showMembershipSubscribePopup
import com.heart.heartmerge.popup.showNormalNewPopup
import com.heart.heartmerge.ui.activities.MainActivity
import com.heart.heartmerge.ui.activities.anchor.AnchorVideoActivity
import com.heart.heartmerge.ui.activities.anchor.detail.AnchorDetailActivity
import com.heart.heartmerge.ui.activities.message.MyRongConversationActivity
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.PurchaseScene
import com.heart.heartmerge.utils.toJson
import com.heart.heartmerge.viewmodes.AnchorQueryType
import com.opensource.svgaplayer.SVGAImageView
import com.opensource.svgaplayer.SVGAParser
import com.opensource.svgaplayer.SVGAVideoEntity
import io.rong.imlib.model.Conversation

/**
 * 作者：Lxf
 * 创建日期：2024/7/27 17:17
 * 描述：
 */
class HomeAnchorItem : DslAdapterItem() {
    init {
        itemLayoutId = R.layout.item_home_anchor
        itemSpanCount = 1
    }

    var anchorInfo: UserBean? = null
    var anchorType: AnchorQueryType? = null

    var isLocked = false

    override fun onItemViewAttachedToWindow(itemHolder: DslViewHolder, itemPosition: Int) {
        super.onItemViewAttachedToWindow(itemHolder, itemPosition)
        val svgaImageView = itemHolder.view(R.id.anchor_video) as SVGAImageView
        if (svgaImageView.isVisible()) {
            // 检查Lifecycle状态，确保只在活跃状态下启动动画
            (itemHolder.context as? LifecycleOwner)?.lifecycle?.let { lifecycle ->
                if (lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
                    svgaImageView.startAnimation()
                }
            }
        }
    }

    override fun onItemViewDetachedToWindow(itemHolder: DslViewHolder, itemPosition: Int) {
        if (lifecycle.currentState != Lifecycle.State.DESTROYED) {
            super.onItemViewDetachedToWindow(itemHolder,itemPosition)
        }
        val svgaImageView = itemHolder.view(R.id.anchor_video) as SVGAImageView
        if (svgaImageView.isVisible()) {
            // 检查Lifecycle状态，确保只在活跃状态下启动动画
            (itemHolder.context as? LifecycleOwner)?.lifecycle?.let { lifecycle ->
                if (svgaImageView.isVisible()) {
                    svgaImageView.stopAnimation()
                }
            }
        }
    }

    override fun onItemBind(
        itemHolder: DslViewHolder,
        itemPosition: Int,
        adapterItem: DslAdapterItem,
        payloads: List<Any>
    ) {
        super.onItemBind(itemHolder, itemPosition, adapterItem, payloads)
        itemHolder.apply {
            anchorInfo?.apply {
//                v<View>(R.id.placeholder_container)?.apply {
//                    makeVisible(!MMKVDataRep.userInfo.isVIP && tabIndex == 1)
//                    img(R.id.iv_placeholder)?.loadAnchorImageWithBlur(headFileName)
//                }

                //非会员New分类下高斯模糊显示
                if (isLocked) {
                    v<View>(R.id.placeholder_container)?.apply {
                        makeVisible()
                        img(R.id.iv_placeholder)?.loadAnchorImageWithBlur(avatar.buildImageUrl())
                    }
                } else {
                    v<View>(R.id.placeholder_container)?.makeGone()
                    img(R.id.anchor_image)?.load(avatar.buildImageUrl(list = true)) {
                        crossfade(true)
                        placeholder(R.mipmap.ic_default_anchor_bg)
                        error(R.mipmap.ic_default_anchor_bg)
                    }
                }

                clickItem { v ->
                    //非vip点击New item 查看详情
                    if (isLocked) {
                        showNormalNewPopup(
                            itemHolder.context as AppCompatActivity,
                            R.mipmap.ic_dialog_warning,
                            title = itemHolder.context.getString(R.string.unlock_her_secret),
                            content = itemHolder.context.getString(R.string.unlock_her_secret_content),
                            btnSure = itemHolder.context.getString(R.string.become_vip_to_unlock),
                            btnCancel = itemHolder.context.getString(R.string.cancel),
                            mainColor = R.color.color_EC12E2,
                            block = {
                                showMembershipSubscribePopup(
                                    itemHolder.context as AppCompatActivity, PurchaseScene.HomeNew
                                )
                            },
                        ) {}
                    } else {
                        jump(
                            AnchorDetailActivity::class.java, Bundle().apply {
                                putString(
                                    Constants.INTENT_PARAM_KEY_ANCHOR_INFO, anchorInfo?.toJson()
                                )
                            })
                    }
                }

                tv(R.id.anchor_name)?.text = nickname

                tv(R.id.tv_age)?.text = "$age"

                img(R.id.iv_top)?.makeVisible(isTop == "1")
                img(R.id.iv_star)?.makeVisible(hotAnchor == "1")

                //本地load国家图片
                if (anchorCountry?.title?.isNotEmpty() == true) {
                    AppUtil.setLocalCountryImage(anchorCountry.code, img(R.id.iv_country), 8f)
                }

                tv(R.id.videoPrice)?.apply {
                    text = String.format(
                        context.getString(R.string.label_diamond_every_min),
                        (anchorLevel?.discountPrice ?: Constants.VIDEO_DEFAULT_PRICE).toShowDiamond()
                    )
                }

                tv(R.id.tv_origin_price)?.apply {
                    makeVisible(MMKVDataRep.userInfo.isVIP)
                    text = String.format(
                        context.getString(R.string.label_diamond_every_min),
                        (anchorLevel?.callPrice ?: Constants.VIDEO_DEFAULT_PRICE).toShowDiamond()
                    )
                    paint.flags = Paint.STRIKE_THRU_TEXT_FLAG
                }
                tv(R.id.iv_online_status)?.apply {
                    when (status) {
                        "0", "3", "4" -> {
                            setTextCompatColor(R.color.color_DADADA)
                            text = context.getString(R.string.off_line)
                            setDrawableLeft(R.drawable.shape_offline_dot, 3)
                        }

                        "1" -> {
                            setTextCompatColor(R.color.color_21C76E)
                            text = context.getString(R.string.on_line)
                            setDrawableLeft(R.drawable.shape_online_dot, 3)
                        }

                        "2" -> {
                            setTextCompatColor(R.color.color_F53D3D)
                            text = context.getString(R.string.status_busy)
                            setDrawableLeft(R.drawable.shape_busy_dot, 3)
                        }
                    }
                }

                view(R.id.anchor_video_or_message)?.apply {
                    when (status) {
                        "1" -> {
                            img(R.id.anchor_message)?.makeGone()
                            val sVGAImageView: SVGAImageView =
                                view(R.id.anchor_video) as SVGAImageView
                            sVGAImageView.makeVisible()
                            val parser = SVGAParser(context)
                            parser.decodeFromAssets(
                                "ic_video_small.svga", object : SVGAParser.ParseCompletion {
                                    override fun onComplete(videoItem: SVGAVideoEntity) {
                                        sVGAImageView.setVideoItem(videoItem)
                                        sVGAImageView.startAnimation()
                                    }

                                    override fun onError() {
                                        // Handle error
                                    }
                                })
                            click(this) {
                                itemDslAdapter?._recyclerView?.context?.let {
                                    AppUtil.checkoutDiamondAndShowPopup(
                                        it as MainActivity,
                                        anchorLevel?.discountPrice ?: Constants.VIDEO_DEFAULT_PRICE,
                                        purchaseScene = PurchaseScene.VideoCallHome(
                                            anchorId = anchorInfo?.id ?: ""
                                        )
                                    ) {
                                        AppUtil.requestVideoPermission(
                                            context, onGrantedCallBack = {
                                                jump(
                                                    AnchorVideoActivity::class.java,
                                                    Bundle().apply {
                                                        putString(
                                                            Constants.INTENT_PARAM_KEY_ANCHOR_INFO,
                                                            anchorInfo?.toJson()
                                                        )
                                                    })
                                            }) {}
                                    }
                                }
                            }
                        }

                        else -> {
                            img(R.id.anchor_message)?.makeVisible()
                            img(R.id.anchor_video)?.makeGone()
                            click(this) {
                                if (!MMKVDataRep.userInfo.isVIP && isLocked) {
                                    showNormalNewPopup(
                                        itemHolder.context as AppCompatActivity,
                                        R.mipmap.ic_dialog_warning,
                                        title = itemHolder.context.getString(R.string.unlock_her_secret),
                                        content = itemHolder.context.getString(R.string.unlock_her_secret_content),
                                        btnSure = itemHolder.context.getString(R.string.become_vip_to_unlock),
                                        btnCancel = itemHolder.context.getString(R.string.cancel),
                                        mainColor = R.color.color_EC12E2,
                                        block = {
                                            showMembershipSubscribePopup(
                                                itemHolder.context as AppCompatActivity,
                                                PurchaseScene.HomeNew
                                            )
                                        },
                                    ) {}
                                } else {
                                    jump(MyRongConversationActivity::class.java, Bundle().apply {
                                        putString("targetId", anchorInfo?.id)
                                        putString(
                                            "ConversationType",
                                            Conversation.ConversationType.PRIVATE.name
                                        )
                                    })
                                }
                            }
                        }
                    }
                }

            }
        }
    }
}