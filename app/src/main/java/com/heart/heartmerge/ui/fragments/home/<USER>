package com.heart.heartmerge.ui.fragments.home

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.TextView
import com.bdc.android.library.flowlayout.FlowLayout
import com.bdc.android.library.flowlayout.FlowLayoutAdapter
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.LanguageBean

/**
 * Author:Lxf
 * Create on:2024/7/27
 * Description:
 */
class LanguageFlowAdapter() : FlowLayoutAdapter() {
    private var dataList: MutableList<LanguageBean> = mutableListOf()
    private var mCheckedPosition = -1

    fun setCheckedPosition(checkedPosition: Int) {
        if (this.mCheckedPosition == checkedPosition) {
            this.mCheckedPosition = -1
        } else {
            this.mCheckedPosition = checkedPosition
        }
        notifyChange()
    }

    fun setNewData(datas: List<LanguageBean>) {
        dataList.apply {
            clear()
            addAll(datas)
        }
        notifyChange()
    }

    fun addData(datas: List<LanguageBean>) {
        dataList.addAll(datas)
        notifyChange()
    }


    override fun createView(context: Context, flowLayout: FlowLayout, position: Int): View {
        val textView = TextView(context)
        textView.text = dataList[position].languageName
        textView.textSize = 13f
        if (position == mCheckedPosition) {
            textView.setTextColor(context.resources.getColor(R.color.color_white))
            textView.setBackgroundResource(R.drawable.shape_home_language_item_sel)
        } else {
            textView.setTextColor(context.resources.getColor(R.color.color_language_sel))
            textView.setBackgroundResource(R.drawable.shape_home_language_item_normal)
        }
        val params =
            MarginLayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
        //        params.topMargin = dip2px(context, 6);
//        params.leftMargin = dip2px(context, 3);
//        params.rightMargin = dip2px(context, 3);
        textView.layoutParams = params
        val horizontalMargin = dip2px(context, 16f)
        val vertical = dip2px(context, 6f)
        textView.setPadding(horizontalMargin, vertical, horizontalMargin, vertical)
        return textView
    }

    override fun getItemCount(): Int = dataList.size

    override fun getItem(position: Int): Any = dataList[position]

    fun dip2px(context: Context, dpValue: Float): Int {
        val scale = context.resources.displayMetrics.density
        return (dpValue * scale + 0.5f).toInt()
    }
}