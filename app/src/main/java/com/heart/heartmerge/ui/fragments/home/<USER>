package com.heart.heartmerge.ui.fragments.home

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.angcyo.dsladapter.DslLoadMoreItem.Companion.LOAD_MORE_ERROR
import com.angcyo.dsladapter.DslLoadMoreItem.Companion.LOAD_MORE_NORMAL
import com.angcyo.dsladapter.DslLoadMoreItem.Companion.LOAD_MORE_NO_MORE
import com.bdc.android.library.base.fragment.BaseCoreFragment
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.imageloader.ImageLoader
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.mvi.observeState
import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.databinding.FragmentHomeNewBinding
import com.heart.heartmerge.extension.buildImageUrl
import com.heart.heartmerge.extension.openLoadMore
import com.heart.heartmerge.lce.BaseRequestEvent
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.ui.activities.mine.MembershipCenterActivity
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.PurchaseScene
import com.heart.heartmerge.viewmodes.AnchorQueryType
import com.heart.heartmerge.viewmodes.HomePageState
import com.heart.heartmerge.viewmodes.HomeViewModel
import com.heart.heartmerge.viewmodes.RequestStatus

/**
 * 作者：Lxf
 * 创建日期：2024/7/27 14:11
 * 描述：
 */

class NewFragment : BaseCoreFragment<FragmentHomeNewBinding, HomeViewModel>(),
    View.OnClickListener {
    private var loadPage = 1
    private val pageSize = 20
    private var mCountId = ""
    private var currentQueryType = AnchorQueryType.NEW

    companion object {
        fun getInstance(): NewFragment {
            return NewFragment()
        }
    }

    override fun getLayoutId(): Int = R.layout.fragment_home_new

    override fun initData() {
        mBinding.xRefreshLayout.onRefresh()
        mViewModel.getLanguages()
        FlowBus.with<String>(Constants.PUSH_TYPE_BLACK_ANCHOR).register(this) {
            mBinding.xRefreshLayout.recyclerView.dslAdapter.apply {
                dataItems.forEach { dslAdapterItem ->
                    val item = dslAdapterItem as HomeAnchorItem
                    if (item.anchorInfo?.id == it) {
                        mBinding.xRefreshLayout.recyclerView.dslAdapter.removeItem(dslAdapterItem)
                        updateItemDepend()
                        return@forEach
                    }
                }
            }
        }

        FlowBus.with<Boolean>(Constants.SUBSCRIBE_SUCCESS).register(this) { //支付成功
            if (it) {
                //充值成功 刷新主播列表
                mBinding.xRefreshLayout.onRefresh()
            }
        }
    }

    @SuppressLint("SimpleDateFormat")
    override fun initView() {
        mBinding.xRefreshLayout.recyclerView.apply {
            applyGridLayoutManager(2)
            setOnRefreshListener { b: Boolean, page: Int ->
                if (b) {
                    loadPage = 1
                    mCountId = ""
                    mViewModel.cancelLoadAnchorJob()
                }
                //第一页不传随机种子
//                mViewModel.getAnchorList(page, pageSize, mCountId, mSelectLanguage?.languageCode ?: "")
//                mViewModel.getLanguages()
                mViewModel.getAnchorList(page, pageSize, mCountId, "", currentQueryType.type)
            }

            // 添加滚动监听器
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)

                    // 获取第一个可见项的位置
                    val layoutManager = recyclerView.layoutManager as GridLayoutManager
                    val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()
                    // 判断滑动距离，控制按钮显示/隐藏并添加动画
                    if (firstVisibleItemPosition > 5 && mBinding.backTop.visibility == View.GONE) {
                        mBinding.backTop.apply {
                            visibility = View.VISIBLE
                            translationY = height.toFloat() // 初始化为屏幕下方
                            animate().translationY(0f).setDuration(300).start() // 从底部浮出
                        }

                    } else if (firstVisibleItemPosition <= 5 && mBinding.backTop.visibility == View.VISIBLE) {
                        mBinding.backTop.animate().translationY(height.toFloat()).setDuration(300)
                            .withEndAction {
                                mBinding.backTop.visibility = View.GONE // 收回到底部并隐藏
                            }.start()
                    }
                }
            })
        }

        mBinding.backTop.click {
            mBinding.xRefreshLayout.recyclerView.smoothScrollToPosition(0)
        }

        mBinding.btnUnlock.click {
            jump(MembershipCenterActivity::class.java, Bundle().apply {
                putParcelable(MembershipCenterActivity.SCENE, PurchaseScene.HomeNew)
            })
        }

        mBinding.lockContainer.click {
            jump(MembershipCenterActivity::class.java, Bundle().apply {
                putParcelable(MembershipCenterActivity.SCENE, PurchaseScene.HomeNew)
            })
        }
    }


    override fun initViewStates() {
        mViewModel.viewStates.let { states ->
            states.observeState(this, HomePageState::anchorGetStatus) {
                when (it) {
                    is RequestStatus.AnchorGetStatusSuccess -> {
                        mCountId = it.countId
                        mBinding.xRefreshLayout.apply {
                            //第一页清空数据
                            if (loadPage == 1) {
                                clearItems()
                            }
                            //追加数据到列表
                            append<HomeAnchorItem>(it.list) { data ->
                                anchorType = AnchorQueryType.NEW
                                anchorInfo = data as UserBean
                            }
                        }.recyclerView.apply {
                            if (loadPage == 1) {
                                mBinding.lockContainer.makeVisible(it.list?.isNotEmpty() == true && !MMKVDataRep.userInfo.isVIP)
                                ImageLoader.with(mBinding.ivAvatar)
                                    .load(MMKVDataRep.userInfo.avatar.buildImageUrl()).asAvatar()
                                    .into(mBinding.ivAvatar)

                                mBinding.xRefreshLayout.recyclerView.postDelayed({
                                    mBinding.xRefreshLayout.recyclerView.smoothScrollToPosition(0)
                                    mBinding.xRefreshLayout.recyclerView.layoutManager?.scrollToPosition(
                                        0
                                    )
                                }, 200)

                                openLoadMore {
                                    if ((it.list?.size ?: 0) >= pageSize) {
                                        dslAdapter.setLoadMoreEnable(true)
                                        loadPage++
                                        mViewModel.getAnchorList(
                                            loadPage, pageSize, mCountId,
//                                        mSelectLanguage?.languageCode ?: ""
                                            "", currentQueryType.type
                                        )
                                    } else {
                                        dslAdapter.setLoadMore(LOAD_MORE_NO_MORE)
                                        dslAdapter.setLoadMoreEnable(false)
                                    }
                                }
                            }

                            if (it.list.isNullOrEmpty() || it.list.size < pageSize) {
                                dslAdapter.setLoadMore(LOAD_MORE_NO_MORE)
                            } else {
                                dslAdapter.setLoadMore(LOAD_MORE_NORMAL)
                            }
                        }
                    }

                    is RequestStatus.AnchorGetStatusFailed -> {
                        if (loadPage == 1) {
                            mBinding.xRefreshLayout.onFinishRefresh()
                        } else {
                            val tmpPage = loadPage--
                            loadPage = if (tmpPage >= 1) tmpPage else 1
                            mBinding.xRefreshLayout.recyclerView.dslAdapter.setLoadMore(
                                LOAD_MORE_ERROR
                            )
                        }
                    }

                    is RequestStatus.LanguagesSuccess -> {
                    }

                    is RequestStatus.LanguagesFailed -> {
                        ToastUtil.show(it.msg)
                    }

                    else -> {}
                }
            }
        }
    }

    override fun initViewEvents() {
        mViewModel.viewEvents.observeEvent(this) {
            when (it) {
                is BaseRequestEvent.ShowToast -> ToastUtil.show(it.message)
                else -> {}
            }
        }
    }

    override fun onClick(view: View?) {
        view?.run {
            when (id) {
                else -> {}
            }
        }
    }
}
