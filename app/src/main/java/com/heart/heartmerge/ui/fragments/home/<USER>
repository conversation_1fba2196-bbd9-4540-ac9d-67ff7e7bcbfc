package com.heart.heartmerge.ui.fragments.home

import RechargeActivityView
import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.drawable.BitmapDrawable
import android.view.View
import android.widget.FrameLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import androidx.lifecycle.asLiveData
import androidx.recyclerview.widget.LinearLayoutManager
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.ItemSelectorHelper.Companion.MODEL_SINGLE
import com.angcyo.dsladapter.OnItemSelectorListener
import com.angcyo.dsladapter.SelectorParams
import com.angcyo.dsladapter.addDslItem
import com.angcyo.dsladapter.removeAllDslItem
import com.bdc.android.library.base.fragment.BaseCoreFragment
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.LanguageBean
import com.heart.heartmerge.databinding.FragmentHomeBinding
import com.heart.heartmerge.extension.attach
import com.heart.heartmerge.lce.BaseRequestEvent
import com.heart.heartmerge.popup.showRechargeActivityPopup
import com.heart.heartmerge.ui.activities.WhoSeeMeActivity
import com.heart.heartmerge.ui.activities.anchor.search.AnchorSearchActivity
import com.heart.heartmerge.ui.theme.HeartMergeTheme
import com.heart.heartmerge.ui.widget.DailyGrowingNumber
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.viewmodes.HomeViewModel
import com.heart.heartmerge.viewmodes.TabRequestEvent

/**
 * 作者：Lxf
 * 创建日期：2024/7/27 14:11
 * 描述：
 */
interface HomeFragmentCallback {
    fun onChildAction(index: Int, action: String)
}

class HomeFragment : BaseCoreFragment<FragmentHomeBinding, HomeViewModel>(), View.OnClickListener,
    HomeFragmentCallback {
    private var loadPage = 1
    private var mCountId = ""
    private var mSelectLanguage: LanguageBean? = null
    private var hasClosedNotify = false

    override fun getLayoutId(): Int = R.layout.fragment_home

    override fun initData() {
//        mViewModel.getLanguages()
    }

    @SuppressLint("SimpleDateFormat")
    override fun initView() {
        mBinding.topView.apply {
            val drawable = ContextCompat.getDrawable(context, R.mipmap.ic_search)
            val bitmap = (drawable as BitmapDrawable).bitmap
            val scaledDrawable =
                BitmapDrawable(resources, Bitmap.createScaledBitmap(bitmap, 48, 48, true))
            if (AppUtil.isRtl()) {
                setCompoundDrawablesWithIntrinsicBounds(null, null, scaledDrawable, null)
            } else {
                setCompoundDrawablesWithIntrinsicBounds(scaledDrawable, null, null, null)
            }
        }.click {
            jump(AnchorSearchActivity::class.java)
        }

        mBinding.languageHorList.apply {
            setOrientation(LinearLayoutManager.HORIZONTAL)
        }.dslAdapter.itemSelectorHelper.apply {
            selectorModel = MODEL_SINGLE
            onItemSelectorListener = object : OnItemSelectorListener {
                override fun onSelectorItemChange(
                    selectorItems: MutableList<DslAdapterItem>,
                    selectorIndexList: MutableList<Int>,
                    isSelectorAll: Boolean,
                    selectorParams: SelectorParams
                ) {
                    if (selectorItems.isNotEmpty()) {
                        val languagesItem = selectorItems[0] as HomeLanguagesItem
                        languagesItem.language?.let {
                            mSelectLanguage = it
                            loadPage = 1
                            mCountId = ""
//                            mViewModel.getAnchorList(loadPage, pageSize, mCountId, it.languageCode)
                        }
                    }
                }
            }
        }

        updateTabData()

        mBinding.llLikeMe.click {
            jump(WhoSeeMeActivity::class.java)
        }
        mBinding.tvLikeMeCount.setContent {
            HeartMergeTheme {
                DailyGrowingNumber(fontSize = 8.sp, isFromMain = true)
            }
        }

        mBinding.root.addView(
            RechargeActivityView(requireContext()).apply {
                click {
                    showRechargeActivityPopup(requireActivity() as AppCompatActivity)
                }
            }, FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.WRAP_CONTENT, FrameLayout.LayoutParams.WRAP_CONTENT
            )
        )

//        mBinding.root.addView(
//            SpecialOfferView(requireContext()).apply {
//                click {
//                    showSpecialOfferPopup(requireActivity(), GoodsBean.getSampleInstance())
//                }
//            }, FrameLayout.LayoutParams(
//                FrameLayout.LayoutParams.WRAP_CONTENT, FrameLayout.LayoutParams.WRAP_CONTENT
//            )
//        )

    }

    override fun onResume() {
        super.onResume()
        if (!hasClosedNotify) {
            mViewModel.getTopNotifyInfo().asLiveData().observe(this) {
                it?.let {
                    if (it.show) {
                        mBinding.llNoticeContainer.removeAllDslItem()
                        mBinding.llNoticeContainer.addDslItem(HomeNoticeItem(it) {
                            mBinding.llNoticeContainer.removeAllDslItem()
                            hasClosedNotify = true
                        })
                    }
                }
            }
        }
    }

    private fun updateTabData() {
        mViewModel.pageEvents.observeEvent(this) {
            when (it) {
                is TabRequestEvent.GetTabsSuccess -> {
                    mBinding.tabLayout.apply {
                        val titles = it.tabs.map { tabBean ->
                            tabBean.content
                        }.toTypedArray()

                        val fragments = it.tabs.mapIndexed { index, tabBean ->
                            HomeChildFragment.getInstance(index, tabBean.type)
                                .apply { callback = this@HomeFragment }
                        }

                        mBinding.tabLayout.setTitles(titles)
                        mBinding.viewPager.offscreenPageLimit = titles.size
                        mBinding.viewPager.attach(
                            childFragmentManager, lifecycle, fragments = fragments
                        ) { position ->
                            mBinding.tabLayout.setCurrentTab(position)
                        }
                        mBinding.tabLayout.setViewPager(mBinding.viewPager)
                    }
                }

                is TabRequestEvent.GetTabsFailed -> {
                    ToastUtil.show(it.msg)
                }
            }
        }

        mViewModel.getTabs()
    }

    override fun initViewEvents() {
        mViewModel.viewEvents.observeEvent(this) {
            when (it) {
                is BaseRequestEvent.ShowToast -> ToastUtil.show(it.message)
                else -> {}
            }
        }
    }

    override fun onClick(view: View?) {
        view?.run {
            when (id) {
                else -> {}
            }
        }
    }

    override fun onChildAction(index: Int, action: String) {
        if (action == "lock") {
            mBinding.tabLayout.showMsg(index, 99)
        } else {
            mBinding.tabLayout.hideMsg(index)
        }
    }
}
