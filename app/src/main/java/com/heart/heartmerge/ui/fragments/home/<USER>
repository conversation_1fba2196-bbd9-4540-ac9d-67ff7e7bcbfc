package com.heart.heartmerge.ui.fragments.home

import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.DslViewHolder
import com.bdc.android.library.extension.makeVisible
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.PageTopNotifyBean
import com.heart.heartmerge.utils.AppUtil

/**
 * 作者：Lxf
 * 创建日期：2024/7/27 14:10
 * 描述：
 */
class HomeNoticeItem(val notifyBean: PageTopNotifyBean? = null, val closeBlock: () -> Unit = {}) :
    DslAdapterItem() {

    init {
        itemLayoutId = R.layout.item_home_notice //指定布局, 这是必须的.
        itemSpanCount = -1
    }

    //可以定义任意数据类型
    //var itemBean:HttpBean? = null //像这样, 只不过数据需要手动控制.
    //如:创建/赋值/释放. 如果数据的改变, 还需要更新界面, 请调用item.updateAdapterItem, 或者直接使用系统的adapter.notifyItemChanged, 均可以.

    //重写`onItemBind`方法, 实现界面的绑定
    override fun onItemBind(
        itemHolder: DslViewHolder,
        itemPosition: Int,
        adapterItem: DslAdapterItem,
        payloads: List<Any>
    ) {
        super.onItemBind(itemHolder, itemPosition, adapterItem, payloads)
        itemHolder.apply {
            tv(R.id.tv_content)?.text = notifyBean?.content
            click(R.id.tv_renew) {
//                showMembershipSubscribePopup(
//                    itemHolder.context as AppCompatActivity, PurchaseScene.Renew
//                )
            }
            clickItem {
                AppUtil.jumpWithType(itemHolder.context, notifyBean?.jump_url)
            }
            img(R.id.iv_close)?.makeVisible(notifyBean?.avail_close == true)

            click(R.id.iv_close) {
                closeBlock.invoke()
            }
        }
    }
}