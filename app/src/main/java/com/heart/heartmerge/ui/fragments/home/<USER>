package com.heart.heartmerge.ui.fragments.home

import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.View
import androidx.core.content.ContextCompat
import coil.load
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.DslViewHolder
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.GiftItemBean
import com.heart.heartmerge.beans.VideoMessageBean
import com.heart.heartmerge.ui.activities.anchor.AnchorVideoActivity

/**
 * 作者：Lxf
 * 创建日期：2024/8/2 16:30
 * 描述：
 */
class VideoMessageItem : DslAdapterItem() {
    init {
        itemLayoutId = R.layout.item_video_message
    }

    var videoMessageBean: VideoMessageBean? = null

    //重写`onItemBind`方法, 实现界面的绑定
    override fun onItemBind(
        itemHolder: DslViewHolder,
        itemPosition: Int,
        adapterItem: DslAdapterItem,
        payloads: List<Any>
    ) {
        super.onItemBind(itemHolder, itemPosition, adapterItem, payloads)
        itemHolder.apply {
            videoMessageBean?.apply {
                val foregroundColor = if (name == context.getString(R.string.label_me)) {
                    ContextCompat.getColor(context, R.color.color_B452FF)
                }else {
                    ContextCompat.getColor(context, R.color.color_EC12E2)
                }
                if (giftImage.isNotEmpty()) {
                    var showMsg = context.getString(R.string.label_give_gift)
                    if (isAsk) {
                        val spanStr = SpannableString("$name:  ${context.getString(R.string.label_gift_ask_tip)}")
                        spanStr.setSpan(
                            ForegroundColorSpan(foregroundColor),
                            0,
                            name.length,
                            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                        showMsg = spanStr.toString()

                        tv(R.id.fast_give)?.visibility = View.VISIBLE
                    }else{
                        tv(R.id.fast_give)?.visibility = View.GONE
                    }
                    tv(R.id.tv_label)?.text = showMsg

                    img(R.id.gift_img)?.apply {
                        visibility = View.VISIBLE
                        load(giftImage)
                    }
                    tv(R.id.gift_count)?.apply {
                        visibility = View.VISIBLE
                        text = "X$giftCount"
                    }
                } else {
                    val spanStr = SpannableString("$name:  $message")
                    spanStr.setSpan(
                        ForegroundColorSpan(foregroundColor),
                        0,
                        name.length,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    tv(R.id.tv_label)?.text = spanStr
                    tv(R.id.gift_count)?.visibility = View.GONE
                    img(R.id.gift_img)?.visibility = View.GONE
                    tv(R.id.fast_give)?.visibility = View.GONE
                }
                click(R.id.fast_give) {
                    if (context is AnchorVideoActivity) {
                        val videoActivity = context as AnchorVideoActivity
                        val giftItemBean = GiftItemBean(id = giftId, giftCode = giftCode, icon = giftImage, coin = giftPrice, giftSvgaUrl = svgaUrl)
                        videoActivity.giveGift(giftItemBean)
                    }

                }
            }
        }

    }
}