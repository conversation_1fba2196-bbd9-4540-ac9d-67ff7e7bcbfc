package com.heart.heartmerge.manager

import com.bdc.android.library.cache.CacheManager
import com.bdc.android.library.utils.MD5Util
import com.heart.heartmerge.beans.ResourceBean
import com.heart.heartmerge.http.RetrofitClient
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.utils.ContextHolder
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import okhttp3.Call
import okhttp3.Callback
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import java.io.File
import java.io.IOException
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit

/**
 * 全局资源管理器
 *
 * 主要功能：
 * 1. 应用初始化时拉取服务器资源列表
 * 2. 检查资源本地缓存状态
 * 3. 多线程断点续传下载资源 (通过OkHttp实现)
 * 4. 基于URL MD5值管理本地文件
 * 5. 提供单个和批量资源获取接口
 *
 * 使用示例：
 * ```kotlin
 * // 初始化资源管理器
 * GlobalResourceManager.initialize()
 *
 * // 获取单个资源
 * val file = GlobalResourceManager.getResource("https://example.com/video.mp4")
 *
 * // 获取多个资源
 * val files = GlobalResourceManager.getResources(listOf("url1", "url2"))
 *
 * // 监听下载状态
 * GlobalResourceManager.downloadState.collect { state ->
 *     // 处理下载状态变化
 * }
 * ```
 */
object FileResourceManager {

    private const val TAG = "GlobalResourceManager"
    private const val CACHE_DIR_NAME = "global_resources"
    private const val RESOURCE_LIST_CACHE_KEY = "cached_resource_list"
    private const val RESOURCE_METADATA_PREFIX = "resource_meta_"
    private const val MAX_CONCURRENT_DOWNLOADS = 3

    // 下载状态
    data class DownloadState(
        val isInitialized: Boolean = false,
        val totalResources: Int = 0,
        val downloadedResources: Int = 0,
        val downloadingResources: Set<String> = emptySet(),
        val failedResources: Set<String> = emptySet()
    )

    // 内部状态管理
    private val _downloadState = MutableStateFlow(DownloadState())
    val downloadState: StateFlow<DownloadState> = _downloadState.asStateFlow()

    // 资源缓存
    private val resourceCache = ConcurrentHashMap<String, ResourceBean>()
    private val downloadingCalls = ConcurrentHashMap<String, Call>() // 存储 OkHttp Call 对象

    // OkHttpClient 实例
    private val okHttpClient: OkHttpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()

    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    // 缓存目录
    private val cacheDir: File by lazy {
        File(ContextHolder.context.filesDir, CACHE_DIR_NAME).apply {
            if (!exists()) mkdirs()
        }
    }

    /**
     * 初始化资源管理器
     * 应在应用启动时调用
     */
    fun initialize() {
        LogX.d(TAG, "初始化全局资源管理器")
        scope.launch {
            try {
                // 加载缓存的资源列表
                loadCachedResourceList()

                // 拉取最新资源列表
                fetchResourceList()

                // 开始下载缺失的资源
                startResourceDownload()

                _downloadState.value = _downloadState.value.copy(isInitialized = true)
                LogX.d(TAG, "资源管理器初始化完成")
            } catch (e: Exception) {
                LogX.e(TAG, "资源管理器初始化失败", e)
            }
        }
    }

    /**
     * 获取单个资源文件
     * @param url 资源URL
     * @return 本地文件，如果不存在则返回null
     */
    fun getResource(url: String): File? {
        if (url.isBlank()) return null

        val fileName = generateFileName(url)
        val file = File(cacheDir, fileName)

        return if (file.exists() && file.length() > 0) {
            LogX.d(TAG, "获取资源成功: $url -> ${file.absolutePath}")
            file
        } else {
            LogX.w(TAG, "资源不存在: $url")
            // 如果资源不存在，尝试触发下载
            triggerResourceDownload(url)
            null
        }
    }

    /**
     * 获取多个资源文件
     * @param urls 资源URL列表
     * @return 存在的本地文件映射 (URL -> File)
     */
    fun getResources(urls: List<String>): Map<String, File> {
        val result = mutableMapOf<String, File>()
        val missingUrls = mutableListOf<String>()

        urls.forEach { url ->
            val file = getResource(url)
            if (file != null) {
                result[url] = file
            } else {
                missingUrls.add(url)
            }
        }

        // 批量触发缺失资源的下载
        if (missingUrls.isNotEmpty()) {
            triggerBatchResourceDownload(missingUrls)
        }

        LogX.d(TAG, "批量获取资源: 成功${result.size}个，缺失${missingUrls.size}个")
        return result
    }

    /**
     * 检查资源是否已缓存
     * @param url 资源URL
     * @return 是否已缓存
     */
    fun isResourceCached(url: String): Boolean {
        if (url.isBlank()) return false
        val fileName = generateFileName(url)
        val file = File(cacheDir, fileName)
        return file.exists() && file.length() > 0
    }

    /**
     * 获取缓存目录大小
     * @return 缓存大小（字节）
     */
    fun getCacheSize(): Long {
        return cacheDir.walkTopDown().filter { it.isFile }.map { it.length() }.sum()
    }

    /**
     * 清理缓存
     * @param olderThanDays 清理多少天前的文件，默认7天
     */
    fun clearCache(olderThanDays: Int = 7) {
        scope.launch {
            try {
                val cutoffTime = System.currentTimeMillis() - (olderThanDays * 24 * 60 * 60 * 1000L)
                var deletedCount = 0
                var deletedSize = 0L

                cacheDir.listFiles()?.forEach { file ->
                    if (file.isFile && file.lastModified() < cutoffTime) {
                        deletedSize += file.length()
                        if (file.delete()) {
                            deletedCount++
                        }
                    }
                }

                LogX.d(TAG, "缓存清理完成: 删除${deletedCount}个文件，释放${deletedSize}字节")
            } catch (e: Exception) {
                LogX.e(TAG, "缓存清理失败", e)
            }
        }
    }

    /**
     * 销毁资源管理器
     */
    fun destroy() {
        LogX.d(TAG, "销毁全局资源管理器")
        scope.cancel()
        downloadingCalls.values.forEach { it.cancel() } // 取消所有正在进行的 OkHttp 请求
        downloadingCalls.clear()
        resourceCache.clear()
    }

    // ==================== 私有方法 ====================

    /**
     * 加载缓存的资源列表
     */
    private suspend fun loadCachedResourceList() {
        try {
            val cachedList = CacheManager.get<List<ResourceBean>>(RESOURCE_LIST_CACHE_KEY)
            cachedList?.forEach { resource ->
                resourceCache[resource.url] = resource
            }
            LogX.d(TAG, "加载缓存资源列表: ${cachedList?.size ?: 0}个")
        } catch (e: Exception) {
            LogX.e(TAG, "加载缓存资源列表失败", e)
        }
    }

    /**
     * 从服务器拉取资源列表
     */
    private suspend fun fetchResourceList() {
        try {
            val response = RetrofitClient.aggregatedService.getResourceList()
            if (response.isSuccess()) {
                val resourceList = response.data?.records ?: emptyList()

                // 更新缓存
                resourceList.forEach { resource ->
                    if (resource.isValid()) {
                        resourceCache[resource.url] = resource
                    }
                }

                // 缓存到本地
                CacheManager.put(RESOURCE_LIST_CACHE_KEY, resourceList)

                _downloadState.value = _downloadState.value.copy(
                    totalResources = resourceList.size
                )

                LogX.d(TAG, "拉取资源列表成功: ${resourceList.size}个")
            } else {
                LogX.e(TAG, "拉取资源列表失败: ${response.msg}")
            }
        } catch (e: Exception) {
            LogX.e(TAG, "拉取资源列表异常", e)
        }
    }

    /**
     * 开始下载缺失的资源
     */
    private suspend fun startResourceDownload() {
        val resourcesToDownload = resourceCache.values.filter { resource ->
            !isResourceCached(resource.url)
        }

        LogX.d(TAG, "开始下载资源: ${resourcesToDownload.size}个")

        resourcesToDownload.forEach { resource ->
            downloadResource(resource)
        }
    }

    /**
     * 下载单个资源
     */
    private fun downloadResource(resource: ResourceBean) {
        val url = resource.url
        if (downloadingCalls.containsKey(url)) {
            LogX.d(TAG, "资源正在下载中: $url")
            return
        }

        val fileName = generateFileName(url)
        val finalFile = File(cacheDir, fileName)

        // 如果最终文件已存在，跳过下载
        if (finalFile.exists() && finalFile.length() > 0) {
            LogX.d(TAG, "资源已存在，跳过下载: $url")
            updateDownloadedCount()
            return
        }

        val request = Request.Builder().url(url).build()
        val call = okHttpClient.newCall(request)

        downloadingCalls[url] = call
        updateDownloadingState(url, true)

        call.enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                downloadingCalls.remove(url)
                updateDownloadingState(url, false)
                updateFailedState(url, true)
                LogX.e(TAG, "资源下载失败: $url, error: ${e.message}", e)
                // 清理不完整的文件
                if (finalFile.exists()) {
                    finalFile.delete()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                downloadingCalls.remove(url)
                updateDownloadingState(url, false)

                if (!response.isSuccessful) {
                    onFailure(call, IOException("Unexpected code $response"))
                    return
                }

                try {
                    response.body?.let { body ->
                        val totalLength = body.contentLength()
                        var currentOffset: Long = 0
                        val buffer = ByteArray(4096)
                        var bytesRead: Int

                        finalFile.outputStream().use { outputStream ->
                            body.byteStream().use { inputStream ->
                                while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                                    outputStream.write(buffer, 0, bytesRead)
                                    currentOffset += bytesRead
                                    // 可以根据需要更新进度，这里简化为只在完成时更新
                                }
                            }
                        }

                        if (finalFile.exists() && finalFile.length() > 0) {
                            LogX.d(TAG, "资源下载完成: $url")
                            updateDownloadedCount()
                            saveResourceMetadata(resource)
                        } else {
                            LogX.e(TAG, "资源文件下载后为空或不存在: $url")
                            updateFailedState(url, true)
                            if (finalFile.exists()) {
                                finalFile.delete()
                            }
                        }
                    } ?: run {
                        onFailure(call, IOException("Response body is null"))
                    }
                } catch (e: Exception) {
                    onFailure(call, IOException("Error writing file: ${e.message}", e))
                }
            }
        })
    }

    /**
     * 触发单个资源下载
     */
    private fun triggerResourceDownload(url: String) {
        val resource = resourceCache[url]
        if (resource != null) {
            scope.launch {
                downloadResource(resource)
            }
        } else {
            LogX.w(TAG, "未找到资源信息，无法下载: $url")
        }
    }

    /**
     * 批量触发资源下载
     */
    private fun triggerBatchResourceDownload(urls: List<String>) {
        scope.launch {
            urls.forEach { url ->
                val resource = resourceCache[url]
                if (resource != null) {
                    downloadResource(resource)
                } else {
                    LogX.w(TAG, "未找到资源信息，无法下载: $url")
                }
            }
        }
    }

    /**
     * 生成文件名（基于URL的MD5值）
     */
    private fun generateFileName(url: String): String {
        val md5 = MD5Util.getStringMD5(url) ?: url.hashCode().toString()
        val extension = url.substringAfterLast('.', "")
        return if (extension.isNotBlank() && extension.length <= 10) {
            "$md5.$extension"
        } else {
            md5
        }
    }

    /**
     * 保存资源元数据
     */
    private fun saveResourceMetadata(resource: ResourceBean) {
        try {
            val key = RESOURCE_METADATA_PREFIX + generateFileName(resource.url)
            CacheManager.put(key, resource)
        } catch (e: Exception) {
            LogX.e(TAG, "保存资源元数据失败", e)
        }
    }

    /**
     * 获取资源元数据
     */
    private fun getResourceMetadata(url: String): ResourceBean? {
        return try {
            val key = RESOURCE_METADATA_PREFIX + generateFileName(url)
            CacheManager.get<ResourceBean>(key)
        } catch (e: Exception) {
            LogX.e(TAG, "获取资源元数据失败", e)
            null
        }
    }

    /**
     * 更新已下载数量
     */
    private fun updateDownloadedCount() {
        _downloadState.value = _downloadState.value.copy(
            downloadedResources = _downloadState.value.downloadedResources + 1
        )
    }

    /**
     * 更新正在下载状态
     */
    private fun updateDownloadingState(url: String, isDownloading: Boolean) {
        val currentDownloading = _downloadState.value.downloadingResources.toMutableSet()
        if (isDownloading) {
            currentDownloading.add(url)
        } else {
            currentDownloading.remove(url)
        }
        _downloadState.value = _downloadState.value.copy(
            downloadingResources = currentDownloading
        )
    }

    /**
     * 更新失败状态
     */
    private fun updateFailedState(url: String, isFailed: Boolean) {
        val currentFailed = _downloadState.value.failedResources.toMutableSet()
        if (isFailed) {
            currentFailed.add(url)
        } else {
            currentFailed.remove(url)
        }
        _downloadState.value = _downloadState.value.copy(
            failedResources = currentFailed
        )
    }
}
