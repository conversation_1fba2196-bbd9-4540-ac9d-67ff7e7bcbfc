package com.heart.heartmerge.manager.word

import com.heart.heartmerge.beans.WordEntity

/**
 * Author:Lxf
 * Create on:2024/9/11
 * Description:
 */
class WordFilter private constructor() {

    private val trie = TrieWord()
    private var isInserted = false // 是否已经load到tri树
    private val unicodeRegex = Regex("[^\\x00-\\x7F]")

    companion object {
        @Volatile
        private var INSTANCE: WordFilter? = null

        fun getInstance(): WordFilter {
            return INSTANCE ?: synchronized(this) {
                val instance = WordFilter()
                INSTANCE = instance
                instance
            }
        }
    }

//    suspend fun initializeTrie() {
//        if (isInserted) return
//        withContext(Dispatchers.IO) {
//            val wordDao = AppDatabase.getInstance(ContextHolder.context).wordDao()
//            val allWords = wordDao.getAllWords()
//            initializeTrie(allWords)
//        }
//    }

    fun initializeTrie(allWords: List<WordEntity>) {
        if (isInserted) return
        isInserted = true
        allWords.forEach { wordEntity ->
            trie.insert(wordEntity.keyword, wordEntity.type)
        }
    }


    // 过滤文本内容
    fun filterMessage(input: String): String {
        return trie.filterText(input)
    }

    // 检查是否包含屏蔽词
    fun canSendMessage(input: String): Boolean {
        return !trie.containsBlockedWord(input)/* && !unicodeRegex.containsMatchIn(input)*/
    }
}