package com.heart.heartmerge.manager

import com.heart.heartmerge.extension.toMD5
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.utils.ContextHolder
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.File
import java.io.IOException
import java.util.concurrent.ConcurrentHashMap

/**
 * <AUTHOR>
 * @date 2025/6/20 17:50
 * @description 视频下载 管理 (不依赖okDownload)
 */
object DownloadVideoManager {
    private var TAG = DownloadVideoManager::class.simpleName
    private const val SAVE_VIDEO_FOLDER = "downloadedFiles"
    private val client = OkHttpClient()
    private val activeDownloads = ConcurrentHashMap<String, DownloadTaskWrapper>()
    private val activeCalls = ConcurrentHashMap<String, okhttp3.Call>()

    interface DownloadListener {
        fun onStart(url: String)
        fun onProgress(url: String, currentOffset: Long, totalLength: Long, progress: Int)
        fun onComplete(url: String, localPath: String)
        fun onError(url: String, error: String)
        fun onCancel(url: String)
    }

    data class DownloadTaskWrapper(
        val url: String,
        val listener: DownloadListener?,
        @Volatile var isCanceled: Boolean = false
    )

    fun enqueueDownload(tag: String, url: String, listener: DownloadListener? = null) {
        TAG = tag
        if (url.isEmpty()) {
            LogX.e("$TAG: videoUrl is null or empty")
            listener?.onError(url, "Video URL is null or empty")
            return
        }

        // 如果已有下载任务在进行中，取消之前的任务并重新开始
        if (activeDownloads.containsKey(url)) {
            LogX.d("$TAG: Download for $url is already in progress, canceling previous task and restarting")
            val cancelSuccess = cancelDownloadAndWait(url, 2000) // 等待最多2秒
            if (!cancelSuccess) {
                LogX.w("$TAG: Failed to cancel previous download task for $url, but proceeding with new download")
            }
        }

        val fileExtension = "mp4"
//        val fileName = if (fileExtension.isNotEmpty()) {
//            "${url.toMD5()}.$fileExtension"
//        } else {
//            url.toMD5()
//        }
        val fileName = "${url.toMD5()}.$fileExtension"

        val downloadDir = File(ContextHolder.context.filesDir, SAVE_VIDEO_FOLDER)
        if (!downloadDir.exists()) {
            downloadDir.mkdirs()
        }
        val outputFile = File(downloadDir, fileName)

        if (outputFile.exists()) {
            LogX.e("$TAG, File already exists: ${outputFile.absolutePath}")
            listener?.onComplete(url, outputFile.absolutePath)
            return
        }

        // 创建临时文件，添加 .tmp 后缀
        val tempFile = File(downloadDir, "$fileName.tmp")

        val taskWrapper = DownloadTaskWrapper(url, listener)
        activeDownloads[url] = taskWrapper

        listener?.onStart(url)
        LogX.d("$TAG, startDownload: $url to temp file ${tempFile.absolutePath}")

        Thread {
            try {
                // 在开始网络请求前检查是否已被取消
                if (taskWrapper.isCanceled) {
                    LogX.d("$TAG, Download canceled before network request: $url")
                    return@Thread
                }

                val request = Request.Builder().url(url).build()
                val call = client.newCall(request)

                // 存储网络调用，以便在取消时能够取消网络请求
                activeCalls[url] = call

                // 如果任务被取消，取消网络请求
                if (taskWrapper.isCanceled) {
                    call.cancel()
                    LogX.d("$TAG, Network call canceled: $url")
                    return@Thread
                }

                call.execute().use { response ->
                    // 在处理响应前检查是否已被取消
                    if (taskWrapper.isCanceled) {
                        LogX.d("$TAG, Download canceled after network response: $url")
                        return@Thread
                    }

                    if (!response.isSuccessful) {
                        throw IOException("Failed to download file: ${response.code} ${response.message}")
                    }

                    val body = response.body ?: throw IOException("Response body is null")
                    val totalLength = body.contentLength()
                    var currentOffset: Long = 0

                    body.byteStream().use { inputStream ->
                        tempFile.outputStream().use { outputStream ->
                            val buffer = ByteArray(4096)
                            var bytesRead: Int
                            var lastProgress = 0

                            while (inputStream.read(buffer).also { bytesRead = it } != -1 && !taskWrapper.isCanceled) {
                                outputStream.write(buffer, 0, bytesRead)
                                currentOffset += bytesRead
                                val progress = if (totalLength > 0) ((currentOffset * 100) / totalLength).toInt() else 0
                                if (progress != lastProgress) {
                                    listener?.onProgress(url, currentOffset, totalLength, progress)
                                    lastProgress = progress
                                }

                                // 每写入一次数据后检查是否被取消，提高响应速度
                                if (taskWrapper.isCanceled) {
                                    LogX.d("$TAG, Download canceled during data transfer: $url")
                                    break
                                }
                            }
                        }
                    }
                }

                    if (taskWrapper.isCanceled) {
                        LogX.d("$TAG, Download canceled: $url")
                        // 删除临时文件
                        if (tempFile.exists()) {
                            tempFile.delete()
                        }
                        listener?.onCancel(url)
                    } else {
                        // 下载成功，将临时文件重命名为正式文件
                        val renameSuccess = tempFile.renameTo(outputFile)
                        if (renameSuccess) {
                            LogX.e("$TAG, Download success: ${outputFile.absolutePath}")
                            listener?.onComplete(url, outputFile.absolutePath)
                        } else {
                            // 重命名失败，尝试复制文件
                            LogX.w("$TAG, Rename failed, trying to copy file")
                            try {
                                tempFile.copyTo(outputFile, overwrite = true)
                                tempFile.delete()
                                LogX.e("$TAG, Download success (via copy): ${outputFile.absolutePath}")
                                listener?.onComplete(url, outputFile.absolutePath)
                            } catch (copyException: Exception) {
                                LogX.e("$TAG, Failed to copy temp file: ${copyException.message}")
                                tempFile.delete()
                                listener?.onError(url, "Failed to finalize download: ${copyException.message}")
                            }
                        }
                    }
            } catch (e: Exception) {
                LogX.e("$TAG, Download error for $url: ${e.message}")
                // 删除临时文件
                if (tempFile.exists()) {
                    tempFile.delete()
                }
                listener?.onError(url, e.message ?: "Unknown error")
            } finally {
                activeDownloads.remove(url)
                activeCalls.remove(url)
            }
        }.start()
    }

    fun cancelDownload(url: String) {
        activeDownloads[url]?.let {
            it.isCanceled = true
            LogX.d("$TAG, Attempting to cancel download for $url")
        } ?: LogX.d("$TAG, No active download found for $url to cancel.")

        // 取消网络请求
        activeCalls[url]?.let { call ->
            if (!call.isCanceled()) {
                call.cancel()
                LogX.d("$TAG, Network call canceled for $url")
            }
        }
    }

    /**
     * 取消下载并等待任务完成清理
     * @param url 下载URL
     * @param timeoutMs 等待超时时间（毫秒），默认3秒
     * @return 是否成功取消
     */
    fun cancelDownloadAndWait(url: String, timeoutMs: Long = 3000): Boolean {
        val taskWrapper = activeDownloads[url]
        if (taskWrapper == null) {
            LogX.d("$TAG, No active download found for $url to cancel.")
            return true
        }

        // 标记为取消
        taskWrapper.isCanceled = true
        LogX.d("$TAG, Canceling download for $url and waiting for cleanup")

        // 等待任务完成清理
        val startTime = System.currentTimeMillis()
        while (activeDownloads.containsKey(url) && (System.currentTimeMillis() - startTime) < timeoutMs) {
            try {
                Thread.sleep(50) // 每50ms检查一次
            } catch (e: InterruptedException) {
                Thread.currentThread().interrupt()
                break
            }
        }

        val success = !activeDownloads.containsKey(url)
        if (success) {
            LogX.d("$TAG, Successfully canceled download for $url")
        } else {
            LogX.w("$TAG, Timeout waiting for download cancellation: $url")
            // 强制移除，避免内存泄漏
            activeDownloads.remove(url)
        }

        return success
    }

    /**
     * 获取当前活跃的下载任务信息
     */
    fun getActiveDownloads(): Map<String, String> {
        return activeDownloads.mapValues { (url, wrapper) ->
            "URL: $url, Canceled: ${wrapper.isCanceled}"
        }
    }

    /**
     * 获取活跃下载任务数量
     */
    fun getActiveDownloadCount(): Int {
        return activeDownloads.size
    }

    /**
     * 检查指定URL是否正在下载
     */
    fun isDownloading(url: String): Boolean {
        return activeDownloads.containsKey(url)
    }

    fun getVideoFolder(): File {
        val dir = File(ContextHolder.context.filesDir, SAVE_VIDEO_FOLDER)
        if (!dir.exists()) {
            dir.mkdirs()
        }
        return dir
    }

    /**
     * 清理下载目录的视频文件
     * @param cleanupType 清理类型
     * @param daysOld 清理旧文件时的天数阈值，默认7天
     * @return 清理结果信息
     */
    fun cleanupDownloadedVideos(cleanupType: CleanupType = CleanupType.ALL, daysOld: Int = 7): CleanupResult {
        val downloadDir = File(ContextHolder.context.filesDir, SAVE_VIDEO_FOLDER)
        if (!downloadDir.exists()) {
            LogX.d("$TAG, Download directory does not exist")
            return CleanupResult(0, 0, 0, "下载目录不存在")
        }

        val files = downloadDir.listFiles() ?: return CleanupResult(0, 0, 0, "无法读取目录文件")

        var deletedCount = 0
        var totalSize = 0L
        var failedCount = 0
        val deletedFiles = mutableListOf<String>()

        files.forEach { file ->
            if (shouldDeleteFile(file, cleanupType, daysOld)) {
                totalSize += file.length()
                try {
                    if (file.delete()) {
                        deletedCount++
                        deletedFiles.add(file.name)
                        LogX.d("$TAG, Deleted file: ${file.name}")
                    } else {
                        failedCount++
                        LogX.w("$TAG, Failed to delete file: ${file.name}")
                    }
                } catch (e: Exception) {
                    failedCount++
                    LogX.e("$TAG, Error deleting file ${file.name}: ${e.message}")
                }
            }
        }

        val message = when (cleanupType) {
            CleanupType.ALL -> "清理了所有下载文件"
            CleanupType.TEMP_FILES_ONLY -> "清理了临时文件"
            CleanupType.OLD_FILES -> "清理了旧文件"
        }

        LogX.i("$TAG, Cleanup completed: $deletedCount files deleted, ${totalSize / 1024 / 1024}MB freed, $failedCount failed")
        return CleanupResult(deletedCount, totalSize, failedCount, message, deletedFiles)
    }

    /**
     * 清理临时文件（.tmp 文件）
     */
    fun cleanupTempFiles(): CleanupResult {
        return cleanupDownloadedVideos(CleanupType.TEMP_FILES_ONLY)
    }

    /**
     * 清理指定天数之前的文件
     * @param daysOld 文件天数，默认7天
     */
    fun cleanupOldFiles(daysOld: Int = 7): CleanupResult {
        return cleanupDownloadedVideos(CleanupType.OLD_FILES, daysOld)
    }

    /**
     * 获取下载目录的统计信息
     */
    fun getDownloadDirectoryStats(): DirectoryStats {
        val downloadDir = File(ContextHolder.context.filesDir, SAVE_VIDEO_FOLDER)
        if (!downloadDir.exists()) {
            return DirectoryStats(0, 0, 0, 0)
        }

        val files = downloadDir.listFiles() ?: return DirectoryStats(0, 0, 0, 0)

        var totalFiles = 0
        var totalSize = 0L
        var tempFiles = 0
        var videoFiles = 0

        files.forEach { file ->
            if (file.isFile) {
                totalFiles++
                totalSize += file.length()

                when {
                    file.name.endsWith(".tmp") -> tempFiles++
                    isVideoFile(file) -> videoFiles++
                }
            }
        }

        return DirectoryStats(totalFiles, totalSize, tempFiles, videoFiles)
    }

    /**
     * 判断是否应该删除文件
     */
    private fun shouldDeleteFile(file: File, cleanupType: CleanupType, daysOld: Int = 7): Boolean {
        if (!file.isFile) return false

        return when (cleanupType) {
            CleanupType.ALL -> true
            CleanupType.TEMP_FILES_ONLY -> file.name.endsWith(".tmp")
            CleanupType.OLD_FILES -> {
                val fileAge = System.currentTimeMillis() - file.lastModified()
                val maxAge = daysOld * 24 * 60 * 60 * 1000L // 转换为毫秒
                fileAge > maxAge
            }
        }
    }

    /**
     * 判断是否为视频文件
     */
    private fun isVideoFile(file: File): Boolean {
        val videoExtensions = setOf("mp4", "avi", "mov", "mkv", "flv", "wmv", "webm", "m4v")
        val extension = file.extension.lowercase()
        return videoExtensions.contains(extension)
    }

    /**
     * 清理类型枚举
     */
    enum class CleanupType {
        ALL,            // 清理所有文件
        TEMP_FILES_ONLY, // 只清理临时文件
        OLD_FILES       // 清理旧文件
    }

    /**
     * 清理结果数据类
     */
    data class CleanupResult(
        val deletedCount: Int,
        val freedSpace: Long, // 释放的空间（字节）
        val failedCount: Int,
        val message: String,
        val deletedFiles: List<String> = emptyList()
    ) {
        fun getFreedSpaceMB(): Double = freedSpace / 1024.0 / 1024.0
        fun getFreedSpaceGB(): Double = freedSpace / 1024.0 / 1024.0 / 1024.0
    }

    /**
     * 目录统计信息数据类
     */
    data class DirectoryStats(
        val totalFiles: Int,
        val totalSize: Long, // 总大小（字节）
        val tempFiles: Int,
        val videoFiles: Int
    ) {
        fun getTotalSizeMB(): Double = totalSize / 1024.0 / 1024.0
        fun getTotalSizeGB(): Double = totalSize / 1024.0 / 1024.0 / 1024.0
    }
}