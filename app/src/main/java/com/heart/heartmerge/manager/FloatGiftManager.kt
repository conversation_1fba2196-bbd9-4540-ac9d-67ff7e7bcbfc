package com.heart.heartmerge.manager

import androidx.appcompat.app.AppCompatActivity

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2024/12/2 14:58
 * @description :首页飘屏礼物管理
 */
object FloatGiftManager {

    private var isFinish = true

    fun start(activity: AppCompatActivity) {
//        activity.lifecycleScope.launch {
//            while (isFinish) {
//                request<List<GiftItemBean>> {
//                    call { RetrofitClient.aggregatedService.getLargeGift() }
//                    onSuccess { beans ->
//                        isFinish = false
//                        activity.lifecycleScope.launch {
//                            beans?.forEach { t ->
//                                MovableOverlayView(activity).apply {
//                                    show(
//                                        activity,
//                                       t
//                                    )
//                                }
//                                delay(10000)
//                            }
//                            isFinish = true
//                        }
//                    }
//                }
//                delay(5 * 60 * 1000)
//            }
//        }
    }
}