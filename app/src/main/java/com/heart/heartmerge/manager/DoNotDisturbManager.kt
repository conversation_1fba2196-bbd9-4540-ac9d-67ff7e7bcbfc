package com.heart.heartmerge.manager


import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.utils.FlexibleCountdownTimer

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/12/17 10:10
 * @description :免打扰管理类
 */
object DoNotDisturbManager {

    private val countdownTimeInSeconds = MutableLiveData<Long>()
    private lateinit var flexibleCountdownTimer: FlexibleCountdownTimer

    fun checkDoNotDisturbMode() {
//        request {
//            call { RetrofitClient.aggregatedService.getDisturbStatus() }
//            onSuccess { any ->
//                switchDoDotDisturbMode(any?.dnd_expire ?: 0 > 0, false)
//                startCountdown(any?.dnd_expire ?: 0L)
//            }
//        }
        MMKVDataRep.userInfo.dnd_expire.let {
            switchDoDotDisturbMode(it > 0L, false)
            startCountdown(it)
        }
    }

    private fun startCountdown(seconds: Long) {
        if (::flexibleCountdownTimer.isInitialized) {
            flexibleCountdownTimer.cancel()
        }
        if (seconds <= 0) {
            countdownTimeInSeconds.value = 0L
            switchDoDotDisturbMode(false, false)
            return
        }
        flexibleCountdownTimer =
            FlexibleCountdownTimer(seconds - System.currentTimeMillis(), onTick = { t ->
                countdownTimeInSeconds.value = t
            }, onFinish = {
                switchDoDotDisturbMode(false, false)
            })
        flexibleCountdownTimer.start()
    }

    fun switchDoDotDisturbMode(disabled: Boolean, refresh: Boolean = true) {
        MMKVDataRep.doNotDisturb = disabled
        if (refresh) {
            checkDoNotDisturbMode()
        }
    }

    val remainingTime: LiveData<Long> get() = countdownTimeInSeconds
}
