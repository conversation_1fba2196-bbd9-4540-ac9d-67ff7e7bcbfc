package com.heart.heartmerge.manager

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.os.Environment
import android.view.Gravity
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import androidx.core.view.contains
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.bdc.android.library.http.request
import com.heart.heartmerge.beans.GiftBean
import com.heart.heartmerge.beans.GiftItemBean
import com.heart.heartmerge.extension.buildImageUrl
import com.heart.heartmerge.http.RetrofitClient
import com.heart.heartmerge.logger.LogX
import com.opensource.svgaplayer.SVGACallback
import com.opensource.svgaplayer.SVGAImageView
import com.opensource.svgaplayer.SVGAParser
import com.opensource.svgaplayer.SVGAVideoEntity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.net.HttpURLConnection
import java.net.URL

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/12/16 18:18
 * @description :礼物管理类
 */
class GiftManager private constructor(
    private val context: Context
) {

    companion object {
        @SuppressLint("StaticFieldLeak")
        @Volatile
        private var instance: GiftManager? = null

        fun getInstance(context: Context): GiftManager {
            return instance ?: synchronized(this) {
                instance ?: GiftManager(context.applicationContext).also {
                    instance = it
                }
            }
        }
    }

    private val giftDir: File by lazy {
        File(context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), "gifts").apply {
            if (!exists()) mkdirs()
        }
    }

    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    /**
     * 初始化，下载所有 SVG 文件
     */
    fun initializeGifts(callback: (success: Boolean, failedList: List<String>) -> Unit) {
        coroutineScope.launch {
            try {
                val failedDownloads = mutableListOf<String>()
                fetchGiftList { beans ->
                    if (beans?.isNotEmpty() == true) {
                        coroutineScope.launch {
                            beans.forEach { t ->
                                if (!isGiftDownloaded(t.id)) {
                                    val success = downloadGift(t.id, t.giftSvgaUrl)
                                    if (!success) failedDownloads.add(t.id)
                                }
                            }
                        }
                    }
                }

                withContext(Dispatchers.Main) {
                    callback(failedDownloads.isEmpty(), failedDownloads)
                }
            } catch (e: Exception) {
                LogX.e("Initialization error: $e")
            }
        }
    }

    /**
     * 判断礼物是否已下载
     */
    private fun isGiftDownloaded(giftId: String): Boolean {
        val file = File(giftDir, "$giftId.svg")
        return file.exists()
    }

    /**
     * 下载单个 SVG 动图
     */
    private suspend fun downloadGift(giftId: String, svgUrl: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val url = URL(svgUrl)
                val connection = url.openConnection() as HttpURLConnection
                connection.connectTimeout = 5000
                connection.readTimeout = 5000

                if (connection.responseCode == HttpURLConnection.HTTP_OK) {
                    val inputStream = connection.inputStream
                    val giftFile = File(giftDir, "$giftId.svg")

                    FileOutputStream(giftFile).use { output ->
                        inputStream.copyTo(output)
                    }
                    inputStream.close()
                    LogX.d("Gift downloaded: $giftId")
                    true
                } else {
                    LogX.e("Failed to download gift: $giftId")
                    false
                }
            } catch (e: Exception) {
                LogX.e("Error downloading gift: $giftId, $e")
                false
            }
        }
    }

    /**
     * 获取 SVG 列表 (从远程获取 JSON 数据)
     * 返回格式：Map<giftId, svgUrl>
     */
    private fun fetchGiftList(callback: (List<GiftItemBean>?) -> Unit) {
        request<GiftBean> {
            call { RetrofitClient.aggregatedService.getGiftList() }
            onSuccess { beans ->
                beans?.let { it ->
                    callback.invoke(it.list)
                }
            }
        }
    }

    /**
     * 播放礼物动画
     */
    fun playGiftAnimation(
        lifecycleOwner: LifecycleOwner, activity: Activity, giftSvgaUrl: String
    ) {
        val giftFile = FileResourceManager.getResource(giftSvgaUrl.buildImageUrl()) ?: return
        if (!giftFile.exists() || activity.isFinishing || activity.isDestroyed) {
            LogX.e("Gift file not found or activity invalid for url: $giftSvgaUrl")
            return
        }

        SVGAParser(activity).decodeFromInputStream(
            giftFile.inputStream(), giftSvgaUrl, callback = object : SVGAParser.ParseCompletion {
                override fun onComplete(videoItem: SVGAVideoEntity) {
                    if (activity.isFinishing || activity.isDestroyed) return

                    val rootView = activity.findViewById<ViewGroup>(android.R.id.content)
                    val imageView = SVGAImageView(activity)
                    imageView.loops = 1

                    imageView.layoutParams = FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT
                    ).apply {
                        gravity = Gravity.CENTER
                    }

                    imageView.setVideoItem(videoItem)
                    imageView.setBackgroundColor(
                        ContextCompat.getColor(
                            activity, com.bdc.android.library.R.color.black_80
                        )
                    )

                    val fullScreenParams = FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT
                    )

                    // 自动移除逻辑
                    val removeSelfSafely = {
                        runCatching {
                            val parent = imageView.parent
                            if (parent is ViewGroup && parent.contains(imageView)) {
                                parent.removeView(imageView)
                            }
                        }
                    }

                    imageView.callback = object : SVGACallback {
                        override fun onFinished() {
                            removeSelfSafely()
                        }

                        override fun onPause() {}
                        override fun onRepeat() {}
                        override fun onStep(frame: Int, percentage: Double) {}
                    }

                    // 添加到 RootView
                    rootView.addView(imageView, fullScreenParams)
                    imageView.startAnimation()

                    // 监听生命周期：自动清理动画 View
                    lifecycleOwner.lifecycle.addObserver(object : DefaultLifecycleObserver {
                        override fun onDestroy(owner: LifecycleOwner) {
                            removeSelfSafely()
                        }
                    })
                }

                override fun onError() {
                    LogX.e("svg load error: $giftSvgaUrl")
                }
            })
    }

    /**
     * 播放礼物动画
     * TODO 动画播放过程中 会崩溃
     */
//    fun playGiftAnimation(activity: Activity, giftSvgaUrl: String) {
//        val giftFile = FileResourceManager.getResource(giftSvgaUrl.buildImageUrl()) ?: return
//        if (!giftFile.exists()) {
//            LogX.e("Gift file not found for url: $giftSvgaUrl")
//            return
//        }
//
//        SVGAParser(activity).decodeFromInputStream(
//            giftFile.inputStream(), "", callback = object : SVGAParser.ParseCompletion {
//                override fun onComplete(videoItem: SVGAVideoEntity) {
//                    val rootView = activity.findViewById<ViewGroup>(android.R.id.content)
//
//                    val imageView = SVGImageView(activity, callback = object : SVGACallback {
//                        override fun onFinished() {
//                            rootView.children.find { view -> view is SVGImageView }?.let { view ->
//                                rootView.removeView(view) // 此时可以访问 imageView
//                            }
//                        }
//
//                        override fun onPause() {
//                        }
//
//                        override fun onRepeat() {
//                        }
//
//                        override fun onStep(frame: Int, percentage: Double) {
//                        }
//                    })
//
//                    imageView.layoutParams = FrameLayout.LayoutParams(
//                        ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT
//                    ).apply {
//                        gravity = Gravity.CENTER
//                    }
//                    imageView.setVideoItem(videoItem)
//                    imageView.startAnimation()
//                    rootView.addView(
//                        imageView.apply {
//                            setBackgroundColor(
//                                ContextCompat.getColor(
//                                    activity, com.bdc.android.library.R.color.black_80
//                                )
//                            )
//                        }, FrameLayout.LayoutParams(
//                            ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT
//                        )
//                    )
//                }
//
//                override fun onError() {
//                    LogX.e("svg load error")
//                }
//            })
//    }
}
