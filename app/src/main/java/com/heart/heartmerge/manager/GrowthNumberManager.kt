package com.heart.heartmerge.manager

import androidx.compose.runtime.State
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import com.bdc.android.library.utils.Logger
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Author:Lxf
 * Create on:2024/8/20
 * Description: 管理钻石的变化 自动更新所有监听的页面
 */
object GrowthNumberManager {
    private val _currentValue = mutableIntStateOf(0)
    val currentValue: State<Int> get() = _currentValue

    private val mmkv = MMKV.defaultMMKV()

    // 先快后慢的时间表（30分钟）
    private val growthSchedule = listOf(
        0L,
        1 * 60 * 1000L,
        3 * 60 * 1000L,
        6 * 60 * 1000L,
        9 * 60 * 1000L,
        12 * 60 * 1000L,
        16 * 60 * 1000L,
        20 * 60 * 1000L,
        24 * 60 * 1000L,
        27 * 60 * 1000L,
        30 * 60 * 1000L,
    )

    private const val startTimeKey = "curved_start_time"
    private const val dayKey = "curved_day"

    private var trackingJob: Job? = null
    private var isStarted = false

    fun startTracking() {
        if (isStarted) return
        isStarted = true

        val now = System.currentTimeMillis()
        val today = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date(now))

        var startTime = mmkv.decodeLong(startTimeKey, -1)
        val savedDay = mmkv.decodeString(dayKey, "")

        if (savedDay != today || startTime == -1L) {
            startTime = now
            mmkv.encode(startTimeKey, startTime)
            mmkv.encode(dayKey, today)
        }

        trackingJob = CoroutineScope(Dispatchers.Default).launch {
            while (true) {
                val nowTime = System.currentTimeMillis()
                val elapsed = nowTime - startTime
                val index = growthSchedule.indexOfLast { it <= elapsed }.coerceIn(0, 10)

                _currentValue.intValue = index

                if (index >= 10) break // 达到最大值退出

                val nextTime = growthSchedule[index + 1]
                val delayMs = nextTime - elapsed
                delay(delayMs)
            }
        }
    }

    fun stopTracking() {
        trackingJob?.cancel()
        trackingJob = null
        isStarted = false
    }
}
