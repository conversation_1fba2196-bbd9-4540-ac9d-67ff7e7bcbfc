package com.heart.heartmerge.manager

import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.beans.CheckOrderBean
import com.heart.heartmerge.beans.CheckOrderBody
import com.heart.heartmerge.firebase.report.ReportManager
import com.heart.heartmerge.http.RetrofitClient
import com.heart.heartmerge.logger.LogX
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlin.time.Duration.Companion.seconds

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/9/4 15:06
 * @description :支付订单查询
 */

object OrderManager {
    private val PERIOD_ARRAY = listOf(0, 1, 3, 5, 8)//订单状态查询周期,1s,3s,5s

    fun checkOrderStatus(body: CheckOrderBody, block: (CheckOrderBean?) -> Unit) {
        var currentPeriod = 0
        MainScope().launch {
            LogX.d("开始轮询检查订单状态: OrderId: ${body.payOrderId}")
            runCatching {
                do {
                    kotlinx.coroutines.delay(PERIOD_ARRAY[currentPeriod].seconds)
                    val result = RetrofitClient.aggregatedService.checkOrder(body)
                    if (result.isSuccess()) {
                        result.data?.let { data ->
                            LogX.d("轮询第${(currentPeriod + 1)}次检查订单状态:  ${body.payOrderId}, payStatus: ${data.payStatus}")
                            if (data.isPaid) {
                                block.invoke(data)
                                cancel()
                            }
                        }
                    } else {
                        LogX.e("轮询检查订单状态失败 : ${body.payOrderId}, payType: ${body.payType}, sku: ${body.sku}, payStatus: ${result?.data?.payStatus}, msg: ${result.msg} ")
                        ToastUtil.show(result.msg)
                        ReportManager.logException(Exception(result.msg))
                    }

                    currentPeriod++
                    if (currentPeriod >= PERIOD_ARRAY.size) {
                        LogX.d("轮询检查订单状态结束: ${body.payOrderId}, payType: ${body.payType}, sku: ${body.sku}, payStatus: ${result?.data?.payStatus} ")
                        block.invoke(null)
                        cancel()
                    }
                } while (currentPeriod < PERIOD_ARRAY.size)
            }
        }
    }
}