package com.heart.heartmerge.manager

import android.content.Context
import android.net.Uri
import android.provider.OpenableColumns
import android.webkit.MimeTypeMap
import com.heart.heartmerge.extension.requestChannelFlow
import com.heart.heartmerge.http.RetrofitClient
import com.heart.heartmerge.logger.LogX
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import okio.BufferedSink
import okio.source
import java.io.File
import java.util.UUID
import java.util.concurrent.TimeUnit

/**
 * 统一文件上传管理器
 *
 * 功能特性：
 * - 支持多种文件类型（图片、视频、音频、文档、日志）
 * - 统一的上传接口，自动处理文件类型识别
 * - 支持File和Uri两种输入方式
 * - 完整的错误处理和日志记录
 * - 可配置的HTTP客户端
 * - 支持自定义文件名和业务类型
 *
 * 使用示例：
 * ```kotlin
 * // 上传图片并返回URL
 * FileUploadManager.upload(context, imageUri).collect { result ->
 *     when (result) {
 *         is ApiResult.Success -> println("上传成功: ${result.data.accessUrl}")
 *         is ApiResult.Error -> println("上传失败: ${result.message}")
 *     }
 * }
 *
 * // 上传日志文件
 * FileUploadManager.upload(
 *     file = logFile,
 *     fileType = FileType.LOG,
 *     bizType = BizType.LOG_UPLOAD
 * )
 * ```
 */

sealed class ApiResult<out T> {

    /**
     * 加载中状态
     */
    object Loading : ApiResult<Nothing>()

    /**
     * 成功状态
     * @param data 返回的数据
     */
    data class Success<T>(val data: T) : ApiResult<T>()

    /**
     * 错误状态
     * @param exception 异常信息
     * @param message 错误消息
     */
    data class Error(
        val code: Int = -1,
        val exception: Throwable? = null,
        val message: String = exception?.message ?: "unknown error"
    ) : ApiResult<Nothing>()

    /**
     * 判断是否为成功状态
     */
    val isSuccess: Boolean
        get() = this is Success

    /**
     * 判断是否为错误状态
     */
    val isError: Boolean
        get() = this is Error

    /**
     * 判断是否为加载状态
     */
    val isLoading: Boolean
        get() = this is Loading

    /**
     * 获取数据，如果不是成功状态则返回 null
     */
    fun getDataOrNull(): T? {
        return if (this is Success) data else null
    }

    /**
     * 获取错误信息，如果不是错误状态则返回 null
     */
    fun getErrorOrNull(): String? {
        return if (this is Error) message else null
    }
}

/**
 * 扩展函数：对 ApiResult 进行映射转换
 */
inline fun <T, R> ApiResult<T>.map(transform: (T) -> R): ApiResult<R> {
    return when (this) {
        is ApiResult.Loading -> ApiResult.Loading
        is ApiResult.Success -> ApiResult.Success(transform(data))
        is ApiResult.Error -> this
    }
}

/**
 * 扩展函数：当结果为成功时执行操作
 */
inline fun <T> ApiResult<T>.onSuccess(action: (T) -> Unit): ApiResult<T> {
    if (this is ApiResult.Success) {
        action(data)
    }
    return this
}

/**
 * 扩展函数：当结果为错误时执行操作
 */
inline fun <T> ApiResult<T>.onError(action: (String) -> Unit): ApiResult<T> {
    if (this is ApiResult.Error) {
        action(message)
    }
    return this
}

/**
 * 扩展函数：当结果为加载中时执行操作
 */
inline fun <T> ApiResult<T>.onLoading(action: () -> Unit): ApiResult<T> {
    if (this is ApiResult.Loading) {
        action()
    }
    return this
}

object FileUploadManager {

//    private val fileRepository = FileRepository()

    // 默认HTTP客户端
    private val defaultHttpClient = OkHttpClient.Builder().connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(60, TimeUnit.SECONDS).writeTimeout(60, TimeUnit.SECONDS).build()

    // ==================== 枚举定义 ====================

    /**
     * 文件类型枚举
     */
    enum class FileType(val extension: String, val mimeTypePrefix: String) {
        IMAGE(".png", "image/"), VIDEO(".mp4", "video/"), AUDIO(".mp3", "audio/"), DOCUMENT(
            ".doc", "application/"
        ),
        LOG(".log", "text/")
    }

    /**
     * 业务类型枚举
     */
    enum class BizType(val value: Int) {
        NORMAL_UPLOAD(0),    // 普通业务上传
        LOG_UPLOAD(1)        // 日志上传
    }

    /**
     * 上传配置
     */
    data class UploadConfig(
        val fileType: FileType? = null,        // 文件类型（null时自动识别）
        val bizType: BizType = BizType.NORMAL_UPLOAD,  // 业务类型
        val customFileName: String? = null,     // 自定义文件名
        val useLogClient: Boolean = false       // 是否使用日志专用客户端
    )

    /**
     * 上传源密封类 - 统一处理File和Uri两种输入
     */
    private sealed class UploadSource {
        data class FileSource(val file: File) : UploadSource()
        data class UriSource(val context: Context, val uri: Uri) : UploadSource()
    }

    private data class FileInfo(
        val name: String, val size: Long, val mimeType: String
    )

    data class FileUploadResult(
        val success: Boolean,
        val objectKey: String? = null,
        val accessUrl: String? = null,
        val errorMessage: String? = null
    )

    /**
     * 文件上传进度
     */
    data class UploadProgress(
        val bytesUploaded: Long,
        val totalBytes: Long,
        val percentage: Int = ((bytesUploaded * 100) / totalBytes).toInt()
    )

    data class UploadCredentialBean(
        val uploadURL: String,
        val method: String,
        val objectKey: String,
        val url: String,
    )


    // ==================== 统一上传接口 ====================

    fun upload(
        context: Context,
        uri: Uri,
        fileType: FileType = FileType.IMAGE,
        customFileName: String? = null
    ): Flow<ApiResult<FileUploadResult>> =
        upload(context, uri, UploadConfig(fileType, customFileName = customFileName))

    /**
     * 上传日志文件
     */
    suspend fun uploadLog(logFile: File): ApiResult<FileUploadResult> = upload(
        file = logFile, config = UploadConfig(
            fileType = FileType.LOG, bizType = BizType.LOG_UPLOAD, useLogClient = true
        )
    )

    /**
     * 统一上传方法 - Uri版本
     * @param context 上下文
     * @param fileUri 文件URI
     * @param config 上传配置
     * @return 上传结果流
     */
    fun upload(
        context: Context, fileUri: Uri, config: UploadConfig = UploadConfig()
    ): Flow<ApiResult<FileUploadResult>> = flow {
        emit(ApiResult.Loading)

        val result = uploadInternal(
            source = UploadSource.UriSource(context, fileUri), config = config
        )

        emit(result)
    }.flowOn(Dispatchers.IO)

    /**
     * 统一上传方法 - File版本（主要用于日志文件）
     * @param file 文件对象
     * @param config 上传配置
     * @return 上传结果
     */
    suspend fun upload(
        file: File, config: UploadConfig = UploadConfig()
    ): ApiResult<FileUploadResult> = uploadInternal(
        source = UploadSource.FileSource(file), config = config
    )

    /**
     * 通用的URL提取方法
     */
    private fun extractUrlFromResult(uploadFlow: Flow<ApiResult<FileUploadResult>>): Flow<ApiResult<String>> {
        return uploadFlow.map { result ->
            when (result) {
                is ApiResult.Success -> {
                    if (result.data.success && result.data.accessUrl != null) {
                        ApiResult.Success(result.data.accessUrl)
                    } else {
                        ApiResult.Error(message = result.data.errorMessage ?: "Upload failed")
                    }
                }

                is ApiResult.Error -> result
                is ApiResult.Loading -> result
            }
        }
    }

    // ==================== 核心私有方法 ====================
    /**
     * 核心上传方法 - 统一处理所有上传逻辑
     */
    private suspend fun uploadInternal(
        source: UploadSource, config: UploadConfig
    ): ApiResult<FileUploadResult> = withContext(Dispatchers.IO) {
        try {
            // 1. 智能源类型优化 - 将file:// URI转换为FileSource以获得更好的性能和可靠性
            val optimizedSource = optimizeUploadSource(source)

            // 2. 预处理和验证
            val (fileType, mimeType, logInfo) = when (optimizedSource) {
                is UploadSource.FileSource -> {
                    val file = optimizedSource.file
                    // 检查文件
                    if (!file.exists()) {
                        return@withContext ApiResult.Error(message = "file does not exist: ${file.absolutePath}")
                    }
                    if (file.length() == 0L) {
                        return@withContext ApiResult.Error(message = "The file is empty: ${file.absolutePath}")
                    }

                    val fileType = config.fileType ?: detectFileTypeFromExtension(file.extension)
                    val mimeType = getMimeTypeFromFile(file)
                    val logInfo = "路径: ${file.absolutePath}, 大小: ${file.length()}"

                    Triple(fileType, mimeType, logInfo)
                }

                is UploadSource.UriSource -> {
                    val fileInfo = getFileInfoEnhanced(
                        optimizedSource.context, optimizedSource.uri, config.customFileName
                    )

                    // 验证文件大小
                    if (fileInfo.size <= 0L) {
                        return@withContext ApiResult.Error(message = "Unable to retrieve file size or file is empty: ${optimizedSource.uri}")
                    }

                    val fileType = config.fileType ?: detectFileType(fileInfo.mimeType)
                    val logInfo =
                        "URI: ${optimizedSource.uri}, 名称: ${fileInfo.name}, 大小: ${fileInfo.size}"

                    Triple(fileType, fileInfo.mimeType, logInfo)
                }
            }

            LogX.d("FileUploadManager: 开始上传文件 - $logInfo, 类型: $fileType")

            // 3. 获取上传凭证
            val credentialsResult = getUploadCredentials(config.bizType, fileType)

            when (credentialsResult) {
                is ApiResult.Success -> {
                    val credentials = credentialsResult.data
                    LogX.d("FileUploadManager: 获取上传凭证成功 - URL: ${credentials.uploadURL}")

                    // 4. 执行S3上传 - 使用优化后的源
                    val uploadResult = uploadToS3(
                        source = optimizedSource,
                        credentials = credentials,
                        mimeType = mimeType,
                    )

                    return@withContext when (uploadResult) {
                        is ApiResult.Success -> {
                            LogX.d("FileUploadManager: 文件上传完成")
                            uploadResult
                        }

                        is ApiResult.Error -> uploadResult
                        is ApiResult.Loading -> ApiResult.Error(message = "Upload timeout")
                    }
                }

                is ApiResult.Error -> {
                    return@withContext ApiResult.Error(message = "Failed to obtain and upload credentials: ${credentialsResult.message}")
                }

                is ApiResult.Loading -> {
                    return@withContext ApiResult.Error(message = "Timeout for obtaining and uploading credentials")
                }
            }
        } catch (e: Exception) {
            LogX.e("FileUploadManager: 文件上传异常", e)
            return@withContext ApiResult.Error(
                exception = e, message = "File upload failed: ${e.message}"
            )
        }
    }

    /**
     * 获取上传凭证
     */
    private suspend fun getUploadCredentials(
        bizType: BizType, fileType: FileType
    ): ApiResult<UploadCredentialBean> {
        return requestChannelFlow<UploadCredentialBean, ApiResult<UploadCredentialBean>> {
            request = {
                if (bizType == BizType.LOG_UPLOAD) {
                    RetrofitClient.aggregatedService.getLogUploadUrl(
                        mapOf("ext" to fileType.extension)
                    )
                } else {
                    RetrofitClient.aggregatedService.getUploadUrl(
                        bizType.value, fileType.extension
                    )
                }
            }
            transform = {
                ApiResult.Success(it)
            }
        }.first()
    }

    /**
     * 统一的S3上传方法 - 支持File和Uri两种输入源
     */
    private suspend fun uploadToS3(
        source: UploadSource,
        credentials: UploadCredentialBean,
        mimeType: String,
    ): ApiResult<FileUploadResult> = withContext(Dispatchers.IO) {
        try {
            // 根据不同的源创建RequestBody
            val requestBody = when (source) {
                is UploadSource.FileSource -> {
                    source.file.asRequestBody(mimeType.toMediaType())
                }

                is UploadSource.UriSource -> {
                    val contentResolver = source.context.contentResolver
                    val inputStream = contentResolver.openInputStream(source.uri)
                        ?: return@withContext ApiResult.Error(message = "Unable to read file")

                    // 使用增强的文件信息获取方法
                    val fileInfo = getFileInfoEnhanced(source.context, source.uri, null)
                    val fileSize = fileInfo.size

                    if (fileSize <= 0L) {
                        inputStream.close()
                        return@withContext ApiResult.Error(message = "Unable to retrieve file size or file is empty")
                    }

                    object : RequestBody() {
                        override fun contentType() = mimeType.toMediaType()

                        override fun writeTo(sink: BufferedSink) {
                            inputStream.use { input ->
                                sink.writeAll(input.source())
                            }
                        }

                        override fun contentLength(): Long = fileSize
                    }
                }
            }

            // 构建请求
            val requestBuilder = Request.Builder().url(credentials.uploadURL)
                .method(credentials.method.uppercase(), requestBody)
                .addHeader("Content-Type", mimeType)

            // 添加Content-Length头 - 对所有源类型都设置
            val contentLength = when (source) {
                is UploadSource.FileSource -> source.file.length()
                is UploadSource.UriSource -> {
                    val fileInfo = getFileInfoEnhanced(source.context, source.uri, null)
                    fileInfo.size
                }
            }

            if (contentLength > 0) {
                requestBuilder.addHeader("Content-Length", contentLength.toString())
            }

            val request = requestBuilder.build()

            // 选择HTTP客户端并执行请求
            val response = defaultHttpClient.newCall(request).execute()

            // 处理响应
            val result = if (response.isSuccessful) {
                val sourceName = when (source) {
                    is UploadSource.FileSource -> source.file.name
                    is UploadSource.UriSource -> "Uri文件"
                }
                LogX.d("FileUploadManager: S3上传成功 - $sourceName, 响应码: ${response.code}")
                ApiResult.Success(
                    FileUploadResult(
                        success = true,
                        objectKey = credentials.objectKey,
                        accessUrl = credentials.url
                    )
                )
            } else {
                val errorBody = response.body?.string() ?: "Unknown error"
                LogX.e("FileUploadManager: S3上传失败 - ${response.code}: $errorBody")
                ApiResult.Error(
                    message = "S3 upload failed: ${response.code} - $errorBody"
                )
            }

            response.close()
            result

        } catch (e: Exception) {
            LogX.e("FileUploadManager: S3上传异常", e)
            ApiResult.Error(
                exception = e, message = "S3 upload exception: ${e.message}"
            )
        }
    }

    /**
     * 智能优化上传源 - 将file:// URI转换为FileSource以获得更好的性能
     */
    private fun optimizeUploadSource(source: UploadSource): UploadSource {
        return when (source) {
            is UploadSource.FileSource -> source // 已经是FileSource，无需优化
            is UploadSource.UriSource -> {
                val uri = source.uri
                // 检查是否为file:// URI
                if (uri.scheme == "file" && uri.path != null) {
                    try {
                        val file = File(uri.path!!)
                        if (file.exists() && file.isFile()) {
                            return UploadSource.FileSource(file)
                        }
                    } catch (e: Exception) {
                        LogX.w("FileUploadManager: file:// URI转换失败，继续使用UriSource - ${e.message}")
                    }
                }
                source // 保持原有的UriSource
            }
        }
    }

    /**
     * 增强的文件信息获取方法 - 改进文件大小获取逻辑
     */
    private fun getFileInfoEnhanced(
        context: Context, fileUri: Uri, customFileName: String?
    ): FileInfo {
        val contentResolver = context.contentResolver
        var fileName = customFileName
        var fileSize = 0L
        val mimeType = contentResolver.getType(fileUri) ?: "application/octet-stream"

        // 方法1: 尝试通过ContentResolver查询获取文件信息
        try {
            contentResolver.query(fileUri, null, null, null, null)?.use { cursor ->
                if (cursor.moveToFirst()) {
                    val nameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                    val sizeIndex = cursor.getColumnIndex(OpenableColumns.SIZE)

                    if (nameIndex != -1 && fileName == null) {
                        fileName = cursor.getString(nameIndex)
                    }
                    if (sizeIndex != -1) {
                        val size = cursor.getLong(sizeIndex)
                        if (size > 0) {
                            fileSize = size
                        }
                    }
                }
            }
        } catch (e: Exception) {
            LogX.w("FileUploadManager: ContentResolver查询失败 - ${e.message}")
        }

        // 方法2: 如果文件大小仍然为0，尝试通过InputStream获取
        if (fileSize <= 0L) {
            try {
                contentResolver.openInputStream(fileUri)?.use { inputStream ->
                    fileSize = inputStream.available().toLong()
                }
            } catch (e: Exception) {
                LogX.w("FileUploadManager: InputStream获取文件大小失败 - ${e.message}")
            }
        }

        // 如果还是没有文件名，生成一个
        if (fileName == null) {
            val extension = MimeTypeMap.getSingleton().getExtensionFromMimeType(mimeType) ?: ""
            fileName = "file_${UUID.randomUUID()}.$extension"
        }

        return FileInfo(
            name = fileName, size = fileSize, mimeType = mimeType
        )
    }

    /**
     * 统一的文件类型检测器
     */
    private object FileTypeDetector {

        // 扩展名到文件类型的映射
        private val extensionMap = mapOf(
            // 图片
            "jpg" to FileType.IMAGE,
            "jpeg" to FileType.IMAGE,
            "png" to FileType.IMAGE,
            "gif" to FileType.IMAGE,
            "bmp" to FileType.IMAGE,
            "webp" to FileType.IMAGE,
            // 视频
            "mp4" to FileType.VIDEO,
            "avi" to FileType.VIDEO,
            "mov" to FileType.VIDEO,
            "wmv" to FileType.VIDEO,
            "flv" to FileType.VIDEO,
            "mkv" to FileType.VIDEO,
            // 音频
            "mp3" to FileType.AUDIO,
            "wav" to FileType.AUDIO,
            "aac" to FileType.AUDIO,
            "flac" to FileType.AUDIO,
            "ogg" to FileType.AUDIO,
            // 日志
            "log" to FileType.LOG,
            "txt" to FileType.LOG
        )

        // MIME类型前缀到文件类型的映射
        private val mimeTypeMap = mapOf(
            "image/" to FileType.IMAGE,
            "video/" to FileType.VIDEO,
            "audio/" to FileType.AUDIO,
            "text/" to FileType.LOG
        )

        /**
         * 根据扩展名检测文件类型
         */
        fun detectByExtension(extension: String): FileType {
            return extensionMap[extension.lowercase()] ?: FileType.DOCUMENT
        }

        /**
         * 根据MIME类型检测文件类型
         */
        fun detectByMimeType(mimeType: String): FileType {
            return mimeTypeMap.entries.find { mimeType.startsWith(it.key) }?.value
                ?: FileType.DOCUMENT
        }

        /**
         * 获取文件的MIME类型
         */
        fun getMimeType(extension: String): String {
            val cleanExtension = extension.lowercase()
            return MimeTypeMap.getSingleton().getMimeTypeFromExtension(cleanExtension)
                ?: when (cleanExtension) {
                    "log" -> "text/plain; charset=utf-8"
                    else -> "application/octet-stream"
                }
        }

        /**
         * 获取文件的MIME类型（File版本）
         */
        fun getMimeType(file: File): String = getMimeType(file.extension)
    }

    // 兼容性方法
    private fun detectFileType(mimeType: String): FileType =
        FileTypeDetector.detectByMimeType(mimeType)

    private fun detectFileTypeFromExtension(extension: String): FileType =
        FileTypeDetector.detectByExtension(extension)

    private fun getMimeTypeFromFile(file: File): String = FileTypeDetector.getMimeType(file)
}
