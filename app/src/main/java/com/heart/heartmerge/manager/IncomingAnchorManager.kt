package com.heart.heartmerge.manager

import com.heart.heartmerge.beans.AIBParamBean
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.fromJson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume
import kotlin.random.Random

/**
 * 作者：Lxf
 * 创建日期：2024/8/30 15:53
 * 描述：定时请求来电主播管理类
 */
object IncomingAnchorManager {
    private var aibDurationTime = 0
    private var AIB_FIRST_INTERVAL_TIME = 45
    private var AIB_DEFAULT_INTERVAL_TIME = 50
    private var AIB_GAP_RANDOM_MAX_NUM = 15
    private var AIB_INTERVAL_TIME_COEFFICIENT = 30
    private var aibCancelCount = 0 //用户取消次数
    private var intervalTime = AIB_FIRST_INTERVAL_TIME //默认下一次间隔
    private var loopJob: Job? = null
    private var isPaused = false
    private var resumeAction: (() -> Unit)? = null

    /**
     * 计时器 45s后开始发起请求来电主播
     * aib默认间隔 50s aib通话时长 充值页面时不计入时间 aibDurationTime
     * aib拒接延时发起 设置间隔（默认50s）+随机数（1-15s）+N*30(N为用户拒接aib的次数)
     * aib挂断时长：30s（）
     */
    fun startLoopIncomingAnchor(lifecycleScope: CoroutineScope, block: () -> Unit) {
        if (loopJob?.isActive == true) {
            return
        }
        resetDefaultConfig()
        loopJob = lifecycleScope.launch {
            while (isActive) {
                if (isPaused) {
                    suspendCancellableCoroutine { continuation ->
                        resumeAction = { continuation.resume(Unit) }
                    }
                }

//                LogX.e("LoopIncomingAnchor aibDurationTime:$aibDurationTime,  intervalTime: $intervalTime")
                if (AppUtil.canAutoPopupVideoCallingPage) { //如果当前页面不能视频 不计入时间 通话页面 充值页面
                    aibDurationTime++ //计时时间
                }
                if (aibDurationTime == intervalTime) { //第一次45s或者计算延迟时间开始获取来电主播
                    block.invoke()
                    reSetNextIncomingTime()
                }
                delay(1000) // 每1秒延迟
            }
        }
    }

    /**
     * 暂停轮询
     */
    fun pauseLoopIncomingAnchor() {
        isPaused = true
    }

    /**
     * 恢复轮询 搭配pauseLoopIncomingAnchor使用
     */
    fun resumeLoopIncomingAnchor() {
        //本地开启了免打扰
        if (MMKVDataRep.doNotDisturb) {
            return
        }
        isPaused = false
        resumeAction?.invoke()
        resumeAction = null
    }

    /**
     * 彻底停止轮询 搭配startLoopIncomingAnchor使用
     */
    fun stopLoopIncomingAnchor() {
        loopJob?.cancel()
        loopJob = null
    }

    private fun resetDefaultConfig() {
        aibDurationTime = 0
        aibCancelCount = 0
        MMKVDataRep.cacheAibParam?.fromJson<AIBParamBean>()?.let {
            updateInitTimes(it)
        }
        intervalTime = AIB_FIRST_INTERVAL_TIME
    }

    //每次请求更新时间
    fun updateInitTimes(param: AIBParamBean) {
        AIB_FIRST_INTERVAL_TIME = if (param.aibFirstTimeScheduling > 0) param.aibFirstTimeScheduling else 45
        AIB_DEFAULT_INTERVAL_TIME = if (param.aibNextTimeGap > 0) param.aibNextTimeGap else 50
        AIB_GAP_RANDOM_MAX_NUM = if (param.aibGapRandomMaxNum > 0) param.aibGapRandomMaxNum else 15
        AIB_INTERVAL_TIME_COEFFICIENT = if (param.aibIntervalTimeCoefficient > 0) param.aibIntervalTimeCoefficient else 30

        LogX.i("vvvvvAIB_FIRST_INTERVAL_TIME: $AIB_FIRST_INTERVAL_TIME,  AIB_DEFAULT_INTERVAL_TIME: $AIB_DEFAULT_INTERVAL_TIME, AIB_GAP_RANDOM_MAX_NUM: $AIB_GAP_RANDOM_MAX_NUM, AIB_INTERVAL_TIME_COEFFICIENT:$AIB_INTERVAL_TIME_COEFFICIENT ")
    }

    //从新计算下一次请求时间
    fun reSetNextIncomingTime() {
        intervalTime = aibDurationTime + AIB_DEFAULT_INTERVAL_TIME + Random.nextInt(1, AIB_GAP_RANDOM_MAX_NUM) + aibCancelCount * AIB_INTERVAL_TIME_COEFFICIENT
        LogX.i("reset aibDurationTime:$aibDurationTime,aibCancelCount:$aibCancelCount, intervalTime:$intervalTime")
    }

    //增加取消次数
    fun addCancelCount() {
        aibCancelCount++
    }
}