package com.heart.heartmerge.manager

import com.heart.heartmerge.mmkv.MMKVDataRep
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

/**
 * Author:Lxf
 * Create on:2024/8/20
 * Description: 管理钻石的变化 自动更新所有监听的页面
 */
object DiamondChangeManager {
    private val mmkv = MMKV.defaultMMKV()
    private const val KEY_DIAMOND = "diamondKey"

    // 创建 StateFlow 来跟踪数据变化
    private val _sharedValueFlow = MutableStateFlow(mmkv.getInt(KEY_DIAMOND, 0))

    // 暴露不可变的 StateFlow
    val sharedValueFlow: StateFlow<Int> = _sharedValueFlow

    fun updateDiamond(value: Int) {
        mmkv.putInt(KEY_DIAMOND, value)
        MMKVDataRep.userInfo.balance = value
        _sharedValueFlow.value = value // 更新 Flow 中的值
    }

    fun reduceDiamond(value: Int) {
        var saveDiamondCount = mmkv.getInt(KEY_DIAMOND, 0)
        saveDiamondCount -= value
        if (saveDiamondCount< 0) {
            saveDiamondCount = 0
        }
        updateDiamond(saveDiamondCount)
    }

    fun addDiamond(value: Int) {
        var saveDiamondCount = mmkv.getInt(KEY_DIAMOND, 0)
        saveDiamondCount += value
        if (saveDiamondCount< 0) {
            saveDiamondCount = 0
        }
        updateDiamond(saveDiamondCount)
    }

    fun getDiamond(): Int {
        return mmkv.getInt(KEY_DIAMOND, 0)
    }

    fun Int.toShowDiamond(): Int {
        return this/100
    }

}