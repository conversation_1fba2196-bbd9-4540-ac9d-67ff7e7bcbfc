package com.heart.heartmerge.manager

import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.utils.LruCacheManager

object UserCacheManager {

    private val cache = LruCacheManager<String, UserBean>(100)

    fun cacheUser(user: UserBean) {
        cache.put(user.id, user)
    }

    fun getUser(id: String): UserBean? = cache.get(id)

    fun removeUser(id: String) = cache.remove(id)

    fun clear() = cache.clear()

    fun contains(id: String): Boolean = cache.contains(id)
}
