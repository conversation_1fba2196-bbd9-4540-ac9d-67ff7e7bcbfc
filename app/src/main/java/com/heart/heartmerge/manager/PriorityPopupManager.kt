package com.heart.heartmerge.manager

import com.lxj.xpopup.XPopup
import com.heart.heartmerge.popup.BasePriorityPopup

data class PopupData(
    val priority: MainPopupPriority, var popup: BasePriorityPopup?
)

enum class MainPopupPriority {
    FIRST_LAUNCHER_PLATFORM_RULE,//首次启动-平台使用规则
    FIRST_USER_LOGIN_REWARD,//用户首次登录奖励钻石
    USER_SIGN//用户签到
}

object PriorityDialogManager {
    private val popupQueue = mutableListOf<PopupData>()
    private var isShowing = false

//    init {
//        popupQueue.add(PopupData(MainPopupPriority.FIRST_LAUNCHER_PLATFORM_RULE, null))
//        popupQueue.add(PopupData(MainPopupPriority.FIRST_USER_LOGIN_REWARD, null))
//        popupQueue.add(PopupData(MainPopupPriority.USER_SIGN, null))
//    }

    fun addPopup(priority: MainPopupPriority, popup: BasePriorityPopup) {
        popupQueue.add(PopupData(priority, popup))
        showNext()
    }

    fun removeData(dialogPriority: MainPopupPriority) {
        popupQueue.removeIf { it.priority == dialogPriority }
        showNext()
    }

    fun postData(dialogPriority: MainPopupPriority, dialog: BasePriorityPopup) {
        popupQueue.firstOrNull { it.priority == dialogPriority }?.popup = dialog
        showNext()
    }

    fun onPopDismiss() {
        isShowing = false
        showNext()
    }

    private fun showNext() {
        if (isShowing) {
            return
        }
        if (popupQueue.isEmpty()) {
            isShowing = false
            return
        }
        val targetPopup = popupQueue.firstOrNull()?.popup ?: return
        popupQueue.removeAt(0)
        isShowing = true
        XPopup.Builder(targetPopup.context).asCustom(targetPopup).show()
    }
}