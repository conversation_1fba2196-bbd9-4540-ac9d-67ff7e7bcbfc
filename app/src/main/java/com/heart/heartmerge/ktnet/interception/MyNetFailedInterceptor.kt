package com.heart.heartmerge.ktnet.interception

import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.ktnet.interception.NetWorkFailedInterceptor
import com.bdc.android.library.utils.ActivityManager
import com.heart.heartmerge.ktnet.error.code
import com.heart.heartmerge.utils.Constants
import kotlinx.coroutines.launch
import java.net.HttpURLConnection.HTTP_BAD_REQUEST
import java.net.HttpURLConnection.HTTP_INTERNAL_ERROR

/**
 *
 * @Author： Lxf
 * @Time： 2022/6/1 2:25 下午
 * @description：网络错误拦截器 返回false表示有自己的处理 不弹toast提示 默认返回true表示使用默认的异常处理
 */
class MyNetFailedInterceptor : NetWorkFailedInterceptor {
    override fun intercept(throwable: Throwable): Boolean {
        when (throwable.code) {
            Constants.ErrorCode.USER_NOT_EXIST, Constants.ErrorCode.TOKEN_EXPIRED -> {
                ActivityManager.current?.let {
                    it as AppCompatActivity
                }?.let {
                    it.lifecycleScope.launch {
                        FlowBus.with<Int>(Constants.TOKEN_EXPIRED)
                            .post(Constants.ErrorCode.TOKEN_EXPIRED)
                    }
                }
                return false
            }

//            HTTP_BAD_REQUEST, HTTP_INTERNAL_ERROR -> {
////                ActivityManager.current?.let {
////                    it as? AppCompatActivity
////                }?.let {
////                    it.runOnUiThread {
////                        ToastUtil.show(throwable.message)
////                    }
////                }
//                return false
//            }
        }
        return true
    }
}