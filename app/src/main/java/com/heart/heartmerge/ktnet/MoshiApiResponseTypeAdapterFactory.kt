package com.heart.heartmerge.ktnet

import com.bdc.android.library.ktnet.exception.ParseException
import com.bdc.android.library.ktnet.isAwait
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.JsonReader
import com.squareup.moshi.JsonWriter
import com.squareup.moshi.Moshi
import com.squareup.moshi.rawType
import java.lang.reflect.ParameterizedType
import java.lang.reflect.Type

/**
 *
 * @Author： Lxf
 * @Time： 2022/2/16 10:25 上午
 * @description：moshi 自定义转换， 因为自己在数据返回结构体碗面封装了Await， 所以需要把结构体从Await中提前出来
 */
class MoshiApiResponseTypeAdapterFactory : JsonAdapter.Factory {
    override fun create(
        type: Type,
        annotations: MutableSet<out Annotation>,
        moshi: Moshi
    ): JsonAdapter<*>? {
        val rawType = type.rawType
        //检查是否是Await<T>
        if (!isAwait(rawType)) return null
        // 获取 Await 的泛型参数，比如 User
        val dataType: Type = (type as? ParameterizedType)
            ?.actualTypeArguments?.firstOrNull()
            ?: return null
        // 获取 结构体如User 的 JsonAdapter
        val dataTypeAdapter = moshi.nextAdapter<Any>(
            this, dataType, annotations
        )
        return ApiResponseTypeAdapter(rawType, dataTypeAdapter)
    }

    class ApiResponseTypeAdapter<T>(
        private val outerType: Type,
        private val dataTypeAdapter: JsonAdapter<T>
    ) : JsonAdapter<T>() {

        override fun fromJson(reader: JsonReader): T? {
            reader.beginObject()

            var errorCode: Int? = null
            var errorMsg: String? = null
            var data: Any? = null

            while (reader.hasNext()) {
                when (reader.nextName()) {
                    "code" -> errorCode = reader.nextInt()
                    "message" -> errorMsg = reader.nextString()
                    "data" -> data = dataTypeAdapter.fromJson(reader)
                    else -> reader.skipValue()
                }
            }
            reader.endObject()
            if (errorCode == NetConstant.SUCCESS_CODE) {
                if (data == null) {// TODO 如果data为null 则返回""
//                    throw ParseException(errorCode, "Data parse exception")
//                    return null
                    data = ""
                }
            } else { //TODO 如果errorCode 不是成功则抛出异常
                throw ParseException(errorCode ?: NetConstant.DEFAULT_ERROR_CODE, errorMsg)
            }

            //如果服务端返回了data 解析data并返回 如果没有返回data，但是code是ok 返回"" 保证await<String>能解析
            data.let {
                return it as T
            }
            return "" as T

        }

        // 不需要序列化的逻辑
        override fun toJson(writer: JsonWriter, value: T?): Unit = TODO("Not yet implemented")
    }
}