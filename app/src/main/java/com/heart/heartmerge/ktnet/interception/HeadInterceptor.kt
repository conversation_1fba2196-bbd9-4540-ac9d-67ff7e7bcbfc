package com.heart.heartmerge.ktnet.interception

import android.os.Build
import com.bdc.android.library.ktnet.utils.NetKey
import com.bdc.android.library.utils.AppManager
import com.heart.heartmerge.BuildConfig
import com.heart.heartmerge.ktnet.ApiQueryInfo
import com.heart.heartmerge.mmkv.MMKVBaseDataRep
import com.heart.heartmerge.mmkv.MMKVGuiYinDataRep
import com.heart.heartmerge.utils.RequestParametersBuilder
import com.heart.heartmerge.utils.UniqueIDUtil
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException


/**
 *
 * @Author： Lxf
 * @Time： 2022/2/17 3:45 下午
 * @description： 自定义头部参数拦截器，传入heads
 */
class HeadInterceptor : Interceptor {
    private val apiQueryInfo by lazy { ApiQueryInfo() }

    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        var builder = chain.request().newBuilder()
        val getBuilder = chain.request().url.newBuilder()
        if ("GET" == chain.request().method.uppercase()) {
//            getBuilder.addQueryParameter(NetKey.KEY_VERSION, "1.0")
//            getBuilder.addQueryParameter(NetKey.KEY_USER_ID, "")
            builder = builder.url(getBuilder.build()).build().newBuilder()
        }

        //添加公共参数
        getBuilder.apply {
            RequestParametersBuilder.build().forEach { key, value ->
                addQueryParameter(key, value)
            }
//            addQueryParameter(NetKey.KEY_MODEL, apiQueryInfo.model)
//            addQueryParameter(NetKey.KEY_OS_TYPE, "1")
//            addQueryParameter(
//                NetKey.KEY_DEVICE_ID, UniqueIDUtil.getUniqueID(AppManager.getApplication())
//            )
//            addQueryParameter(NetKey.KEY_DEVICE_MODEL, Build.MODEL)
//            addQueryParameter(NetKey.KEY_OS_VERSION, Build.VERSION.RELEASE)
//            addQueryParameter(
//                NetKey.KEY_PACKAGE_NAME,
//                        BuildConfig.APPLICATION_ID
//            )
//            addQueryParameter(NetKey.KEY_VERSION, "${BuildConfig.VERSION_CODE}")
//            addQueryParameter(
//                NetKey.KEY_APP_ID, MMKVDataRep.userInfo.app_id.toString()
//            )
//            addQueryParameter(
//                NetKey.KEY_LANG, Locale.getDefault().language
//            )
//
//            if (MMKVDataRep.userInfo.id.isNotEmpty()) {
//                addQueryParameter(
//                    "uid", MMKVDataRep.userInfo.id
//                )
//            }
        }

        //添加header
        builder.run {
            runCatching {
                addHeader(NetKey.KEY_CONTENT_TYPE, "application/json")
                addHeader(NetKey.KEY_ACCESS_TOKEN, MMKVBaseDataRep.token ?: "")
                addHeader(NetKey.KEY_VERSION_CODE, BuildConfig.VERSION_CODE.toString())
                addHeader(NetKey.KEY_VERSION_NAME, BuildConfig.VERSION_NAME)
                addHeader(NetKey.KEY_PKG, BuildConfig.APPLICATION_ID)
                addHeader(NetKey.KEY_MODEL, Build.MODEL)
                addHeader(
                    NetKey.KEY_DEVICEID, UniqueIDUtil.getUniqueID(AppManager.getApplication())
                )
                addHeader(NetKey.KEY_DEVICETYPE, "android")
//            addHeader(
//                NetKey.KEY_GOOGLE_INSTALL_REFERRER,
//                MMKVDataRep.googleInstallReferrer ?: ""
//            )
//            addHeader(
//                NetKey.KEY_APPSFLYER_INSTALL_REFERRER,
//                URLEncoder.encode(MMKVDataRep.appsFlyerInstallReferrer ?: "", "UTF-8")
//            )
                addHeader(NetKey.KEY_APPSFLYER_APPID, MMKVGuiYinDataRep.fetchAppsflyerAppId())
                addHeader(NetKey.KEY_FIREBASE_APPID, MMKVGuiYinDataRep.fetchFirebaseAppId())
                addHeader(NetKey.KEY_GOOGLE_GAID, MMKVGuiYinDataRep.fetchGoogleAdvertisingId())
            }
        }

        val request = builder.url(getBuilder.build()).build()
        return chain.proceed(request)
    }

}