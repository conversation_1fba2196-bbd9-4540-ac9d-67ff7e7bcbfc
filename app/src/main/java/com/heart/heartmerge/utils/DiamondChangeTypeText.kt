package com.heart.heartmerge.utils

import com.heart.heartmerge.R

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/28 16:34
 * @description :钻石获取、消耗类型字符串
 */
object DiamondChangeType {

    fun get(type: String): String = when (type) {
        "0" ->  ContextHolder.context.getString(R.string.diamond_change_type_recharge)
        "1" -> ContextHolder.context.getString(R.string.diamond_change_type_videochat)
        "2" -> ContextHolder.context.getString(R.string.diamond_change_type_privatephoto)
        "3" -> ContextHolder.context.getString(R.string.diamond_change_type_gift)
        "4" -> ContextHolder.context.getString(R.string.diamond_change_type_extract)
        "5" -> ContextHolder.context.getString(R.string.diamond_change_type_manualrecharge)
        "6" -> ContextHolder.context.getString(R.string.diamond_change_type_rejectcash)
        "7" -> ContextHolder.context.getString(R.string.diamond_change_type_subscribe)
        "8" -> ContextHolder.context.getString(R.string.diamond_change_type_signin)
        "9" -> ContextHolder.context.getString(R.string.diamond_change_type_advert)
        "10" -> ContextHolder.context.getString(R.string.diamond_change_type_message)
        "11" -> ContextHolder.context.getString(R.string.diamond_change_type_share)
        "12" -> ContextHolder.context.getString(R.string.diamond_change_type_inviteanchor)
        "13" -> ContextHolder.context.getString(R.string.diamond_change_type_rankreward)
        "14" -> ContextHolder.context.getString(R.string.diamond_change_type_wishdeduct)
        "15" -> ContextHolder.context.getString(R.string.diamond_change_type_admingive)
        "16" -> ContextHolder.context.getString(R.string.diamond_change_type_taskcomplete)
        "17" -> ContextHolder.context.getString(R.string.diamond_change_type_levelrecharge)
        "18" -> ContextHolder.context.getString(R.string.diamond_change_type_levelweekbonus)
        "19" -> ContextHolder.context.getString(R.string.diamond_change_type_inviteuser)
        "24" -> ContextHolder.context.getString(R.string.diamond_change_type_paid_video)
        "25" -> ContextHolder.context.getString(R.string.diamond_change_type_paid_image)
        else -> ""
    }
}