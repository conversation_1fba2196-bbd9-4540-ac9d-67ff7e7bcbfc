package com.heart.heartmerge.utils

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/4/29 14:18
 * @description :支付场景
 */
@Parcelize
sealed class PurchaseScene : Parcelable {

    /** 首次充值 */
    data object FirstRecharge : PurchaseScene()

    /** 充值活动 */
    data object RechargePromotion : PurchaseScene()

    /** 匹配 */
    data class Match(val anchorId: String, val videoId: Long? = -1L) : PurchaseScene()

    data class MatchAiv(val anchorId: String, val videoId: Long? = -1L) : PurchaseScene()

    /** 聊天场景，可能也需要用户 ID（可选） */
    data class Chat(val anchorId: String) : PurchaseScene()

    /** 赠送礼物，需要接收者 ID 和礼物 ID（可选） */
    data class Gift(val anchorId: String) : PurchaseScene()

    data class CallGift(val anchorId: String) : PurchaseScene()

    /** 钱包相关操作（如余额充值） */
    data object Wallet : PurchaseScene()

    /** 视频通话 */
    data class VideoCallHome(val anchorId: String) : PurchaseScene()

    /** 主播视频通话触发充值，需要主播 ID */
    data class VideoCallDetail(val anchorId: String) : PurchaseScene()

    /** 主播详情直接充值*/
    data class AnchorDetail(val anchorId: String) : PurchaseScene()

    data class VideoCallFollow(val anchorId: String) : PurchaseScene()

    data class VideoCall(val anchorId: String) : PurchaseScene()

    /*付费视频*/
    data class PaidVideo(val anchorId: String, val videoId: Long? = -1L, val url: String = "") :
        PurchaseScene()

    /** 虚拟视频购买 */
    data class VirtualVideo(val anchorId: String, val videoId: Long? = -1L) : PurchaseScene()

    data class WhatsApp(val anchorId: String) : PurchaseScene()

    /** 首页Hot */
    data object HomeHot : PurchaseScene()

    /** 首页New */
    data object HomeNew : PurchaseScene()

    /** WhoLikeMe */
    data object WhoLikeMe : PurchaseScene()

    data object Mine : PurchaseScene()

    /** 客服 */
    data object CustomService : PurchaseScene()

    data object Task : PurchaseScene()

    data object TaskReward : PurchaseScene()

    data object SignIn : PurchaseScene()

    data object Renew : PurchaseScene()

    data object Voice : PurchaseScene()

    data object SystemNotice : PurchaseScene()

    data class AlbumUnlock(val anchorId: String) : PurchaseScene()

    data class CoverUnlock(val anchorId: String) : PurchaseScene()

    data object LuckReward : PurchaseScene()

    data object SpecialFirstRecharge : PurchaseScene()

    data object SpecialOffer : PurchaseScene()

    data class RealAib(val anchorId: String) : PurchaseScene()
    data class FakeAib(val anchorId: String) : PurchaseScene()


    val sceneValue: String
        get() = when (this) {
            //首充
            is FirstRecharge -> "first_recharge"
            //充值活动
            is RechargePromotion -> "recharge_activity"
            //匹配
            is Match -> "match_page"
            //匹配avi
            is MatchAiv -> "match_aiv"
            //聊天
            is Chat -> "im"
            //视频通话
            is VideoCall -> "call"
            //付费视频
            is PaidVideo -> "paid_video"
            //首页拨打视频通话
            is VideoCallHome -> "home_call"
            //主播详情视频通话
            is VideoCallDetail -> "anchor_detail_call"
            //主播详情充值
            is AnchorDetail -> "anchor_detail"
            //关注粉丝列表
            is VideoCallFollow -> "follow_fans_call"
            //虚拟视频
            is VirtualVideo -> "aiv"
            //假aib
            is FakeAib -> "fake_aib"
            //真aib
            is RealAib -> "real_aib"
            //IM赠送礼物
            is Gift -> "im_send_gift"
            //通话
            is CallGift -> "call_send_gift"
            //钱包
            is Wallet -> "wallet"
            //订阅
//            is Subscription -> "13"
            //WhatsApp
            is WhatsApp -> "recharge_for_anchor_whatsapp"
            //客诉
            is CustomService -> "custom_service"
            //首页Hot
            is HomeHot -> "home_hot"
            //首页New
            is HomeNew -> "home_follow"
            //我的
            is Mine -> "user_center"
            //WhoLikeMe
            is WhoLikeMe -> "who_like_me"
            //任务
            is Task -> "task"
            //任务奖励
            is TaskReward -> "reward_task"
            //签到
            is SignIn -> "sign"
            //解锁主播相册
            is AlbumUnlock -> "album_lock"
            //解锁主播封面
            is CoverUnlock -> "cover_lock"
            //会员续费
            is Renew -> "24"
            //语音
            is Voice -> "voice"
            //系统通知充值
            is SystemNotice -> "system"

//            x.y.z版本新增
            //幸运奖励
            is LuckReward -> "luck_reward"
            //特惠首充
            is SpecialFirstRecharge -> "special_first_recharge"
            //特惠
            is SpecialOffer -> "special_offer"

        }
}

val PurchaseScene.anchorIdOrEmpty: String
    get() = when (this) {
        is PurchaseScene.Match -> this.anchorId
        is PurchaseScene.MatchAiv -> this.anchorId
        is PurchaseScene.Chat -> this.anchorId
        is PurchaseScene.Gift -> this.anchorId
        is PurchaseScene.CallGift -> this.anchorId
        is PurchaseScene.VideoCallHome -> this.anchorId
        is PurchaseScene.VideoCallDetail -> this.anchorId
        is PurchaseScene.AnchorDetail -> this.anchorId
        is PurchaseScene.AlbumUnlock -> this.anchorId
        is PurchaseScene.CoverUnlock -> this.anchorId
        is PurchaseScene.VideoCallFollow -> this.anchorId
        is PurchaseScene.VideoCall -> this.anchorId
        is PurchaseScene.PaidVideo -> this.anchorId
        is PurchaseScene.VirtualVideo -> this.anchorId
        is PurchaseScene.WhatsApp -> this.anchorId
        is PurchaseScene.RealAib -> this.anchorId
        is PurchaseScene.FakeAib -> this.anchorId
        else -> "0"
    }

val PurchaseScene.videoIdOrEmpty: Long?
    get() = when (this) {
        is PurchaseScene.Match -> this.videoId
        is PurchaseScene.VirtualVideo -> this.videoId
        else -> -1L
    }

val PurchaseScene.videoUrlOrEmpty: String?
    get() = when (this) {
        is PurchaseScene.PaidVideo -> this.url
        else -> ""
    }
