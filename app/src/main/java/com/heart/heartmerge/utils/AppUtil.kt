package com.heart.heartmerge.utils

import ai.datatower.analytics.DT
import ai.datatower.analytics.DTAnalytics
import ai.datatower.analytics.DTChannel
import android.Manifest
import android.app.Activity
import android.app.Application
import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.BitmapDrawable
import android.media.MediaPlayer
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.renderscript.Allocation
import android.renderscript.RenderScript
import android.renderscript.ScriptIntrinsicBlur
import android.util.Log
import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import android.view.Window
import android.view.WindowManager
import android.widget.ImageView
import androidx.activity.ComponentActivity
import androidx.appcompat.app.AppCompatActivity
import androidx.core.text.TextUtilsCompat
import coil.load
import coil.transform.RoundedCornersTransformation
import com.angcyo.dsladapter.className
import com.bdc.android.library.cache.CacheManager
import com.bdc.android.library.extension.jump
import com.bdc.android.library.extension.jumpThenFinish
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.utils.ActivityManager
import com.bdc.android.library.utils.ToastUtil
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.facebook.FacebookSdk
import com.facebook.LoggingBehavior
import com.facebook.appevents.AppEventsLogger
import com.google.firebase.analytics.FirebaseAnalytics
import com.heart.heartmerge.BuildConfig
import com.heart.heartmerge.R
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.mmkv.MMKVGuiYinDataRep
import com.heart.heartmerge.popup.showDiamondRechargePopup
import com.heart.heartmerge.popup.showRechargeActivityPopup
import com.heart.heartmerge.popup.showUserSignPopup
import com.heart.heartmerge.socket.WebSocketManager
import com.heart.heartmerge.ui.activities.MainActivity
import com.heart.heartmerge.ui.activities.WebViewActivity
import com.heart.heartmerge.ui.activities.anchor.AnchorVideoActivity
import com.heart.heartmerge.ui.activities.anchor.IncomingVideoActivity
import com.heart.heartmerge.ui.activities.login.LoginActivity
import com.heart.heartmerge.ui.activities.message.MyRongConversationActivity
import com.heart.heartmerge.ui.activities.mine.LevelActivity
import com.heart.heartmerge.ui.activities.mine.MembershipCenterActivity
import com.heart.heartmerge.ui.activities.mine.RechargeHistoryActivity
import com.heart.heartmerge.ui.activities.mine.TaskActivity
import com.heart.heartmerge.ui.activities.mine.WalletActivity
import com.heart.heartmerge.ui.fragments.message.CustomSystemConversationProvider
import com.heart.heartmerge.ui.fragments.message.MyCustomConversationProvider
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.XXPermissions
import io.rong.imkit.GlideKitImageEngine
import io.rong.imkit.IMCenter
import io.rong.imkit.RongIM
import io.rong.imkit.config.RongConfigCenter
import io.rong.imkit.conversation.extension.MyExtensionConfig
import io.rong.imkit.conversation.extension.RongExtensionManager
import io.rong.imkit.conversation.extension.parsemessage.MikChatAskGiftMessage
import io.rong.imkit.conversation.extension.parsemessage.MikChatGiftMessage
import io.rong.imkit.conversation.extension.parsemessage.MikChatSystemMessage
import io.rong.imkit.conversation.extension.parsemessage.MikChatVideoCallMessage
import io.rong.imkit.conversation.extension.provider.AskGiftMessageItemProvider
import io.rong.imkit.conversation.extension.provider.GiftMessageItemProvider
import io.rong.imkit.conversation.extension.provider.MyImageMessageItemProvider
import io.rong.imkit.conversation.extension.provider.MySightMessageItemProvider
import io.rong.imkit.conversation.extension.provider.MyTextMessageItemProvider
import io.rong.imkit.conversation.extension.provider.SystemNoticeMessageItemProvider
import io.rong.imkit.conversation.extension.provider.VideCallMessageItemProvider
import io.rong.imkit.conversation.messgelist.provider.ImageMessageItemProvider
import io.rong.imkit.conversation.messgelist.provider.SightMessageItemProvider
import io.rong.imkit.conversation.messgelist.provider.TextMessageItemProvider
import io.rong.imkit.conversationlist.provider.PrivateConversationProvider
import io.rong.imkit.userinfo.RongUserInfoManager
import io.rong.imkit.utils.RouteUtils
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.InitOption
import io.rong.imlib.model.Message
import io.rong.imlib.model.MessageContent
import io.rong.imlib.model.UserInfo
import io.rong.message.SightMessage
import java.io.BufferedReader
import java.io.File
import java.io.FileReader
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Author:Lxf
 * Create on:2024/8/29
 * Description:
 */
object AppUtil {
    @Volatile
    var canAutoPopupVideoCallingPage = true //常量控制是否能弹起自动打电话

    //支付白名单页面（这些页面不弹虚拟视频,aia...）
    val purchaseWhiteList = listOf<String>(
        WalletActivity::class.java.className(),
        MembershipCenterActivity::class.java.className(),
        AnchorVideoActivity::class.java.className(),
        IncomingVideoActivity::class.java.className()
    )

    fun getDrawableByName(resourceName: String): Int? {
        return try {
            val res = R.mipmap::class.java.getField(resourceName).getInt(null)
            res
        } catch (e: Exception) {
            LogX.e("getDrawableByName failed resourceName: $resourceName")
            e.printStackTrace()
            null
        }
    }

    /**
     * 从本地资源文件获取国家图片
     */
    fun setLocalCountryImage(country: String?, countryView: ImageView?, radio: Float = 16f) {
        if (country?.isNotEmpty() == true) {
            val resourceName = "country_${
                (if (country.lowercase() == "ae-du") "ae_du" else country).lowercase(Locale.getDefault())
            }"
            val resourceId = getDrawableByName(resourceName)
            if (resourceId != null) {
                countryView?.load(resourceId) {
                    transformations(RoundedCornersTransformation(radio))
                }
            } else {
//                        img(R.id.anchor_country)?.setImageResource(R.mipmap.ic_default_header)
            }
        }
    }

    /**
     * 删除文件夹内容
     */
    fun deleteFolder(folderPath: String) {
        val file = File(folderPath)
        deleteFolder(file)
    }

    /**
     * 删除文件夹内容
     */
    fun deleteFolder(folder: File) {
        val files = folder.listFiles()
        LogX.e("delete folder ${files?.size}")
        files?.forEach { file ->
            if (file.isDirectory) {
                deleteFolder(file) //递归删除
            } else {
                file.delete()
            }
        }
    }

    fun setMargins(v: View, l: Int, t: Int, r: Int, b: Int) {
        if (v.layoutParams is MarginLayoutParams) {
            val p = v.layoutParams as MarginLayoutParams
            p.setMargins(l, t, r, b)
            v.requestLayout()
        }
    }

    //播放铃声
    fun startRing(context: Context): MediaPlayer? {
        try {
            val fd = context.assets.openFd("startvideobg.mp3")
            val mediaPlayer = MediaPlayer().apply {
                setDataSource(fd.fileDescriptor, fd.startOffset, fd.getLength())
                isLooping = true
                prepare()
                start()
            }
            return mediaPlayer
        } catch (e: IOException) {
            e.printStackTrace()
            return null
        }
    }

    fun checkoutDiamondAndShowPopup(
        activity: ComponentActivity,
        videoPrice: Int,
        purchaseScene: PurchaseScene,
        block: () -> Unit
    ) {
        if (MMKVDataRep.userInfo.balance < videoPrice) {
            showDiamondRechargePopup(
                activity, purchaseScene = purchaseScene
            )
        } else {
            block.invoke()
        }
//        if (!MMKVDataRep.userInfo.isVIP && MMKVDataRep.userInfo.balance < videoPrice) {
//            showDiamondRechargePopup(
//                activity, purchaseScene = purchaseScene
//            )
//        } else if (hasOffer && MMKVDataRep.userInfo.isVIP && MMKVDataRep.userInfo.balance < videoPrice.applyVIPPrice()) {
//            showDiamondRechargePopup(
//                activity, purchaseScene = purchaseScene
//            )
//        } else {
//            block.invoke()
//        }
    }

    fun clearCache() {
        CacheManager.clearAll()
        MMKVDataRep.clearAll()
        RongIM.getInstance().logout() //断开连接（不允许推送）
    }

    //判断文字方向从用往左
    fun isRtl(): Boolean {
        return TextUtilsCompat.getLayoutDirectionFromLocale(
            ContextHolder.context.resources.configuration.locales.get(0)
        ) == View.LAYOUT_DIRECTION_RTL
    }

    //禁止截屏
    fun screenSecure(window: Window) {
        window.addFlags(WindowManager.LayoutParams.FLAG_SECURE)
    }

    fun requestVideoPermission(
        context: Context, onGrantedCallBack: () -> Unit, onDenied: () -> Unit
    ) {
        XXPermissions.with(context)
            .permission(arrayOf(Manifest.permission.RECORD_AUDIO, Manifest.permission.CAMERA))
            .request(object : OnPermissionCallback {
                override fun onGranted(permissions: MutableList<String>, all: Boolean) {
                    if (all) {
                        onGrantedCallBack.invoke()
                    } else {
                        ToastUtil.show(context.getString(R.string.tip_video_permission_request))
                    }
                }

                override fun onDenied(permissions: MutableList<String>, never: Boolean) {
                    ToastUtil.show(context.getString(R.string.tip_video_permission_denied))
                    if (never) {
                        // 如果是被永久拒绝就跳转到应用权限系统设置页面
                        XXPermissions.startPermissionActivity(context, permissions)
                    } else {
                        onDenied.invoke()
                    }
                }
            })
    }

    fun getProcessName(): String? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            Application.getProcessName()
        } else {
            getProcessNameCompat()
        }
    }

    private fun getProcessNameCompat(): String? {
        val pid = android.os.Process.myPid()
        try {
            BufferedReader(FileReader("/proc/$pid/cmdline")).use { reader ->
                val processName = reader.readLine()
                return processName.split(0.toChar())[0].trim() // 只保留第一个子串并去除多余空白
            }
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }

    /**
     * 页面背景高斯模糊
     */
    fun applyBlur(activity: Activity, imageView: ImageView) {
        // 获取当前页面的截图
        runCatching {
            val view = activity.window.decorView
            view.isDrawingCacheEnabled = true
            view.buildDrawingCache(true)
            val bitmap = Bitmap.createBitmap(view.drawingCache)
            view.isDrawingCacheEnabled = false

            // 使用 RenderScript 进行模糊处理
            val renderScript = RenderScript.create(view.context)
            val input = Allocation.createFromBitmap(renderScript, bitmap)
            val output = Allocation.createTyped(renderScript, input.type)

            // 使用内置的模糊脚本
            val script = ScriptIntrinsicBlur.create(renderScript, input.element)
            script.setRadius(25f)  // 模糊半径，范围 0 < radius <= 25
            script.setInput(input)
            script.forEach(output)

            // 将模糊后的结果复制到 Bitmap
            output.copyTo(bitmap)

            // 将模糊的 Bitmap 设置为 ImageView 背景
            imageView.setImageDrawable(BitmapDrawable(view.context.resources, bitmap))
            imageView.makeVisible()

            // 释放资源
            input.destroy()
            output.destroy()
            script.destroy()
            renderScript.destroy()
        }
    }

    fun compareVersions(currentVersion: String, latestVersion: String): Int {
        return kotlin.runCatching {
            // 将版本号字符串分割成数字数组
            val currentParts = currentVersion.split(".").map { it.toInt() }
            val latestParts = latestVersion.split(".").map { it.toInt() }

            // 比较每一部分
            for (i in 0 until minOf(currentParts.size, latestParts.size)) {
                val current = currentParts[i]
                val latest = latestParts[i]
                if (current < latest) return 1 // 当前版本较旧
                if (current > latest) return 0  // 当前版本较新
            }

            // 如果版本长度不一致，较长的版本号视为较新版本
            return if (currentParts.size < latestParts.size) 1 else 0
        }.getOrDefault(0)
    }

    fun jumpWithType(
        context: Context, type: String?, vararg params: Any?
    ) {
        LogX.d("jumpWithType $type")
        if (type != null) {
            if (type.startsWith("http")) {
                WebViewActivity.jump(context, "", type)
                return
            }
            when (type) {
                "Home", "xxx", "xxxx" -> {
                    context.jump(MainActivity::class.java, Bundle().apply {
                        putInt(Constants.INTENT_PARAM_TAB_INDEX, 0)
                    })
                }

                "match" -> {
                    context.jump(MainActivity::class.java, Bundle().apply {
                        putInt(Constants.INTENT_PARAM_TAB_INDEX, 1)
                    })
                }

                "message" -> {
                    context.jump(MainActivity::class.java, Bundle().apply {
                        putInt(Constants.INTENT_PARAM_TAB_INDEX, 2)
                    })
                }

                "user" -> {
                    context.jump(MainActivity::class.java, Bundle().apply {
                        putInt(Constants.INTENT_PARAM_TAB_INDEX, 3)
                    })
                }

                "recharge" -> context.jump(WalletActivity::class.java, Bundle().apply {
                    putParcelable(
                        MembershipCenterActivity.SCENE,
                        params.firstOrNull { it is PurchaseScene } as? PurchaseScene)
                })

                "recharge_list" -> context.jump(RechargeHistoryActivity::class.java)

                "task" -> context.jump(TaskActivity::class.java)

                "activateVIP", "subscription", "subscription_vip" -> context.jump(
                    MembershipCenterActivity::class.java, Bundle().apply {
                        putParcelable(
                            MembershipCenterActivity.SCENE,
                            params.firstOrNull { it is PurchaseScene } as? PurchaseScene)
                    })

                "conversation" -> {
                    context.jump(MyRongConversationActivity::class.java, Bundle().apply {
                        putString("targetId", params.firstOrNull().toString())
                        putString(
                            "ConversationType", Conversation.ConversationType.PRIVATE.name
                        )
                    })
                }

                //首页-签到弹窗
                "sign_dialog" -> {
                    ActivityManager.current?.let {
                        it as? AppCompatActivity
                    }?.let {
                        showUserSignPopup(it)
                    }
                }

                //首页-快速充值弹窗
                "fast_recharge_dialog" -> {

                }

                //首页-免费金币弹窗
                "free_coins_dialog" -> {

                }

                //首页-首充弹窗
                "first_recharge_dialog" -> {
                    ActivityManager.current?.let {
                        it as? AppCompatActivity
                    }?.let {
                        showRechargeActivityPopup(it)
                    }
                }

                "level", "update_level" -> {
                    context.jump(LevelActivity::class.java)
                }

                else -> {
                    context.jump(MainActivity::class.java)
                }
            }
        }
    }

    fun cacheCustomServiceInfo() {
        val baseNickName = ContextHolder.context.getString(R.string.custom_service)
        val baseHeadFileName =
            "https://bdckj.s3.ap-southeast-1.amazonaws.com/1734599875374customerReq.png"
        val userInfo = UserInfo(
            Constants.RONG_YUN_ID_CUSTOM_SERVICE, baseNickName, Uri.parse(baseHeadFileName)
        )
        RongUserInfoManager.getInstance().refreshUserInfoCache(userInfo)
    }

    fun cacheSystemNoticeInfo() {
        val baseNickName = ContextHolder.context.getString(R.string.system_notice)
        val baseHeadFileName = "https://agent.heartmerge.vip/1734599904598officialNo.png"
        val userInfo = UserInfo(
            Constants.RONG_YUN_ID_SYSTEM, baseNickName, Uri.parse(baseHeadFileName)
        )
        RongUserInfoManager.getInstance().refreshUserInfoCache(userInfo)
    }

    fun getPathFromUri(context: Context, uri: Uri): String? {
        var path: String? = null
        val projection = arrayOf(MediaStore.MediaColumns.DATA)
        val cursor = context.contentResolver.query(uri, projection, null, null, null)
        cursor?.use {
            if (it.moveToFirst()) {
                val columnIndex = it.getColumnIndexOrThrow(MediaStore.MediaColumns.DATA)
                path = it.getString(columnIndex)
            }
        }
        return path
    }

    fun initRongYunIM(rongCloudAppKey: String) {
        val appKey = rongCloudAppKey
        val areaCode: InitOption.AreaCode = InitOption.AreaCode.SG

        LogX.e("vvvvvvvvvvvvvvvvvvvvvvvvvv  initRongYunIM $appKey  $areaCode")

        val initOption = InitOption.Builder().setAreaCode(areaCode).build()
        IMCenter.init(ContextHolder.context, appKey, initOption)
        RongConfigCenter.featureConfig().kitImageEngine = object : GlideKitImageEngine() {
            override fun loadConversationListPortrait(
                context: Context, url: String, imageView: ImageView, conversation: Conversation
            ) {
                Glide.with(context).load(url).apply(RequestOptions.bitmapTransform(CircleCrop()))
                    .placeholder(R.mipmap.ic_pic_default_oval).error(R.mipmap.ic_pic_default_oval)
                    .into(imageView)
            }

            override fun loadConversationPortrait(
                context: Context, url: String, imageView: ImageView, message: Message?
            ) {
                Glide.with(context).load(url).apply(RequestOptions.bitmapTransform(CircleCrop()))
                    .placeholder(R.mipmap.ic_pic_default_oval).error(R.mipmap.ic_pic_default_oval)
                    .into(imageView)
            }
        }
        //获取会话模板管理器
        val providerManager = RongConfigCenter.conversationListConfig().providerManager
        //注册一个自定义模板
        providerManager.replaceProvider(
            PrivateConversationProvider::class.java, MyCustomConversationProvider()
        )
        providerManager.addProvider(CustomSystemConversationProvider())
        //注册自己的会话页面
        RouteUtils.registerActivity(
            RouteUtils.RongActivityType.ConversationActivity, MyRongConversationActivity::class.java
        )
        //自定义扩展
        RongExtensionManager.getInstance().extensionConfig = MyExtensionConfig()
        //增加礼物展示自定义消息
        RongConfigCenter.conversationConfig().apply {
            isShowMoreClickAction = false
            conversationHistoryMessageCount = 100 // 默认拉取历史消息数量
            conversationRemoteMessageCount = 100// 默认拉取远端历史消息数量
            addMessageProvider(GiftMessageItemProvider()) //送礼物消息展示
            addMessageProvider(AskGiftMessageItemProvider()) //索要礼物消息展示
            addMessageProvider(VideCallMessageItemProvider())//视频通话
            addMessageProvider(SystemNoticeMessageItemProvider())//系统消息
            replaceMessageProvider(
                TextMessageItemProvider::class.java, MyTextMessageItemProvider()
            ) //替换文本消息 新的文本消息不支持链接点击
            replaceMessageProvider(
                ImageMessageItemProvider::class.java, MyImageMessageItemProvider()
            )

            replaceMessageProvider(
                SightMessageItemProvider::class.java, MySightMessageItemProvider()
            )
        }

        var localLanguage = Locale.getDefault().language
        if (localLanguage == "zh") { // 对中文简体和繁体做处理
            val country = Locale.getDefault().country
            localLanguage = when (country) {
                "TW", "HK", "MO" -> {
                    //繁体
                    "zh_TW"
                }

                else -> "zh_CN"
            }
        }
        RongConfigCenter.featureConfig().rc_translation_target_language = localLanguage
        //增加自定义消息类型
        val myMessages = ArrayList<Class<out MessageContent?>>()
        myMessages.add(MikChatGiftMessage::class.java)
        myMessages.add(MikChatAskGiftMessage::class.java)
        myMessages.add(MikChatVideoCallMessage::class.java)
        myMessages.add(MikChatSystemMessage::class.java)
        myMessages.add(SightMessage::class.java)
        RongIMClient.registerMessageType(myMessages)
    }

    fun initFacebookSdk(appId: String, clientToken: String) {
        runCatching {
            if (!FacebookSdk.isInitialized()) {
                LogX.i("initFacebookSdk $appId $clientToken")
                FacebookSdk.setApplicationId(appId)
                FacebookSdk.setClientToken(clientToken)
                FacebookSdk.setAdvertiserIDCollectionEnabled(true)
                FacebookSdk.fullyInitialize()
                FacebookSdk.sdkInitialize(
                    ContextHolder.context, object : FacebookSdk.InitializeCallback {
                        override fun onInitialized() {
                            LogX.e("FacebookSdk onInitialized")
                        }
                    })
                FacebookSdk.setAutoLogAppEventsEnabled(true)
                FacebookSdk.setIsDebugEnabled(BuildConfig.DEBUG)
                FacebookSdk.addLoggingBehavior(LoggingBehavior.APP_EVENTS)
                AppEventsLogger.activateApp(ContextHolder.context)
                LogX.i("initFacebookSdk success ${FacebookSdk.getMonitorEnabled()}")
            }
        }.onFailure {
            it.printStackTrace()
            LogX.e("FacebookSdk 初始化失败 $appId $clientToken")
            LogX.e(Log.getStackTraceString(it))
        }
    }

    fun initDataTowerSdk(appId: String, serverUrl: String) {
        runCatching {
            if (appId.isNotEmpty() && serverUrl.isNotEmpty()) {
                DT.initSDK(
                    ContextHolder.context,
                    appId,
                    serverUrl,
                    DTChannel.GP,
                    BuildConfig.DEBUG,
                    Log.DEBUG,
                    false
                )

                FirebaseAnalytics.getInstance(ContextHolder.context).appInstanceId.addOnSuccessListener {
                    LogX.i("FirebaseAnalytics firebaseAppId $it")
                    DTAnalytics.setFirebaseAppInstanceId(it)
                    MMKVGuiYinDataRep.firebaseAppId = it
                }
                LogX.i("initDataTowerSdk success $appId")
            }
        }.onFailure {
            it.printStackTrace()
            LogX.e("DataTowerSdk 初始化失败 $appId")
            LogX.e(Log.getStackTraceString(it))
        }
    }

    fun translateDuration(timestampInSeconds: Long): String {
        val date = Date(timestampInSeconds)
        val formatter = SimpleDateFormat("MM/dd HH:mm:ss", Locale.getDefault())
        return formatter.format(date)
    }

    fun translateCallDuration(durationInSeconds: Long): String {
        val hours = durationInSeconds / 3600
        val minutes = (durationInSeconds % 3600) / 60
        val seconds = durationInSeconds % 60

        return if (hours > 0) {
            String.format("%02d:%02d:%02d", hours, minutes, seconds)
        } else {
            String.format("%02d:%02d", minutes, seconds)
        }
    }

    fun forceReLogin(context: Context) {
        MMKVDataRep.clearAll()
        RongIM.getInstance().logout() //断开连接（不允许推送）
        WebSocketManager.getInstance().disconnectAndClear() //完全断开socket并清理状态
        ActivityManager.clearAll()
        context.jumpThenFinish(LoginActivity::class.java)
    }
}