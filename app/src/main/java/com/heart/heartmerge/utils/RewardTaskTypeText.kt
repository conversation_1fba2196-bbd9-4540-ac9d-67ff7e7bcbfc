package com.heart.heartmerge.utils

import com.heart.heartmerge.R

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2024/12/25 9:33
 * @description :奖励任务
 */
object RewardTaskTypeText {
    fun get(id: String, current: Int, total: Int): Pair<String, String> = when (id) {
        //充值累计超过50美金
        "1" -> Pair(
            ContextHolder.context.getString(R.string.reward_task_type_recharge),
            ContextHolder.context.getString(R.string.reward_task_type_recharge_desc, total)
        )
        //开通VIP(
        "2" -> Pair(
            ContextHolder.context.getString(R.string.reward_task_type_subscribe_vip),
            ContextHolder.context.getString(R.string.reward_task_type_subscribe_vip_desc)
        )
        //单次通话超过10分钟
        "3" -> Pair(
            ContextHolder.context.getString(R.string.reward_task_type_long_call, total),
            ContextHolder.context.getString(R.string.reward_task_type_long_call_desc, total)
        )
        //匹配通话10次
        "4" -> Pair(
            ContextHolder.context.getString(R.string.reward_task_type_match_x_call, total),
            ContextHolder.context.getString(
                R.string.reward_task_type_match_x_call_desc, total, current, total
            )
        )
        //通话时送礼
        "5" -> Pair(
            ContextHolder.context.getString(R.string.reward_task_type_send_video_call_gift),
            ContextHolder.context.getString(R.string.reward_task_type_send_video_call_gift_desc),
        )
        //在线时长满
        "6" -> Pair(
            ContextHolder.context.getString(
                R.string.reward_task_type_online_x_hours, current, total
            ), ""
        )
        //匹配在线时长
        "7" -> Pair(
            ContextHolder.context.getString(
                R.string.reward_task_type_match_x_hours, current, total
            ), ""
        )
        //收入金额大于x
        "8" -> Pair(
            ContextHolder.context.getString(
                R.string.reward_task_type_earn_x_coin, current, total
            ), ""
        )
        //收到大额礼物
        "9" -> Pair(ContextHolder.context.getString(R.string.reward_task_type_receive_big_gift), "")
        //上传*张照片或者视频到私密相册
        "10" -> Pair(
            ContextHolder.context.getString(
                R.string.reward_task_type_upload_private_album, current, total
            ), ""
        )

        else -> Pair("", "")
    }
}