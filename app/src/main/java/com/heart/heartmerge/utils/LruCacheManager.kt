package com.heart.heartmerge.utils

import android.util.LruCache

/**
 * 通用型 LRU 缓存工具类，支持自定义 key 和 value 类型
 */
class LruCacheManager<K, V>(maxSize: Int) {

    private val lruCache: Lru<PERSON>ache<K, V> = object : Lru<PERSON>ache<K, V>(maxSize) {
        override fun sizeOf(key: K, value: V): Int {
            // 默认每个 item 占 1 单位
            return 1
        }
    }

    @Synchronized
    fun put(key: K, value: V) {
        lruCache.put(key, value)
    }

    @Synchronized
    fun get(key: K): V? {
        return lruCache.get(key)
    }

    @Synchronized
    fun remove(key: K) {
        lruCache.remove(key)
    }

    @Synchronized
    fun clear() {
        lruCache.evictAll()
    }

    @Synchronized
    fun contains(key: K): <PERSON><PERSON>an {
        return lruCache.get(key) != null
    }

    @Synchronized
    fun snapshot(): Map<K, V> {
        return lruCache.snapshot()
    }

    fun currentSize(): Int {
        return lruCache.size()
    }

    fun maxSize(): Int {
        return lruCache.maxSize()
    }
}
