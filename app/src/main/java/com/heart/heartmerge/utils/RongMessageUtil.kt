package com.heart.heartmerge.utils

import android.net.Uri
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.GiftItemBean
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.extension.buildImageUrl
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.mmkv.MMKVDataRep
import io.rong.imkit.IMCenter
import io.rong.imkit.conversation.extension.parsemessage.MikChatGiftMessage
import io.rong.imkit.conversation.extension.parsemessage.MikChatVideoCallMessage
import io.rong.imkit.userinfo.RongUserInfoManager
import io.rong.imlib.IRongCoreCallback
import io.rong.imlib.IRongCoreEnum.CoreErrorCode
import io.rong.imlib.RongCoreClient
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.imlib.model.Message.ReceivedStatus
import io.rong.imlib.model.Message.SentStatus
import io.rong.imlib.model.UserInfo


/**
 * Author:Lxf
 * Create on:2024/9/3
 * Description: 消息处理
 */
object RongMessageUtil {
    fun sendGiftMessage(
        targetId: String,
        giftBean: GiftItemBean,
        callId: String = "",
        type: Conversation.ConversationType = Conversation.ConversationType.PRIVATE
    ) {
        val nickName: String = MMKVDataRep.userInfo.nickname
        val textMessage: MikChatGiftMessage =
            MikChatGiftMessage.obtain(
                giftBean.id,
                giftBean.showName,
                giftBean.icon,
                callId,
                giftBean.resource_id
            )
        // 获取特定属性的值
        val message = Message.obtain(targetId, type, textMessage)
        IMCenter.getInstance()
            .sendMessage(
                message,
                (nickName + ContextHolder.context.getString(R.string.label_give_gift_tip) + giftBean.showName) + " x " + "1",
                null,
                null
            )
    }

    fun insertOutgoingVideoMessage(targetId: String, callTime: String) {
        val conversationType: Conversation.ConversationType = Conversation.ConversationType.PRIVATE
        val sentStatus = SentStatus.SENT
        val textMessage: MikChatVideoCallMessage =
            MikChatVideoCallMessage.obtain(targetId, "1", callTime)

        val sentTime: Long = System.currentTimeMillis()
        IMCenter.getInstance()
            .insertOutgoingMessage(
                conversationType,
                targetId,
                sentStatus,
                textMessage,
                sentTime,
                object : RongIMClient.ResultCallback<Message>() {
                    /**
                     * 成功回调
                     * @param message 插入的消息
                     */
                    override fun onSuccess(message: Message) {
                    }

                    /**
                     * 失败回调
                     * @param errorCode 错误码
                     */
                    override fun onError(errorCode: RongIMClient.ErrorCode) {
                        LogX.e("insertOutgoingMessage ${errorCode.code}  ${errorCode.message}")
                    }
                })

    }

    fun insertIncomingVideoMessage(targetId: String, callTime: String) {
        val conversationType: Conversation.ConversationType = Conversation.ConversationType.PRIVATE
        val receivedStatus = ReceivedStatus(0x1)
        val textMessage: MikChatVideoCallMessage =
            MikChatVideoCallMessage.obtain(targetId, "1", callTime)

        val sentTime: Long = System.currentTimeMillis()
        IMCenter.getInstance()
            .insertIncomingMessage(
                conversationType,
                targetId,
                targetId,
                receivedStatus,
                textMessage,
                sentTime,
                object : RongIMClient.ResultCallback<Message>() {
                    /**
                     * 成功回调
                     * @param message 插入的消息
                     */
                    override fun onSuccess(message: Message) {
                    }

                    /**
                     * 失败回调
                     * @param errorCode 错误码
                     */
                    override fun onError(errorCode: RongIMClient.ErrorCode) {
                        LogX.e("insertIncomingVideoMessage ${errorCode.code}  ${errorCode.message}")
                    }
                })

    }


    /**
     * 刷新IM用户信息
     */
    fun refreshCacheUserInfo(userBean: UserBean?) {
        userBean?.let { user ->
            val userInfo = UserInfo(
                user.id,
                user.nickname,
                Uri.parse(user.avatar.buildImageUrl())
            )

            RongUserInfoManager.getInstance().refreshUserInfoCache(userInfo)
        }
    }

    //获取自己的融云信息带到每一个消息里
    fun getSelfUserInfo(): UserInfo {
        MMKVDataRep.userInfo.let {
            return UserInfo(it.id, it.nickname, Uri.parse(it.avatar.buildImageUrl()))
        }
    }

    fun clearHistoryMessageBefore15Days(targetId: String, deleteRemote: Boolean = true) {
        val conversationType = Conversation.ConversationType.PRIVATE
        val fifteenDaysInMillis = 15L * 24 * 60 * 60 * 1000
        val recordTime = System.currentTimeMillis() - fifteenDaysInMillis

        RongCoreClient.getInstance().cleanHistoryMessages(
            conversationType, targetId, recordTime, deleteRemote,
            object : IRongCoreCallback.OperationCallback() {
                override fun onSuccess() {
                    LogX.i("clearHistoryMessageBefore15Days: 成功删除 $targetId 的 15 天前消息")
                }

                override fun onError(coreErrorCode: CoreErrorCode?) {
                    LogX.i("clearHistoryMessageBefore15Days: 删除失败: ${coreErrorCode?.message} (${coreErrorCode?.code})")
                }
            }
        )
    }


}