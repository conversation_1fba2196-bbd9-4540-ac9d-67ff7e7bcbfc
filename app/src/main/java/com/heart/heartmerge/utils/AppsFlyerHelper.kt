package com.heart.heartmerge.utils

import ai.datatower.analytics.DTAnalytics
import com.appsflyer.AppsFlyerConversionListener
import com.appsflyer.AppsFlyerLib
import com.bdc.android.library.http.request
import com.google.gson.Gson
import com.heart.heartmerge.BuildConfig
import com.heart.heartmerge.firebase.report.ReportManager
import com.heart.heartmerge.http.RetrofitClient
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.mmkv.MMKVGuiYinDataRep

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/6/19 15:10
 * @description :\
 */
object AppsFlyerHelper {

    fun initialize() {
        AppsFlyerLib.getInstance()
            .init("uzEkGPuLW2gqy2qsdzepf", object : AppsFlyerConversionListener {
                override fun onConversionDataSuccess(p0: Map<String, Any>) {
                    val appsFlyerAppId =
                        AppsFlyerLib.getInstance().getAppsFlyerUID(ContextHolder.context)
                    LogX.i("AppsFlyer onConversionDataSuccess appsFlyerAppId $appsFlyerAppId, $p0")
                    DTAnalytics.setAppsFlyerId(appsFlyerAppId)
                    MMKVGuiYinDataRep.appsFlyerAppId = appsFlyerAppId
                    MMKVGuiYinDataRep.appsFlyerInstallReferrer = Gson().toJson(p0)
//                        MMKVDataRep.appsFlyerInstallReferrer =
//                            Gson().toJson(
//                                "{\"af_adset_id\":\"334891425\",\"is_incentivized\":\"false\",\"af_ad_type\":\"__CTYPE__\",\"retargeting_conversion_type\":\"none\",\"orig_cost\":\"0.0\",\"is_first_launch\":false,\"sha1_advertising_id\":\"231448f584aec5adb21abc18244d2889351552cc\",\"af_model\":\"__MODEL__\",\"iscache\":true,\"click_time\":\"2025-02-20 05:42:32.350\",\"match_type\":\"id_matching\",\"af_channel\":\"__PLACEMENT__\",\"af_viewthrough_lookback\":\"24h\",\"campaign_id\":\"334891331\",\"af_ua\":\"Dalvik/2.1.0 (Linux; U; Android 11; SM-A107F Build/RP1A.200720.012)\",\"install_time\":\"2025-02-20 05:49:26.228\",\"af_c_id\":\"334891331\",\"md5_advertising_id\":\"fb4fa14e42e81f6388480b723c6f45d2\",\"media_source\":\"kwaiforbusiness_int\",\"idfa\":\"__IDFA__\",\"advertising_id\":\"445e2bcf-1177-4453-befb-b487b888a41d\",\"clickid\":\"344xe1kMzDGPsclc2hpQwvfQi2h1ronnjdfp9xWkHQU7XfGiDiwvLoZTkBV6ueUyja2fXY2cs1iy4t2UoeDhcCpCkee8h5grjpJ9GkbnGxysDWSIYbHTyqc4PTyu5msv\",\"af_siteid\":\"1001\",\"af_status\":\"Non-organic\",\"af_lang\":\"in-id\",\"af_os_version\":\"__OS__\",\"cost_cents_USD\":\"0\",\"af_ad_id\":\"334891631\",\"af_adset\":\"sq\",\"campaign\":\"heartmerge-220-01\",\"af_ad\":\"无标题视频——使用Clipchamp制作 (22).mp4\",\"android_id\":\"__ANDROIDID1__\",\"is_retargeting\":\"false\"}"
//                        )
                    reportAppsFlyerReferrer(
                        appsFlyerAppId ?: "", appsFlyerParams = Gson().toJson(p0)
                    )
                }

                override fun onConversionDataFail(p0: String?) {
                    LogX.e("AppsFlyer onConversionDataFail $p0")
                }

                override fun onAppOpenAttribution(p0: Map<String, String>) {
                    LogX.i("AppsFlyer onAppOpenAttribution $p0")
                    reportAppsFlyerReferrer(
                        appsFlyerId = MMKVGuiYinDataRep.appsFlyerAppId ?: "",
                        oneLinkParams = Gson().toJson(p0)
                    )
                }

                override fun onAttributionFailure(p0: String?) {
                    LogX.e("AppsFlyer onAppOpenAttribution $p0")
                    p0?.let { ReportManager.log(it) }
                }
            }, ContextHolder.context)

        AppsFlyerLib.getInstance().setCollectAndroidID(true)
        AppsFlyerLib.getInstance().setDebugLog(BuildConfig.DEBUG)
        AppsFlyerLib.getInstance().start(ContextHolder.context)
    }

    private fun reportAppsFlyerReferrer(
        appsFlyerId: String, appsFlyerParams: String = "", oneLinkParams: String = ""
    ) {
        request {
            call {
                RetrofitClient.aggregatedService.reportAF(
                    buildMap {
                        put("af_id", appsFlyerId)
                        put("af_params_json", appsFlyerParams)
                        put("callback_param", oneLinkParams)
                        put("google_referer", MMKVGuiYinDataRep.googleInstallReferrer ?: "")
                    })
            }
        }
    }
}