package com.heart.heartmerge.utils // Replace with your actual package name

import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageInstaller
import android.content.pm.PackageManager
import android.os.Build
import android.util.Log

/**
 * Utility object to determine the installation source of the current application.
 */
object InstallChannelUtil {

    // --- Private Constants ---
    private const val TAG = "InstallChannelUtil" // Logging Tag

    // Known store package names mapped to their user-friendly names
    private val STORES: Map<String, String> = mapOf(
        // Google
        "com.android.vending" to "Google Play",
        // Major Chinese OEMs
        "com.xiaomi.market" to "Xiaomi App Store",
        "com.huawei.appmarket" to "Huawei AppGallery",
        "com.bbk.appstore" to "Vivo App Store",
        "com.vivo.appstore" to "Vivo App Store",
        "com.heytap.market" to "OPPO App Store", // Covers OPPO, OnePlus (China), Realme (China)
        "com.oppo.market" to "OPPO App Store", // Covers OPPO, OnePlus (China), Realme (China)
        // Other Major Stores
        "com.sec.android.app.samsungapps" to "Samsung Galaxy Store",
        "com.amazon.venezia" to "Amazon Appstore",
        // Common Third-Party Stores
        "com.tencent.android.qqdownloader" to "Tencent MyApp (应用宝)",
        "com.qihoo.appstore" to "360 App Store",
        "com.wandoujia.phoenix2" to "Wandoujia (豌豆荚)", // Common package name, check if others exist
        "com.pp.assistant" to "PP Assistant (PP助手)",
        "com.baidu.appsearch" to "Baidu Mobile Assistant",
        "com.hiapk.marketpho" to "HiAPK (安卓市场)",
        "com.apkpure.aegon" to "APKPure"
        // Add other relevant stores here
    )

    // --- Public Constants for Result Map Keys ---
    const val RESULT_KEY_INSTALLER_PACKAGE = "installer_package"
    const val RESULT_KEY_INSTALLER_NAME = "installer_name"
    const val RESULT_KEY_PACKAGE_SOURCE = "package_source"
    const val RESULT_KEY_IS_FROM_KNOWN_STORE = "is_from_known_store" // Renamed for clarity

    // --- Public Constants for Package Source Values ---
    const val SOURCE_TYPE_STORE = "store"
    const val SOURCE_TYPE_LOCAL_FILE = "local_file"
    const val SOURCE_TYPE_DOWNLOADED_FILE = "downloaded_file"
    const val SOURCE_TYPE_OTHER = "other"
    const val SOURCE_TYPE_UNSPECIFIED = "unspecified"
    const val SOURCE_TYPE_UNKNOWN = "unknown" // General unknown
    const val SOURCE_TYPE_LEGACY_API = "legacy_api" // Pre-R determination method
    const val SOURCE_TYPE_PRE_TIRAMISU = "pre_tiramisu" // R+, but before detailed source API
    const val SOURCE_TYPE_VENDOR_UNSUPPORTED =
        "vendor_unsupported" // T+, but API failed/not implemented
    const val SOURCE_TYPE_ERROR_R_PLUS = "error_api_r_plus" // Error getting info on R+
    const val SOURCE_TYPE_ERROR_LEGACY = "error_api_legacy" // Error getting info on pre-R

    // --- Public Constants for Special Installer Package/Name Values ---
    const val INSTALLER_PACKAGE_UNKNOWN = "unknown"
    const val INSTALLER_PACKAGE_UNKNOWN_OR_ADB = "unknown_or_adb"
    const val INSTALLER_PACKAGE_MANUAL_INSTALL = "manual_install"
    const val INSTALLER_PACKAGE_MANUAL_DOWNLOADED = "manual_install_downloaded"

    const val INSTALLER_NAME_UNKNOWN = "Unknown"
    const val INSTALLER_NAME_UNKNOWN_INSTALLER_APP = "Unknown Installer App"
    const val INSTALLER_NAME_MANUAL_LOCAL = "Manual Install (Local File)"
    const val INSTALLER_NAME_MANUAL_DOWNLOADED = "Manual Install (Downloaded File)"
    const val INSTALLER_NAME_UNKNOWN_OR_ADB = "Unknown / ADB Install"


    /**
     * Retrieves information about how the current application was installed.
     *
     * Provides the installer's package name, a friendly name (if known),
     * the installation method type (where available), and device details.
     *
     * @param context The application context.
     * @return A Map where keys are the `RESULT_KEY_*` constants defined in this object
     *         (e.g., [RESULT_KEY_INSTALLER_PACKAGE], [RESULT_KEY_INSTALLER_NAME], etc.)
     *         and values describe the installation source.
     */
    @SuppressLint("MemberExtensionConflict")
    fun getInstallSource(context: Context): Map<String, String> {
        var rawInstallerPackageName: String? = null
        var packageSourceType = SOURCE_TYPE_UNKNOWN
        val pm: PackageManager = context.packageManager
        val currentPackageName: String = context.packageName

        // 1. Get Installer Package Name using the appropriate API level method
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            try {
                // Android 11 (R) and above: Use InstallSourceInfo
                val installSourceInfo = pm.getInstallSourceInfo(currentPackageName)
                rawInstallerPackageName = installSourceInfo.installingPackageName // Can be null

                // 2. Get Detailed Package Source (Android 13 / Tiramisu+)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    try {
                        packageSourceType = when (installSourceInfo.packageSource) {
                            PackageInstaller.PACKAGE_SOURCE_STORE -> SOURCE_TYPE_STORE
                            PackageInstaller.PACKAGE_SOURCE_LOCAL_FILE -> SOURCE_TYPE_LOCAL_FILE
                            PackageInstaller.PACKAGE_SOURCE_DOWNLOADED_FILE -> SOURCE_TYPE_DOWNLOADED_FILE
                            PackageInstaller.PACKAGE_SOURCE_OTHER -> SOURCE_TYPE_OTHER
                            PackageInstaller.PACKAGE_SOURCE_UNSPECIFIED -> SOURCE_TYPE_UNSPECIFIED
                            else -> SOURCE_TYPE_UNKNOWN // Should ideally not happen
                        }
                    } catch (e: Throwable) {
                        // Catch Throwable for wider compatibility (e.g., NoSuchFieldError)
                        Log.w(
                            TAG,
                            "Failed to get packageSource on Tiramisu+ (API ${Build.VERSION.SDK_INT}). Device vendor might not fully implement the API.",
                            e
                        )
                        packageSourceType = SOURCE_TYPE_VENDOR_UNSUPPORTED
                    }
                } else {
                    // Android 11 or 12: Detailed source not available via this API
                    packageSourceType = SOURCE_TYPE_PRE_TIRAMISU
                }

            } catch (e: Exception) {
                // Catch potential SecurityException or others if query permission is missing (rare for own package)
                Log.e(
                    TAG,
                    "Failed to get InstallSourceInfo (API ${Build.VERSION.SDK_INT}) for $currentPackageName",
                    e
                )
                rawInstallerPackageName = null // Ensure null on failure
                packageSourceType = SOURCE_TYPE_ERROR_R_PLUS
            }
        } else {
            // Android 10 (Q) and below: Use legacy API
            try {
                @Suppress("DEPRECATION") // Required for targeting older versions
                rawInstallerPackageName =
                    pm.getInstallerPackageName(currentPackageName) // Can be null or empty
                packageSourceType = SOURCE_TYPE_LEGACY_API // Indicate the method used
            } catch (e: Exception) {
                Log.e(
                    TAG,
                    "Failed to get InstallerPackageName (Legacy API) for $currentPackageName",
                    e
                )
                rawInstallerPackageName = null
                packageSourceType = SOURCE_TYPE_ERROR_LEGACY
            }
        }

        // 3. Determine Resolved Installer Name, Package, and Store Status
        var resolvedInstallerPackage = INSTALLER_PACKAGE_UNKNOWN
        var resolvedInstallerName = INSTALLER_NAME_UNKNOWN
        var isFromKnownStore = false

        if (!rawInstallerPackageName.isNullOrEmpty()) {
            // We have a non-empty installer package name
            resolvedInstallerPackage = rawInstallerPackageName // Use the actual package name

            if (STORES.containsKey(rawInstallerPackageName)) {
                // It's a store we recognize
                resolvedInstallerName =
                    STORES[rawInstallerPackageName] ?: INSTALLER_NAME_UNKNOWN // Use friendly name
                isFromKnownStore = true
            } else {
                // It's some other app (e.g., a file manager, browser, MDM, unknown store)
                resolvedInstallerName = INSTALLER_NAME_UNKNOWN_INSTALLER_APP
                // Optional: You could try to query the Package Manager for the app label of
                // 'rawInstallerPackageName' here for a more specific name, but handle exceptions.
                // runCatching { pm.getApplicationLabel(pm.getApplicationInfo(rawInstallerPackageName, 0)).toString() }
                //     .getOrNull()?.let { resolvedInstallerName = it }
                isFromKnownStore = false
            }
        } else {
            // Installer package name is null or empty (e.g., ADB install, manual install via some methods)
            // Use packageSourceType (if available and specific) to refine the description
            when (packageSourceType) {
                SOURCE_TYPE_LOCAL_FILE -> {
                    resolvedInstallerPackage = INSTALLER_PACKAGE_MANUAL_INSTALL
                    resolvedInstallerName = INSTALLER_NAME_MANUAL_LOCAL
                }

                SOURCE_TYPE_DOWNLOADED_FILE -> {
                    resolvedInstallerPackage = INSTALLER_PACKAGE_MANUAL_DOWNLOADED
                    resolvedInstallerName = INSTALLER_NAME_MANUAL_DOWNLOADED
                }
                // Add more specific handling for SOURCE_TYPE_OTHER or SOURCE_TYPE_UNSPECIFIED if needed
                else -> {
                    // Default for null/empty installer and non-specific source
                    resolvedInstallerPackage = INSTALLER_PACKAGE_UNKNOWN_OR_ADB
                    resolvedInstallerName = INSTALLER_NAME_UNKNOWN_OR_ADB
                }
            }
            isFromKnownStore = false // Cannot be from a known store if package name is null/empty
        }

        // 4. Assemble Result Map
        return mapOf(
            RESULT_KEY_INSTALLER_PACKAGE to resolvedInstallerPackage,
            RESULT_KEY_INSTALLER_NAME to resolvedInstallerName,
            RESULT_KEY_PACKAGE_SOURCE to packageSourceType,
            RESULT_KEY_IS_FROM_KNOWN_STORE to isFromKnownStore.toString(),
        )
    }
}