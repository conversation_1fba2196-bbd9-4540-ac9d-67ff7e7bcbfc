package com.heart.heartmerge.utils

/**
 * 作者：Lxf
 * 创建日期：2024/7/31 11:57
 * 描述：moshi 扩展类 方便json转换
 */
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.JsonReader
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.squareup.moshi.internal.Util
import okio.Buffer
import okio.BufferedSource
import java.io.InputStream
import java.lang.reflect.ParameterizedType
import java.lang.reflect.Type
object MoshiUtil {
    val moshiBuild: Moshi = Moshi.Builder().build()
    // 使用 Kotlin-Reflect 时取消注释下面这行
    // val moshiBuild = Moshi.Builder().add(KotlinJsonAdapterFactory()).build()

    // 普通序列化（需要传入类型）
    fun <T> fromJson(json: String, type: Type): T? = getAdapter<T>(type).fromJson(json)
    fun <T> fromJson(buffer: BufferedSource, type: Type): T? = getAdapter<T>(type).fromJson(buffer)
    fun <T> fromJson(`is`: InputStream, type: Type): T? = getAdapter<T>(type).fromJson(Buffer().readFrom(`is`))
    fun <T> fromJson(reader: JsonReader, type: Type): T? = getAdapter<T>(type).fromJson(reader)

    // 自动获取 type 序列化，性能较差
    inline fun <reified T> fromJson(json: String): T? = getAdapter<T>().fromJson(json)
    inline fun <reified T> fromJson(buffer: BufferedSource): T? = getAdapter<T>().fromJson(buffer)
    inline fun <reified T> fromJson(`is`: InputStream): T? = getAdapter<T>().fromJson(Buffer().readFrom(`is`))
    inline fun <reified T> fromJson(reader: JsonReader): T? = getAdapter<T>().fromJson(reader)

    // 高效序列化为 List
    inline fun <reified T> listFromJson(json: String): MutableList<T> =
        fromJson(json, Types.newParameterizedType(MutableList::class.java, T::class.java)) ?: mutableListOf()

    inline fun <reified T> listFromJson(buffer: BufferedSource): MutableList<T> =
        fromJson(buffer, Types.newParameterizedType(MutableList::class.java, T::class.java)) ?: mutableListOf()

    inline fun <reified T> listFromJson(`is`: InputStream): MutableList<T> =
        fromJson(`is`, Types.newParameterizedType(MutableList::class.java, T::class.java)) ?: mutableListOf()

    inline fun <reified T> listFromJson(reader: JsonReader): MutableList<T> =
        fromJson(reader, Types.newParameterizedType(MutableList::class.java, T::class.java)) ?: mutableListOf()

    // 高效序列化为 Map
    inline fun <reified K, reified V> mapFromJson(json: String): MutableMap<K, V> =
        fromJson(json, Types.newParameterizedType(MutableMap::class.java, K::class.java, V::class.java)) ?: mutableMapOf()

    inline fun <reified K, reified V> mapFromJson(buffer: BufferedSource): MutableMap<K, V> =
        fromJson(buffer, Types.newParameterizedType(MutableMap::class.java, K::class.java, V::class.java)) ?: mutableMapOf()

    inline fun <reified K, reified V> mapFromJson(`is`: InputStream): MutableMap<K, V> =
        fromJson(`is`, Types.newParameterizedType(MutableMap::class.java, K::class.java, V::class.java)) ?: mutableMapOf()

    inline fun <reified K, reified V> mapFromJson(reader: JsonReader): MutableMap<K, V> =
        fromJson(reader, Types.newParameterizedType(MutableMap::class.java, K::class.java, V::class.java)) ?: mutableMapOf()

    // 序列化为 JSON
    inline fun <reified T> toJson(t: T): String = getAdapter<T>().toJson(t) ?: ""

    // 获取适配器（支持类型）
    fun <T> getAdapter(type: Type): JsonAdapter<T> = moshiBuild.adapter(type)
    inline fun <reified T> getAdapter(): JsonAdapter<T> = moshiBuild.adapter(T::class.java)
}

// 用于处理类型信息的 TypeToken（优化了类型擦除问题）
abstract class TypeToken<T> {
    val type: Type? get() = run {
        val superclass = javaClass.genericSuperclass
        if (superclass is ParameterizedType) {
            (superclass as ParameterizedType).actualTypeArguments.getOrNull(0)?.let { Util.canonicalize(it) }
        } else {
            null
        }
    }
}

// 快捷序列化
inline fun <reified T: Any> String.fromJson(): T? = MoshiUtil.fromJson(this)

// 快捷反序列化
fun Any.toJson(): String = MoshiUtil.toJson(this)
