package com.heart.heartmerge.utils;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 用于获取Google广告ID的辅助类
 */
public class GoogleAdvertisingIdHelper {

    public interface AdvertisingIdCallback {
        void onAdvertisingIdObtained(String advertisingId);
        void onFailure(Exception e);
    }

    /**
     * 获取Google广告ID
     * 
     * @param context 上下文
     * @param callback 回调接口
     */
    public static void getAdvertisingId(Context context, AdvertisingIdCallback callback) {
        ExecutorService executor = Executors.newSingleThreadExecutor();
        Handler handler = new Handler(Looper.getMainLooper());
        
        executor.execute(() -> {
            try {
                String advertisingId = AdvertisingIdClient.getGoogleAdId(context);
                handler.post(() -> {
                    if (advertisingId != null && !advertisingId.isEmpty() && 
                        !advertisingId.startsWith("Cannot call")) {
                        callback.onAdvertisingIdObtained(advertisingId);
                    } else {
                        callback.onFailure(new Exception("Failed to get advertising ID"));
                    }
                });
            } catch (Exception e) {
                handler.post(() -> callback.onFailure(e));
            }
        });
    }
}
