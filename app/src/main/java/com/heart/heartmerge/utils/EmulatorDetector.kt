package com.heart.heartmerge.utils

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorManager
import android.os.Build
import android.telephony.TelephonyManager
import java.io.File

/**
 * 一个全面的模拟器检测工具类。
 *
 * 通过组合多种检测策略并使用计分系统来提高准确性。
 * 每个检测方法如果命中，会返回一个分数。总分超过阈值即被认为是模拟器。
 *
 * 使用方法:
 * ```
 * if (EmulatorDetector.isEmulator(context)) {
 * // 在模拟器上运行的逻辑
 * } else {
 * // 在真机上运行的逻辑
 * }
 * ```
 */
object EmulatorDetector {

    /**
     * 模拟器检测阈值。
     * 当检测得分达到或超过此值时，设备被判定为模拟器。
     * 你可以根据自己的严格程度调整此值。较高的值会减少误判，但可能漏掉一些高级模拟器。
     */
    private const val DETECTION_THRESHOLD = 1

    private val knownEmulatorFiles = arrayOf(
        "/system/lib/libdroid4x.so",
        "/system/bin/qemu-props",
        "/system/bin/qemud",
        "/dev/socket/qemud",
        "/dev/qemu_pipe"
    )

    private val knownEmulatorPackages = arrayOf(
        "com.bignox.app.player", // 夜神
        "com.bluestacks",       // 蓝叠
        "com.microvirt.tools",  // 逍遥
        "com.mumu.store"        // mumu
    )

    /**
     * 主检测方法，综合所有检查并返回最终结果。
     * @param context Context 对象。
     * @return 如果设备被认为是模拟器，则返回 `true`。
     */
    fun isEmulator(context: Context): Boolean {
        var score = 0

        score += checkBuildProperties()
        score += checkCpuArchitecture()
        score += checkEmulatorFiles()
        score += checkSensors(context)
        score += checkOperatorName(context)
        score += checkInstalledPackages(context)

        // RootBeer 可以作为加分项，因为模拟器开启Root的嫌疑非常大
        // 如果你需要引入 RootBeer:
        // val rootBeer = RootBeer(context)
        // if (rootBeer.isRooted) {
        //     score += 2 // Rooted an high score
        // }

        return score >= DETECTION_THRESHOLD
    }

    /**
     * [检测点 1] 检查系统 Build 属性。
     * 模拟器通常在系统属性中留下特定痕迹。
     * @return 命中则返回 1 分，否则返回 0 分。
     */
    private fun checkBuildProperties(): Int {
        val properties = listOf(
            Build.FINGERPRINT.startsWith("generic"),
            Build.FINGERPRINT.contains("vbox", true),
            Build.FINGERPRINT.contains("test-keys", true),
            Build.MODEL.contains("google_sdk", true),
            Build.MODEL.contains("Emulator", true),
            Build.MODEL.contains("Android SDK built for x86", true),
            Build.MANUFACTURER.contains("Genymotion", true),
            (Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic")),
            "google_sdk" == Build.PRODUCT,
            Build.HARDWARE.contains("goldfish", true),
            Build.HARDWARE.contains("vbox", true),
            Build.HARDWARE.contains("ranchu", true) // 新版模拟器的 QEMU 硬件名称
        )

        return if (properties.any { it }) 1 else 0
    }

    /**
     * [检测点 2] 检查 CPU 架构。
     * 绝大多数真机是 ARM 架构，而 PC 上的模拟器是 x86 架构。
     * @return 命中则返回 1 分，否则返回 0 分。
     */
    private fun checkCpuArchitecture(): Int {
        val supportedAbis = Build.SUPPORTED_ABIS
        return if (supportedAbis?.any { it.contains("x86", true) } == true) 1 else 0
    }

    /**
     * [检测点 3] 检查是否存在模拟器特有的文件。
     * @return 命中则返回 1 分，否则返回 0 分。
     */
    private fun checkEmulatorFiles(): Int {
        return if (knownEmulatorFiles.any { File(it).exists() }) 1 else 0
    }

    /**
     * [检测点 4] 检查硬件传感器。
     * 模拟器通常缺少真实的光线、加速度等传感器。
     * @param context Context 对象。
     * @return 命中则返回 1 分，否则返回 0 分。
     */
    private fun checkSensors(context: Context): Int {
        val sensorManager = context.getSystemService(Context.SENSOR_SERVICE) as? SensorManager
        if (sensorManager == null) {
            return 1 // 无法获取传感器服务，嫌疑很大
        }

        val hasLightSensor = sensorManager.getDefaultSensor(Sensor.TYPE_LIGHT) != null
        val hasAccelerometer = sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER) != null

        return if (!hasLightSensor || !hasAccelerometer) 1 else 0
    }

    /**
     * [检测点 5] 检查网络运营商信息。
     * 模拟器通常返回默认的、非真实的运营商名称，如 "Android"。
     * @param context Context 对象。
     * @return 命中则返回 1 分，否则返回 0 分。
     */
    private fun checkOperatorName(context: Context): Int {
        val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as? TelephonyManager
        if (telephonyManager == null) {
            return 0 // 没有电话功能是正常的 (例如平板)
        }
        val operatorName = telephonyManager.networkOperatorName
        return if ("android".equals(operatorName, ignoreCase = true)) 1 else 0
    }

    /**
     * [检测点 6] 检查是否安装了模拟器厂商的特定应用。
     * @param context Context 对象。
     * @return 命中则返回 1 分，否则返回 0 分。
     */
    private fun checkInstalledPackages(context: Context): Int {
        val pm = context.packageManager
        val installedPackages = pm.getInstalledPackages(0)
        for (pkgInfo in installedPackages) {
            if (knownEmulatorPackages.contains(pkgInfo.packageName)) {
                return 1
            }
        }
        return 0
    }
}