package com.heart.heartmerge.utils

import android.os.CountDownTimer

class FlexibleCountdownTimer(
    private val totalTimeInMillis: Long = 60000, // 默认60秒
    private val intervalInMillis: Long = 1000, // 默认每秒回调一次
    private val onTick: (Long) -> Unit, private val onFinish: () -> Unit
) {
    private var countDownTimer: CountDownTimer? = null
    private var timeRemaining: Long = 0

    fun start() {
        countDownTimer?.cancel()
        timeRemaining = totalTimeInMillis

        countDownTimer = object : CountDownTimer(totalTimeInMillis, intervalInMillis) {
            override fun onTick(millisUntilFinished: Long) {
                timeRemaining = millisUntilFinished
                <EMAIL>(millisUntilFinished / 1000)
            }

            override fun onFinish() {
                timeRemaining = 0
                <EMAIL>()
            }
        }.start()
    }

    fun cancel() {
        countDownTimer?.cancel()
        timeRemaining = 0
    }

    fun isRunning(): Boolean {
        return timeRemaining > 0
    }

    fun getTimeRemaining(): Long {
        return timeRemaining / 1000 // 返回剩余秒数
    }
}