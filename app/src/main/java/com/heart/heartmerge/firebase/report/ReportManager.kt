package com.heart.heartmerge.firebase.report


/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2024/8/28 9:23
 * @description :Firebase数据上报
 */
import ai.datatower.analytics.DTAnalytics
import android.os.Bundle
import com.appsflyer.AppsFlyerLib
import com.bdc.android.library.utils.AppManager
import com.facebook.appevents.AppEventsConstants
import com.facebook.appevents.AppEventsLogger
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.crashlytics.BuildConfig
import com.google.firebase.crashlytics.FirebaseCrashlytics
import org.json.JSONObject

object DT_EVENT_CONSTANTS {
    const val EVENT_PREPARE_LOGIN = "dt_event_prepare_login"
    const val EVENT_LOGIN = "dt_event_login"
    const val EVENT_REGISTRATION = "dt_event_registration"
    const val EVENT_CHECKOUT = "dt_event_checkout"
    const val EVENT_VIDEO_CALL = "dt_event_video_call"
    const val EVENT_SHARE = "dt_event_share"
}

object ReportManager {

    private val firebaseAnalytics: FirebaseAnalytics by lazy {
        FirebaseAnalytics.getInstance(
            AppManager.getApplication()
        )
    }

    private val appEventsLogger =
        runCatching { AppEventsLogger.newLogger(AppManager.getApplication()) }.getOrNull()

    private val crashlytics: FirebaseCrashlytics = FirebaseCrashlytics.getInstance()

    fun setUserProperty(name: String, userId: String) {
        firebaseAnalytics.setUserId(userId)
        firebaseAnalytics.setUserProperty(name, userId)
        DTAnalytics.setAccountId(userId)
        JSONObject().apply {
            put("username", name)
            put("userId", userId)
        }.apply {
            DTAnalytics.userSetOnce(this)
            DTAnalytics.userSet(this)
        }
        AppsFlyerLib.getInstance().setCustomerUserId(userId)
        setCustomKey(name, userId)
    }

    fun logRegistration(params: Map<String, Any>? = null) {
//        AppsFlyerLib.getInstance()
//            .logEvent(ContextHolder.context, AFInAppEventType.COMPLETE_REGISTRATION, params)
//        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.SIGN_UP, mapToBundle(params))
        appEventsLogger?.logEvent(
            AppEventsConstants.EVENT_NAME_COMPLETED_REGISTRATION, mapToBundle(params)
        )
        logEvent(DT_EVENT_CONSTANTS.EVENT_REGISTRATION, params)
    }

    fun logLogin(params: Map<String, Any>? = null) {
//        AppsFlyerLib.getInstance().logEvent(ContextHolder.context, AFInAppEventType.LOGIN, params)
//        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.LOGIN, mapToBundle(params))
//        appEventsLogger?.logEvent("fb_mobile_login", mapToBundle(params))
        logEvent(DT_EVENT_CONSTANTS.EVENT_LOGIN, params)
    }

    fun logEvent(eventName: String, params: Map<String, Any>? = null) {
//        if (BuildConfig.DEBUG) {
//        val bundle = mapToBundle(params)
//            AppsFlyerLib.getInstance().logEvent(ContextHolder.context, eventName, params)
//            firebaseAnalytics.logEvent(eventName, bundle)
//            appEventsLogger?.logEvent(eventName, bundle)
        DTAnalytics.track(eventName, params)
//        }
    }

    fun logCheckout(
        params: Map<String, Any>? = null
    ) {
        logEvent(DT_EVENT_CONSTANTS.EVENT_CHECKOUT, params)
    }

    fun logPurchase(
        order: String, sku: String, price: Double, currency: String, properties: Map<String, Any>?
    ) {
        if (!BuildConfig.DEBUG) {
            //调整为服务端上报
//            DTIAPReport.reportPurchaseSuccess(
//                order, sku, price, currency, properties as MutableMap<String, Any>?
//            )
//            logEvent(FirebaseAnalytics.Event.PURCHASE, buildMap {
//                put("id", order)
//                put("sku", sku)
//                put("price", price)
//                put("currency", currency)
//            })
//            appEventsLogger?.logPurchase(
//                BigDecimal.valueOf(price), java.util.Currency.getInstance(currency)
//            )
//            AppsFlyerLib.getInstance()
//                .logEvent(ContextHolder.context, AFInAppEventType.PURCHASE, properties)
        }
    }

    fun logSubscribe(
        originalOrderId: String,
        orderId: String,
        sku: String,
        price: Double,
        currency: String,
        properties: Map<String, Any>? = null
    ) {
        if (!BuildConfig.DEBUG) {
            //调整为服务端上报
//            DTIASReport.reportSubscribeSuccess(
//                originalOrderId,
//                orderId,
//                sku,
//                price,
//                currency,
//                properties as MutableMap<String, Any>?
//            )
//            logEvent(FirebaseAnalytics.Event.PURCHASE, buildMap {
//                put("id", originalOrderId)
//                put("orderId", orderId)
//                put("sku", sku)
//                put("price", price)
//                put("currency", currency)
//            })
//            appEventsLogger?.logPurchase(
//                BigDecimal.valueOf(price), java.util.Currency.getInstance(currency)
//            )
//            appEventsLogger?.logEvent(AppEventsConstants.EVENT_NAME_SUBSCRIBE, Bundle().apply {
//                putString("id", originalOrderId)
//                putString("orderId", orderId)
//                putString("sku", sku)
//                putDouble("price", price)
//                putDouble("valueToSum", price)
//                putString("currency", currency)
//            })
//            AppsFlyerLib.getInstance()
//                .logEvent(ContextHolder.context, AFInAppEventType.SUBSCRIBE, properties)
        }
    }

    private fun mapToBundle(params: Map<String, Any?>? = null): Bundle {
        val bundle = Bundle()
        params?.forEach { (key, value) ->
            when (value) {
                is String -> bundle.putString(key, value)
                is Int -> bundle.putInt(key, value)
                is Long -> bundle.putLong(key, value)
                is Double -> bundle.putDouble(key, value)
                is Boolean -> bundle.putBoolean(key, value)
                else -> bundle.putString(key, value.toString())
            }
        }
        return bundle
    }

    fun logException(throwable: Throwable) {
        if (!BuildConfig.DEBUG) {
            crashlytics.recordException(throwable)
        }
    }

    fun setCustomKey(key: String, value: String) {
        crashlytics.setCustomKey(key, value)
    }

    fun log(message: String) {
        if (!BuildConfig.DEBUG) {
            crashlytics.log(message)
        }
    }
}