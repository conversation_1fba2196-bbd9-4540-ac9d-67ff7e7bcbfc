package com.heart.heartmerge.extension

import androidx.core.text.isDigitsOnly
import com.android.billingclient.api.BillingClient
import com.heart.heartmerge.beans.GoodsBean
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.payment.BillingService

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/15 14:20
 * @description :价格显示处理
 */
fun Int.applyVIPPrice(force: Boolean = false): Int {
    val discount =
        MMKVDataRep.configuration?.takeIf { bean -> bean.vipVideoCallSpecialOffer.isDigitsOnly() }
            ?.let { string -> (100 - string.vipVideoCallSpecialOffer.toInt()) / 100F }
    return if (force || MMKVDataRep.userInfo.isVIP) (this * (discount ?: 1F)).toInt() else this
}

suspend fun List<GoodsBean>.applyGooglePrice(
    billingService: BillingService, block: () -> Unit = {}
) {
    val iapList = this.filter { !it.isSubscribe }.takeIf { it.isNotEmpty() }?.let {
        billingService.fetchProductList(it.map { it.sku }, BillingClient.ProductType.INAPP)
    }

    val subscribeList = this.filter { it.isSubscribe }.takeIf { it.isNotEmpty() }?.let {
        billingService.fetchProductList(it.map { it.sku }, BillingClient.ProductType.SUBS)
    }
    this.forEach { item ->
        item.googleExtras = iapList?.find { it.productId == item.sku }
            ?: subscribeList?.find { it.productId == item.sku }
    }
    block.invoke()
}