package com.heart.heartmerge.extension

import android.text.Html
import android.text.Spanned
import androidx.core.text.isDigitsOnly
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.DslLoadMoreItem.Companion.LOAD_MORE_ERROR
import com.angcyo.dsladapter.DslLoadMoreItem.Companion.LOAD_MORE_LOADING
import com.angcyo.dsladapter.DslLoadMoreItem.Companion.LOAD_MORE_NORMAL
import com.angcyo.dsladapter.DslLoadMoreItem.Companion.LOAD_MORE_NO_MORE
import com.angcyo.dsladapter.DslLoadMoreItem.Companion._LOAD_MORE_RETRY
import com.angcyo.dsladapter._dslAdapter
import com.bdc.android.library.refreshlayout.XRecyclerView
import com.bdc.android.library.utils.DateUtil
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.GoodsBean
import com.heart.heartmerge.beans.GoodsWrapperBean
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond
import com.heart.heartmerge.utils.ContextHolder
import com.heart.heartmerge.utils.FlexibleCountdownTimer


inline fun XRecyclerView.openLoadMore(crossinline loadMore: () -> Unit) {
    enableLoadMore(true)
    dslAdapter.apply {
        dslLoadMoreItem.apply {
            itemStateLayoutMap[LOAD_MORE_NORMAL] = R.layout.more_loading_layout
            itemStateLayoutMap[LOAD_MORE_LOADING] = R.layout.more_loading_layout
            itemStateLayoutMap[LOAD_MORE_NO_MORE] = R.layout.more_no_more_layout
            itemStateLayoutMap[LOAD_MORE_ERROR] = R.layout.more_error_layout
            itemStateLayoutMap[_LOAD_MORE_RETRY] = R.layout.more_error_layout
            onLoadMore = {
                it.postDelay(300L) {
                    render {
                        loadMore.invoke()
                        setLoadMore(LOAD_MORE_LOADING)
                    }
                }
            }
        }
    }
}

fun XRecyclerView.applyMembershipRights(
    vipCallSpecialOfferBonus: String = "",
    checkInBonus: String = "",
    matchSpecialOfferBonus: String = "",
    privileges: List<GoodsWrapperBean.PrivilegeBean>? = null,
    index: Int = 0
) {
    val list = listOf(
//            Triple(
//                R.mipmap.ic_membership_rights_avatar_border,
//                getString(R.string.membership_benefits_avatar_border),
//                getString(R.string.membership_benefits_avatar_border_desc)
//            ),

        Triple(
            R.mipmap.ic_membership_rights_video_call,
            this.context.getString(R.string.membership_benefits_video_call),
            this.context.getString(
                R.string.membership_benefits_video_call_desc, "${vipCallSpecialOfferBonus}%"
            )
        ), Triple(
            R.mipmap.ic_membership_rights_unlimited_msg,
            this.context.getString(R.string.membership_benefits_unlimited_messages),
            this.context.getString(R.string.membership_benefits_unlimited_messages_desc)
        ), Triple(
            R.mipmap.ic_membership_rights_check_bonus,
            this.context.getString(R.string.membership_benefits_check_bonus),
            this.context.getString(R.string.membership_benefits_check_bonus_desc, checkInBonus)
        ), Triple(
            R.mipmap.ic_membership_rights_match_special_offer,
            this.context.getString(R.string.membership_benefits_match_special_offer),
            this.context.getString(
                R.string.membership_benefits_match_special_offer_desc, matchSpecialOfferBonus
            )
        ), Triple(
            R.mipmap.ic_membership_rights_who_see_me,
            this.context.getString(R.string.membership_benefits_who_see_me),
            this.context.getString(R.string.membership_benefits_who_see_me_desc)
        ), Triple(
            R.mipmap.ic_membership_rights_photo_voice_msg,
            this.context.getString(R.string.membership_benefits_photo_voice_msg),
            this.context.getString(R.string.membership_benefits_photo_voice_msg_desc)
        )
    )
    clearAllItems()
    append<DslAdapterItem>(privileges) {
        itemLayoutId = R.layout.item_membership_rights
        itemBindOverride = { itemHolder, itemPosition, adapterItem, payloads ->
            val item = itemData as? GoodsWrapperBean.PrivilegeBean
//            itemHolder.img(R.id.iv_rights)?.setImageResource(item?.first ?: 0)
            itemHolder.tv(R.id.tv_title)?.apply {
                text = item?.title
            }
            itemHolder.tv(R.id.tv_desc)?.text = renderSubtitleHtml(
                item?.sub_title, item?.kvs, index + 1
            )
        }
    }
}

fun renderSubtitleHtml(
    subTitle: String?,
    kvs: List<GoodsWrapperBean.PrivilegeBean.KvBean>?,
    index: Int,
    colorHex: String = "#EC12E2"
): Spanned {
    val kv = kvs?.find { it.level == index } ?: return Html.fromHtml(subTitle ?: "")
    val newValue = if (kv.value.isNotEmpty() && kv.value.isDigitsOnly()) {
        kv.value.toInt().toShowDiamond().toString()
    } else {
        kv.value
    }

    val coloredValue = "<font color=\"$colorHex\">$newValue</font>"
    val rendered = subTitle?.replace(kv.key, coloredValue) ?: coloredValue
    return Html.fromHtml(rendered, Html.FROM_HTML_MODE_LEGACY)
}

fun XRecyclerView.applyRechargeCountdown(
    targetList: List<GoodsBean>? = null, onFinish: (Int) -> Unit = {}
): FlexibleCountdownTimer? {
    val maxTimestamp = targetList?.filter { it.hasCountdown }?.maxOfOrNull { it.deadline } ?: 0L

    if (maxTimestamp <= 0) {
        onFinish.invoke(-1)
        return null
    }

    var currentItemId: String? = null
    var count = 0
    var flexibleCountdownTimer: FlexibleCountdownTimer? = null
    flexibleCountdownTimer = FlexibleCountdownTimer(
        totalTimeInMillis = (maxTimestamp - System.currentTimeMillis()),
        onTick = {
            targetList?.filter { it.hasCountdown }?.minByOrNull { it.deadline }?.let { item ->
                val timeDiff = item.deadline - System.currentTimeMillis()
                val current = _dslAdapter?.dataItems?.find {
                    ((it.itemData as? GoodsBean)?.deadline ?: 0) > 0
                }
                current?.itemData = item

                if (currentItemId != null && currentItemId != item.id) {
                    onFinish.invoke(count)
                    count += 1
                }

                if (timeDiff > 0) {
                    (current?.itemData as? GoodsBean)?.countdown = ContextHolder.context.getString(
                        R.string.first_recharge_text, DateUtil.formatCountdown((timeDiff) / 1000)
                    )
                } else {
                    current?.removeAdapterItem()
                }

//                    if (MMKVDataRep.userInfo.alreadyFirstRecharge) {
//                        current?.removeAdapterItem()
//                        flexibleCountdownTimer?.cancel()
//                        onFinish.invoke(-1)
//                    }
                currentItemId = item.id
                current?.updateItemDepend()
            }
        },
        onFinish = {
            _dslAdapter?.dataItems?.mapNotNull {
                it.takeIf { item ->
                    ((item.itemData as? GoodsBean)?.deadline
                        ?: 0) > 0 && ((item.itemData as? GoodsBean)?.deadline
                        ?: 0) <= System.currentTimeMillis()
                }
            }?.onEach {
                it.removeAdapterItem()
                it.updateItemDepend()
            }
            onFinish.invoke(-1)
        })

    flexibleCountdownTimer.start()
    return flexibleCountdownTimer
}
