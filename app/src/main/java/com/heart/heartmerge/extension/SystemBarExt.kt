package com.heart.heartmerge.extension

import com.gyf.immersionbar.ImmersionBar
import com.bdc.android.library.R

/**
 *
 * @Author： Lxf
 * @Time： 2022/4/12 3:34 下午
 * @description： 状态栏 导航栏扩展属性设置  默认不全屏 状态栏和底部导航栏 都显示为白色黑色图标
 */
fun ImmersionBar.commonSetup(isLight: Boolean): ImmersionBar {
    navigationBarDarkIcon(isLight)
    statusBarDarkFont(isLight)
    if (isLight) {
        statusBarColor(R.color.white)
        navigationBarColor(R.color.white)
    } else {
        statusBarColor(R.color.black)
        navigationBarColor(R.color.black)
    }
    return this
}
