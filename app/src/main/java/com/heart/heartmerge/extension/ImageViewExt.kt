package com.heart.heartmerge.extension

import android.graphics.Bitmap
import android.media.MediaMetadataRetriever
import android.widget.ImageView
import coil.ImageLoader
import coil.load
import coil.request.ImageRequest
import com.heart.heartmerge.utils.BlurTransformation
import coil.transform.CircleCropTransformation
import coil.transform.RoundedCornersTransformation
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool
import com.heart.heartmerge.R
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.utils.ContextHolder
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Author:Lxf
 * Create on:2024/9/14
 * Description:
 */

fun ImageView.loadAvatar(url: String) {
    load(url) {
        crossfade(true)
        error(R.mipmap.ic_pic_default_oval)
        placeholder(R.mipmap.ic_pic_default_oval)
        transformations(CircleCropTransformation())
    }
}

fun ImageView.loadAnchorImage(
    url: String, radio: Float = ContextHolder.context.resources.getDimension(
        com.bdc.android.library.R.dimen.dp_16
    )
) {
    load(url) {
        crossfade(true)
        error(R.mipmap.ic_default_anchor_bg)
        placeholder(R.mipmap.ic_default_anchor_bg)
        transformations(RoundedCornersTransformation(radio))
    }
}

fun ImageView.loadAnchorImageWithBlur(
    url: String, radio: Float = ContextHolder.context.resources.getDimension(
        com.bdc.android.library.R.dimen.dp_16
    )
) {
    load(url) {
        crossfade(true)
        error(R.mipmap.ic_default_anchor_bg)
        placeholder(R.mipmap.ic_default_anchor_bg)
        transformations(BlurTransformation(context, 2f, 16f, radio))
    }
}

fun ImageView.loadVideoFirstFrame(
    videoUrl: String,
    radius: Float = ContextHolder.context.resources.getDimension(com.bdc.android.library.R.dimen.dp_16),
    needBlur: Boolean = false,
    blur: Float = 2f,
    sampling: Float = 16f,
    placeholderRes: Int = R.mipmap.ic_default_anchor_bg
) {
    CoroutineScope(Dispatchers.Main).launch {
        val bitmap = withContext(Dispatchers.IO) {
            extractFirstFrameFromUrl(videoUrl)
        }

        if (bitmap != null) {
            val loader = ImageLoader(ContextHolder.context)
            val transformations = if (needBlur) {
                listOf(
                    BlurTransformation(ContextHolder.context, blur, sampling, radius)
                )
            } else {
                listOf(
                    RoundedCornersTransformation(radius)
                )
            }

            val request = ImageRequest.Builder(ContextHolder.context)
                .data(bitmap)
                .placeholder(placeholderRes)
                .error(placeholderRes)
                .transformations(transformations)
                .target(this@loadVideoFirstFrame)
                .build()

            loader.enqueue(request)
        } else {
            setImageResource(placeholderRes)
        }
    }
}


fun extractFirstFrameFromUrl(url: String): Bitmap? {
    return try {
        val retriever = MediaMetadataRetriever()
        retriever.setDataSource(url, HashMap()) // AWS S3 可加 header
        val bitmap = retriever.getFrameAtTime(1_000_000, MediaMetadataRetriever.OPTION_CLOSEST)
        retriever.release()
        bitmap
    } catch (e: Exception) {
        e.printStackTrace()
        null
    }
}
