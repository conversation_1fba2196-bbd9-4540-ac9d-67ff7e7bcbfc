package com.heart.heartmerge.extension

import com.bdc.android.library.http.exception.RequestException
import com.bdc.android.library.http.request
import com.heart.heartmerge.http.ResultX
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.channelFlow

@JvmName("requestChannelFlowT")
fun <T> requestChannelFlow(block: RequestChannelFlowBuilder<T, T>.() -> Unit): Flow<T> =
    requestChannelFlow<T, T>(block)

fun <T, R> requestChannelFlow(block: RequestChannelFlowBuilder<T, R>.() -> Unit): Flow<R> =
    channelFlow {
        val builder = RequestChannelFlowBuilder<T, R>().apply(block)
        request<T> {
            call { builder.request() }
            onSuccess { result ->
                result?.let {
                    trySend(builder.transform?.let { it1 -> it1(it) } ?: it as R)
                } ?: run {
                    trySend(Any() as R)
                }
            }
            onFailure {
                builder.failure(it)
//                trySend(it as R)
            }
        }
        awaitClose()
    }

class RequestChannelFlowBuilder<T, R> {
    lateinit var request: suspend () -> ResultX<T>
    var transform: ((T) -> R)? = null
    var failure: (RequestException) -> Unit = {}
}