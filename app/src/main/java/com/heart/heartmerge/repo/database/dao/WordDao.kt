package com.heart.heartmerge.repo.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.heart.heartmerge.beans.WordEntity

//@Dao
//interface WordDao {
//    @Insert(onConflict = OnConflictStrategy.REPLACE) //冲突策略替换
//    suspend fun insertWords(words: List<WordEntity>)
//
//    @Query("SELECT * FROM words")
//    suspend fun getAllWords(): List<WordEntity>
//
//    @Query("SELECT * FROM words WHERE type = :type")
//    suspend fun getWordsByType(type: Int): List<WordEntity>
//
//    @Query("SELECT COUNT(*) FROM words")
//    suspend fun getWordCount(): Int
//}