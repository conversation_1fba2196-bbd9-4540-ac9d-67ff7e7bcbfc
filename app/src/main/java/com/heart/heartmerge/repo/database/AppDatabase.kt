package com.heart.heartmerge.repo.database

//@Database(entities = [WordEntity::class], version = 2)
//@TypeConverters(Converters::class)
//abstract class AppDatabase : RoomDatabase() {
////    abstract fun userDao(): UserDao
//
//    abstract fun wordDao(): WordDao
//
//    companion object {
//        private var instance: AppDatabase? = null
//
//        fun getInstance(context: Context): AppDatabase {
//            return instance ?: synchronized(this) {
//                instance ?: buildDatabase(context).also { instance = it }
//            }
//        }
//
//        private fun buildDatabase(context: Context): AppDatabase {
//            return Room.databaseBuilder(
//                context.applicationContext, AppDatabase::class.java, "app_database"
//            ).build()
//        }
//    }
//}