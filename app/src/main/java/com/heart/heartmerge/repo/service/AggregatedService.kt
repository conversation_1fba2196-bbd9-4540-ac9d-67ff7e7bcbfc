package com.heart.heartmerge.repo.service

import com.google.gson.JsonObject
import com.heart.heartmerge.beans.CheckOrderBean
import com.heart.heartmerge.beans.CheckOrderBody
import com.heart.heartmerge.beans.CreateOrderBody
import com.heart.heartmerge.beans.GiftBean
import com.heart.heartmerge.beans.GoodsWrapperBean
import com.heart.heartmerge.beans.OrderBean
import com.heart.heartmerge.beans.PageBean
import com.heart.heartmerge.beans.ReportBean
import com.heart.heartmerge.beans.ResourceBean
import com.heart.heartmerge.beans.UserSource
import com.heart.heartmerge.beans.VersionBean
import com.heart.heartmerge.http.ResultX
import com.heart.heartmerge.i18n.I18nBean
import com.heart.heartmerge.manager.FileUploadManager
import com.heart.heartmerge.repo.service.anchor.AnchorService
import com.heart.heartmerge.repo.service.user.UserService
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query


interface AggregatedService : UserService, AnchorService {

    @GET("/api/v1/common/lang/info")
    suspend fun getLangInfo(@Query("md5") md5: String? = null): ResultX<I18nBean>

    @GET("/api/v1/user/gift/list")
    suspend fun getGiftList(): ResultX<GiftBean>

    @GET("/api/v1/user/goods/gold/list")
    suspend fun getGoodsList(
        @Query("product_type") goodsQueryType: Int/*1普通商品。2vip 商品 3首充商品*/,
        @Query("country_id") countryId: String? = ""
    ): ResultX<GoodsWrapperBean?>

    @POST("/api/v1/pay/order/top_up/create")
    suspend fun createOrder(@Body body: CreateOrderBody): ResultX<OrderBean>

    @POST("/api/v1/user/event/upload")
    suspend fun feedback(@Body params: JsonObject): ResultX<Any>

    @POST("/api/v1/pay/order/top_up/check_v2")
    suspend fun checkOrder(@Body body: CheckOrderBody): ResultX<CheckOrderBean>

    @GET("/mateuser/auth/package/renew")
    suspend fun checkVersion(): ResultX<VersionBean>

    @GET("/api/v1/common/aws/s3/upload_url")
    suspend fun getUploadUrl(
        @Query("bizType") bizType: Int = 1/*0普通业务上传 1日志上传*/,
        @Query("ext") extension: String = FileUploadManager.FileType.IMAGE.extension
    ): ResultX<FileUploadManager.UploadCredentialBean>

    @POST("/api/v1/user/log/upload")
    suspend fun getLogUploadUrl(
        @Body params: Map<String, String>
    ): ResultX<FileUploadManager.UploadCredentialBean>

    @POST("/api/v1/common/report/referer")
    suspend fun reportReferer(@Body params: Map<String, String>): ResultX<UserSource>

    @POST("/api/v1/user/report/data_tower")
    suspend fun reportAF(@Body params: Map<String, String>): ResultX<Any>

    @GET("/api/v1/common/resource/list")
    suspend fun getResourceList(): ResultX<PageBean<ResourceBean>>

    @POST("/api/v1/user/event/upload")
    suspend fun reportEvent(@Body params: Map<String, String>): ResultX<Any>

    @GET("/api/v1/user/report/list")
    suspend fun getReportList(): ResultX<List<ReportBean>>

}

