package com.heart.heartmerge.repo.service.user

import com.bdc.android.library.ktnet.coroutines.Await
import com.google.gson.JsonObject
import com.heart.heartmerge.beans.AnchorList
import com.heart.heartmerge.beans.AuthBean
import com.heart.heartmerge.beans.BackpackBean
import com.heart.heartmerge.beans.CountryBean
import com.heart.heartmerge.beans.DefaultAvatarBean
import com.heart.heartmerge.beans.DiamondFlowBean
import com.heart.heartmerge.beans.DotDisturbBean
import com.heart.heartmerge.beans.GoodsBean
import com.heart.heartmerge.beans.IsolatedConfigBean
import com.heart.heartmerge.beans.LevelWrapperBean
import com.heart.heartmerge.beans.MatchCredentialBean
import com.heart.heartmerge.beans.PageBean
import com.heart.heartmerge.beans.PageTopNotifyBean
import com.heart.heartmerge.beans.SignInBean
import com.heart.heartmerge.beans.SubscribeBenefitBean
import com.heart.heartmerge.beans.TaskCategoryBean
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.beans.WalletTransactionBean
import com.heart.heartmerge.http.ResultX
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

interface UserService {

    /*登录*/
    @POST("/api/v1/user/login")
    suspend fun login(@Body params: Map<String, String>): ResultX<AuthBean>

    @POST("/api/v1/user/bind/google")
    suspend fun googleBind(@Body params: Map<String, String>): ResultX<AuthBean>

    @GET("/api/v1/common/avatar/list")
    suspend fun defaultAvatarList(): ResultX<DefaultAvatarBean>

    @POST("/api/v1/user/logout")
    suspend fun logout(@Query("userId") userId: String): ResultX<Any>

    /*完善资料*/
    @POST("/api/v1/user/update/info")
    suspend fun updateProfile(@Body params: JsonObject): ResultX<AuthBean>

    @GET("/api/v1/user/info")
    suspend fun getUserDetail(
    ): ResultX<AuthBean>

    @GET("/api/v1/user/sign/vip/list")
    suspend fun getSignInList(): ResultX<SignInBean>

    @POST("/api/v1/user/sign/draw")
    suspend fun signIn(@Body params: Map<String, Int>): ResultX<Any>

    @GET("/api/v1/common/login/country/list")
    suspend fun getCountryList(): ResultX<CountryBean>

    @GET("/api/v1/user/coin_record/list")
    suspend fun getWalletTransactionList(
        @Query("cursor") cursor: String, @Query("size") size: Int
    ): ResultX<PageBean<WalletTransactionBean>>

    @POST("/mateuser/user/bag/list")
    suspend fun getBackpackList(): ResultX<List<BackpackBean>>

    @GET("/mateuser/user/matchingCard/num")
    suspend fun getMatchingCardCount(): ResultX<Int>

    @POST("/mateuser/user/matchingCard/list")
    suspend fun getMatchingCardList(@Body params: Map<String, String>): ResultX<PageBean<DiamondFlowBean>>

    @GET("/api/v1/user/relation/{name}/list")
    suspend fun getBlacklistList(
        @Path("name") name: String, @Query("cursor") cursor: String
    ): ResultX<PageBean<UserBean>>

    @GET("/api/v1/user/relation/{name}/list")
    suspend fun getFollowList(
        @Path("name") name: String,
        @Query("cursor") cursor: String,
        @Query("size") size: Int
    ): Await<AnchorList?>

    @POST("/api/v1/user/del")
    suspend fun cancelAccount(): ResultX<Any>

    @POST("/api/v1/user/update/dnd")
    suspend fun updateDisturbStatus(@Body body: RequestBody): Await<DotDisturbBean>

    @POST("/api/v1/user/report/add")
    suspend fun userReport(@Body body: RequestBody): Await<Any?>

    @POST("/api/v1/user/relation/{name}/op")
    suspend fun removeBlack(@Path("name") path: String, @Body params: Map<String, Int>): ResultX<Any?>

    @POST("/mateuser/user/subtract/msg/send")
    suspend fun subtractMsgSend(): Await<Int?>

    @GET("/api/v1/user/relation/anchor_visit_user/list")
    suspend fun getWhoSeeMe(): Await<AnchorList?>

    @GET("/api/v1/user/level/configs")
    suspend fun getLevelConfig(): ResultX<LevelWrapperBean>

    @GET("/api/v1/user/top_up/list")
    suspend fun getRechargeList(
        @Query("product_type") productType: Int,
        @Query("cursor") cursor: String, @Query("size") size: Int
    ): ResultX<PageBean<GoodsBean>>

    @GET("/api/v1/user/task/list")
    suspend fun getTaskList(): ResultX<TaskCategoryBean>

    @POST("/api/v1/user/task/draw")
    suspend fun taskClaim(@Body params: Map<String, Int>): ResultX<Any>

    @GET("/mateorder/order/subscribe/bonus")
    suspend fun getSubscribeBenefit(@Query("priceId") priceId: String): ResultX<SubscribeBenefitBean>

    @POST("/api/v1/user/level/sign/draw")
    suspend fun bonusClaim(@Body params: Map<String, Int>): ResultX<Any>

    @GET("/mateuser/user/freeRandomMatch/check")
    suspend fun getFreeRandomMatchCredential(): ResultX<MatchCredentialBean>

    @GET("/api/v1/user/config")
    suspend fun getUserConfig(): ResultX<IsolatedConfigBean>

    @GET("/api/v1/user/page/top_notify/info")
    suspend fun getTopNotifyInfo(): ResultX<PageTopNotifyBean>
}