package com.heart.heartmerge.repo.database.convert

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.heart.heartmerge.beans.AnchorFileBean
import com.heart.heartmerge.beans.AnchorLabelBean
import com.heart.heartmerge.beans.LanguageBean
import com.heart.heartmerge.beans.WordEntity

class Converters {

//    @TypeConverter
//    fun fromList(value: String): List<LanguageBean> {
//        val listType = object : TypeToken<List<LanguageBean>>() {}.type
//        return Gson().fromJson(value, listType)
//    }
//
//    @TypeConverter
//    fun toString(array: List<LanguageBean>): String {
//        return Gson().toJson(array)
//    }
//
//    @TypeConverter
//    fun fromAnchorFileList(value: String): List<AnchorFileBean> {
//        val listType = object : TypeToken<List<AnchorFileBean>>() {}.type
//        return Gson().fromJson(value, listType)
//    }
//
//    @TypeConverter
//    fun toAnchorFileString(array: List<AnchorFileBean>): String {
//        return Gson().toJson(array)
//    }
//
//    @TypeConverter
//    fun fromAnchorLabelList(value: String): List<AnchorLabelBean> {
//        val listType = object : TypeToken<List<AnchorLabelBean>>() {}.type
//        return Gson().fromJson(value, listType)
//    }
//
//    @TypeConverter
//    fun toAnchorLabelString(array: List<AnchorLabelBean>): String {
//        return Gson().toJson(array)
//    }
//
//
//    @TypeConverter
//    fun fromPhotos(value: String): List<String> {
//        val listType = object : TypeToken<List<String>>() {}.type
//        return Gson().fromJson(value, listType)
//    }
//
//    @TypeConverter
//    fun toPhotosString(array: List<String>): String {
//        return Gson().toJson(array)
//    }

    @TypeConverter
    fun fromWordEntity(value: String): List<WordEntity> {
        val listType = object : TypeToken<List<LanguageBean>>() {}.type
        return Gson().fromJson(value, listType)
    }

    @TypeConverter
    fun toWordEntity(array: List<WordEntity>): String {
        return Gson().toJson(array)
    }
}