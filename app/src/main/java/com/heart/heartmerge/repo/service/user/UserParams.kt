package com.heart.heartmerge.repo.service.user

import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.toJson
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * 作者：Lxf
 * 创建日期：2024/8/17 11:08
 * 描述：
 */
object UserParams {

    fun userFollowFansParamsBody(pageIndex: Int, size: Int): RequestBody {
        val map = mapOf(
            "current" to pageIndex,
            "size" to size,
        )
        return jsonToBody(map.toJson())
    }

    fun updateDisturbParamsBody(isOpen: Boolean): RequestBody {
        val map = mapOf(
            "dnd" to isOpen,
        )
        return jsonToBody(map.toJson())
    }

    fun userReportParamsBody(
        reportUserId: Int,
        reportContent: String,
        reportLang: String,
        reportUserRole: String = Constants.ROLE_ANCHOR
    ): RequestBody {
        val map = mapOf(
            "reportContent" to reportContent,
            "peer_id" to reportUserId,
            "lang_keys" to listOf(reportLang)
        )
        return jsonToBody(map.toJson())
    }

    /**
     * TODO json 字符串转RequestBody
     *
     * @param json
     * @return
     */
    private fun jsonToBody(json: String): RequestBody =
        json.toRequestBody("application/json; charset=utf-8".toMediaTypeOrNull())
}