package com.heart.heartmerge.repo.service.anchor

import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody

/**
 *
 * @Author： Lxf
 * @Time： 2022/2/24 10:53 上午
 * @description：
 */
object AnchorParams {

    fun getPopularAnchorParamsBody(
        current: Int, size: Int, countId: String, language: String
    ): RequestBody {
        val map = mapOf(
            "current" to current, "size" to size, "countId" to countId, "language" to language
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun searchAnchorParamsBody(
        current: Int, size: Int, countId: String, keyword: String
    ): RequestBody {
        val map = mapOf(
            "current" to current, "size" to size, "countId" to countId, "keyword" to keyword
        )
        return jsonToBody(objectsMapToJson(map))
    }

    /**
     * chatType  1:语音通话;2:视频通话
     */
    fun getVideoChannelParamsBody(
        userId: String, callType: Int, chatType: Int = 1,
    ): RequestBody {
        val map = mutableMapOf<String, Any>()
        map["call_type"] = callType
        map["peer_id"] = userId.toLong()
        map["chat_type"] = chatType
        return jsonToBody(objectsMapToJson(map))
    }

    fun getVideoDeductParamsBody(channelId: String, firstFlag: String = "0"): RequestBody {
        val map = mapOf(
            "channelId" to channelId,
            "firstFlag" to firstFlag,
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun giftGiveParamsBody(
        anchorId: Int, id: Int, callId: Int
    ): RequestBody {
        val map = mapOf(
            "anchor_id" to anchorId,
            "gift_id" to id,
            "call_id" to callId,
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun getRongYunTokenBody(): RequestBody {
        return jsonToBody(objectsMapToJson(emptyMap()))
    }

    fun getRandomAnchorBody(): RequestBody {
        return jsonToBody(objectsMapToJson(emptyMap()))
    }

    fun videoHistoryParamsBody(cursor: String, size: Int): RequestBody {
        val map = mapOf(
            "cursor" to cursor,
            "size" to size,
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun anchorRateParamBody(
        score: Int,
        callId: String,
        tagIds: MutableList<Int>,
        callIn: Boolean = true,
        virtual: Boolean = false
    ): RequestBody {
        val map = mapOf(
            "score" to score,
            "callId" to callId,
            "tag_ids" to tagIds,
            "callIn" to callIn,
            "virtual" to virtual,
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun videoReportBody(
        requestId: String, current: Long, total: Long
    ): RequestBody {
        val map = mapOf(
            "id" to requestId,
            "playDuration" to current,
            "totalDuration" to total,
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun albumBuyParamBody(
        msgId: String,
    ): RequestBody {
        val map = mapOf(
            "msg_id" to msgId,
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun relationOpParamsBody(peerId: String): RequestBody {
        val map = mapOf(
            "peerId" to peerId.toLong(),
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun getAnchorListBody(peerIds: MutableList<String>): RequestBody {
        val map = mapOf(
            "anchor_id_list" to peerIds,
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun aivRecordParamsBody(
        recordId: Int, playDuration: Int
    ): RequestBody {
        val map = mapOf(
            "record_id" to recordId,
            "play_duration" to playDuration,
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun aivStartParamsBody(
        recordId: Int, dispatchType: Int
    ): RequestBody {
        val map = mapOf(
            "record_id" to recordId,
            "dispatch_type" to dispatchType,
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun aivRefuseParamsBody(
        recordId: Int, dispatchType: Int, reasonType:Int
    ): RequestBody {
        val map = mapOf(
            "record_id" to recordId,
            "dispatch_type" to dispatchType,
            "reason_type" to reasonType,
        )
        return jsonToBody(objectsMapToJson(map))
    }


    /**
     * TODO moshi map 转json字符串
     *
     *
     * @param map
     * @return
     */
    private fun objectsMapToJson(map: Map<String, Any>): String {
        val moshi = Moshi.Builder().build()
        val jsonAdapter: JsonAdapter<Map<String, Any>> = moshi.adapter(
            Types.newParameterizedType(
                MutableMap::class.java, String::class.java, Any::class.java
            )
        )
        return jsonAdapter.indent(" ").toJson(map) ?: ""
    }

    /**
     * TODO json 字符串转RequestBody
     *
     * @param json
     * @return
     */
    private fun jsonToBody(json: String): RequestBody =
        json.toRequestBody("application/json; charset=utf-8".toMediaTypeOrNull())
}