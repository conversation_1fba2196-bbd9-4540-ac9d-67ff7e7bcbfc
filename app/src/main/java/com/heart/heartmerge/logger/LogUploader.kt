package com.heart.heartmerge.logger

import com.bdc.android.library.utils.Logger
import com.heart.heartmerge.manager.ApiResult
import com.heart.heartmerge.manager.FileUploadManager
import java.io.File

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025/6/5 13:43
 * @description :
 */
object LogUploader {

    suspend fun upload(file: File, block: (Boolean) -> Unit) {
        runCatching {
            Logger.d("LogUploader: 开始上传日志: ${file.name}, 路径: ${file.absolutePath}")

            // 检查文件是否存在
            if (!file.exists()) {
                Logger.e("文件不存在: ${file.absolutePath}")
            }

            if (file.length() == 0L) {
                Logger.e("文件为空: ${file.absolutePath}")
            }

            Logger.d("LogUploader: 文件检查通过，大小: ${file.length()} bytes")

            val result = FileUploadManager.uploadLog(file)

            when (result) {
                is ApiResult.Success -> {
                    if (result.data.success) {
                        Logger.d("LogUploader: 日志文件上传成功: ${file.name}")
                    } else {
                        Logger.e("LogUploader: 日志文件上传失败: ${file.name} - ${result.data.errorMessage}")
                    }
                    block.invoke(result.data.success)
                }

                is ApiResult.Error -> {
                    block.invoke(false)
                    Logger.e("LogUploader: 日志文件上传失败: ${file.name} - ${result.message}")
                }

                is ApiResult.Loading -> {
                    block.invoke(false)
                    Logger.d("LogUploader: 日志文件上传中...")
                }
            }
        }.onFailure {
            Logger.e("LogUploader: 日志上传失败: ${it.message}")
            it.printStackTrace()
            block.invoke(false)
        }
    }

}
