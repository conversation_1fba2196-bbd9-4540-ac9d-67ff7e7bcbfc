package com.heart.heartmerge.logger

import com.bdc.android.library.utils.Logger
import com.heart.heartmerge.BuildConfig
import com.heart.heartmerge.utils.ContextHolder
import java.io.BufferedWriter
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStreamWriter
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/6/5 13:42
 * @description :
 */
object LogX {
    private var MAX_SIZE = if (BuildConfig.DEBUG) 1000 * 1024 /* 100KB */ else 1 * 1024 * 1024 // 1MB
    private val logDir by lazy {
        File(ContextHolder.context.filesDir, "logs").apply { mkdirs() }
    }

    private var writer: BufferedWriter? = null
    private var currentFile: File? = null

    // 文件状态跟踪
    private val uploadingFiles = mutableSetOf<String>() // 正在上传的文件名
    private val pendingDeleteFiles = mutableSetOf<String>() // 等待删除的文件名
    private var isCurrentFileUploading = false

    // 用于同步的锁
    private val fileLock = Any()

    init {
        Logger.init("HeartMerge", BuildConfig.DEBUG)
        initializeLogFile()
    }

    /**
     * 初始化日志文件
     * 检查是否有未完成的日志文件，如果有则继续使用，否则创建新文件
     */
    private fun initializeLogFile() {
        synchronized(fileLock) {
            try {
                // 首先处理已达到阈值的文件
                processOversizedFiles()

                // 查找未完成的日志文件（小于阈值的文件）
                val incompleteFile = findIncompleteLogFile()

                if (incompleteFile != null) {
                    // 继续使用未完成的文件，指定 UTF-8 编码
                    currentFile = incompleteFile
                    writer = BufferedWriter(
                        OutputStreamWriter(
                            FileOutputStream(incompleteFile, true), Charsets.UTF_8
                        )
                    )
                    Logger.d("LogX: 继续使用未完成的日志文件: ${incompleteFile.name}, 当前大小: ${incompleteFile.length()} bytes")
                } else {
                    // 创建新的日志文件
                    createNewLogFile()
                }
            } catch (e: Exception) {
                Logger.e("LogX: 初始化日志文件失败: ${e.message}")
                e.printStackTrace()
                // 出错时创建新文件
                createNewLogFile()
            }
        }
    }

    /**
     * 查找未完成的日志文件（小于阈值且未在上传中的文件）
     */
    private fun findIncompleteLogFile(): File? {
        return try {
            logDir.listFiles { file ->
                file.isFile && file.name.startsWith("log_") && file.name.endsWith(".txt") && file.length() < MAX_SIZE && file.length() > 0 && !uploadingFiles.contains(
                    file.name
                ) // 排除正在上传的文件
            }?.maxByOrNull { it.lastModified() } // 选择最新的未完成文件
        } catch (e: Exception) {
            Logger.e("LogX: 查找未完成文件失败: ${e.message}")
            null
        }
    }

    /**
     * 处理已达到阈值的文件，将其加入上传队列
     */
    private fun processOversizedFiles() {
        try {
            logDir.listFiles { file ->
                file.isFile && file.name.startsWith("log_") && file.name.endsWith(".txt") && file.length() >= MAX_SIZE && !uploadingFiles.contains(
                    file.name
                ) // 排除已在上传中的文件
            }?.forEach { file ->
                Logger.d("LogX: 发现超大文件，标记为上传中: ${file.name}, 大小: ${file.length()} bytes")
                markFileAsUploading(file)
                startUpload(file)
            }
        } catch (e: Exception) {
            Logger.e("LogX: 处理超大文件失败: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 标记文件为上传中状态
     */
    private fun markFileAsUploading(file: File) {
        synchronized(fileLock) {
            uploadingFiles.add(file.name)
            Logger.d("LogX: 标记文件为上传中: ${file.name}")
        }
    }

    /**
     * 标记文件上传完成，可以删除
     */
    private fun markFileUploadComplete(file: File) {
        synchronized(fileLock) {
            uploadingFiles.remove(file.name)
            pendingDeleteFiles.add(file.name)
            Logger.d("LogX: 标记文件上传完成，等待删除: ${file.name}")

            // 立即删除文件
            try {
                if (file.exists()) {
                    file.delete()
                    Logger.d("LogX: 文件删除成功: ${file.name}")
                }
                pendingDeleteFiles.remove(file.name)
            } catch (e: Exception) {
                Logger.e("LogX: 文件删除失败: ${file.name}, error: ${e.message}")
            }
        }
    }

    /**
     * 标记文件上传失败
     */
    private fun markFileUploadFailed(file: File) {
        synchronized(fileLock) {
            uploadingFiles.remove(file.name)
            Logger.w("LogX: 文件上传失败，移除上传标记: ${file.name}")
        }
    }

    /**
     * 开始上传文件
     */
    private fun startUpload(file: File) {
        UploadQueueManager.enqueue(file) { success ->
            if (success) {
                markFileUploadComplete(file)
            } else {
                markFileUploadFailed(file)
            }
        }
    }

    /**
     * 创建新的日志文件
     */
    private fun createNewLogFile() {
        try {
            // 关闭当前 writer
            writer?.apply {
                flush()
                close()
            }
            writer = null

            // 生成新的文件名（包含毫秒确保唯一性）
            val timestamp =
                SimpleDateFormat("yyyyMMdd_HHmmss_SSS", Locale.getDefault()).format(Date())
            currentFile = File(logDir, "log_${timestamp}.txt")

            // 创建新的 writer，指定 UTF-8 编码
            writer = currentFile?.bufferedWriter(Charsets.UTF_8)

            Logger.d("LogX: 创建新日志文件: ${currentFile?.name}")
        } catch (e: Exception) {
            Logger.e("LogX: 创建日志文件失败: ${e.message}")
            e.printStackTrace()
        }
    }

    fun i(str: String?) {
        Logger.i(str ?: "")
        log(str ?: "")
    }

    fun i(tag: String, str: String?) {
        Logger.i(tag, str ?: "")
        log(str ?: "")
    }

    fun v(str: String?) {
        Logger.v(str ?: "")
        log(str ?: "")
    }

    fun d(str: String?) {
        Logger.d(str ?: "")
        log(str ?: "")
    }

    fun d(tag: String, str: String?) {
        Logger.d(tag, str ?: "")
        log(str ?: "")
    }

    fun w(str: String?) {
        Logger.w(str ?: "")
        log(str ?: "")
    }

    fun w(tag: String, str: String?) {
        Logger.w(tag, str ?: "")
        log(str ?: "")
    }

    fun e(str: String?) {
        Logger.e(str ?: "")
        log(str ?: "")
    }

    fun e(tag: String, str: String?) {
        Logger.e(tag, str ?: "")
        log(str ?: "")
    }

    fun e(tag: String, e: Throwable) {
        Logger.e(tag, e.message ?: "")
        log(e)
    }

    fun e(tag: String, str: String?, e: Exception? = null) {
        Logger.e(tag, str ?: "")
        log(str ?: "")
    }

    fun json(str: String?) {
        Logger.json(str)
        log(str ?: "")
    }

    fun log(throwable: Throwable) {
        synchronized(fileLock) {
            try {
                ensureLogFileAvailable()
                val time =
                    SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault()).format(Date())
                val content = StringBuilder()
                content.append("$time: 崩溃信息 - ${throwable.message}\n")
                content.append("堆栈跟踪:\n")
                throwable.printStackTrace(java.io.PrintWriter(java.io.StringWriter().apply {
                    throwable.printStackTrace(java.io.PrintWriter(this))
                }).append("\n"))
                writer?.apply {
                    write(content.toString())
                    flush()
                }
                currentFile?.let { file ->
                    if (file.length() >= MAX_SIZE) {
                        Logger.d("LogX: 文件大小达到上限 (${file.length()} bytes)，开始轮转")
                        rotateLogFile()
                    }
                }
            } catch (e: Exception) {
                Logger.e("LogX: 写入崩溃日志失败: ${e.message}")
                e.printStackTrace()
                try {
                    initializeLogFile()
                } catch (e2: Exception) {
                    Logger.e("LogX: 重新初始化也失败: ${e2.message}")
                }
            }
        }
    }

    fun log(message: String) {
        synchronized(fileLock) {
            try {
                // 确保有可用的日志文件
                ensureLogFileAvailable()

                val time =
                    SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault()).format(Date())
                val content = "$time: $message\n"

                writer?.apply {
                    write(content)
                    flush()
                }

                // 检查文件大小，如果达到上限则轮转
                currentFile?.let { file ->
                    if (file.length() >= MAX_SIZE) {
                        Logger.d("LogX: 文件大小达到上限 (${file.length()} bytes)，开始轮转")
                        rotateLogFile()
                    }
                }
            } catch (e: Exception) {
                Logger.e("LogX: 写入日志失败: ${e.message}")
                e.printStackTrace()
                // 出错时尝试重新初始化
                try {
                    initializeLogFile()
                } catch (e2: Exception) {
                    Logger.e("LogX: 重新初始化也失败: ${e2.message}")
                }
            }
        }
    }

    /**
     * 确保有可用的日志文件
     */
    private fun ensureLogFileAvailable() {
        // 如果当前文件正在上传，需要找到或创建新文件
        if (isCurrentFileUploading) {
            Logger.w("LogX: 当前文件正在上传，寻找可用文件")
            val incompleteFile = findIncompleteLogFile()
            if (incompleteFile != null) {
                currentFile = incompleteFile
                writer = BufferedWriter(
                    OutputStreamWriter(
                        FileOutputStream(incompleteFile, true), Charsets.UTF_8
                    )
                )
                Logger.d("LogX: 切换到未完成文件: ${incompleteFile.name}")
            } else {
                createNewLogFile()
            }
        }

        // 如果当前文件不存在或 writer 为空，重新初始化
        if (currentFile == null || !currentFile!!.exists() || writer == null) {
            Logger.w("LogX: 当前文件不可用，重新初始化")
            initializeLogFile()
        }
    }

    /**
     * 轮转日志文件
     * 关闭当前文件，标记为上传中，创建新文件继续写入
     */
    private fun rotateLogFile() {
        synchronized(fileLock) {
            try {
                val fileToUpload = currentFile

                // 确保 writer 正确关闭并刷新
                writer?.apply {
                    flush()
                    close()
                }
                writer = null
                currentFile = null

                // 检查文件是否存在且达到阈值
                fileToUpload?.let { file ->
                    Logger.d("LogX: 检查待轮转文件: ${file.name}, 存在: ${file.exists()}, 大小: ${file.length()} bytes")
                    if (file.exists() && file.length() >= MAX_SIZE) {
                        Logger.d("LogX: 文件达到阈值，标记为上传中: ${file.name}, 大小: ${file.length()} bytes")

                        // 标记文件为上传中，禁止再次操作
                        markFileAsUploading(file)

                        // 开始上传
                        startUpload(file)
                    } else {
                        Logger.w("LogX: 文件不满足轮转条件: ${file.absolutePath}, 存在: ${file.exists()}, 大小: ${file.length()}")
                    }
                } ?: Logger.w("LogX: 待轮转文件为null")

                // 寻找可用的未完成文件（排除上传中的文件）
                val incompleteFile = findIncompleteLogFile()
                if (incompleteFile != null) {
                    currentFile = incompleteFile
                    writer = BufferedWriter(
                        OutputStreamWriter(
                            FileOutputStream(incompleteFile, true), Charsets.UTF_8
                        )
                    )
                    Logger.d("LogX: 轮转后使用未完成文件: ${incompleteFile.name}, 大小: ${incompleteFile.length()} bytes")
                } else {
                    // 没有可用的未完成文件，创建新文件
                    createNewLogFile()
                }
            } catch (e: Exception) {
                Logger.e("LogX: 日志文件轮转失败: ${e.message}")
                e.printStackTrace()
                // 确保即使出错也能创建新文件
                try {
                    createNewLogFile()
                } catch (e2: Exception) {
                    Logger.e("LogX: 创建新文件也失败了: ${e2.message}")
                }
            }
        }
    }

    /**
     * 强制上传当前日志文件
     */
    fun forceUpload() {
        synchronized(fileLock) {
            try {
                val fileToUpload = currentFile

                // 确保 writer 正确关闭并刷新
                writer?.apply {
                    flush()
                    close()
                }
                writer = null
                currentFile = null

                // 检查文件是否存在且有内容
                fileToUpload?.let { file ->
                    if (file.exists() && file.length() > 0) {
                        Logger.d("LogX: 强制上传当前文件: ${file.name}, 大小: ${file.length()} bytes")

                        // 标记文件为上传中
                        markFileAsUploading(file)

                        // 开始上传
                        startUpload(file)
                    } else {
                        Logger.w("LogX: 强制上传时文件不存在或为空: ${file?.absolutePath}")
                    }
                }

                // 寻找可用的未完成文件（排除上传中的文件）
                val incompleteFile = findIncompleteLogFile()
                if (incompleteFile != null) {
                    currentFile = incompleteFile
                    writer = BufferedWriter(
                        OutputStreamWriter(
                            FileOutputStream(incompleteFile, true), Charsets.UTF_8
                        )
                    )
                    Logger.d("LogX: 强制上传后使用未完成文件: ${incompleteFile.name}, 大小: ${incompleteFile.length()} bytes")
                } else {
                    createNewLogFile()
                }
            } catch (e: Exception) {
                Logger.e("LogX: 强制上传失败: ${e.message}")
                e.printStackTrace()
                // 确保即使出错也能创建新文件
                try {
                    createNewLogFile()
                } catch (e2: Exception) {
                    Logger.e("LogX: 创建新文件失败: ${e2.message}")
                }
            }
        }
    }

    /**
     * 测试日志功能 - 写入一些测试日志并强制上传
     */
    fun testLogFunction() {
        i("=== 日志功能测试开始 ===")
        d("这是一条调试日志")
        w("这是一条警告日志")
        e("这是一条错误日志")

        // 检查当前文件状态
        synchronized(fileLock) {
            currentFile?.let { file ->
                i("当前日志文件: ${file.absolutePath}")
                i("文件是否存在: ${file.exists()}")
                i("文件大小: ${file.length()} bytes")
                i("文件可读: ${file.canRead()}")
                i("文件可写: ${file.canWrite()}")
            } ?: i("当前日志文件: null")
        }

        i("日志目录: ${logDir.absolutePath}")
        i("日志目录文件数: ${logDir.listFiles()?.size ?: 0}")

        // 写入更多测试数据，确保有足够的内容上传
        repeat(10) { index ->
            d("测试日志条目 #$index - 时间戳: ${System.currentTimeMillis()}")
        }

        i("=== 日志功能测试结束 ===")

        // 强制上传当前日志文件
        forceUpload()
    }

    /**
     * 测试中文编码 - 写入包含中文、emoji 等特殊字符的日志
     */
    fun testChineseEncoding() {
        i("=== 中文编码测试开始 ===")

        // 测试各种中文字符
        d("简体中文：你好世界！这是一条包含中文的日志信息。")
        d("繁體中文：你好世界！這是一條包含繁體中文的日誌信息。")
        d("日文：こんにちは世界！これは日本語のログメッセージです。")
        d("韩文：안녕하세요 세계! 이것은 한국어 로그 메시지입니다.")

        // 测试特殊字符和 emoji
        d("特殊字符：©®™€£¥§¶†‡•…‰‹›–—")
        d("Emoji 测试：😀😃😄😁😆😅😂🤣😊😇🙂🙃😉😌😍🥰😘😗😙😚")
        d("数学符号：∑∏∫∂∆∇√∞≠≤≥±×÷∈∉∪∩⊂⊃⊆⊇")

        // 测试混合内容
        d("混合内容：Hello 世界 🌍 こんにちは 안녕하세요 123 ABC ©2024")

        // 测试长文本
        val longText =
            "这是一段很长的中文文本，用来测试UTF-8编码是否正确处理长文本内容。" + "包含各种标点符号：，。！？；：（）【】《》〈〉「」『』〔〕" + "以及一些特殊字符和数字：1234567890 ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz"
        d("长文本测试：$longText")

        i("当前文件编码测试完成，文件大小: ${currentFile?.length() ?: 0} bytes")
        i("=== 中文编码测试结束 ===")

        // 强制上传测试编码
        forceUpload()
    }

    /**
     * 测试大量日志写入（用于测试文件轮转）
     */
    fun testLogRotation() {
        i("=== 开始测试日志轮转 ===")

        // 显示初始状态
        Logger.d(getLogDirectoryStatus())

        // 写入大量日志，触发文件轮转
        repeat(100) { index ->
            d("轮转测试日志 #$index - ${System.currentTimeMillis()} - 这是一条比较长的日志内容，用于快速达到文件大小限制，触发文件轮转机制")

            // 每10条日志显示一次状态
            if (index % 10 == 0) {
                Logger.d("=== 第${index}条日志后的状态 ===")
                Logger.d(getLogDirectoryStatus())
            }
        }

        i("=== 日志轮转测试结束 ===")
        Logger.d("=== 最终状态 ===")
        Logger.d(getLogDirectoryStatus())
    }

    /**
     * 获取当前日志文件信息（用于调试）
     */
    fun getCurrentFileInfo(): String {
        return synchronized(fileLock) {
            buildString {
                append("当前文件: ${currentFile?.name ?: "无"}\n")
                append("文件大小: ${currentFile?.length() ?: 0} bytes\n")
                append("是否上传中: $isCurrentFileUploading\n")
                append("日志目录: ${logDir.absolutePath}\n")

                val files = logDir.listFiles()
                append("目录文件数: ${files?.size ?: 0}\n")

                if (!files.isNullOrEmpty()) {
                    append("文件列表:\n")
                    files.forEach { file ->
                        append("  - ${file.name} (${file.length()} bytes)\n")
                    }
                }
            }
        }
    }

    /**
     * 清理所有日志文件（用于测试）
     */
    fun clearAllLogs() {
        synchronized(fileLock) {
            try {
                writer?.close()
                writer = null
                currentFile = null

                logDir.listFiles()?.forEach { file ->
                    if (file.isFile) {
                        file.delete()
                        Logger.d("LogX: 删除日志文件: ${file.name}")
                    }
                }

                createNewLogFile()
                Logger.d("LogX: 所有日志文件已清理")
            } catch (e: Exception) {
                Logger.e("LogX: 清理日志文件失败: ${e.message}")
                e.printStackTrace()
            }
        }
    }

    /**
     * 获取日志目录状态（用于调试）
     */
    fun getLogDirectoryStatus(): String {
        return synchronized(fileLock) {
            buildString {
                append("=== 日志目录状态 ===\n")
                append("目录: ${logDir.absolutePath}\n")
                append("正在上传: ${uploadingFiles.size} 个文件\n")
                append("等待删除: ${pendingDeleteFiles.size} 个文件\n")

                val files = logDir.listFiles()?.filter {
                    it.isFile && it.name.startsWith("log_") && it.name.endsWith(".txt")
                }?.sortedBy { it.lastModified() }

                if (files.isNullOrEmpty()) {
                    append("无日志文件\n")
                } else {
                    append("文件总数: ${files.size}\n")
                    files.forEach { file ->
                        val status = when {
                            uploadingFiles.contains(file.name) -> "上传中"
                            pendingDeleteFiles.contains(file.name) -> "待删除"
                            file.length() >= MAX_SIZE -> "需上传"
                            file.length() > 0 -> "未完成"
                            else -> "空文件"
                        }
                        val isCurrent = file == currentFile
                        append("  ${if (isCurrent) "→ " else "  "}${file.name} (${file.length()} bytes) [$status]${if (isCurrent) " [当前]" else ""}\n")
                    }
                }

                append("当前文件: ${currentFile?.name ?: "无"}\n")
                append("上传中文件: ${uploadingFiles.joinToString(", ")}\n")
                append("待删除文件: ${pendingDeleteFiles.joinToString(", ")}")
            }
        }
    }
}
