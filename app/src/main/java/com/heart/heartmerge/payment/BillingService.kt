package com.heart.heartmerge.payment

import android.app.Activity
import android.content.Intent
import androidx.activity.ComponentActivity
import androidx.core.net.toUri
import androidx.lifecycle.lifecycleScope
import com.android.billingclient.api.AcknowledgePurchaseParams
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.PendingPurchasesParams
import com.android.billingclient.api.ProductDetails
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.QueryProductDetailsParams
import com.android.billingclient.api.QueryPurchasesParams
import com.android.billingclient.api.queryProductDetails
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.http.request
import com.bdc.android.library.utils.AppManager
import com.bdc.android.library.utils.ToastUtil
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.CheckOrderBody
import com.heart.heartmerge.beans.CreateOrderBody
import com.heart.heartmerge.beans.GoodsBean
import com.heart.heartmerge.beans.OrderBean
import com.heart.heartmerge.firebase.report.DT_EVENT_CONSTANTS
import com.heart.heartmerge.firebase.report.ReportManager
import com.heart.heartmerge.http.RetrofitClient
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.manager.OrderManager
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.popup.showBecomeMembershipPopup
import com.heart.heartmerge.popup.showPaymentSuccessPopup
import com.heart.heartmerge.popup.showRemindPopup
import com.heart.heartmerge.ui.activities.WebViewActivity
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.PurchaseScene
import com.heart.heartmerge.utils.anchorIdOrEmpty
import com.heart.heartmerge.utils.videoIdOrEmpty
import com.heart.heartmerge.utils.videoUrlOrEmpty
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference


class BillingService(
    activity: ComponentActivity,
) {

    private val TAG = BillingService::class.simpleName ?: "BillingService"

    private val weakActivity = WeakReference(activity)
    private var purchaseItem: GoodsBean? = null
    private var currentOrderBean: OrderBean? = null
    private var handleResultBlock: ((Boolean) -> Unit)? = null

    private val billingClient: BillingClient by lazy {
        BillingClient.newBuilder(AppManager.getApplication()).enablePendingPurchases(
            PendingPurchasesParams.newBuilder().enablePrepaidPlans().enableOneTimeProducts().build()
        ).setListener { billingResult, purchases ->
            if (billingResult.responseCode == BillingClient.BillingResponseCode.OK && purchases != null) {
                val uniqueSet = purchases.distinctBy { it.orderId }
                for (purchase in uniqueSet) {
                    handlePurchaseResult(purchase)
                }
            } else if (billingResult.responseCode == BillingClient.BillingResponseCode.USER_CANCELED) {
                // Handle an error caused by a user cancelling the purchase flow.
                LogX.e("google pay result user cancel")
                billingClient.endConnection()
                handleResultBlock?.invoke(false)
            } else {
                // Handle any other error codes.
                LogX.e("google pay result other error")
                billingClient.endConnection()
                handleResultBlock?.invoke(false)
            }
        }.build()
    }

    init {
        connection()
    }


    internal fun connection(block: (() -> Unit)? = null) {
//        if (!billingClient.isReady) {
//            showUnavailable()
//            return
//        }
        billingClient.startConnection(object : BillingClientStateListener {
            override fun onBillingSetupFinished(billingResult: BillingResult) {
                LogX.i("onBillingSetupFinished $billingResult")
                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                    // The BillingClient is ready. You can query purchases here.
                    block?.invoke()
                } else {
                    if (block != null) {
                        showUnavailable()
                    }
                }
            }

            override fun onBillingServiceDisconnected() {
                LogX.e("onBillingServiceDisconnected")
                //恢复AIA AIB
                // Try to restart the connection on the next request to
                // Google Play by calling the startConnection() method.
            }
        })
    }

    fun checkRestorePurchases(block: (Boolean) -> Unit) {
        billingClient.queryPurchasesAsync(
            QueryPurchasesParams.newBuilder().setProductType(BillingClient.ProductType.SUBS) // 查询订阅
                .build()
        ) { billingResult, purchasesList ->
            LogX.i("queryPurchasesAsync ${purchasesList}, responseCode=${billingResult.responseCode}")
            if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                if (purchasesList.isNotEmpty()) {
                    handlePurchases(purchasesList, block)
                } else {
                    ToastUtil.show(
                        weakActivity.get()?.getString(R.string.restore_purchases_success)
                    )
                }
            } else {
                block(false)
                val userMessage = when (billingResult.responseCode) {
                    BillingClient.BillingResponseCode.SERVICE_DISCONNECTED -> "The service is temporarily unavailable, please try again later."
                    BillingClient.BillingResponseCode.USER_CANCELED -> "The operation has been canceled."
                    BillingClient.BillingResponseCode.BILLING_UNAVAILABLE -> "The current device does not support purchasing function."
                    BillingClient.BillingResponseCode.ITEM_UNAVAILABLE -> "The product is temporarily unavailable."
                    BillingClient.BillingResponseCode.ERROR -> "An unknown error has occurred, please try again later."
                    BillingClient.BillingResponseCode.DEVELOPER_ERROR -> "Development configuration error, please contact support personnel."
                    else -> "Query failed：${billingResult.debugMessage}"
                }
                ToastUtil.show(userMessage)
                // 查询失败，处理错误
                LogX.e("queryPurchasesAsync failed, responseCode=${billingResult.responseCode}")
                ReportManager.log("queryPurchasesAsync failed, responseCode=${billingResult.responseCode}")
            }
        }
    }

    fun launch(
        purchaseParams: GoodsBean, purchaseScene: PurchaseScene?, block: (Boolean) -> Unit = {}
    ) {
        LogX.d(TAG, "开始创建订单，商品信息：$purchaseParams, 支付场景：$purchaseScene")
        //暂停AIA AIB
        this.purchaseItem = purchaseParams
        this.handleResultBlock = block
        weakActivity.get()?.takeIf { it is BaseCoreActivity<*, *> }?.let {
            it as? BaseCoreActivity<*, *>
        }?.showLoading("Loading...")
        val params = buildMap {
//            put("priceId", purchaseItem?.id ?: "")/*价格Id*/
//            put("discountType", purchaseItem?.discountType ?: "")/*价格Id*/
//            put("priceType", purchaseItem?.priceType ?: "")/*支付类型，1-充值 2-订阅*/

            put("product_id", purchaseItem?.product_id?.toInt() ?: "")
            put("pay_type", purchaseItem?.targetChannel?.payType)
            put("channel", purchaseItem?.targetChannel?.channel ?: "")

            put("scene", purchaseScene?.sceneValue ?: "")/*支付场景，  */
            put("triggerAnchorId", purchaseScene?.anchorIdOrEmpty ?: "")/*支付场景，  */
            put("triggerVideoId", purchaseScene?.videoIdOrEmpty?.toString() ?: "")/*支付场景，  */
        }
        request {
            call {
                RetrofitClient.aggregatedService.createOrder(
                    CreateOrderBody(
                        product_id = purchaseItem?.product_id?.toInt() ?: 0,
                        pay_type = purchaseItem?.targetChannel?.payType ?: 0,
                        channel = purchaseItem?.targetChannel?.channel ?: "",
                        anchor_id = purchaseScene?.anchorIdOrEmpty?.toIntOrNull() ?: 0,
                        resource_id = purchaseScene?.videoIdOrEmpty ?: 0L,
                        resource_url = purchaseScene?.videoUrlOrEmpty ?: "",
                        country_id = purchaseParams?.country_id?.takeIf { it > 0 }
                            ?: MMKVDataRep.userInfo?.country_code ?: 0,
                        scene = purchaseScene?.sceneValue ?: "",
                        path = purchaseScene?.sceneValue ?: ""),
                )
            }
            onSuccess {
                LogX.d(TAG, "创建订单成功，订单信息：${it}")
                if (it != null) {
                    currentOrderBean = it
                    ReportManager.logEvent(DT_EVENT_CONSTANTS.EVENT_CHECKOUT, buildMap {
//                        put("id", it.id)
//                        put("sku", it.diamondNum)
//                        put("orderType", it.orderType)
//                        put("googleProductId", it.googleProductId)
//                        put("priceType", purchaseItem?.priceType ?: "")
                        put("product_id", purchaseItem?.product_id ?: "")
                        put("pay_type", purchaseItem?.targetChannel?.payType ?: "")
                        put("channel", purchaseItem?.targetChannel?.channel ?: "")
                    })

                    weakActivity.get()?.let { activity ->
                        activity.lifecycleScope.launch {
                            LogX.d(TAG, "上报支付拉起事件，订单信息：${it}")
                            RetrofitClient.aggregatedService.reportEvent(buildMap {
                                put("event", "pay_pull")
                                put("data", JsonObject().apply {
                                    addProperty("pay_order_id", it.payOrderId)
                                    addProperty("product_id", purchaseItem?.product_id ?: "")
                                    addProperty(
                                        "pay_type", purchaseItem?.targetChannel?.payType.toString()
                                    )
                                    addProperty(
                                        "channel", purchaseItem?.targetChannel?.channel ?: ""
                                    )
                                }.toString())
                            })
                        }
                    }
                }
                //谷歌支付
                if (purchaseParams?.targetChannel?.isGooglePay == true) {
                    LogX.d(TAG, "开始谷歌支付，商品信息：$purchaseItem")
                    val productType =
                        if (purchaseItem?.isSubscribe == true) BillingClient.ProductType.SUBS else BillingClient.ProductType.INAPP
                    LogX.d(
                        TAG,
                        "开始查询商品信息，商品ID：${it?.googleProductId ?: purchaseItem?.sku ?: ""}"
                    )
                    fetchProductList(
                        listOf(it?.googleProductId ?: purchaseItem?.sku ?: ""), productType
                    ) {
                        LogX.d("查询商品信息成功，商品信息：${it}")
                        it?.takeIf { it.isNotEmpty() && weakActivity.get() != null }?.apply {
                            weakActivity.get()?.let { activity ->
                                LogX.d(TAG, "开始启动谷歌支付流程，商品信息：${firstOrNull()}")
                                firstOrNull()?.let {
                                    launchPurchaseFlow(
                                        activity, productType, it, currentOrderBean
                                    ) {
                                        LogX.i("google pay launch result $it")
                                        if (it.responseCode == BillingClient.BillingResponseCode.OK) {
                                        } else {
//                            showUnavailable()
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else if (it?.url?.isNotEmpty() == true) {
                    LogX.d(TAG, "开始第三方支付，支付地址： ${it.url}")
                    if (it.external) {
                        LogX.d(TAG, "开始外部跳转，支付地址： ${it.url}")
                        // 外部跳转，打开系统浏览器
                        weakActivity.get()?.let { activity ->
                            try {
                                val intent = Intent(Intent.ACTION_VIEW, it.url.toUri())
                                // 尝试直接启动浏览器
                                activity.startActivity(intent)
                                LogX.i("External browser opened successfully with URL: ${it.url}")

                                // 外部跳转成功，调用回调
                                handleResultBlock?.invoke(true)
                            } catch (e: Exception) {
                                LogX.e("Failed to open external browser: ${e.message}")
                                ReportManager.logException(e)

                                // 如果无法打开浏览器，显示错误提示
                                ToastUtil.show(activity.getString(R.string.unable_to_open_browser))
                                handleResultBlock?.invoke(false)
                            }
                        }
                    } else {
                        LogX.d(TAG, "开始内部跳转，支付地址： ${it.url}")
                        weakActivity.get()?.let { activity ->
                            WebViewActivity.jump(
                                activity,
                                title = purchaseItem?.targetChannel?.channel ?: "",
                                url = it?.url ?: ""
                            )
                        }
                    }
                } else {
                    LogX.d(TAG, "支付地址为空，支付失败")
                    ReportManager.log("create order result payUrl is empty $it")
                    ToastUtil.show(weakActivity.get()?.getString(R.string.payment_fail))
                }
            }
            onFailure {
                LogX.d(TAG, "创建订单失败，失败原因： ${it.message}")
                ToastUtil.show(it.message)
                ReportManager.logException(it)
            }
            onCompleted {
                weakActivity.get()?.takeIf { it is BaseCoreActivity<*, *> }?.let {
                    it as? BaseCoreActivity<*, *>
                }?.stopLoading()
            }
        }
    }

    fun fetchProductList(
        productIds: List<String>,
        productType: String = BillingClient.ProductType.INAPP,
        block: (List<ProductDetails>?) -> Unit
    ) {
        if (productIds.isEmpty()) return
        LogX.i("fetchProductList ${billingClient.isReady}")
        connection {
            if (!billingClient.isReady) {
                showUnavailable()
                return@connection
            }
            buildProductQueryParams(productIds, productType)?.let {
                billingClient.queryProductDetailsAsync(
                    it
                ) { p0, p1 ->
                    LogX.i("queryProductDetailsAsync result $p1")
                    if (p1.productDetailsList.isNotEmpty()) {
                        block.invoke(p1.productDetailsList)
                    } else {
                        showUnavailable()
                    }
                }
            }
        }
    }

    suspend fun fetchProductList(
        productIds: List<String>, productType: String = BillingClient.ProductType.INAPP
    ): List<ProductDetails>? {
        LogX.i("fetchProductList ${billingClient.isReady}")
        if (billingClient.isReady && productIds.isNotEmpty()) {
            return buildProductQueryParams(
                productIds, productType
            )?.let {
                billingClient.queryProductDetails(
                    it
                )
            }?.productDetailsList
        }
        return null
    }


    private fun buildProductQueryParams(
        productIds: List<String?>?, productType: String
    ): QueryProductDetailsParams? {
        val productList = productIds?.filter { it?.isNotEmpty() == true }?.map {
            QueryProductDetailsParams.Product.newBuilder().setProductId(it ?: "")
                .setProductType(productType).build()
        }

        val params = QueryProductDetailsParams.newBuilder()
        productList?.takeIf { it.isNotEmpty() }?.let { params.setProductList(it) }
        return if (productList.isNullOrEmpty()) null else params.build()
    }

    private fun handlePurchaseResult(purchase: Purchase) {
        LogX.i(TAG, "google pay result success ${purchase.orderId}")
        if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {
            billingClient.endConnection()

            if (purchaseItem == null || purchaseItem?.product_id?.isNullOrEmpty() == true) return
            (weakActivity.get() as? BaseCoreActivity<*, *>)?.showLoading(
                weakActivity.get()?.getString(R.string.confirming_subscription_results)
            )

            LogX.d(
                TAG,
                "开始检查订单状态，订单信息：currentOrderBean: ${currentOrderBean.toString()}, originalJson: ${purchase.originalJson}, purchase: ${
                    runCatching { Gson().toJson(purchase) }.getOrElse { purchase.originalJson }
                }")
            OrderManager.checkOrderStatus(
                body = CheckOrderBody(
                    receiptData = purchase.originalJson,
                    jsonPurchaseInfo = runCatching { Gson().toJson(purchase) }.getOrElse { purchase.originalJson },
                    payOrderId = currentOrderBean?.payOrderId,
                    sku = purchaseItem?.sku,
                    outOrderId = purchase.orderId,
                    payType = 1
                )
            ) { result ->
                (weakActivity.get() as? BaseCoreActivity<*, *>)?.stopLoading()
                LogX.d(TAG, "检查订单状态结果：$result")
                if (result?.isPaid == true) {
                    weakActivity.get()?.apply {
                        LogX.d(TAG, "开始展示支付成功弹窗")
                        if (result?.isSubscribe == true) {
                            showBecomeMembershipPopup(this) {
                                this.lifecycleScope.launch {
                                    FlowBus.with<Boolean>(Constants.SUBSCRIBE_SUCCESS).post(true)
                                }
                                handleResultBlock?.invoke(true)
                            }
                        } else {
                            showPaymentSuccessPopup(this) {
                                this.lifecycleScope.launch {
                                    FlowBus.with<Boolean>(Constants.PAYMENT_SUCCESS).post(true)
                                }
                                handleResultBlock?.invoke(true)
                            }
                        }
                    }
                } else {
                    ToastUtil.show(weakActivity.get()?.getString(R.string.payment_processing))
                }
            }
        } else {
            LogX.e(TAG, "google purchase result ${purchase.purchaseState}")
        }
    }

    private fun handlePurchases(purchases: List<Purchase>, block: (Boolean) -> Unit) {
        val canPurchase =
            purchases.find { it.purchaseState == Purchase.PurchaseState.PURCHASED } != null
        if (!canPurchase) {
            ToastUtil.show(weakActivity.get()?.getString(R.string.restore_purchases_success))
            block.invoke(false)
            return
        }
        for (purchase in purchases) {
            if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {
                // 验证购买（例如，向自己的服务器发送验证请求）
                acknowledgePurchase(purchase, block)
            }
        }
    }

    private fun acknowledgePurchase(purchase: Purchase, block: (Boolean) -> Unit) {
        if (!purchase.isAcknowledged) {
            val params =
                AcknowledgePurchaseParams.newBuilder().setPurchaseToken(purchase.purchaseToken)
                    .build()
            billingClient.acknowledgePurchase(params) { billingResult ->
                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                    // 恢复购买成功
                    block.invoke(true)
                } else {
                    block.invoke(false)
                }
            }
        } else {
            weakActivity.get()?.runOnUiThread {
                ToastUtil.show(weakActivity.get()?.getString(R.string.restore_purchases_success))
                block.invoke(false)
            }
        }
    }

    private fun launchPurchaseFlow(
        activity: Activity,
        productType: String,
        productDetails: ProductDetails,
        orderBean: OrderBean?,
        block: (BillingResult) -> Unit
    ) {
        val productDetailParams =
            if (productType == BillingClient.ProductType.INAPP) BillingFlowParams.ProductDetailsParams.newBuilder()
                .setProductDetails(productDetails) else BillingFlowParams.ProductDetailsParams.newBuilder()
                .setProductDetails(productDetails)
                // For One-time product, "setOfferToken" method shouldn't be called.
                // For subscriptions, to get an offer token, call ProductDetails.subscriptionOfferDetails()
                // for a list of offers that are available to the user
                .setOfferToken(
                    productDetails.subscriptionOfferDetails?.firstOrNull()?.offerToken ?: ""
                )
        val productDetailsParamsList = listOf(
            productDetailParams.build()
        )

        val billingFlowParams =
            BillingFlowParams.newBuilder().setProductDetailsParamsList(productDetailsParamsList)
                .setObfuscatedAccountId(MMKVDataRep.userInfo.id)
                .setObfuscatedProfileId(orderBean?.payOrderId ?: "").build()
        // Launch the billing flow
        val billingResult = billingClient.launchBillingFlow(activity, billingFlowParams)
        block.invoke(billingResult)
    }

    private fun showUnavailable() {
        weakActivity.get()?.let { activity ->
            showRemindPopup(
                activity,
                R.mipmap.ic_dialog_warning,
                activity.getString(R.string.billing_service_unavailable)
            ) {
                handleResultBlock?.invoke(false)
            }
        }
    }

    fun onDestroy() {
        LogX.i("BillingService onDestroy")
        billingClient.endConnection()
        weakActivity.clear()
        purchaseItem = null
        currentOrderBean = null
        handleResultBlock = null
    }
}
