package com.heart.heartmerge.mmkv

import com.bdc.android.library.mmkv.MMKVOwner
import com.bdc.android.library.mmkv.mmKvString
import com.tencent.mmkv.MMKV

/**
 *
 * @Author： Lxf
 * @Time： 2022/2/17 5:56 下午
 * @description：mmKv base 数据存储库
 */
object MMKVBaseDataRep : MMKVOwner {
    override val kv: MMKV = MMKV.mmkvWithID("app_base")
    var token by mmKvString()
    var rongYunToken by mmKvString()
    var rongCloudAppKey by mmKvString()
    var rongYunTransitionToken by mmKvString()
    var agoraRtcAppId by mmKvString()


    fun clearAll() {
        token = null
        rongYunToken = null
        rongCloudAppKey = null
        rongYunTransitionToken = null
        agoraRtcAppId = null
    }

}