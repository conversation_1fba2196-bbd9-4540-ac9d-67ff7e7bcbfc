package com.heart.heartmerge.mmkv

import com.bdc.android.library.mmkv.MMKVOwner
import com.bdc.android.library.mmkv.mmKvBool
import com.bdc.android.library.mmkv.mmKvParcelable
import com.bdc.android.library.mmkv.mmKvString
import com.bdc.android.library.mmkv.mmKvStringSet
import com.bdc.android.library.utils.AppManager
import com.bdc.android.library.utils.PreferencesUtil
import com.heart.heartmerge.beans.ConfigurationBean
import com.heart.heartmerge.beans.IsolatedConfigBean
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.beans.UserConfigBean
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.manager.DiamondChangeManager
import com.heart.heartmerge.utils.RongMessageUtil
import kotlin.properties.Delegates

/**
 *
 * @Author： Lxf
 * @Time： 2022/2/17 5:56 下午
 * @description：mmKv base 数据存储库
 */
object MMKVDataRep : MMKVOwner {

    init {
        PreferencesUtil.init(AppManager.getApplication())
    }

    var isFirstLoginRewardShowed by mmKvBool(false)
    var isPlatformRuleRead by mmKvBool(false)
    var lastSignDate by mmKvString()
    var lastSignPopupShowDate by mmKvString()
    var configuration by mmKvParcelable<ConfigurationBean>()
    var searchHistory by mmKvStringSet()
    var doNotDisturb by mmKvBool(false) //免打扰 默认关闭
    private var selfUserId by mmKvString()   //目前遇到KV decodeParcelable 会失败的情况， 此字段放在用户信息丢失
    var freeRandomMatchPopupShowDate by mmKvString()
    var cacheFilePath by mmKvString() // 播放了但没删除的文件地址
    var cacheAibParam by mmKvString() // aib参数
    var lastAppUpgradePopupShowDate by mmKvString()

    var hasVersionUpdate by mmKvBool(false)

    var userConfig by mmKvParcelable<UserConfigBean>()

    var userIsolatedConfig by mmKvParcelable<IsolatedConfigBean>()

    // 初始化时读取 MMKV 中的 UserInfo
    var userInfo: UserBean by Delegates.observable(loadUserInfo()) { _, oldValue, newValue ->
        DiamondChangeManager.updateDiamond(newValue.balance) //需要放在判断之外 因为如果没有变化 就不会触发composeView更新
        if (oldValue != newValue) {
            saveUserInfo(newValue)
            selfUserId = newValue.id
//            saveDoNotDisturb(newValue.isNotDisturb)
            //用户信息有修改 更新数据IM用户信息
            RongMessageUtil.refreshCacheUserInfo(newValue)
        }
    }

    // 从 MMKV 加载 UserInfo
    private fun loadUserInfo(): UserBean {
        return runCatching {
            val parcelable = kv.decodeParcelable("userInfo", UserBean::class.java)
            LogX.i(
                "parcelable $parcelable PreferencesUtil userBean : ${
                    PreferencesUtil.get<UserBean>(
                        "userInfo"
                    )
                }"
            )
            return parcelable?.takeIf { bean -> bean.id.isNotEmpty() && bean.nickname.isNotEmpty() }
                ?: PreferencesUtil.get("userInfo") ?: UserBean(id = selfUserId ?: "")
        }.getOrElse { UserBean(reLogin = true) }
    }

    // 保存 UserInfo 到 MMKV
    private fun saveUserInfo(userInfo: UserBean) {
        kv.encode("userInfo", userInfo)
        PreferencesUtil.put("userInfo", userInfo)
    }

    fun clearAll() {
//        kv.clearAll()
        runCatching {
            kv.clearAll()
            kv.remove("userInfo")
            MMKVBaseDataRep.clearAll()
            PreferencesUtil.clear()
        }.onFailure {
            LogX.e("clearAll error ${it.message}")
        }
    }

    //服务端返回存储 “0” 关闭 “1” 开启
    private fun saveDoNotDisturb(isNotDisturb: String) {
        doNotDisturb = isNotDisturb == "1"
    }
}