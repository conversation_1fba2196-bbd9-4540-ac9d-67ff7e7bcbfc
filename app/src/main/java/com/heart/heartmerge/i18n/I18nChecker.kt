package com.heart.heartmerge.i18n

import android.content.Context
import com.heart.heartmerge.http.RetrofitClient
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.utils.toJson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2025/6/5 13:58
 * @description :
 */

data class I18nBean(
    val mapping: Map<String, String>?,
    val default_lang: String?,
    val lang: Map<String, Map<String, String>>,
    val md5: String?
)

object I18nChecker {

    suspend fun checkAndUpdateLanguage(context: Context): Boolean {
        return runCatching {
            withContext(Dispatchers.IO) {
                val result =
                    RetrofitClient.aggregatedService.getLangInfo(I18nManager.getLangMd5(context))
                if (result.isSuccess()) {
                    LogX.d("Language info fetched: ${result.data}")
                    I18nManager.saveLangMd5(context, result.data?.md5 ?: "")
                    result.data?.let { config ->
                        config.lang.forEach { (langCode, langData) ->
                            I18nManager.saveLangJson(context, langCode, langData.toJson(), 1)
                        }
                    }
                }
                result.isSuccess() && result.data != null
            }
        }.getOrElse {
            LogX.e("Failed to update language: ${it.message}")
            false
        }
    }
}
