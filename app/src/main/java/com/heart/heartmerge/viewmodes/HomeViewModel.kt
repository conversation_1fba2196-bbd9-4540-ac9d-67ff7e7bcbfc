package com.heart.heartmerge.viewmodes

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bdc.android.library.ktnet.tryAwait
import com.bdc.android.library.mvi.SharedFlowEvents
import com.bdc.android.library.mvi.asLiveData
import com.bdc.android.library.mvi.setEvent
import com.bdc.android.library.mvi.setState
import com.heart.heartmerge.beans.LanguageBean
import com.heart.heartmerge.beans.PageTopNotifyBean
import com.heart.heartmerge.beans.TabBean
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.extension.requestChannelFlow
import com.heart.heartmerge.ktnet.NetDelegates
import com.heart.heartmerge.ktnet.error.msg
import com.heart.heartmerge.lce.BaseRequestEvent
import com.heart.heartmerge.repo.service.anchor.AnchorService
import com.heart.heartmerge.utils.RongMessageUtil
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch

/**
 *Ø
 * @Author： Lxf
 * @Time： 2022/1/21 3:54 下午
 * @description：
 */
class HomeViewModel : BaseViewModel() {
    private val service: AnchorService by NetDelegates()

    private val _viewStates = MutableLiveData(HomePageState())
    val viewStates = _viewStates.asLiveData()

    private var getAnchorJob: Job? = null
    private val _pageEvents = SharedFlowEvents<TabRequestEvent>()
    val pageEvents = _pageEvents.asSharedFlow()

    /**
     * 当取更多的时候 用户又下拉刷新 这个时候需要取消之前的job 否则等异步回调之后 数据会乱
     */
    fun cancelLoadAnchorJob() {
        getAnchorJob?.cancel()
    }

//    fun getTabs() = requestChannelFlow {
//        request = { aggregatedService.getTabs() }v
//    }

    fun getTabs() {
        viewModelScope.launch {
            val defaultTabs = buildList {
                add(TabBean("Hot", type = "7"))
                add(TabBean("New", type = "6"))
                add(TabBean("Follow", type = "3"))
            }
            val languages = service.getTabs().tryAwait {
                _pageEvents.setEvent(TabRequestEvent.GetTabsFailed(it.msg))
                _pageEvents.setEvent(TabRequestEvent.GetTabsSuccess(defaultTabs))
            }
            languages?.let {
                _pageEvents.setEvent(TabRequestEvent.GetTabsSuccess(it.list ?: defaultTabs))
            }
        }
    }


    /**
     * TODO 获取热门主播列表
     *
     * @param current
     */
    fun getAnchorList(
        current: Int, size: Int, countId: String = "", language: String = "",
//        type: AnchorQueryType = AnchorQueryType.HOT
        type: String
    ) {
        getAnchorJob = viewModelScope.launch {
            _viewStates.setState {
                copy(
                    anchorGetStatus = RequestStatus.Default
                )
            }

            val anchorList = service.getHomepage(type.toInt(), countId, size).tryAwait {
                _viewStates.setState {
                    copy(anchorGetStatus = RequestStatus.AnchorGetStatusFailed(it.msg))
                }
                _viewEvents.setEvent(BaseRequestEvent.ShowToast(it.message ?: ""))
            }
            anchorList?.let {
                //缓存融云信息
                it.records?.forEach { userBean ->
                    RongMessageUtil.refreshCacheUserInfo(userBean)
                }
                _viewStates.setState {
                    copy(
                        anchorGetStatus = RequestStatus.AnchorGetStatusSuccess(
                            it.records, it.countId, it.tabInfo
                        )
                    )
                }
            }
        }
    }

    /**
     * TODO 获取语言列表
     */
    fun getLanguages() {
        viewModelScope.launch {
            val languages = service.getLanguages().tryAwait {
                _viewStates.setState { copy(anchorGetStatus = RequestStatus.LanguagesFailed(it.msg)) }
            }
            languages?.let {
                _viewStates.setState {
                    copy(anchorGetStatus = RequestStatus.LanguagesSuccess(it))
                }
            }
        }
    }

    fun getTopNotifyInfo(): Flow<PageTopNotifyBean> = requestChannelFlow {
        request = { aggregatedService.getTopNotifyInfo() }
    }
}

enum class AnchorQueryType(val type: String) {
    HOT("1"), NEW("2"), FOLLOW("3")
}

data class HomePageState(
    val anchorGetStatus: RequestStatus = RequestStatus.Default,
)

sealed class RequestStatus {
    data object Default : RequestStatus()
    data object Loading : RequestStatus()
    data class AnchorGetStatusSuccess(
        val list: MutableList<UserBean>?, val countId: String, val tabInfo: TabBean?
    ) : RequestStatus()

    data class LanguagesSuccess(val list: MutableList<LanguageBean>?) : RequestStatus()
    data class AnchorGetStatusFailed(val msg: String) : RequestStatus()
    data class LanguagesFailed(val msg: String) : RequestStatus()
}


sealed class TabRequestEvent {
    data class GetTabsSuccess(val tabs: List<TabBean>) : TabRequestEvent()
    data class GetTabsFailed(val msg: String) : TabRequestEvent()
}
