package com.heart.heartmerge.viewmodes

import com.bdc.android.library.utils.ToastUtil
import com.google.gson.JsonObject
import com.heart.heartmerge.extension.requestChannelFlow
import com.heart.heartmerge.manager.FileUploadManager
import com.heart.heartmerge.manager.onError
import com.heart.heartmerge.manager.onSuccess
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.io.File

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/9/10 13:50
 * @description :反馈ViewModel
 */
class FeedbackViewModel : BaseViewModel() {

    fun upload(path: String): Flow<FileUploadManager.FileUploadResult> = flow {
        FileUploadManager.upload(
            File(path), FileUploadManager.UploadConfig(fileType = FileUploadManager.FileType.IMAGE)
        ).onSuccess { result ->
            emit(result)
        }.onError { error ->
            emit(FileUploadManager.FileUploadResult(false, errorMessage = error))
            ToastUtil.show(error)
        }
    }

    fun submit(params: JsonObject): Flow<Any> = requestChannelFlow {
        request = {
            aggregatedService.feedback(params)
        }
    }
}