package com.heart.heartmerge.viewmodes

import androidx.lifecycle.viewModelScope
import com.bdc.android.library.ktnet.tryAwait
import com.bdc.android.library.mvi.SharedFlowEvents
import com.bdc.android.library.mvi.setEvent
import com.heart.heartmerge.beans.RelationList
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.ktnet.NetDelegates
import com.heart.heartmerge.ktnet.error.msg
import com.heart.heartmerge.repo.service.user.UserParams
import com.heart.heartmerge.repo.service.user.UserService
import com.heart.heartmerge.ui.fragments.message.RelationType
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch


class FollowFansViewModel : BaseViewModel() {
    private val service: UserService by NetDelegates()
    private val _pageEvents = SharedFlowEvents<FollowFansRequestEvent>()
    val pageEvents = _pageEvents.asSharedFlow()
    private var getAnchorJob: Job? = null

    fun fetchFollowOrFansList(cursor: String, size: Int) {
        getAnchorJob = viewModelScope.launch {
            val anchors =
                service.getFollowList(RelationList.FOLLOW.value, cursor, size).tryAwait {
                    _pageEvents.setEvent(FollowFansRequestEvent.GetFollowFailed(it.msg))
                }
            anchors?.let {
                _pageEvents.setEvent(FollowFansRequestEvent.GetFollowSuccess(it.countId, it.records))
            }
        }
    }

    /**
     * 当取更多的时候 用户又下拉刷新 这个时候需要取消之前的job 否则等异步回调之后 数据会乱
     */
    fun cancelLoadAnchorJob() {
        getAnchorJob?.cancel()
    }
}

sealed class FollowFansRequestEvent {
    data class GetFollowSuccess(val cursor: String, val list: List<UserBean>?) : FollowFansRequestEvent()
    data class GetFollowFailed(val msg: String) : FollowFansRequestEvent()
}