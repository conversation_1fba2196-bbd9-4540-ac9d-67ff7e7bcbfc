package com.heart.heartmerge.viewmodes

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bdc.android.library.mvi.SharedFlowEvents
import com.bdc.android.library.mvi.setEvent
import com.bdc.android.library.mvi.setState
import com.heart.heartmerge.http.RetrofitClient
import com.heart.heartmerge.ktnet.NetConstant
import com.heart.heartmerge.ktnet.error.code
import com.heart.heartmerge.ktnet.error.msg
import com.heart.heartmerge.lce.BaseRequestEvent
import com.heart.heartmerge.lce.BaseViewState
import com.heart.heartmerge.lce.PageState
import com.heart.heartmerge.logger.LogX
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

/**
 *
 * @Author： Lxf
 * @Time： 2022/2/15 11:57 上午
 * @description：ViewMode 统一处理类 包括统一网络访问处理等
 */
open class BaseViewModel : ViewModel() {

    val aggregatedService by lazy { RetrofitClient.aggregatedService }

    //暴露一个LiveData，包括页面所有状态,显示loading或者空页面等，经过测试 这里如果只监听viewEvent的flow， 在compose页面如果有collect监听的话，flow值改变，页面不会重组
    val viewState = MutableLiveData<PageState>()

    //页面状态
    private val _viewStates = MutableStateFlow(BaseViewState())
    val pageStates = _viewStates.asStateFlow()

    //页面一次性事件 比如showToast
    val _viewEvents = SharedFlowEvents<BaseRequestEvent>()
    val viewEvents = _viewEvents.asSharedFlow()

    fun ktHttpRequest(requestDslClass: suspend CoroutineScope.() -> Unit) {
        viewModelScope.launch {
            kotlin.runCatching {
                viewState.value = PageState.Loading
                _viewStates.setState { copy(pageState = PageState.Loading) }
                _viewEvents.setEvent(BaseRequestEvent.Loading)
                requestDslClass.invoke(this)
                viewState.value = PageState.Success
                _viewStates.setState { copy(pageState = PageState.Success) }
                _viewEvents.setEvent(BaseRequestEvent.Success)
            }.onFailure {
                if (isActive) {
                    if (it.code == NetConstant.EMPTY_CODE) {
                        viewState.value = PageState.Empty
                        _viewStates.setState { copy(pageState = PageState.Empty) }
                    } else {
                        LogX.e(it.stackTraceToString())
                        //是否需要走默认的错误处理（比如弹提示） 默认为true
//                        val userDefaultError = netWorkFailedInterceptor?.intercept(it) ?: true
//                        if (userDefaultError) {
                            viewState.value = PageState.Error(it)
                            _viewStates.setState { copy(pageState = PageState.Error(it)) }
                            _viewEvents.setEvent(BaseRequestEvent.ShowToast(it.msg))
//                        }
                    }
                }
            }
        }
    }
}