package com.heart.heartmerge.viewmodes

import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.viewModelScope
import com.bdc.android.library.ktnet.tryAwait
import com.bdc.android.library.mvi.setEvent
import com.heart.heartmerge.beans.CallSettlementBean
import com.heart.heartmerge.ktnet.NetDelegates
import com.heart.heartmerge.ktnet.error.msg
import com.heart.heartmerge.lce.BaseRequestEvent
import com.heart.heartmerge.repo.service.anchor.AnchorService
import kotlinx.coroutines.launch

/**
 * 作者：Lxf
 * 创建日期：2024/8/3 13:42
 * 描述：
 */
class VideoCallSettlementModel : BaseViewModel() {
    private val service: AnchorService by NetDelegates()
    val callSettlement = mutableStateOf(CallSettlementBean())

    fun getCallSettlement(channelId: String) {
        viewModelScope.launch {
            val result =
                service.getCallSettlement(channelId).tryAwait {
                    _viewEvents.setEvent(BaseRequestEvent.ShowToast(it.msg))
                }
            result?.let {
                callSettlement.value = it
            }
        }
    }
}
