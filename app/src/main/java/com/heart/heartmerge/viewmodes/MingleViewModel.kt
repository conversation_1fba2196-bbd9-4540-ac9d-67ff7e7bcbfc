package com.heart.heartmerge.viewmodes

import android.content.Context
import android.content.Intent
import androidx.lifecycle.viewModelScope
import com.bdc.android.library.ktnet.tryAwait
import com.bdc.android.library.mvi.SharedFlowEvents
import com.bdc.android.library.mvi.setEvent
import com.heart.heartmerge.beans.PageBean
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.beans.VideoRecordInfo
import com.heart.heartmerge.extension.requestChannelFlow
import com.heart.heartmerge.ktnet.NetDelegates
import com.heart.heartmerge.ktnet.error.msg
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.manager.DownloadVideoManager
import com.heart.heartmerge.repo.service.anchor.AnchorService
import com.heart.heartmerge.repo.service.user.UserParams
import com.heart.heartmerge.ui.activities.anchor.AnchorVideoActivity
import com.heart.heartmerge.ui.activities.anchor.IncomingVideoActivity
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.ContextHolder
import com.heart.heartmerge.utils.toJson
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch

class MingleViewModel : BaseViewModel() {
    private val service: AnchorService by NetDelegates()
    private val _pageEvents = SharedFlowEvents<MatchRequestEvent>()
    val pageEvents = _pageEvents.asSharedFlow()
    var fetchRandomAnchor: Job? = null //匹配随机主播的job 在cancel的时候需要取消掉

    fun fetchSwipeList(pageIndex: Int): Flow<List<UserBean>> =
        requestChannelFlow<PageBean<UserBean>, List<UserBean>> {
            request = {
                val params = buildMap<String, String> {
                    put("current", pageIndex.toString())
                    put("size", "30")
                }
                aggregatedService.getRecommend(params)
            }
            transform = { it.records }
        }

    fun fetchRandomAnchor(context: Context) {
        fetchRandomAnchor = viewModelScope.launch {
            delay(2000)
            val randomAnchor =
                service.getRandomAnchor().tryAwait { exception ->
                    _pageEvents.setEvent(MatchRequestEvent.MatchRandomFailed(exception.msg))
                }

            randomAnchor?.let { randomAnchor ->
                _pageEvents.setEvent(MatchRequestEvent.MatchRandomRestView)
                when (randomAnchor.type) {
                    1 -> {
                        _pageEvents.setEvent(MatchRequestEvent.MatchRandomRealPeople(randomAnchor.anchor))
                    }
                    2 -> {
                        if (randomAnchor.url.isNotEmpty()) {
                            randomAnchor.anchor?.let {
                                ContextHolder.context.getLifecycleCallbacks()?.apply {
                                    if (activityList.isNotEmpty()) {
                                        val activity = activityList[0]
                                        if (activity is AnchorVideoActivity) {
                                            LogX.i("AnchorVideoActivity is top")
                                        } else {
                                            // 通过 Intent 启动播放器页面，并传递视频的 URI
                                            val intent = Intent(ContextHolder.context, IncomingVideoActivity::class.java)
                                            intent.putExtra(Constants.INTENT_PARAM_KEY_ANCHOR_INFO, it.toJson())
                                            intent.putExtra(Constants.INTENT_PARAM_FROM_MATCH, true)
                                            intent.putExtra(Constants.INTENT_PARAM_VIDEO_URL, randomAnchor.url)
                                            intent.putExtra(Constants.INTENT_PARAM_VIDEO_DURATION, randomAnchor.duration)
                                            intent.putExtra(Constants.INTENT_PARAM_ISNEEDMUTE, !randomAnchor.openVoice)
                                            intent.putExtra(Constants.INTENT_PARAM_RECORD_ID, randomAnchor.recordId)
                                            activity.startActivity(intent)
                                        }
                                    }
                                }
                            }
//                            DownloadVideoManager.enqueueDownload(randomAnchor.url) { isSuccess, localPath ->
//                                viewModelScope.launch {
//                                    if (isSuccess) {
//                                        LogX.i("DownloadVideoManager, VideoPlaybackController playVideo $localPath")
//                                        _pageEvents.setEvent(MatchRequestEvent.MatchRandomRestView)
//                                        randomAnchor.anchor?.let {
//                                            ContextHolder.context.getLifecycleCallbacks()?.apply {
//                                                if (activityList.isNotEmpty()) {
//                                                    val activity = activityList[0]
//                                                    if (activity is AnchorVideoActivity) {
//                                                        LogX.i("AnchorVideoActivity is top")
//                                                    } else {
//                                                        // 通过 Intent 启动播放器页面，并传递视频的 URI
//                                                        val intent = Intent(ContextHolder.context, IncomingVideoActivity::class.java)
//
//                                                        intent.putExtra(Constants.INTENT_PARAM_KEY_ANCHOR_INFO, it.toJson())
//                                                        intent.putExtra(Constants.INTENT_PARAM_VIDEO_URL, localPath)
//                                                        intent.putExtra(Constants.INTENT_PARAM_ISNEEDMUTE, !randomAnchor.openVoice)
////                                                        intent.putExtra(Constants.INTENT_PARAM_VIDEO_PRICE, videoPlayBean.videoPrice)
//                                                        intent.putExtra(Constants.INTENT_PARAM_RECORD_ID, randomAnchor.recordId)
////                                                        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
//                                                        activity.startActivity(intent)
//                                                    }
//                                                }
//                                            }
//                                        }
//
//                                    } else {
//                                        _pageEvents.setEvent(MatchRequestEvent.MatchRandomFailed(""))
//                                    }
//                                }
//                            }
                        } else {
                            _pageEvents.setEvent(MatchRequestEvent.MatchRandomFailed(""))
                        }
                    }

                    else -> {
                        LogX.e("fetchRandomAnchor: unknow type")
                    }
                }
            }
        }
    }

    fun fetchMatchingHistory(pageIndex: Int, size: Int) {
        viewModelScope.launch {
            val anchors =
                service.getMatchingHistory(UserParams.userFollowFansParamsBody(pageIndex, size))
                    .tryAwait {
                        _pageEvents.setEvent(MatchRequestEvent.MatchHistoryFailed(it.msg))
                    }
            anchors?.let {
                _pageEvents.setEvent(MatchRequestEvent.MatchHistorySuccess(it.records))
            }
        }
    }
}

sealed class MatchRequestEvent {
    data object MatchRandomRestView : MatchRequestEvent()
    data class MatchRandomFailed(val msg: String) : MatchRequestEvent()
//    data class MatchRandomSuccess(val anchorInfo: UserBean) : MatchRequestEvent()
    data class MatchRandomRealPeople(val anchorInfo: UserBean?) : MatchRequestEvent()

    data class MatchHistorySuccess(val list: List<VideoRecordInfo>?) : MatchRequestEvent()
    data class MatchHistoryFailed(val msg: String) : MatchRequestEvent()
}