package com.heart.heartmerge.viewmodes

import androidx.lifecycle.viewModelScope
import com.bdc.android.library.ktnet.tryAwait
import com.bdc.android.library.mvi.SharedFlowEvents
import com.bdc.android.library.mvi.setEvent
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.beans.VideoRecordInfo
import com.heart.heartmerge.ktnet.NetDelegates
import com.heart.heartmerge.ktnet.error.msg
import com.heart.heartmerge.repo.service.anchor.AnchorParams
import com.heart.heartmerge.repo.service.anchor.AnchorService
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch


class VideoHistoryViewModel : BaseViewModel() {
    private val service: AnchorService by NetDelegates()
    private val _pageEvents = SharedFlowEvents<VideoHistoryRequestEvent>()
    val pageEvents = _pageEvents.asSharedFlow()
    private var getAnchorJob: Job? = null
    fun videoHistoryList(cursor: String, size: Int) {
        getAnchorJob = viewModelScope.launch {
            val anchors = service.videoHistory(cursor, size).tryAwait {
                _pageEvents.setEvent(VideoHistoryRequestEvent.GetVideoHistoryFailed(it.msg))
            }
            anchors?.let {
                _pageEvents.setEvent(VideoHistoryRequestEvent.GetVideoHistorySuccess(it.cursor, it.records))
            }
        }
    }

    /**
     * 当取更多的时候 用户又下拉刷新 这个时候需要取消之前的job 否则等异步回调之后 数据会乱
     */
    fun cancelLoadAnchorJob() {
        getAnchorJob?.cancel()
    }
}

sealed class VideoHistoryRequestEvent {
    data class GetVideoHistorySuccess(val cursor: String, val list: List<VideoRecordInfo>?) : VideoHistoryRequestEvent()
    data class GetVideoHistoryFailed(val msg: String) : VideoHistoryRequestEvent()
}