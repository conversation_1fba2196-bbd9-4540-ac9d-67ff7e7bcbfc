package com.heart.heartmerge.viewmodes

import android.media.MediaPlayer
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bdc.android.library.ktnet.tryAwait
import com.bdc.android.library.mvi.asLiveData
import com.bdc.android.library.mvi.setEvent
import com.bdc.android.library.mvi.setState
import com.heart.heartmerge.beans.AvailableTimeBean
import com.heart.heartmerge.beans.ChannelBean
import com.heart.heartmerge.beans.GiftItemBean
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.beans.VideoDeductBean
import com.heart.heartmerge.ktnet.NetDelegates
import com.heart.heartmerge.ktnet.error.msg
import com.heart.heartmerge.lce.BaseRequestEvent
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.repo.service.anchor.AnchorParams
import com.heart.heartmerge.repo.service.anchor.AnchorService
import com.heart.heartmerge.utils.Constants
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch


/**
 * 作者：Lxf
 * 创建日期：2024/7/30 10:17
 * 描述：用户拨打视频
 */
class AnchorVideoViewModel : BaseViewModel() {
    private val service: AnchorService by NetDelegates()

    private val _viewStates = MutableLiveData(AnchorVideoPageState())
    val viewStates = _viewStates.asLiveData()

    private var getAvailableTimeJob: Job? = null

    /**
     * 生成频道号
     */
    fun genVideoChannel(
        userId: String,
        callType: Int
    ) {
        viewModelScope.launch {
            val channelInfo =
                service.genVideoChannel(
                    AnchorParams.getVideoChannelParamsBody(
                        userId,
                        callType,
                    )
                ).tryAwait {
                    _viewEvents.setEvent(BaseRequestEvent.ShowToast(it.msg))
                    _viewStates.setState {
                        copy(anchorVideoStatus = AnchorVideoStatus.GenVideoChannelFailed(it.msg))
                    }
                }
            channelInfo?.let {
                _viewStates.setState {
                    copy(anchorVideoStatus = AnchorVideoStatus.GenVideoChannelSuccess(it))
                }
            }
        }
    }

    fun giftGive(
        anchorId: String,
        giftBean: GiftItemBean,
        callId: Int = 0
    ) {
        _viewStates.setState { copy(anchorVideoStatus = AnchorVideoStatus.Default) }
        viewModelScope.launch {
            val request =
                service.postGiftGive(
                    AnchorParams.giftGiveParamsBody(
                        anchorId.toInt(),
                        giftBean.id.toInt(),
                        callId
                    )
                ).tryAwait {
                    _viewEvents.setEvent(BaseRequestEvent.ShowToast(it.msg))
                }
            request?.let {
                _viewStates.setState {
                    copy(anchorVideoStatus = AnchorVideoStatus.GiftGiveSuccess(giftBean, 1))
                }
            }
        }
    }


    fun getAvailableTime(callId: String) {
        getAvailableTimeJob?.cancel()
        _viewStates.setState { copy(anchorVideoStatus = AnchorVideoStatus.Default) }
        getAvailableTimeJob = viewModelScope.launch {
            val request =
                service.getAvailableTime(callId).tryAwait {
//                    _viewEvents.setEvent(BaseRequestEvent.ShowToast(it.msg))
                }
            request?.let {
                _viewStates.setState {
                    copy(anchorVideoStatus = AnchorVideoStatus.GetAvailableTimeSuccess(it))
                }
            }
        }
    }

    /**
     * 记录虚拟视频的播放结果
     */
    fun aivRecord(recordId: Int, playDuration: Int = 0) {
        viewModelScope.launch {
            val request = service.aivRecord(AnchorParams.aivRecordParamsBody(recordId, playDuration)).tryAwait {
//                _viewEvents.setEvent(BaseRequestEvent.ShowToast(it.msg))
            }
            request?.let {
            }
        }
    }

    /**
     * 开始播放虚拟视频
     * dispatch_type调度类型 1匹配视频 2虚拟视频
     */
    fun aivStart(recordId: Int, dispatchType: Int = 1) {
        viewModelScope.launch {
            val request = service.aivStart(AnchorParams.aivStartParamsBody(recordId, dispatchType)).tryAwait {
//                _viewEvents.setEvent(BaseRequestEvent.ShowToast(it.msg))
            }
            request?.let {
            }
        }
    }

    /**
     * 拒绝虚拟视频
     * dispatch_type调度类型 1真人aib 2虚拟视频拒绝接听 3假aib拒绝接听
     * reason_type0正常拒绝 1超时拒绝 2切换页面拒绝 3通话中 4其他异常 5支付中
     */
    fun aivRefuse(recordId: Int, dispatchType: Int = 1, reasonType: Int = 0) {
        viewModelScope.launch {
            val request = service.aivRefuse(AnchorParams.aivRefuseParamsBody(recordId, dispatchType, reasonType)).tryAwait {
//                _viewEvents.setEvent(BaseRequestEvent.ShowToast(it.msg))
            }
            request?.let {
            }
        }
    }


    /**
     * 获取视频/音频时长,这里获取的是秒
     */
    fun getVideoOrAudioDuration(path: String): Long {
        LogX.e("getVideoOrAudioDuration  $path")
        val mediaPlayer = MediaPlayer()
        try {
            mediaPlayer.setDataSource(path)
            mediaPlayer.prepare()
            val duration = mediaPlayer.duration
            mediaPlayer.release()
            return duration.toLong()
        } catch (e: Exception) {
            mediaPlayer.release()
            e.printStackTrace()
        }
        return 0
    }

    fun countDownCoroutines(
        total: Int,
        onTick: (Int) -> Unit,
        onFinish: () -> Unit,
        scope: CoroutineScope = viewModelScope
    ): Job {
        return flow {
            for (i in total downTo 0 step 1) {
                emit(i)
                delay(1000)
            }
        }.flowOn(Dispatchers.Default)
            .onCompletion {
                onFinish.invoke()
            }
            .onEach { onTick.invoke(it) }
            .flowOn(Dispatchers.Main)
            .launchIn(scope)
    }
}

data class AnchorVideoPageState(
    val anchorVideoStatus: AnchorVideoStatus = AnchorVideoStatus.Default,
)

sealed class AnchorVideoStatus {
    data object Default : AnchorVideoStatus()
    data object Loading : AnchorVideoStatus()
    data class GenVideoChannelSuccess(val channelInfo: ChannelBean?) : AnchorVideoStatus()
    data class GenVideoChannelFailed(val message: String) : AnchorVideoStatus()

    data class GiftGiveSuccess(val giftBean: GiftItemBean, val giveNum: Int) : AnchorVideoStatus()
    data class GetAvailableTimeSuccess(val availableTimeBean: AvailableTimeBean) : AnchorVideoStatus()
}
