package com.heart.heartmerge.beans

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025/6/17 15:49
 * @description :
 */

enum class RelationAction(val value: String) {
    FOLLOW("follow"), UNFOLLOW("cancel_follow"), BLOCK("block"), UNBLOCK("cancel_block")
}

enum class RelationList(val value: String) {
    //关注列表
    FOLLOW("user_follow_anchor"),

    //粉丝列表
    FANS("anchor_follow_user"),

    //拉黑列表
    BLOCK("user_block_anchor"),

    VISIT("anchor_visit_user"),
}

