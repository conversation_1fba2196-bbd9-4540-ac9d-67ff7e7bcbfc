package com.heart.heartmerge.beans

import android.os.Parcelable
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

data class CountryBean(
    val country_list: List<CountryItemBean>?
) {
    @JsonClass(generateAdapter = true)
    @Parcelize
    data class CountryItemBean(
        val id: Int=0,
        val title: String = "",
        val code: String = "",
        var letter: String? = "",
        var icon: String =""
    ): Parcelable
}


