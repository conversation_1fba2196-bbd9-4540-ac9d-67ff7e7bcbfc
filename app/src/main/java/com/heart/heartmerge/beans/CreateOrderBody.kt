package com.heart.heartmerge.beans

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2025/6/5 18:12
 * @description :
 */
data class CreateOrderBody(
    val product_id: Int,
    val pay_type: Int,
    val channel: String,
    val anchor_id: Int = 0,
    val resource_id: Long,
    val resource_url: String,
    val country_id: Int,
    val scene: String,
    val path: String
)

data class CheckOrderBody(
    val receiptData: String? = null,//谷歌支付凭据
    val jsonPurchaseInfo: String? = null,//谷歌支付详细信息
    val payOrderId: String? = null,//创建订单的支付订单id 补单这个值非必须传
    val sku: String? = null, val outOrderId: String? = null,//三方订单号
    val payType: Int? = 1//支付类型 1:google（补单必传，下单的情况通过商品列表去支付类型）
)