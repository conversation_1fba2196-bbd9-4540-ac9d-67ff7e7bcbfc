package com.heart.heartmerge.beans

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

@JsonClass(generateAdapter = true)
@Parcelize
@Entity(tableName = "t_user")
data class UserBean(
    @PrimaryKey var id: String = "",
    var username: String = "",
    var gender: Int = 2, //1男2女
    var countryName: String = "",
    @SerializedName("userCountry")
    @Json(name = "userCountry")
    val userCountry: CountryBean.CountryItemBean? = null,
    @SerializedName("country")
    @Json(name = "country")
    val anchorCountry: CountryBean.CountryItemBean? = null,
    val onlineStatus: String = "",/*用户在线状态*/
    var level: String = "",
    val diamondTotal: Int = 0,/*历史充值点数*/
    var diamond: Int = 0,
    val incomeDiamond: Int = 0,/*主播收益钻石数*/
    val incomeDiamondDecimal: Int = 0,/*主播收益钻石数*/
    val token: String = "",
    val videoPrice: Int = 0,
    val firstFlag: String = "",/*是否首次登录0否1是*/
    val followNum: Int = 0,
    val fansNum: Int = 0,
    val unionId: String = "",
    val userCategory: String = "",
    val freeVideoCall: Int = 0,/*免费视频通话次数*/
    val freeRandomMatch: Int = 0,/*免费随机匹配次数*/
    val freeVideoPlay: Int = 0,/*视频免费播放次数*/
    val initialFreeVideoCall: Int = 0,/*初始免费视频通话次数*/
    val initialFreeRandomMatch: Int = 0,/*初始免费随机匹配次数*/
    val initialFreeVideoPlay: Int = 0,/*初始视频免费播放次数*/
    val coverVideoUrl: String = "",
    val showVideoUrl: String = "",
    val firstRecharge: Int = 0,//剩余首充次数，0-暂无首充次数
    var msgSendNum: Int = 0,
    val vipFlag: String = "",/*是否vip用户0否1是*/
    val vipExpireDay: String = "",
    val vipExpireFlag: String = "",/*vip是否可用 0不可用1可用*/
    val remark: String = "",
    val age: Int = 0,
    val initialFreeDiamondGive: Int = 0, //首次登录赠送的钻石
    val firstRechargeExpireTime: String = "",/*首充活动截止时间*/
    val languages: List<LanguageBean> = emptyList(),
    val photos: List<String> = emptyList(),/*关注列表相关字段*/
    val anchorId: String = "", //关注列表主播id
    val userLanguages: List<LanguageBean>? = null,

    /*主播相关字段*/
    val hotAnchor: String = "0", //0否1是 是否start
    val highGroundFileName: String = "",
    val normalGroundFileName: String = "",
    val normalRoomTitle: String = "",
    val groundFileName: String = "",//直播间背景图片
    val roomTitle: String = "", //直播间标题
    val language: String = "", //语言
//    val isNotDisturb: String = "0", //是否开启免打扰 0 关 1开
//    val disturbExpireSecs: Long = 0L, //免打扰自动关闭时间
    val signature: String = "", //签名
    val isTop: String = "0", //是否top 0否1是
    @Json(name = "anchor_tag_reports") val anchorLabelList: List<AnchorLabelBean>? = emptyList(),  //标签列表
    @Json(name = "media_list") val anchorFileList: List<AnchorFileBean>? = emptyList(), //图片或视频
    @Json(name = "anchor_gift_reports") val anchorGift: List<GiftItemBean>? = emptyList(), //礼物墙
    @Json(name = "show_whats_app") val show_whats_app: Boolean = false, //是否锁WhatsApp

    val nextGradeDiff: Int = 0,
    val currentGradeStd: Int = 0,
    val nextGradeStd: Int = 0,
    val hasGoogleRelation: String = "",//是否关联Google帐号 1-关联，0-不关联
    val hasThirdPay: String = "",/*是否允许第三方支付 1-允许，0-不允许*/
    val isVirVideo: String = "",/*是否是虚拟视频，匹配的时候返回 未使用 1使用*/
    val virVideoId: Long = 0L,/*匹配时返回的虚拟视频对应的ID*/
    val recordId: String = "",/*视频记录*/
    val isNeedMute: Boolean = false, //匹配虚拟视频是否有声音
    val rongCloudAppKey: String = "",//融云appkey
    val agoraRtcAppId: String = "", //声网appid
    val facebookId: String = "", //facebookId
    val facebookClientToken: String = "", //facebook clientToken
    val firstRechargeCountdown: List<Long>? = null,//首充倒计时


    //新版本api字段
    val avatar: String = "",
    val showAvatar: String = "",
    val nickname: String = "",
    var balance: Int = 0,
    val birthdayAt: String = "",
    val followCount: Int = 0,
    val fansCount: Int = 0,
    val is_vip: Boolean = false,
    val rongcloudAppID: String = "",
    val rongcloudToken: String = "",
    val videoCallPrice: Int = 0,
    val enable_dnd: Boolean = false,
    val enable_match: Boolean = false,
    val bind_google: Boolean = false,
    val level_config: LevelInfo? = null,
    val user_vip: UserVIPInfo? = null,
    val app_id: Int = 0,
    var status: String = "3",//1在线 2通话中 3离线 4勿扰
    val showRelation: String = "0", //0无关系 1关注 2被关注 3相互关注 4拉黑 5被拉黑
    var dnd_expire: Long = 0L,
    val country_code: Int = 0,
    @SerializedName("anchor_level")
    @Json(name = "anchor_level")
    val anchorLevel: AnchorLevelBean? = null,

    var reLogin: Boolean = false,//处理必须清理缓存重新登录情况
) : Parcelable {
    val isVIP get() = is_vip

    val isExpired get() = vipExpireFlag == "0"

    val isGoogleUser get() = userCategory == "A"

    //游客登录
    val isGuest get() = true

    //已绑定帐号
    val isBound get() = bind_google

    //    val allowThirdPay get() = hasThirdPay == "1"
    val allowThirdPay get() = true

    val alreadyFirstRecharge get() = firstRecharge == 0

    val isRegistration get() = firstFlag == "1"

    @JsonClass(generateAdapter = true)
    @Parcelize
    data class LevelInfo(
        val level: Int,
        val icon: String,
        val avatar_frame: String,
        val begin: Int,
        val end: Int,
        val exp: Int
    ) : Parcelable

    @JsonClass(generateAdapter = true)
    @Parcelize
    data class UserVIPInfo(
        val uid: Int, val expire_at: Long, val is_vip: Boolean,
        val title: String,
        val level: Int
    ) :
        Parcelable
}

