package com.heart.heartmerge.beans

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class VideoHistoryList(
    @<PERSON>son(name = "cursor") val cursor: String = "",
    @<PERSON><PERSON>(name = "list") val records: MutableList<VideoRecordInfo>? = null,
)

@JsonClass(generateAdapter = true)
data class VideoRecordInfo(
    @Json(name = "callId") val callId: String = "", //通话ID
    @Json(name = "anchor_id") val anchorUserId: String = "",
    @<PERSON><PERSON>(name = "anchorNickName") val nickName: String = "",
    @<PERSON><PERSON>(name = "anchor_avatar") val headFileName: String = "", //主播头像
    @<PERSON><PERSON>(name = "callDurationSecond") val videoTime: Long = 0, //通话秒数
    @<PERSON><PERSON>(name = "connectAt") val createTime: Long = 0L,
    @<PERSON><PERSON>(name = "call_stat") val callStat: String = "", //1未接通 2已接通
    @<PERSON><PERSON>(name = "status") val status: String = "", //1在线 2通话中 3离线 4勿扰
    @<PERSON><PERSON>(name = "coin_total") val coilTotal: Int = 0,  //货币总数
)