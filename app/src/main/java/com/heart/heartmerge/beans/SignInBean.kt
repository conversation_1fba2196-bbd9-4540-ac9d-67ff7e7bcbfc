package com.heart.heartmerge.beans

data class SignInBean(
    val todayFlag: String,
    val todayCanFlag: String,
    val userSignIns: List<SignInItemBean>,
    val list: List<SignInItemBean>,
    val default_list: List<SignInItemBean>,
    val current_date_format: String,
    val next_gain_time: Long
)

data class SignInItemBean(
    val id: String,
    val signFlag: String,
    val signInDate: String,
    val signInNum: Int,
    val signInValue: Int,
    val signInType: Int,
    val isSignIn: String,
    val userSignInGive: SignInGiftBean,

    //新增字段
    val record_id: String,
    val day_num: Int,
    val stat: Int,//1 未领取2已领取 3过期
    val coin: Int,
    var nextCoin: Int? = 0,
    val date_format: String
) {
    val isExpired get() = stat == 3
    val isReceived get() = stat == 2
    val isAvailable get() = stat == 1
}


data class SignInGiftBean(
    val id: String,
    val signInNum: Int,
    val signInType: Int,
    val signInValue: Int,
    val signInUrl: String
)