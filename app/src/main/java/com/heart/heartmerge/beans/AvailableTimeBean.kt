package com.heart.heartmerge.beans

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class AvailableTimeBean(
    @Json(name = "begin_time")
    val beginTime: Long = 0L, //通话开始时间戳(秒)
    @<PERSON>son(name = "end_time")
    val endTime: Long = 0L, //通话结束时间戳(秒)
    @Json(name = "is_balance_not_enough")
    val isBalanceNotEnough: Boolean = false,//下一分钟是否满足通话
    @Json(name = "surplus_duration")
    val surplusDuration: Int = 0, //剩余时长
    @<PERSON>son(name = "user_balance")
    val userBalance: Int = 0,//用户余额
)