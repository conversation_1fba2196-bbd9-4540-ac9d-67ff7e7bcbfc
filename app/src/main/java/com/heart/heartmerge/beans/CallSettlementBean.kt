package com.heart.heartmerge.beans

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class CallSettlementBean(
    @Json(name = "nickName")
    val nickName: String = "", //用户昵称
    @<PERSON><PERSON>(name = "headFileName")
    val headFileName: String = "", //用户头像
    @J<PERSON>(name = "gender") //用户性别
    val gender: Int = 2,
    @<PERSON><PERSON>(name = "country") //国家
    val country: String = "",
    @<PERSON>son(name = "diamondConsumption") //消耗钻石
    val diamondConsumption: Int = 0,
    @<PERSON><PERSON>(name = "videoTime") //国家
    val videoTime: String = "",
    @<PERSON><PERSON>(name = "freeFlag") //是否包含免费通话 是 数量为1 否为0
    val freeFlag: String = "",
    @<PERSON><PERSON>(name = "freeTime") //免费时长 秒
    val freeTime: Int = 0,
    @<PERSON><PERSON>(name = "freeTimeFormat") //免费时长(格式化时间后) 也是匹配通话时长
    val freeTimeFormat: String = "",
    @<PERSON><PERSON>(name = "age") //年龄
    val age: Int = 0,
)