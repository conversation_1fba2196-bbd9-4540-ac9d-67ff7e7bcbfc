package com.heart.heartmerge.beans

import android.os.Parcelable
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

@Parcelize
@JsonClass(generateAdapter = true)
data class AnchorFileBean(
    val id: Int = 0,
    val anchorId: String = "",
    @<PERSON>son(name = "resource_type") //1图片 2视频
    val fileType: String = "",
    @<PERSON>son(name = "url")
    val fileUrl: String = "",
    val thumbnail: String = "",
    val status: String = "",
    @<PERSON><PERSON>(name = "media_type")  //1个人资料 2私密相册信息
    var isLock: String = "",

    // new api field
    val open_voice: Boolean = false,
) : Parcelable