package com.heart.heartmerge.beans

import android.os.Parcelable
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

/**
 * 资源数据模型
 * 
 * @param id 资源ID
 * @param appId 应用ID
 * @param resourceType 资源类型 (1-视频, 2-图片, 3-音频等)
 * @param url 资源URL
 * @param updatedAt 更新时间戳
 * @param createdAt 创建时间戳
 */
@JsonClass(generateAdapter = true)
@Parcelize
data class ResourceBean(
    @Json(name = "id") val id: Long = 0,
    @<PERSON>son(name = "app_id") val appId: Int = 0,
    @<PERSON>son(name = "resource_type") val resourceType: Int = 1,
    @Json(name = "url") val url: String = "",
    @<PERSON><PERSON>(name = "updated_at") val updatedAt: Long = 0,
    @<PERSON>son(name = "created_at") val createdAt: Long = 0
) : Parcelable {
    
    /**
     * 获取文件扩展名
     */
    fun getFileExtension(): String {
        return url.substringAfterLast('.', "")
    }
    
    /**
     * 获取资源类型描述
     */
    fun getResourceTypeDescription(): String {
        return when (resourceType) {
            1 -> "视频"
            2 -> "图片"
            3 -> "音频"
            4 -> "文档"
            else -> "未知"
        }
    }
    
    /**
     * 判断是否为有效资源
     */
    fun isValid(): Boolean {
        return id > 0 && url.isNotBlank() && url.startsWith("http")
    }
}

/**
 * 资源列表响应模型
 */
@JsonClass(generateAdapter = true)
data class ResourceListResponse(
    @Json(name = "list") val list: List<ResourceBean> = emptyList()
)
