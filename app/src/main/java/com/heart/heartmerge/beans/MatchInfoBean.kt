package com.heart.heartmerge.beans

import android.os.Parcelable
import androidx.collection.IntIntMap
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

@JsonClass(generateAdapter = true)
@Parcelize
data class MatchInfoBean(
    @Json(name = "type")
    val type: Int = 0, //1真实 2虚拟视频
    @<PERSON><PERSON>(name = "media_id")
    val mediaId: Int = 0, //媒体id
    @Json(name = "duration")
    val duration: Int = 0, //媒体id
    @<PERSON><PERSON>(name = "record_id")
    val recordId: Int = 0, //记录id
    @<PERSON><PERSON>(name = "url")
    val url: String = "", //视频url
    @<PERSON><PERSON>(name = "open_voice")
    var openVoice: Boolean = false, //是否开启声音
    @<PERSON><PERSON>(name = "is_rate")
    val isRate: Boolean = false,
    @<PERSON><PERSON>(name = "anchor")
    val anchor: UserBean? = null,
) : Parcelable