package com.heart.heartmerge.beans

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

// 通用 WebSocket 消息基类
@JsonClass(generateAdapter = true)
data class WebSocketResponse(
    val code: Int,
    val data: WebSocketMessage?,
    val message: String?
)

// WebSocket 消息体
@JsonClass(generateAdapter = true)
data class WebSocketMessage(
    val cmd: String,
    val owner: Long,
    val token: String,
    val seq: String,
    val msg: String? = null,
    val extend: String? = null, // extend 字段实际是一个 JSON 字符串
    val ack: Any? = null // ack 字段可能为 null 或其他类型
)

// remote_call_refuse 消息的 extend 结构
@JsonClass(generateAdapter = true)
data class RemoteCallRefuseExtend(
    @Json(name = "peerUID") val peerUID: Int = 0, //被叫的uid
    @Json(name = "callID") val callID: Int = 0,  //通话id
    @Json(name = "callIDOther") val callIDOther: Int = 0, //用户主动点击拒绝则传收到邀请的频道id，如果被动拒绝则是其他频道或者其他邀请的频道id
    @Json(name = "reasonType") val reasonType: String = "", //1当前在通话中或已有通话弹窗  2用户主动点击拒绝 3超时不接，拒绝  7切换页面拒绝 8加入频道失败
)

// remote_user_gold_change 消息的 extend 结构
@JsonClass(generateAdapter = true)
data class RemoteCallGoldChangeExtend(
    @Json(name = "beforeGold") val beforeGold: Int = 0, //变化前
    @Json(name = "behindGold") val behindGold: Int = 0, //变化后
    @Json(name = "pay_record_id") val payRecordId: Int = 0, //充值订单id
)

// remote_user_clean_match 消息的 extend 结构
@JsonClass(generateAdapter = true)
data class RemoteUserCleanMatchExtend(
    @Json(name = "times") val times: Int = 0, //当日还剩的匹配次数
    @Json(name = "match_recover_duration") val matchRecoverDuration: Long = 0, //匹配次数恢复剩余时长
)

// remote_socket_status_change 消息的 extend 结构
@JsonClass(generateAdapter = true)
data class RemoteSocketStatusChangeExtend(
    @Json(name = "peerUID") val peerUID: Int = 0, //对方的ui
    @Json(name = "status") val status: Int = 3, //1在线 2通话中。3离线 4勿扰
)

// remote_notify_window 消息的 extend 结构
@JsonClass(generateAdapter = true)
data class RemoteNotifyWindowChangeExtend(
    @Json(name = "type") val type: Int = 0, //1通话相关 2 资源缓存 3更新提醒
    @Json(name = "call_type") val callType: Int = 0, //1虚拟视频 2 假aib 3真aib
    @Json(name = "anchor_id") val anchorId: String = "",  //主播id
    @Json(name = "nickname") val nickname: String = "", //主播昵称
    @Json(name = "anchor_avatar") val anchorAvatar: String = "", //主播头像
    @Json(name = "url") val url: String = "", //虚拟视频地址/提前缓存的资源
    @Json(name = "jump_url") val jumpUrl: String = "", //跳转协议
    @Json(name = "jump_type") val jumpType: String = "",//1 app内部跳转 2http 内部跳转 3 http 外部跳转
    @Json(name = "open_voice") val openVoice: Boolean = false, //是否开启声音
    @Json(name = "duration") val duration: Int = 0, //虚拟视频时长
    @Json(name = "record_id") val recordId: Int = 0, //记录id
)

