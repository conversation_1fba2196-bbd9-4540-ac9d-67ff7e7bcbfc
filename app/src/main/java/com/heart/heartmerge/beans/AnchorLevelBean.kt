package com.heart.heartmerge.beans

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

@JsonClass(generateAdapter = true)
@Parcelize
data class AnchorLevelBean(
    @SerializedName("discount_price")
    @Json(name = "discount_price")
    var discountPrice: Int = 0, //用户折扣价格
    @SerializedName("min_discount_price")
    @Json(name = "min_discount_price")
    var minDiscountPrice: Int = 0, //用户最小折扣价格
    @SerializedName("call_price")
    @Json(name = "call_price")
    var callPrice: Int = 0, //主播通话
) : Parcelable