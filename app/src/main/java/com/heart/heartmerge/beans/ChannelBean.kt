package com.heart.heartmerge.beans

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class ChannelBean(
    @Json(name = "anchorId")
    val anchorId: String = "",
    @<PERSON><PERSON>(name = "callID")
    val channelId: String = "",
    @<PERSON><PERSON>(name = "rtcToken")
    val rtcToken: String = "",
    @<PERSON><PERSON>(name = "callDuration")
    val callDuration: Int = 0,
    @<PERSON><PERSON>(name = "wait_reward")
    val waitReward: Boolean = false,//true 有等待奖励 false 没有等待奖励
    @<PERSON>son(name = "free")
    val free: Boolean = false,//是否免费。true 是免费 false 不是免费
    @<PERSON><PERSON>(name = "wait_duration")
    val waitDuration: Int = 0,//等待时长 x秒自动取消
    @<PERSON>son(name = "join_room_wait_timeout")
    val joinRoomWaitTimeout: Int = 0,//进入房间后对方没进入房间的等待时间
    @Json(name = "join_channel_wait_time")
    val joinChannelWaitTime: Int = 0,//加入房间超时时间
)