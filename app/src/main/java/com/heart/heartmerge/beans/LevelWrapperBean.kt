package com.heart.heartmerge.beans

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: <PERSON>cha<PERSON>
 * @date: 2024/11/19 16:13
 * @description :等级Bean
 */
data class LevelWrapperBean(
    val configs: List<LevelConfigBean>, val exp: Int, val level_ranges: List<LevelBean>
)

data class LevelConfigBean(
    val level: Int, val begin: Int, val end: Int, val icon: String, val avatar_frame: String
)

data class LevelBean(
    val start: Int,
    val end: Int,
    val startGrade: Int,
    val endGrade: Int,

    //new api add field
    val level_start: Int,
    val level_end: Int,
    val weekly_bonus_list: List<WeeklyBonusBean>? = null,
    val permissions: List<PrivilegeBean>? = null
) {
    data class WeeklyBonusBean(
        val day: Int,
        val coin: Int,
    )

    @Parcelize
    data class PrivilegeBean(
        val title: String,
        val permission: String,
        val value: String,
        val bonus: Int,
        var stat: Int,//状态 0 未充值 1未领取 2已领取
        val record_id: Int,
        val rechargeBonus: String,
        val weeklyCanGet: String,
        val weeklyBonus: Int
    ) : Parcelable {

        val isAvailable get() = stat == 1
        val isReceived get() = stat == 2
    }
}
