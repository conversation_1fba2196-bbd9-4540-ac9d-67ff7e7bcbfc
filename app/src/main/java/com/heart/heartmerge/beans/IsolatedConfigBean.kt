package com.heart.heartmerge.beans

import android.os.Parcelable
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/6/20 11:24
 * @description : 用户新配置模型
 */
@Parcelize
@JsonClass(generateAdapter = true)
data class IsolatedConfigBean(
    val show_im_recharge_coin: Boolean? = false/*是否im 充值金币按钮*/,
    val anchor_detail_stay_duration_for_aib: Int? = 0/*查看主播详情在详情页面停留x秒，在线的情况触发aib*/,
    val is_recharge: Boolean? = false/*是否充值*/
) : Parcelable
