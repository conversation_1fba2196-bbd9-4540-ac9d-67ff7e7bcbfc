package com.heart.heartmerge.beans

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2025/2/11 14:51
 * @description :
 */
enum class GoodsQueryType(val value: Int) {
    DIAMOND(1), SUBSCRIBE(2), FIRST_RECHARGE(3), ALL(4)
}

class GoodsWrapperBean(
    val diamondPrices: List<GoodsBean>,
    val firstRechargePrices: List<GoodsBean>,
    val goldGoods: List<GoodsBean>? = null,
    val vip_privileges: List<PrivilegeBean>? = null,
    val level_config: LevelConfigBean? = null
) {
    data class PrivilegeBean(
        val title: String,
        val permission: String,
        val sub_title: String,
        val kvs: List<KvBean>? = null
    ) {
        data class KvBean(
            val key: String, val value: String, val level: Int, val cal_value: Int
        )
    }
}