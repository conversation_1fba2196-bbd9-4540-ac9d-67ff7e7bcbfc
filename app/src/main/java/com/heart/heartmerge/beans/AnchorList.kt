package com.heart.heartmerge.beans

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class AnchorList(
    @<PERSON>son(name = "current") val current: Int = 0,
    @<PERSON><PERSON>(name = "hitCount") val hitCount: Boolean = false,
    @Json(name = "pages") val pages: Int = 0,
    @<PERSON><PERSON>(name = "searchCount") val searchCount: Boolean = false,
    @<PERSON><PERSON>(name = "total") val total: Int = 0,
    @<PERSON>son(name = "size") val size: Int = 0,
    @<PERSON><PERSON>(name = "cursor") val countId: String = "",
    @<PERSON><PERSON>(name = "list") val records: MutableList<UserBean>? = null,
    @Json(name = "tab_info") val tabInfo: TabBean? = null,
)

@JsonClass(generateAdapter = true)
data class RecordInfo(
    @Json(name = "id") val id: String = "",
    @<PERSON><PERSON>(name = "constellation") val constellation: Int = 0, //星座
    @Json(name = "hotAnchor") val hotAnchor: String, //0否1是
    @<PERSON><PERSON>(name = "incomeDiamond") val incomeDiamond: Int = 0, //收入钻石
    @Json(name = "highGroundFileName") val highGroundFileName: String = "",
    @Json(name = "normalGroundFileName") val normalGroundFileName: String = "",
    @Json(name = "normalRoomTitle") val normalRoomTitle: String = "",
    @Json(name = "showVideoUrl") val showVideoUrl: String = "", //show视频url
    @Json(name = "coverVideoUrl") val coverVideoUrl: String = "", //封面视频url
    @Json(name = "vipFlag") val vipFlag: String = "",
    @Json(name = "followFlag") val followFlag: String = "",
    @Json(name = "videoPrice") val videoPrice: Int = 0, //视频价格/分
    @Json(name = "groundFileName") val groundFileName: String = "",//直播间背景图片
    @Json(name = "roomTitle") val roomTitle: String = "", //直播间标题
    @Json(name = "unionId") val unionId: Int = 0, //工会Id
    @Json(name = "language") val language: String = "", //语言
    @Json(name = "country") val country: String = "", //国家
    @Json(name = "gender") val gender: String = "", //性别
    @Json(name = "userCode") val userCode: String = "", //用户编号
    @Json(name = "nickName") val nickName: String = "", //昵称
    @Json(name = "headFileName") val headFileName: String = "", //头像文件名
    @Json(name = "onlineStatus") val onlineStatus: String = "", //主播状态0离线1在线2繁忙
    @Json(name = "age") val age: Int = 0, //年龄

)