package com.heart.heartmerge.beans

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

data class GiftBean(
    val list: List<GiftItemBean>
)

@JsonClass(generateAdapter = true)
@Parcelize
data class GiftItemBean(
    val id: String = "", //礼物ID
    val showName: String = "", //礼物名称
    val giftCode: String = "",
    var coin: Int = 0, //用户显示金币
    @Json(name = "svga_url")
    @SerializedName("svga_url")
    val giftSvgaUrl: String = "",
    val icon: String = "",  //礼物图标
    val giftStatus: String = "",
    val anchorCount: Int = 0,
    val idIcon: String = "",
    val quantity: Int = 0,
    var checked: Boolean = false,
    val num: Int = 1, // 主播详情礼物数量

    val userNickName: String = "",
    val userHeadFileName: String = "",
    val anchorNickName: String = "",
    val anchorHeadFileName: String = "",
    val giftNum: Int = 0,

    //新增字段
    val callId:String = "",//通话ID
    val resource_id:String = "",//资源id

    val title:String = "", //禮物名稱
    val total:Int = 0, //礼物总数
): Parcelable