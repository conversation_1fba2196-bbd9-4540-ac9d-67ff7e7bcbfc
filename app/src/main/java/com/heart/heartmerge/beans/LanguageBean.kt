package com.heart.heartmerge.beans

import android.os.Parcelable
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

@Parcelize
@JsonClass(generateAdapter = true)
data class LanguageBean(
    @Json(name = "id")
    val id: String = "",
    @<PERSON>son(name = "languageName")
    val languageName: String = "",
    @<PERSON>son(name = "languageCode")
    val languageCode: String = "",
    var checked: Boolean = false
): Parcelable