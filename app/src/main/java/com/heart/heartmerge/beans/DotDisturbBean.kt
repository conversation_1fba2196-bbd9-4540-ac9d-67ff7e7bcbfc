package com.heart.heartmerge.beans

import android.os.Parcelable
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/12/17 13:54
 * @description :免打扰
 */
@JsonClass(generateAdapter = true)
@Parcelize
data class DotDisturbBean(val isNotDisturb: String=""/*是否开启免打扰 0 关 1开*/,
 val dnd_expire: Long=0L/*免打扰自动关闭时间*/) : Parcelable
