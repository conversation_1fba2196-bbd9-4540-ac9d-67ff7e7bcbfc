package com.heart.heartmerge.beans

import android.os.Parcelable
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2025/6/5 11:49
 * @description :登录鉴权
 */
data class AuthBean(
    val accessToken: String,
    val accessExpire: Long,
    val refreshAfter: Long,
    val config: UserConfigBean,
    val user: UserBean
)

@Parcelize
@JsonClass(generateAdapter = true)
data class UserConfigBean(
    val daily_duration: Int,
    val cdn: String,
    val agoraConfig: AgoraConfigBean,
    val match_price: Int,
    val vip_match_price: Int,
    val match_times: Int,
    val match_recover_duration: Long,
    val list_param: String,
    val detail_photo_param: String,
    val thumb_param: String,
    val whats_app_recharge_min_coins: Int,
    val facebook_id: String,
    val facebook_client_token: String,
    val dt_app_id: String,
    val dt_server_url: String
) : Parcelable

@Parcelize
@JsonClass(generateAdapter = true)
data class AgoraConfigBean(val appID: String) : Parcelable