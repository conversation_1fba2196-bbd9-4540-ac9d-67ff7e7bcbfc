package com.heart.heartmerge.beans

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2024/8/29 16:39
 * @description :支付订单
 */
data class OrderBean(
    val id: String,
    val createTime: String,
    val updateTime: String,
    val userId: String,
    val priceId: String,
    val discountType: String,
    val diamondNum: String,
    val realDiamondNum: Int,
    val amount: String,
    val actualAmount: String,
    val googleProductId: String,
    val externalOrderId: String,
    val orderStatus: String,
    val orderType: String/*订单类型 1消耗2订阅*/,
    val nickName: String,
    val renewedNum: Int,
    val orderSource: String,
    val payMethodId: String,
    val orderTrigger: String,
    val triggerAnchorId: String,
    val headFileName: String,
    val userCode: String,
    val packageName: String,
    val url: String,
    val country: String,
    val userRegTime: String,

    // new api field
    val payOrderId: String,
    val outOrderId: String,
    val external: Boolean
) {
    val isPaid get() = orderStatus == "1"

    val isSubscribe get() = orderType == "2"
}
