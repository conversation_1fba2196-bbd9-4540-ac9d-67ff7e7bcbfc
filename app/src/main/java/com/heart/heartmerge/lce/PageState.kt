package com.heart.heartmerge.lce

/**
 *
 * @Author： Lxf
 * @Time： 2022/2/15 11:21 上午
 * @description：页面状态密封类
 */
sealed class PageState {
    object Default : PageState()
    object Loading : PageState()
    object Success : PageState()
    object Empty : PageState()
    data class Error(val throwable: Throwable) : PageState()
}

data class BaseViewState(
    val pageState: PageState = PageState.Default
)

/**
 * TODO base 页面时间 事件是一次性事件 比如弹提示
 *
 */
sealed class BaseRequestEvent {
    object Loading : BaseRequestEvent()  //请求开始事件
    object Success : BaseRequestEvent() //请求成功事件
    data class ShowToast(val message: String) : BaseRequestEvent() //请求失败提示
}


