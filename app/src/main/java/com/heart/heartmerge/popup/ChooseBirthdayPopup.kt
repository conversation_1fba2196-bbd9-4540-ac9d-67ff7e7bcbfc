package com.heart.heartmerge.popup

import android.app.Activity
import com.github.gzuliyujiang.wheelpicker.entity.DateEntity
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.PopupChoooseBirthdayBinding
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BottomPopupView
import java.util.Date
import java.util.Locale

class ChooseBirthdayPopup(activity: Activity, private val onClick: (Long) -> Unit) :
    BottomPopupView(activity) {

    override fun getImplLayoutId(): Int = R.layout.popup_chooose_birthday

    override fun initPopupContent() {
        super.initPopupContent()
        val binding = PopupChoooseBirthdayBinding.bind(popupContentView)

        binding.datePickerActions.setRange(
            DateEntity.target(1960, 1, 1), DateEntity.target(Date().apply {
                this.year = this.year - 18
            })
        )
//        binding.datePickerActions.setDefaultValue(DateEntity.target(1960, 1, 1))
        if (Locale.getDefault().language == "zh") {
            binding.datePickerActions.setDateLabel("年", "月", "日")
        }

        binding.tvCancel.setOnClickListener { dismiss() }

        binding.tvConfirm.setOnClickListener {
            onClick.invoke(
                Date(
                    binding.datePickerActions.selectedYear - 1900,
                    binding.datePickerActions.selectedMonth - 1,
                    binding.datePickerActions.selectedDay,
                    0,
                    0
                ).time
            )
            dismiss()
        }
    }
}

fun showBirthdayPopup(activity: Activity, block: (Long) -> Unit) {
    XPopup.Builder(activity).asCustom(ChooseBirthdayPopup(activity, block)).show()
}
