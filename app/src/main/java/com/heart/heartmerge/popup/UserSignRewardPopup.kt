package com.heart.heartmerge.popup

import android.app.Activity
import androidx.core.content.ContextCompat
import com.bdc.android.library.extension.click
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.SignInItemBean
import com.heart.heartmerge.databinding.PopupUserSignReawardBinding
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond
import com.heart.heartmerge.manager.MainPopupPriority
import com.heart.heartmerge.manager.PriorityDialogManager
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.util.XPopupUtils

open class UserSignRewardPopup(
    activity: Activity, protected val signInGiftBean: SignInItemBean?, val block: () -> Unit = {}
) : BasePriorityPopup(activity) {

    override fun getImplLayoutId(): Int = R.layout.popup_user_sign_reaward

    lateinit var mBinding: PopupUserSignReawardBinding

    override fun initPopupContent() {
        super.initPopupContent()
        mBinding = PopupUserSignReawardBinding.bind(contentView)

        mBinding.tvCheckedIn.setGradientColors(
            intArrayOf(
                ContextCompat.getColor(
                    context, R.color.color_EC12E2
                ), ContextCompat.getColor(context, R.color.color_8531FF)
            )
        )
        mBinding.tvClaimTomorrow.setGradientColors(
            intArrayOf(
                ContextCompat.getColor(
                    context, R.color.color_EC12E2
                ), ContextCompat.getColor(context, R.color.color_8531FF)
            )
        )
        mBinding.icClose.click {
            dismiss()
            block.invoke()
        }
        mBinding.tvTitle.text = "${signInGiftBean?.day_num}"
        mBinding.tvContent.text = context.getString(
            R.string.checked_in_successfully_reward_tips,
            signInGiftBean?.coin?.toShowDiamond().toString()
        )
        mBinding.tvAlertContent.text = "${signInGiftBean?.coin?.toShowDiamond()}"
        mBinding.tvTomorrowAlertContent.text = "${signInGiftBean?.nextCoin?.toShowDiamond()}"
//        mBinding.tvDesc.makeVisible()
        mBinding.btnOk.setOnClickListener {
            onOkButtonClick()
            block.invoke()
        }
    }

    open fun onOkButtonClick() {
        dismiss()
    }

    override fun getMaxWidth(): Int {
        return (XPopupUtils.getAppWidth(this.context) * 0.9f).toInt()
    }
}

fun showUserSignRewardPopup(activity: Activity, diamond: SignInItemBean?, block: () -> Unit = {}) {
    XPopup.Builder(activity).asCustom(UserSignRewardPopup(activity, diamond, block)).show()
}

class UserFirstLoginRewardPopup(activity: Activity, diamond: SignInItemBean?) :
    UserSignRewardPopup(activity, diamond) {
    override fun initPopupContent() {
        super.initPopupContent()
        mBinding.tvTitle.setText(R.string.main_reward_user_first_title)
        mBinding.tvAlertContent.text = String.format(
            activity.getString(R.string.label_new_user_give_diamond),
            "${signInGiftBean?.coin?.toShowDiamond()}"
        )
    }

    override fun onOkButtonClick() {
        MMKVDataRep.isFirstLoginRewardShowed = true
        super.onOkButtonClick()
        PriorityDialogManager.removeData(MainPopupPriority.FIRST_USER_LOGIN_REWARD) //删除队列
    }
}