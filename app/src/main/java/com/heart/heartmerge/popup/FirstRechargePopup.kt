package com.heart.heartmerge.popup

import androidx.activity.ComponentActivity
import com.bdc.android.library.extension.click
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.GoodsBean
import com.heart.heartmerge.databinding.PopupFirstRechargeBinding
import com.heart.heartmerge.utils.PurchaseScene
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView
import java.lang.ref.WeakReference
import java.util.Timer
import java.util.TimerTask

/**
 * 首充弹框
 * 作者：Assistant
 * 创建日期：2024/12/25
 * 描述：首充优惠弹框UI
 */
class FirstRechargePopup(
    private val context: ComponentActivity,
    private val rechargeBean: GoodsBean,
) : CenterPopupView(context) {

    private val weakActivity = WeakReference(context)
    private lateinit var binding: PopupFirstRechargeBinding
    private var timer: Timer? = null
    private var remainingSeconds = 3600 // 1小时倒计时

    override fun getImplLayoutId(): Int = R.layout.popup_first_recharge

    override fun initPopupContent() {
        super.initPopupContent()
        binding = PopupFirstRechargeBinding.bind(popupImplView)

        initViews()
        startCountdown()
        setupClickListeners()
    }

    private fun initViews() {
        binding.tvTitle.apply {
            setGradientColors(
                intArrayOf(
                    context.getColor(R.color.color_FFD83C), context.getColor(R.color.color_FFD83C)
                )
            )
        }
        // 设置钻石数量
        binding.tvDiamondCount.text = context.getString(R.string.first_recharge_diamonds, 100)

        // 设置原价（删除线）
        binding.tvOriginalPrice.text =
            context.getString(R.string.first_recharge_original_price, 120)

        // 设置折扣
        binding.tvDiscount.text = context.getString(R.string.first_recharge_discount, 20)

        // 设置价格
        binding.btnPrice.text = context.getString(R.string.first_recharge_price, 2.99)
    }

    private fun startCountdown() {
        timer = Timer()
        timer?.schedule(object : TimerTask() {
            override fun run() {
                if (remainingSeconds <= 0) {
                    timer?.cancel()
                    // 倒计时结束，自动关闭弹框
                    post {
                        dismiss()
                    }
                    return
                }

                val hours = remainingSeconds / 3600
                val minutes = (remainingSeconds % 3600) / 60
                val seconds = remainingSeconds % 60

                val timeString = String.format("%02d:%02d:%02d", hours, minutes, seconds)

                post {
                    binding.tvCountdown.text =
                        context.getString(R.string.first_recharge_countdown, timeString)
                }

                remainingSeconds--
            }
        }, 0, 1000)
    }

    private fun setupClickListeners() {
        // 关闭按钮
        binding.ivClose.click {
            dismiss()
//            onCloseClick()
        }

        // 购买按钮
        binding.btnPrice.click {
            dismiss()

            weakActivity.get()?.let { activity ->
                showDiamondRechargePopup(
                    activity = activity, purchaseScene = PurchaseScene.SpecialFirstRecharge
                )
            }
        }
    }

    override fun onDismiss() {
        super.onDismiss()
        timer?.cancel()
        timer = null
    }
}

/**
 * 显示首充弹框
 */
fun showFirstRechargePopup(
    activity: ComponentActivity, rechargeBean: GoodsBean
) {
    XPopup.Builder(activity).dismissOnBackPressed(false).dismissOnTouchOutside(false).asCustom(
        FirstRechargePopup(
            activity, rechargeBean
        )
    ).show()
}
