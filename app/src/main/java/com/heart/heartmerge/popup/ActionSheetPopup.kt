package com.heart.heartmerge.popup

import android.content.Context
import com.angcyo.dsladapter.DslAdapter
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.ItemSelectorHelper
import com.bdc.android.library.extension.setTextCompatColor
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BottomPopupView
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.PopupActionSheetBinding

class ActionSheetPopup(
    context: Context,
    private val items: List<String>,
    private val checked: Int,
    private val block: (Int) -> Unit
) : BottomPopupView(context) {
    override fun getImplLayoutId(): Int = R.layout.popup_action_sheet

    override fun initPopupContent() {
        super.initPopupContent()
        val binding = PopupActionSheetBinding.bind(popupContentView)

        binding.recyclerView.adapter = DslAdapter(items.map {
            DslAdapterItem().apply {
                itemLayoutId = R.layout.item_popup_action_sheet
                itemData = it
                itemBindOverride = { holder, pos, _, _ ->
                    holder.tv(R.id.tv_option)?.apply {
                        setTextCompatColor(if (itemIsSelected) com.bdc.android.library.R.color.color_333333 else com.bdc.android.library.R.color.color_999999)
                        text = itemData as? String
                    }
                    holder.clickItem {
                        updateItemSelect(true)
                        block(pos)
                        dismiss()
                    }
                }
            }
        }).apply {
            itemSelectorHelper.selectorModel = ItemSelectorHelper.MODEL_SINGLE
            itemSelectorHelper.selector(checked)
        }
    }
}

fun showActionSheetPopup(
    context: Context, items: List<String>, checked: Int = 0, block: (Int) -> Unit
) {
    XPopup.Builder(context).asCustom(ActionSheetPopup(context, items, checked, block)).show()
}