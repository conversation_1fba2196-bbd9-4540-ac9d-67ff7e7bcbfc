package com.heart.heartmerge.popup

import android.content.Context
import com.bdc.android.library.extension.click
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.PopupRemindBinding
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2024/8/30 15:50
 * @description :通用提醒弹框
 */
class RemindPopup(
    context: Context, val icon: Int, val content: String, val block: () -> Unit = {}
) : CenterPopupView(context) {

    override fun getImplLayoutId(): Int = R.layout.popup_remind

    override fun initPopupContent() {
        super.initPopupContent()
        val binding = PopupRemindBinding.bind(popupImplView)

        binding.ivEmoji.setImageResource(icon)

        binding.tvContent.text = content

        binding.btn.click {
            dismiss()
            block.invoke()
        }
    }
}

fun showRemindPopup(
    context: Context,
    icon: Int = R.mipmap.ic_emoji_status_online,
    content: String,
    block: () -> Unit = {}
) {
    XPopup.Builder(context).asCustom(RemindPopup(context, icon, content, block)).show()
}
