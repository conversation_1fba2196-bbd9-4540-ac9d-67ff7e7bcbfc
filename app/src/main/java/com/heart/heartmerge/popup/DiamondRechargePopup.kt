package com.heart.heartmerge.popup

import androidx.activity.ComponentActivity
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.asLiveData
import androidx.lifecycle.lifecycleScope
import com.angcyo.dsladapter._dslAdapter
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.GoodsBean
import com.heart.heartmerge.beans.GoodsQueryType
import com.heart.heartmerge.databinding.PopupDiamondRechargeBinding
import com.heart.heartmerge.extension.applyGooglePrice
import com.heart.heartmerge.extension.applyRechargeCountdown
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.payment.BillingService
import com.heart.heartmerge.ui.adapter.DiamondRechargeAdapter
import com.heart.heartmerge.utils.FlexibleCountdownTimer
import com.heart.heartmerge.utils.PurchaseScene
import com.heart.heartmerge.viewmodes.UserViewModel
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BottomPopupView
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

class DiamondRechargePopup(
    activity: ComponentActivity,
    private val manualDismiss: Boolean = false,
    private val purchaseScene: PurchaseScene? = null,
    val block: () -> Unit
) : BottomPopupView(activity) {
    private val weakActivity = WeakReference(activity)
    private val userViewModel by activity.viewModels<UserViewModel>()
    private lateinit var binding: PopupDiamondRechargeBinding
    private var billingService: BillingService? = null
    private var flexibleCountdownTimer: FlexibleCountdownTimer? = null

    override fun getImplLayoutId(): Int = R.layout.popup_diamond_recharge

    override fun initPopupContent() {
        super.initPopupContent()
        billingService = weakActivity.get()?.let { BillingService(it) }
        binding = PopupDiamondRechargeBinding.bind(popupImplView)

        binding.progressBar.apply {
            visibility = VISIBLE
            isIndeterminate = true
        }

        userViewModel.userBean.asLiveData().observe(this) {
            binding.tvDiamond.text = it.balance.toShowDiamond().toString()
            binding.llMembershipContainer.makeVisible()
            binding.tvNormalUser.makeGone()
        }

        fetchData()
    }

    private fun fetchData() {
        userViewModel.fetchGoodsList(goodsType = if (MMKVDataRep.userInfo.isVIP) GoodsQueryType.DIAMOND else GoodsQueryType.ALL)
            .asLiveData().observe(this) { result ->
                val firstRechargeList = result?.goldGoods?.filter { it.hasCountdown }
                val list = buildList {
                    val current =
                        firstRechargeList?.filter { it.hasCountdown }?.minByOrNull { it.deadline }
                    current?.let { add(it) }
                    result?.goldGoods?.let {
                        addAll(it.filter { !it.hasCountdown })
                    }
                }

                lifecycleScope.launch {
                    billingService?.let {
                        firstRechargeList?.applyGooglePrice(it)
                        list.applyGooglePrice(it)
                    }
                    binding.progressBar.makeGone()

                    list.map {
                        DiamondRechargeAdapter.DiamondRechargeItem(it,result?.level_config?.level ?: 1) {
                            showChoosePaymentPopup(
                                activity as AppCompatActivity, it, purchaseScene
                            ) {
                                if (!manualDismiss) {
                                    dismiss()
                                }
                            }
                        }
                    }.let {
                        binding.recyclerView.adapter = DiamondRechargeAdapter(it)
                    }

                    buildFirstRechargeText()

                    if (firstRechargeList?.isNotEmpty() == true) {
                        flexibleCountdownTimer?.cancel()
                        flexibleCountdownTimer =
                            binding.recyclerView.applyRechargeCountdown(firstRechargeList) {
                                buildFirstRechargeText()
                            }
                    }
                }
            }
    }

    private fun buildFirstRechargeText() {
        val items =
            binding.recyclerView._dslAdapter?.dataItems?.mapNotNull { (it.itemData as? GoodsBean)?.takeIf { bean -> bean.hasCountdown } }

        binding.llFirstRechargeContainer.makeVisible(items?.isNotEmpty() == true)
        items?.takeIf { beans -> beans.isNotEmpty() && beans.size > 0 }?.let { beans ->
            beans.first()
        }?.apply {
            binding.tvFirstRecharge.text = context.getString(
                R.string.first_recharge_text_tips,
                googleExtras?.oneTimePurchaseOfferDetails?.formattedPrice ?: formattedPrice,
                coin.toInt().toShowDiamond()
            )
        }
    }

    override fun onDismiss() {
        super.onDismiss()
        block.invoke()
        flexibleCountdownTimer?.cancel()
        billingService?.onDestroy()
        weakActivity.clear()
    }
}

fun showDiamondRechargePopup(
    activity: ComponentActivity,
    manualDismiss: Boolean = false,
    purchaseScene: PurchaseScene,
    block: () -> Unit = {}
): DiamondRechargePopup {
    val dialog = DiamondRechargePopup(activity, manualDismiss, purchaseScene, block)
    XPopup.Builder(activity).asCustom(dialog).show()
    return dialog
}