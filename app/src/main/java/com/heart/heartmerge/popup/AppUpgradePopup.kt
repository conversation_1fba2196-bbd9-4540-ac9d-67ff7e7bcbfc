package com.heart.heartmerge.popup

import android.content.Context
import android.content.Intent
import androidx.core.net.toUri
import com.bdc.android.library.extension.click
import com.heart.heartmerge.BuildConfig
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.VersionBean
import com.heart.heartmerge.databinding.PopupAppUpgradeBinding
import com.heart.heartmerge.extension.formatDate
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView
import com.lxj.xpopup.util.XPopupUtils

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/12 17:39
 * @description :App升级提示弹框
 */
class AppUpgradePopup(
    context: Context, val versionBean: VersionBean?, val onDismiss: () -> Unit = {}
) : CenterPopupView(context) {

    override fun getImplLayoutId(): Int = R.layout.popup_app_upgrade

    override fun getMaxWidth(): Int {
        return XPopupUtils.getAppWidth(context)
    }

    override fun initPopupContent() {
        super.initPopupContent()
        val binding = PopupAppUpgradeBinding.bind(popupImplView)
        binding.ivClose.click { dismiss() }
        binding.tvUpdateContent.text = versionBean?.helpMessage

        MMKVDataRep.lastAppUpgradePopupShowDate = System.currentTimeMillis().formatDate()

        binding.btnUpdate.click {
            val url =
                "https://play.google.com/store/apps/details?id=${BuildConfig.APPLICATION_ID}".toUri()
            try {
                val intent = Intent(Intent.ACTION_VIEW)
                intent.setData(url)
                intent.setPackage("com.android.vending")
                context.startActivity(intent)
            } catch (e: Exception) {
                e.printStackTrace()
                try {
                    val browserIntent = Intent(Intent.ACTION_VIEW, url)
                    context.startActivity(browserIntent)
                } catch (ex: Exception) {
                    ex.printStackTrace()
                }
            }
            dismiss()
            onDismiss.invoke()
        }

        binding.tvClose.click {
            dismiss()
            onDismiss.invoke()
        }
    }

    override fun onDismiss() {
        super.onDismiss()
        onDismiss.invoke()
    }
}

fun showAppUpgradePopup(activity: Context, versionBean: VersionBean?, onDismiss: () -> Unit = {}) {
    XPopup.Builder(activity).asCustom(AppUpgradePopup(activity, versionBean, onDismiss)).show()
}