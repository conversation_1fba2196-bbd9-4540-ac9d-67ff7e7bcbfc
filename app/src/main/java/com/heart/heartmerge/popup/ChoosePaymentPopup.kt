package com.heart.heartmerge.popup

import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.text.isDigitsOnly
import androidx.lifecycle.asLiveData
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.className
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.dp2px
import com.bdc.android.library.extension.jump
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.putString
import com.bdc.android.library.extension.setDrawableLeft
import com.bdc.android.library.imageloader.ImageLoader
import com.bdc.android.library.utils.ToastUtil
import com.bumptech.glide.load.resource.bitmap.CenterInside
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.GoodsBean
import com.heart.heartmerge.beans.GoodsQueryType
import com.heart.heartmerge.beans.SubscriptionType
import com.heart.heartmerge.databinding.PopupChoosePaymentBinding
import com.heart.heartmerge.extension.toSubscriptionTitle
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.payment.BillingService
import com.heart.heartmerge.socket.WebSocketMessageSender
import com.heart.heartmerge.ui.activities.mine.MembershipCenterActivity
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.PurchaseScene
import com.heart.heartmerge.viewmodes.UserViewModel
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BottomPopupView
import java.lang.ref.WeakReference

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/8/29 10:36
 * @description :选择支付方式
 */
class ChoosePaymentPopup(
    activity: AppCompatActivity,
    val rechargeBean: GoodsBean?,
    val purchaseScene: PurchaseScene?,
    val block: (Boolean) -> Unit
) : BottomPopupView(activity) {

    private val weakActivity = WeakReference(activity)
    private lateinit var binding: PopupChoosePaymentBinding
    private var billingService: BillingService? = null
    private val userViewModel by activity.viewModels<UserViewModel>()

    override fun getImplLayoutId(): Int = R.layout.popup_choose_payment

    override fun initPopupContent() {
        super.initPopupContent()
        AppUtil.canAutoPopupVideoCallingPage = false
        binding = PopupChoosePaymentBinding.bind(popupImplView)
        MMKVDataRep.userInfo.apply {
            binding.tvDiamond.text = rechargeBean?.coin?.toInt()?.toShowDiamond().toString()
            binding.tvCountry.text = userCountry?.title
            AppUtil.setLocalCountryImage(userCountry?.code, binding.ivFlag, radio = 10F)
        }
        binding.ivClose.click { dismiss() }
        binding.llChangeCountry.click {
            weakActivity.get()?.let {
                showCountryPopup(it) {
                    binding.tvCountry.text = it.title
                    AppUtil.setLocalCountryImage(it.code, binding.ivFlag, radio = 8F)
                    fetchData(
                        rechargeBean?.sku, it.id.toString()
                    )
                }
            }
        }

        fetchData(rechargeBean?.sku, MMKVDataRep.userInfo.userCountry?.id.toString())
    }

    private fun fetchData(sku: String?, countryId: String) {
        binding.progressBar.apply {
            visibility = VISIBLE
            isIndeterminate = true
        }
        binding.recyclerView.clearItems()
        userViewModel.fetchGoodsList(
            goodsType = if (SubscriptionType.entries.any { it.value == sku }) GoodsQueryType.SUBSCRIBE else GoodsQueryType.DIAMOND,
            countryId = countryId
        ).asLiveData().observe(this) {
            binding.progressBar.makeGone()
            rechargeBean?.takeIf { bean -> bean.isSubscribe }?.let { bean ->
                binding.tvDiamond.setDrawableLeft(0)
                binding.tvDiamond.text = bean.product_id.toSubscriptionTitle()
            }

            binding.recyclerView.append<DslAdapterItem>(it?.goldGoods?.find {
                it.sku == (sku ?: "")
            }?.channels) {
                itemLayoutId = R.layout.item_choose_payment
                itemBindOverride = { itemHolder, _, _, _ ->
                    val item = itemData as? GoodsBean.ChannelBean
                    itemHolder.img(R.id.iv_platform)?.apply {
                        if (item?.icon?.isNotEmpty() == true) {
                            ImageLoader.with(this).load(item.icon).scaleType(CenterInside())
                                .radius(4F.dp2px()).into(this)
                        }
                    }
                    itemHolder.tv(R.id.tv_platform)?.text = item?.name
                    itemHolder.clickItem {
                        LogX.d("选择支付方式 ${item?.name}")
                        handlePayment(rechargeBean?.apply {
                            country_id =
                                if (countryId.isNotEmpty() && countryId.isDigitsOnly()) countryId.toInt() else MMKVDataRep.userInfo.country_code
                            targetChannel = item
                        })
                    }
                    itemHolder.tv(R.id.btn_pay)?.text =
                        if (item?.payType == 1) rechargeBean?.googleExtras?.let {
                            it.subscriptionOfferDetails?.firstOrNull()?.pricingPhases?.pricingPhaseList?.firstOrNull()?.formattedPrice
                                ?: it.oneTimePurchaseOfferDetails?.formattedPrice
                        } ?: run {
                            "${item?.formattedPrice}"
                        } else {
                            "${item?.formattedPrice}"
                        }
                }
            }
        }
    }

    private fun handlePayment(item: GoodsBean?) {
        if (item == null) return
        val activity = weakActivity.get() ?: return
        billingService = BillingService(activity)
        billingService?.launch(item, purchaseScene = purchaseScene) {
            if (it && weakActivity.get() != null) {
                dismiss()
                block.invoke(it)
            }
        }
    }

    override fun onDismiss() {
        super.onDismiss()

        if (!AppUtil.purchaseWhiteList.contains(weakActivity.get()?.className())) {
            AppUtil.canAutoPopupVideoCallingPage = true
        }

        weakActivity.clear()
        billingService?.onDestroy()
    }
}

fun showChoosePaymentPopup(
    context: AppCompatActivity,
    item: GoodsBean?,
    purchaseScene: PurchaseScene?,
    block: (Boolean) -> Unit = {}
) {
    //通知服务端用户拉起来了支付
    WebSocketMessageSender.sendUserPayingMessage()
    //广告用户
    if ((item?.channels?.size ?: 0) > 1) {
        if (item?.isSubscribe == true && MMKVDataRep.userInfo.isVIP) {
            ToastUtil.show(context.getString(R.string.membership_already_subscribed))
            return
        }
        XPopup.Builder(context).dismissOnBackPressed(true).dismissOnTouchOutside(true)
            .asCustom(ChoosePaymentPopup(context, item, purchaseScene, block)).show()
    }

    //自然量
    else {
        item?.let {
            if (it.isSubscribe && context !is MembershipCenterActivity) {
                if (MMKVDataRep.userInfo.isVIP) {
                    ToastUtil.show(context.getString(R.string.membership_already_subscribed))
                    return
                }
                context.jump(MembershipCenterActivity::class.java, Bundle().apply {
                    putString(it.product_id)
                    putParcelable(MembershipCenterActivity.SCENE, purchaseScene)
                })

            } else {
                BillingService(context).launch(
                    it.apply { targetChannel = it.channels?.firstOrNull { it.isGooglePay } },
                    purchaseScene
                )
            }
        }
    }
}