package com.heart.heartmerge.popup

import android.app.Activity
import com.bdc.android.library.extension.click
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.PopupPayVideoTipBinding
import com.heart.heartmerge.extension.loadAvatar
import com.heart.heartmerge.logger.LogX
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView

/**
 * 作者：Lxf
 * 创建日期：2024/11/9 17:56
 * 描述：
 */
open class PayVideoTipPopup(
    private val context: Activity,
    val image: String,
    val diamond: Int,
    val block: () -> Unit = {},
    private val cancelBlock: () -> Unit = {}
) : CenterPopupView(context) {

    lateinit var binding: PopupPayVideoTipBinding
    override fun getImplLayoutId(): Int = R.layout.popup_pay_video_tip

    override fun initPopupContent() {
        super.initPopupContent()
        binding = PopupPayVideoTipBinding.bind(popupImplView)
        LogX.e("vvvvvvvvvvvvvvvvv  $image")
        binding.ivPhoto.loadAvatar(image)
        binding.tvContent.text = String.format(context.getString(R.string.tip_pay_video), diamond)
        binding.btnSure.text = String.format(context.getString(R.string.btn_pay_video), diamond)
        binding.btnSure.click {
            block.invoke()
        }
        binding.btnCancel.click {
            dismissWith {
                cancelBlock.invoke()
            }
        }
    }

    fun setContent(content: String) {
        binding.tvContent.text = content
    }
}

fun showPayVideoTipPopup(
    context: Activity,
    image: String,
    diamond: Int,
    block: () -> Unit = {},
    cancelBlock: () -> Unit = {}
): PayVideoTipPopup {
    val normalNewPopup = PayVideoTipPopup(
        context,
        image,
        diamond,
        block,
        cancelBlock
    )
    XPopup.Builder(context)
        .dismissOnBackPressed(false)
        .dismissOnTouchOutside(false).asCustom(normalNewPopup).show()
    return normalNewPopup
}
