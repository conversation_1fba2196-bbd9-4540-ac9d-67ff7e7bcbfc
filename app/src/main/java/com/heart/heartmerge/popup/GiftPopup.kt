package com.heart.heartmerge.popup

import android.annotation.SuppressLint
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import androidx.lifecycle.setViewTreeLifecycleOwner
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.savedstate.setViewTreeSavedStateRegistryOwner
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import coil.load
import com.angcyo.dsladapter.visible
import com.bdc.android.library.extension.makeInVisible
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.http.request
import com.bdc.android.library.utils.Logger
import com.google.android.material.tabs.TabLayout
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.GiftBean
import com.heart.heartmerge.beans.GiftItemBean
import com.heart.heartmerge.databinding.PopupGiftBinding
import com.heart.heartmerge.extension.dp
import com.heart.heartmerge.http.RetrofitClient
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.ui.theme.HeartMergeTheme
import com.heart.heartmerge.ui.theme.white
import com.heart.heartmerge.ui.widget.DiamondComposeView
import com.heart.heartmerge.utils.PurchaseScene
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.core.BottomPopupView
import kotlin.math.ceil

@SuppressLint("ViewConstructor")
class GiftPopup(
    val activity: ComponentActivity,
    val anchorId: String? = null,
    private val block: (GiftItemBean) -> Unit = {}
) : BottomPopupView(activity) {

    private var items = mutableListOf<GiftItemBean>()
    private val columnCount = 4
    private val pageSize = 8

    override fun init() {
        super.init()
        //设置让dialog支持compose 不设置会FC
        setViewTreeLifecycleOwner(context as AppCompatActivity)
        setViewTreeSavedStateRegistryOwner(context as AppCompatActivity)
    }

    override fun getImplLayoutId(): Int = R.layout.popup_gift

    @SuppressLint("NotifyDataSetChanged", "SetTextI18n")
    override fun initPopupContent() {
        super.initPopupContent()

        val binding = PopupGiftBinding.bind(popupContentView)

        binding.tvDiamonds.setContent {
            HeartMergeTheme {
                DiamondComposeView(
                    color = white, fontSize = 20.sp,
//                    fontFamily = FontFamily(Font(R.font.baloochettan2))
                )
            }
        }

        binding.tabLayout.apply {
            addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    tab?.customView?.apply {
                        findViewById<TextView>(R.id.tv_name)?.apply {
                            setTextColor(
                                ContextCompat.getColor(
                                    this.context,
                                    com.bdc.android.library.R.color.white
                                )
                            )
                        }
                        findViewById<View>(R.id.indicator)?.makeVisible()
                    }
                }

                override fun onTabUnselected(tab: TabLayout.Tab?) {
                    tab?.customView?.apply {
                        findViewById<TextView>(R.id.tv_name)?.apply {
                            isSelected = false
                            setTextColor(Color.parseColor("#81839A"))
                        }
                        findViewById<View>(R.id.indicator)?.makeInVisible()
                    }
                }

                override fun onTabReselected(tab: TabLayout.Tab?) {
                }
            })

            val titles =
                listOf(activity.getString(R.string.gift), activity.getString(R.string.gift_free))
            titles.forEachIndexed { index, s ->
                addTab(binding.tabLayout.newTab().apply {
                    setCustomView(R.layout.item_gift_category).apply {
                        this.customView?.findViewById<TextView>(R.id.tv_name)?.text = s
                    }
                }, index == 0)
            }
        }

        fetchGift(binding)
        binding.viewPager.registerOnPageChangeCallback(object : OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                binding.tabIndicator.getTabAt(position)?.select()
            }
        })

        binding.tabIndicator.apply {
            addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    tab?.customView?.apply {
                        background = ContextCompat.getDrawable(
                            this.context, R.drawable.shape_indicator_white
                        )
                    }
                    binding.viewPager.setCurrentItem(tab?.position ?: 0, true)
                }

                override fun onTabUnselected(tab: TabLayout.Tab?) {
                    tab?.customView?.apply {
                        background = ContextCompat.getDrawable(
                            this.context, R.drawable.shape_indicator_gray
                        )
                    }
                }

                override fun onTabReselected(tab: TabLayout.Tab?) {
                }
            })
        }

        binding.btnRecharge.setOnClickListener {
            showDiamondRechargePopup(
                activity,
                purchaseScene = PurchaseScene.Gift(anchorId = anchorId ?: "")
            ) {}
        }
    }

    private fun fetchGift(binding: PopupGiftBinding) {
        request {
            start {
                binding.progressBar.apply {
                    visibility = VISIBLE
                    isIndeterminate = true
                }
            }
            call { RetrofitClient.aggregatedService.getGiftList() }
            onSuccess {
                it?.list?.let { response ->
                    items.addAll(response)
                    binding.viewPager.adapter = GiftPagerAdapter(items.toList())
                    initialIndicator(items, binding)
                }
            }
            onFailure {
            }
            onCompleted { binding.progressBar.visible(false) }
        }
    }

    private fun initialIndicator(items: List<GiftItemBean>, binding: PopupGiftBinding) {
        val page = ceil(items.size / pageSize.toDouble()).toInt()
        binding.tabIndicator.removeAllTabs()
        for (i in 0 until page) {
            binding.tabIndicator.apply {
                addTab(binding.tabIndicator.newTab().apply {
                    customView = View(context).apply {
                        layoutParams = ViewGroup.LayoutParams(5.dp, 5.dp)
                        background = ContextCompat.getDrawable(
                            this.context, R.drawable.shape_indicator_gray
                        )
                    }
                }, i == 0)
            }
        }
    }

    inner class GiftPagerAdapter(private val items: List<GiftItemBean>) :
        RecyclerView.Adapter<GiftPagerAdapter.ViewHolder>() {

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

        override fun onCreateViewHolder(
            parent: ViewGroup, viewType: Int
        ): GiftPagerAdapter.ViewHolder {
            val view =
                LayoutInflater.from(parent.context).inflate(R.layout.item_gift_pager, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            holder.itemView.findViewById<RecyclerView>(R.id.recyclerView)?.apply {
                layoutManager = GridLayoutManager(this.context, columnCount)
                val fromIndex = position * pageSize
                val toIndex = fromIndex + pageSize
                adapter = GiftAdapter(
                    items.subList(
                        fromIndex, if (items.size > toIndex) toIndex else items.size
                    )
                ) {
                    if (MMKVDataRep.userInfo.balance < it.coin) {
                        showDiamondRechargePopup(
                            activity,
                            purchaseScene = PurchaseScene.Gift(anchorId = anchorId ?: "")
                        )
                        return@GiftAdapter
                    }
                    notifyDataSetChanged()
                    block.invoke(it)
                    dismiss()
                }
            }
        }

        override fun getItemCount(): Int {
            return ceil(items.size / pageSize.toDouble()).toInt()
        }
    }

    inner class GiftAdapter(
        private val items: List<GiftItemBean>, private val onClick: (GiftItemBean) -> Unit
    ) : RecyclerView.Adapter<GiftAdapter.ViewHolder>() {
        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_gift_pager_child, parent, false)
            return ViewHolder(view)
        }

        override fun getItemCount(): Int {
            return items.size
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val item = items[position]
            holder.itemView.findViewById<ImageView>(R.id.iv_gift)?.load(item.icon)
            holder.itemView.findViewById<TextView>(R.id.tv_gift_name).text = item.showName
            holder.itemView.findViewById<TextView>(R.id.tv_gift_value).text =
                item.coin.toShowDiamond().toString()

            holder.itemView.apply {
                background = if (item.checked) {
                    ContextCompat.getDrawable(
                        this.context, R.drawable.shape_gift_checked
                    )
                } else {
                    null
                }
                setOnClickListener {
                    <EMAIL> { it.checked }.forEach { it.checked = false }
                    item.checked = true
//                    GiftManager.getInstance(activity)
//                        .playGiftAnimation(activity = activity, item.id)
                    onClick.invoke(item)
                }
            }
        }
    }
}

fun showGiftPopup(
    activity: ComponentActivity,
    anchorId: String? = null,
    block: (GiftItemBean) -> Unit = {}
): BasePopupView? {
    return XPopup.Builder(activity).asCustom(GiftPopup(activity, anchorId = anchorId, block)).show()
}