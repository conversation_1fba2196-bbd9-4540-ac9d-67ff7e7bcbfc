package com.heart.heartmerge.popup

import android.content.Context
import com.bdc.android.library.extension.makeVisible
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.PopupConfirmBinding
import com.heart.heartmerge.databinding.PopupMyCustomTipBinding

class MyCustomTipPopup(
    activity: Context,
    val content: String,
    private val singleButton: Boolean,
    private val leftBtnStr: String?,
    private val rightBtnStr: String?,
    val cancelBlock: () -> Unit = {},
    val confirmBlock: () -> Unit = {}
) : CenterPopupView(activity) {
    private lateinit var binding: PopupMyCustomTipBinding
    override fun getImplLayoutId(): Int = R.layout.popup_my_custom_tip

    override fun initPopupContent() {
        super.initPopupContent()
        binding = PopupMyCustomTipBinding.bind(popupImplView)

        leftBtnStr?.let {
            binding.dialogLeftBtn.text = it
        }
        rightBtnStr?.let {
            binding.dialogRightBtn.text = it
        }
        content.takeIf { it.isNotEmpty() }.let {
            binding.tvContent.apply {
                makeVisible()
                text = it
            }
        }
        binding.dialogLeftBtn.makeVisible(singleButton.not())
        binding.dialogLeftBtn.setOnClickListener {
            cancelBlock()
            dismiss()
        }

        binding.dialogRightBtn.setOnClickListener {
            confirmBlock()
            dismiss()
        }
    }

    fun setContent(content: String){
        binding.tvContent.text = content
    }
}

fun showCustomTipPopup(
    activity: Context,
    content: String = "",
    singleButton: Boolean = false,
    autoDismiss: Boolean = true,
    dismissOnBackPressed: Boolean = false,
    dismissOnTouchOutside: Boolean = false,
    leftBtnStr: String? = null,
    rightBtnStr: String? = null,
    cancelBlock: () -> Unit = {},
    confirmBlock: () -> Unit = { }
): MyCustomTipPopup {
    val popup = MyCustomTipPopup(activity, content, singleButton, leftBtnStr, rightBtnStr, cancelBlock, confirmBlock)
    XPopup.Builder(activity).autoDismiss(autoDismiss).dismissOnBackPressed(dismissOnBackPressed)
        .dismissOnTouchOutside(dismissOnTouchOutside)
        .asCustom(popup)
        .show()
    return popup
}