package com.heart.heartmerge.popup

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.asLiveData
import androidx.lifecycle.lifecycleScope
import com.android.billingclient.api.BillingClient
import com.angcyo.dsladapter.ItemSelectorHelper
import com.angcyo.dsladapter._dslAdapter
import com.bdc.android.library.extension.jump
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.GoodsBean
import com.heart.heartmerge.beans.GoodsQueryType
import com.heart.heartmerge.databinding.PopupMembershipSubscribeBinding
import com.heart.heartmerge.extension.applyMembershipRights
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.payment.BillingService
import com.heart.heartmerge.ui.activities.mine.MembershipCenterActivity
import com.heart.heartmerge.ui.adapter.MembershipSubscribeAdapter
import com.heart.heartmerge.utils.PurchaseScene
import com.heart.heartmerge.viewmodes.UserViewModel
import com.lxj.xpopup.core.BottomPopupView
import kotlinx.coroutines.launch

class MembershipSubscribePopup(val activity: ComponentActivity, val purchaseScene: PurchaseScene) :
    BottomPopupView(activity) {

    private val userViewModel: UserViewModel by activity.viewModels()
    private lateinit var binding: PopupMembershipSubscribeBinding
    private var mSelectMembership: GoodsBean? = null
    private lateinit var billingService: BillingService

    override fun getImplLayoutId(): Int = R.layout.popup_membership_subscribe

    override fun initPopupContent() {
        super.initPopupContent()
        billingService = BillingService(activity)
        binding = PopupMembershipSubscribeBinding.bind(popupImplView)
        binding.recyclerView.applySingleSelectorModel(ItemSelectorHelper.MODEL_SINGLE)

        binding.progressBarPrimary.apply {
            visibility = VISIBLE
            isIndeterminate = true
        }
        binding.progressBar.apply {
            visibility = VISIBLE
            isIndeterminate = true
        }

        userViewModel.fetchGoodsList(goodsType = GoodsQueryType.SUBSCRIBE).asLiveData().observe(this) {
            binding.progressBarPrimary.makeGone()
            activity.lifecycleScope.launch {
                it?.diamondPrices?.apply {
                    val list = billingService.fetchProductList(
                        this.map { it.sku }, BillingClient.ProductType.SUBS
                    )
                    val newSubscribeList = this.filterIndexed { index, item ->
                        item.googleExtras = list?.find { it.productId == item.sku }
                        true
                    }

                    newSubscribeList.map {
                        MembershipSubscribeAdapter.MembershipSubscribeItem(it, purchaseScene) {
                            fetchBenefit(it.sku)
                        }
                    }.let {
                        binding.recyclerView.adapter =
                            MembershipSubscribeAdapter(it).apply {
                                itemSelectorHelper.selector(0)
                            }
                    }
                    if (it.diamondPrices?.isNotEmpty() == true) {
                        mSelectMembership = it.diamondPrices[0]
                        binding.recyclerView.dslAdapter.itemSelectorHelper.selector(0)
                        fetchBenefit(it.diamondPrices[0].id)
                    }
                }
            }
        }

        binding.btnConfirm.setOnClickListener {
            dismiss()
//            onClick.invoke(items.filter { it.checked }.map { it.name })
            mSelectMembership =
                binding.recyclerView._dslAdapter?.dataItems?.find { it.itemIsSelected }?.itemData as? GoodsBean
            if (mSelectMembership == null) {
                ToastUtil.show(context.getString(R.string.label_select_vip))
            } else {
//                MMKVDataRep.userInfo = MMKVDataRep.userInfo.apply {
//                    vipFlag = "1"
//                    vipExpireFlag = "1"
//                }
//                activity.lifecycleScope.launch {
//                    FlowBus.with<Boolean>(Constants.SUBSCRIBE_SUCCESS).post(true)
//                }

                if (MMKVDataRep.userInfo.isGoogleUser) {
                    billingService.launch(
                        mSelectMembership!!, purchaseScene = purchaseScene
                    )
                } else {
                    showChoosePaymentPopup(
                        activity as AppCompatActivity,
                        mSelectMembership,
                        purchaseScene = purchaseScene
                    ) {
                        dismiss()
                    }
                }
            }
        }
    }

    private fun fetchBenefit(priceId: String) {
        userViewModel.getSubscribeBenefit(priceId).asLiveData().observe(this) {
            binding.progressBar.makeGone()
            binding.recyclerViewRights.applyMembershipRights(
                it.vipCallSpecialOfferBonus, it.checkInBonus, it.matchSpecialOfferBonus
            )
        }
    }
}

fun showMembershipSubscribePopup(activity: ComponentActivity, purchaseScene: PurchaseScene) {
//    XPopup.Builder(activity).asCustom(MembershipSubscribePopup(activity)).show()
    activity.jump(MembershipCenterActivity::class.java, Bundle().apply {
        putParcelable(MembershipCenterActivity.SCENE, purchaseScene)
    })
}