package com.heart.heartmerge.popup

import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.core.content.ContextCompat
import com.bdc.android.library.extension.click
import com.bdc.android.library.utils.Logger
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BottomPopupView
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.PopupSetupOnlineStatusBinding

enum class OnlineStatus {
    INVISIBILITY, ONLINE, BUSY
}

class SetupOnlineStatusPopup(
    activity: ComponentActivity,
    private val status: OnlineStatus,
    private val block: (OnlineStatus) -> Unit
) : BottomPopupView(activity) {

    override fun getImplLayoutId(): Int = R.layout.popup_setup_online_status

    override fun initPopupContent() {
        super.initPopupContent()
        var currentStatus = status
        val binding = PopupSetupOnlineStatusBinding.bind(popupContentView)

        when (status) {
            OnlineStatus.ONLINE -> changeCheckedStatus(
                binding.tvOnline, binding.tvInvisibility, binding.tvBusy, status = currentStatus
            )
            OnlineStatus.INVISIBILITY -> changeCheckedStatus(
                binding.tvInvisibility, binding.tvOnline, binding.tvBusy, status = currentStatus
            )
            OnlineStatus.BUSY -> changeCheckedStatus(
                binding.tvBusy, binding.tvOnline, binding.tvInvisibility, status = currentStatus
            )
        }

        binding.tvCancel.click { dismiss() }
        binding.tvInvisibility.click {
            currentStatus = OnlineStatus.INVISIBILITY
            changeCheckedStatus(
                binding.tvInvisibility, binding.tvOnline, binding.tvBusy, status = currentStatus
            )
        }
        binding.ivTips.click {
            showRemindPopup(
                context,
                R.mipmap.ic_emoji_status_offline,
                context.getString(R.string.invisibility_explanation)
            )
        }
        binding.tvOnline.click {
            currentStatus = OnlineStatus.ONLINE
            changeCheckedStatus(
                binding.tvOnline, binding.tvInvisibility, binding.tvBusy, status = currentStatus
            )
        }
        binding.tvBusy.click {
            currentStatus = OnlineStatus.BUSY
            changeCheckedStatus(
                binding.tvBusy, binding.tvOnline, binding.tvInvisibility, status = currentStatus
            )
        }
        binding.tvConfirm.click {
            dismiss()
            block.invoke(currentStatus)
        }
    }

    private fun changeCheckedStatus(
        target: TextView, vararg others: TextView, status: OnlineStatus
    ) {
        target.setTextColor(
            ContextCompat.getColor(
                activity, when (status) {
                    OnlineStatus.ONLINE -> R.color.color_21C76E
                    OnlineStatus.INVISIBILITY -> com.bdc.android.library.R.color.color_333333
                    OnlineStatus.BUSY -> R.color.color_FE4918
                }
            )
        )
        others.forEach {
            it.setTextColor(
                ContextCompat.getColor(
                    activity, R.color.color_94969C
                )
            )
        }
    }
}

fun showSetupOnlineStatusPopup(
    activity: ComponentActivity, status: OnlineStatus, block: (OnlineStatus) -> Unit
) {
    XPopup.Builder(activity).asCustom(SetupOnlineStatusPopup(activity, status, block)).show()
}