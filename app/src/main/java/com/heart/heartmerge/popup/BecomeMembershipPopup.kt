package com.heart.heartmerge.popup

import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Shader
import android.text.TextUtils
import androidx.activity.ComponentActivity
import androidx.appcompat.app.AppCompatActivity
import com.bdc.android.library.extension.click
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.PopupBecomeMembershipBinding
import com.heart.heartmerge.extension.formatDate
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView


/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/9/24 15:12
 * @description :开通VIP成功
 */
class BecomeMembershipPopup(
    private val activity: ComponentActivity, private val block: () -> Unit
) : CenterPopupView(activity) {

    override fun getImplLayoutId(): Int = R.layout.popup_become_membership

    override fun initPopupContent() {
        super.initPopupContent()
        val binding = PopupBecomeMembershipBinding.bind(popupImplView)

        binding.tvTitle.post {
            binding.tvTitle.setLayerType(LAYER_TYPE_SOFTWARE, null)
            val colors = intArrayOf(
                Color.parseColor("#7D3DFF"),
                Color.parseColor("#FD8CB7"),
                Color.parseColor("#FD7CFF")
            ) // 颜色数组
            val positions = floatArrayOf(0f, 0.5f, 1.0f) // 颜色渐变位置数组
            val textShader: Shader = LinearGradient(
                0F,
                0F,
                binding.tvTitle.width.toFloat(),
                0F,
                colors,
                positions,
                Shader.TileMode.CLAMP
            )
            binding.tvTitle.paint.setShader(textShader)
            binding.tvTitle.invalidate()
        }

        binding.btnViewer.click {
            val nowString = System.currentTimeMillis().formatDate()
            if (!TextUtils.equals(
                    nowString, MMKVDataRep.lastSignDate
                )
            ) {
                showUserSignPopup(activity = activity as AppCompatActivity) {
                    block.invoke()
                }
            }
            dismiss()
        }
    }
}

fun showBecomeMembershipPopup(activity: ComponentActivity, block: () -> Unit) {
    XPopup.Builder(activity).asCustom(BecomeMembershipPopup(activity, block)).show()
}