package com.heart.heartmerge.popup

import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.text.isDigitsOnly
import androidx.lifecycle.asLiveData
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter._dslAdapter
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.setTextCompatColor
import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.ReportBean
import com.heart.heartmerge.databinding.PopupReportBinding
import com.heart.heartmerge.viewmodes.UserViewModel
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BottomPopupView

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: He<PERSON><PERSON>
 * @date: 2024/12/16 11:52
 * @description :举报弹框
 */
class ReportPopup(activity: AppCompatActivity, val anchorId: String) : BottomPopupView(activity) {

    private val viewModel by activity.viewModels<UserViewModel>()

    override fun getImplLayoutId(): Int = R.layout.popup_report

    override fun initPopupContent() {
        super.initPopupContent()

        val binding = PopupReportBinding.bind(popupImplView)
        binding.progressBar.apply {
            visibility = VISIBLE
            isIndeterminate = true
        }

        viewModel.getReportList().asLiveData().observe(this) { result ->
            result?.let {
                binding.recyclerView.apply {
                    applySingleSelectorModel(0)
                }.append<DslAdapterItem>(result) {
                    itemLayoutId = R.layout.item_popup_report
                    itemBindOverride = { itemHolder, _, _, _ ->
                        val item = itemData as ReportBean
                        itemHolder.v<View>(R.id.container)
                            ?.setBackgroundResource(if (itemIsSelected) R.drawable.shape_subscribe_item_active_border else R.drawable.shape_subscribe_item_normal_border)
                        itemHolder.tv(R.id.tv_name)?.apply {
                            setTextCompatColor(if (itemIsSelected) R.color.color_white else R.color.color_6F7490)
                            text = item.content
                        }
                        itemHolder.cb(R.id.checkbox)?.apply {
                            isChecked = itemIsSelected
                            setOnCheckedChangeListener { buttonView, isChecked ->
                                updateItemSelect(isChecked)
                            }
                        }

                        itemHolder.clickItem { view -> updateItemSelect(true) }
                    }
                    binding.progressBar.makeGone()
                }
            }
        }

        binding.btnReport.click {
            val reason =
                binding.recyclerView._dslAdapter?.dataItems?.find { item -> item.itemIsSelected }?.itemData as? ReportBean
            if (reason == null) {
                ToastUtil.show(context.getString(R.string.please_report_reason))
                return@click
            }

            if (anchorId.isNotEmpty() && anchorId.isDigitsOnly()) {
                viewModel.anchorReport(anchorId.toInt(), reason.content, reason.lang_key)
                ToastUtil.show(context.getString(R.string.tip_report_suc))
                dismiss()
            }
        }
    }
}

fun showReportPopup(activity: AppCompatActivity, anchorId: String) {
    XPopup.Builder(activity).asCustom(ReportPopup(activity, anchorId)).show()
}