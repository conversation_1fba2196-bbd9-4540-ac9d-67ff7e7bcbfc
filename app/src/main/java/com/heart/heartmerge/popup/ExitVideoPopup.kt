package com.heart.heartmerge.popup

import android.content.Context
import android.widget.TextView
import com.lxj.xpopup.core.CenterPopupView
import com.heart.heartmerge.R

/**
 * Author:Lxf
 * Create on:2024/8/2
 * Description:
 */
class ExitVideoPopup constructor(context: Context) : CenterPopupView(context) {
    private lateinit var dialogLeftTv: TextView
    private lateinit var dialogRightTv: TextView
    private var oneBtnClick: (() -> Unit)? = null
    private var leftBtnClick: (() -> Unit)? = null
    private var rightBtnClick: (() -> Unit)? = null

    override fun getImplLayoutId(): Int {
        return R.layout.exit_video_popup_layout
    }

    override fun onCreate() {
        super.onCreate()
        dialogLeftTv = findViewById(R.id.dialog_left_btn)
        dialogRightTv = findViewById(R.id.dialog_right_btn)
        dialogLeftTv.setOnClickListener {
            leftBtnClick?.invoke()
        }
        dialogRightTv.setOnClickListener {
            rightBtnClick?.invoke()
        }
    }

    fun setupLeftBtnClick(onClick: () -> Unit): ExitVideoPopup {
        leftBtnClick = onClick
        return this
    }

    fun setupRightBtnClick(onClick: () -> Unit): ExitVideoPopup {
        rightBtnClick = onClick
        return this
    }
}