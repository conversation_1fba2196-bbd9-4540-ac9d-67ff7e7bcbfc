package com.heart.heartmerge.popup

import androidx.activity.ComponentActivity
import com.bdc.android.library.extension.click
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.PopupLuckRewardBinding
import com.heart.heartmerge.utils.PurchaseScene
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView
import java.lang.ref.WeakReference

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/5/28 20:30
 * @description :
 */
class LuckRewardPopup(val activity: ComponentActivity) : CenterPopupView(activity) {

    private val weakActivity = WeakReference(activity)
    private lateinit var binding: PopupLuckRewardBinding

    override fun getImplLayoutId(): Int = R.layout.popup_luck_reward

    override fun initPopupContent() {
        super.initPopupContent()
        binding = PopupLuckRewardBinding.bind(popupImplView)
        initViews()
    }

    private fun initViews() {
        binding.tvDiamond.text = context.getString(R.string.first_recharge_diamonds, 100)
        binding.tvSubDiamond.text = context.getString(R.string.first_recharge_diamonds, 100)
        binding.tvAmount.apply {
            text = "$2.99"
            click {
                weakActivity.get()?.let {
                    showDiamondRechargePopup(
                        activity = it, purchaseScene = PurchaseScene.LuckReward
                    ).show()
                }
            }
        }
        binding.ivClose.click {
            dismiss()
        }
    }
}

fun showLuckRewardPopup(activity: ComponentActivity,) {
    XPopup.Builder(activity).dismissOnBackPressed(false).dismissOnTouchOutside(false).asCustom(
        LuckRewardPopup(
            activity,
        )
    ).show()
}