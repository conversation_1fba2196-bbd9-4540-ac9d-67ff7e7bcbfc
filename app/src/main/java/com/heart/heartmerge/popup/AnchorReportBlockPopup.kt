package com.heart.heartmerge.popup

import androidx.appcompat.app.AppCompatActivity
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.makeVisible
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.databinding.PopupAnchorBlockReportBinding
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BottomPopupView


class AnchorReportBlockPopup(
    private val activity: AppCompatActivity,
    private val anchorInfo: UserBean?,
    private val showClear: Boolean?,
    private val clearMsgCallback: () -> Unit
) : BottomPopupView(activity) {
    override fun getImplLayoutId(): Int = R.layout.popup_anchor_block_report

    override fun initPopupContent() {
        super.initPopupContent()
        val binding = PopupAnchorBlockReportBinding.bind(popupContentView)
        binding.tvClearMsg.makeVisible((showClear ?: false))
        binding.tvClearMsg.click {
            dismissWith {
                clearMsgCallback.invoke()
            }
        }
        binding.tvBlock.setOnClickListener {
            dismissWith {
                showBlackPopup(activity, anchorInfo)
            }
        }
        binding.tvComplaint.setOnClickListener {
//            jump(AnchorReportActivity::class.java, Bundle().apply {
//                putString(Constants.INTENT_PARAM_KEY_ANCHOR_INFO, anchorInfo?.anchorId)
//            })
            showReportPopup(activity, anchorInfo?.id ?: "")
            dismiss()
        }

        binding.tvCancel.setOnClickListener { dismiss() }
    }
}

fun showReportBlockPopup(
    activity: AppCompatActivity,
    anchorInfo: UserBean?,
    showClear: Boolean? = false,
    clearMsgCallback: () -> Unit = {}
) {
    XPopup.Builder(activity)
        .asCustom(AnchorReportBlockPopup(activity, anchorInfo, showClear, clearMsgCallback)).show()
}