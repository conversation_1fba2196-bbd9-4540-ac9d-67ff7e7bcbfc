package com.heart.heartmerge.popup

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bdc.android.library.extension.setTextCompatColor
import com.bdc.android.library.mvi.observeState
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BottomPopupView
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.LanguageBean
import com.heart.heartmerge.databinding.PopupChoooseLanguageBinding
import com.heart.heartmerge.viewmodes.HomePageState
import com.heart.heartmerge.viewmodes.HomeViewModel
import com.heart.heartmerge.viewmodes.RequestStatus

class ChooseLanguagePopup(
    activity: ComponentActivity,
    private val checkList: List<LanguageBean> = emptyList(),
    private val onClick: (List<LanguageBean>) -> Unit
) : BottomPopupView(activity) {

    private val homeViewModel by activity.viewModels<HomeViewModel>()

    override fun getImplLayoutId(): Int = R.layout.popup_chooose_language

    override fun initPopupContent() {
        super.initPopupContent()
        var items = emptyList<LanguageBean>()

        val binding = PopupChoooseLanguageBinding.bind(popupContentView)

        binding.tvCancel.setOnClickListener {
            dismiss()
        }

        binding.tvConfirm.setOnClickListener {
            dismiss()
            onClick.invoke(items.filter { it.checked })
        }

        homeViewModel.getLanguages()

        homeViewModel.viewStates.observeState(this, HomePageState::anchorGetStatus) {
            if (it is RequestStatus.LanguagesSuccess) {
                items = it.list?.toList() ?: emptyList()
                binding.recyclerView.adapter = LanguageAdapter(items.onEach { item ->
                    checkList.find { it.languageCode == item.languageCode }
                        ?.let { item.checked = true }
                })
            }
        }
    }

    class LanguageAdapter(
        private val items: List<LanguageBean>
    ) : RecyclerView.Adapter<LanguageAdapter.ViewHolder>() {

        class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_choose_language, parent, false)
            return ViewHolder(view)
        }

        override fun getItemCount(): Int {
            return items.size
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val item = items[position]
            holder.itemView.findViewById<TextView>(R.id.tv_name).apply {
                text = item.languageName
                setTextCompatColor(
                    if (item.checked) {
                        R.color.color_FB2CA4
                    } else {
                        com.bdc.android.library.R.color.color_666666
                    }
                )
            }

            holder.itemView.apply {
                background = if (item.checked) {
                    ContextCompat.getDrawable(
                        this.context, R.drawable.shape_item_language_checked
                    )
                } else {
                    ContextCompat.getDrawable(
                        this.context, R.drawable.shape_item_language_normal
                    )
                }
                setOnClickListener {
                    item.checked = !item.checked
                    notifyItemChanged(position)
                }
            }
        }
    }
}

fun showLanguagePopup(
    activity: ComponentActivity,
    checkList: List<LanguageBean> = emptyList(),
    block: (List<LanguageBean>) -> Unit
) {
    XPopup.Builder(activity).asCustom(ChooseLanguagePopup(activity, checkList, block)).show()
}