package com.heart.heartmerge.popup.raffle

import android.annotation.SuppressLint
import android.app.Dialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.heart.heartmerge.R

class RaffleRewardUseDialog : DialogFragment() {

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_raffle_reward_use, container, false)
    }

    @SuppressLint("UseGetLayoutInflater")
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return activity?.let {
            val view = LayoutInflater.from(it).inflate(R.layout.dialog_raffle_reward_use, null)

            val dialog = Dialog(it)
            dialog.setContentView(view)

            val window = dialog.window
            val layoutParams = window?.attributes
            layoutParams?.width = (resources.displayMetrics.widthPixels * 0.8).toInt()
            window?.attributes = layoutParams
            window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            dialog
        } ?: throw IllegalStateException("Activity cannot be null")
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        view.findViewById<View>(R.id.iv_close).setOnClickListener { dialog?.dismiss() }

        view.findViewById<TextView>(R.id.tv_description).apply {
            val prefix = "有效时长："
            val duration = "终身有效"
            val formattedDuration = SpannableString("$prefix$duration")
            formattedDuration.setSpan(
                ForegroundColorSpan(Color.parseColor("#fdaa49")),
                prefix.length,
                prefix.length + duration.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            text = formattedDuration
        }

        view.findViewById<View>(R.id.tv_use).setOnClickListener {
            dialog?.dismiss()
            Toast.makeText(activity, "奖品已使用", Toast.LENGTH_SHORT).show()
        }
    }
}