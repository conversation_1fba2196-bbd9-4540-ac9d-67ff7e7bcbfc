package com.heart.heartmerge.popup.raffle

import android.app.Dialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.heart.heartmerge.R


data class LotteryTimesPurchaseItem(
    val name: String, val diamond: Int, var checked: Boolean = false
)

class LotteryTimesPurchaseDialog : BottomSheetDialogFragment() {

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_raffle_times_purchase, container, false)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return activity?.let {
            val view = LayoutInflater.from(it).inflate(R.layout.dialog_raffle_times_purchase, null)
            val dialog = BottomSheetDialog(it)
            dialog.setContentView(view)

            val window = dialog.window
            val layoutParams = window?.attributes
            layoutParams?.width = (resources.displayMetrics.widthPixels * 0.8).toInt()
            window?.attributes = layoutParams
            window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

            dialog
        } ?: throw IllegalStateException("Activity cannot be null")
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        view.findViewById<View>(R.id.iv_close).setOnClickListener { dialog?.dismiss() }
        val items = listOf(
            LotteryTimesPurchaseItem(name = "单抽", diamond = 10, checked = true),
            LotteryTimesPurchaseItem(name = "10抽", diamond = 90),
            LotteryTimesPurchaseItem(name = "20抽", diamond = 150),
            LotteryTimesPurchaseItem(name = "100抽", diamond = 800),
        )
        view.findViewById<RecyclerView>(R.id.recyclerView).apply {
            adapter = LotteryTimesPurchaseAdapter(
                items,
                object : LotteryTimesPurchaseAdapter.OnItemClickListener {
                    override fun onItemClick(position: Int) {
                        items.forEach { it.checked = false }
                        items[position].checked = true
                        adapter?.notifyDataSetChanged()
                    }
                })
        }

        view.findViewById<View>(R.id.tv_recharge).setOnClickListener {
//            startActivity(Intent(activity, RechargeActivity::class.java))
            dialog?.dismiss()
        }

        view.findViewById<View>(R.id.tv_buy).setOnClickListener {
//            startActivity(Intent(activity, PurchaseSuccessActivity::class.java))
            dialog?.dismiss()
        }
    }

    class LotteryTimesPurchaseAdapter(
        private val items: List<LotteryTimesPurchaseItem>, private val listener: OnItemClickListener
    ) : RecyclerView.Adapter<LotteryTimesPurchaseAdapter.ViewHolder>() {

        class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

        interface OnItemClickListener {
            fun onItemClick(position: Int)
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_raffle_times_purchase, parent, false)
            return ViewHolder(view)
        }

        override fun getItemCount(): Int {
            return items.size
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val item = items[position]
            holder.itemView.setBackgroundResource(if (item.checked) R.drawable.bg_lottery_times_option_checked_shape else R.drawable.bg_lottery_times_option_normal_shape)
            holder.itemView.findViewById<TextView>(R.id.tv_name).text = item.name
            holder.itemView.findViewById<TextView>(R.id.tv_diamond).text = "${item.diamond}钻石"
            holder.itemView.setOnClickListener {
                listener.onItemClick(position)
            }
        }
    }
}