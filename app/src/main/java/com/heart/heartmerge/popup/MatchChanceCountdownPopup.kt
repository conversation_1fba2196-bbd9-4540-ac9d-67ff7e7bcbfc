package com.heart.heartmerge.popup

import android.app.Activity
import com.bdc.android.library.extension.click
import com.heart.heartmerge.R
import com.heart.heartmerge.extension.formatCountdown
import com.heart.heartmerge.utils.FlexibleCountdownTimer
import com.lxj.xpopup.XPopup

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2024/12/24 10:26
 * @description :匹配次数弹框
 */
class MatchChanceCountdownPopup(context: Activity, val timestamp: Long, block: () -> Unit) :
    NormalNewPopup(
        context,
        icon = R.mipmap.ic_dialog_countdown,
        title = "00:00:00",
        subTitle = context.getString(R.string.next_free_chance),
        content = context.getString(R.string.free_match_chance_countdown_tip),
        btnSure = context.getString(R.string.recharge_now),
        btnCancel = "",
        mainColor = R.color.color_EC12E2,
        titleColor = R.color.color_EC12E2,
        block = block
    ) {

    private lateinit var flexibleCountdownTimer: FlexibleCountdownTimer

    override fun initPopupContent() {
        super.initPopupContent()
        if (timestamp > System.currentTimeMillis()) {
            flexibleCountdownTimer =
                FlexibleCountdownTimer(timestamp - System.currentTimeMillis(), onTick = { t ->
                    binding.tvTitle.text = t.formatCountdown()
                }, onFinish = {
                    dismiss()
                })
            flexibleCountdownTimer.start()
        }
        binding.btnSure.click {
            block.invoke()
            dismiss()
        }
    }

    override fun onDismiss() {
        super.onDismiss()
        flexibleCountdownTimer.cancel()
    }
}

fun showMatchChanceCountdownPopup(context: Activity, timestamp: Long, block: () -> Unit) {
    XPopup.Builder(context).asCustom(MatchChanceCountdownPopup(context, timestamp, block)).show()
}