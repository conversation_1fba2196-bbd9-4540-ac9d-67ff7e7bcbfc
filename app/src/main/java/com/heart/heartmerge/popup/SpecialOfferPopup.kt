package com.heart.heartmerge.popup

import android.view.View
import androidx.activity.ComponentActivity
import com.angcyo.dsladapter.DslAdapterItem
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.makeVisible
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.GoodsBean
import com.heart.heartmerge.beans.SpecialOfferBean
import com.heart.heartmerge.databinding.PopupSpecialOfferBinding
import com.heart.heartmerge.extension.formatDate
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView
import com.lxj.xpopup.util.XPopupUtils
import java.lang.ref.WeakReference

/**
 * 三日签到弹框
 * 作者：Assistant
 * 创建日期：2024/12/25
 * 描述：三日签到弹框UI，展示主播信息和签到奖励
 */
class SpecialOfferPopup(
    private val activity: ComponentActivity,
    private val rechargeBean: GoodsBean,
) : CenterPopupView(activity) {

    private val weakActivity = WeakReference(context)

    private lateinit var binding: PopupSpecialOfferBinding

    override fun getImplLayoutId(): Int = R.layout.popup_special_offer

    override fun initPopupContent() {
        super.initPopupContent()
        binding = PopupSpecialOfferBinding.bind(popupImplView)

        initCheckinData()
        setupClickListeners()

        binding.tvDiamond.apply {
            text = "700"
            setGradientColors(
                intArrayOf(
                    context.getColor(R.color.color_EDB5FA), context.getColor(R.color.color_B346F1)
                )
            )
        }

        binding.tvPrice.apply {
            text = "USD \$4.99 ONLY!"
            setGradientColors(
                intArrayOf(
                    context.getColor(R.color.color_FFD83C), context.getColor(R.color.color_FF782F)
                )
            )
        }

        fetchData()
    }

    fun fetchData() {
        val items = listOf(
            SpecialOfferBean(
                "1",
                "Day1",
                System.currentTimeMillis() + 3600,
                diamondCount = 100,
                price = "",
                status = 0
            ), SpecialOfferBean(
                "2",
                "Day2",
                System.currentTimeMillis() + 3600L,
                diamondCount = 200,
                price = "",
                status = 1
            ), SpecialOfferBean(
                "3",
                "Day3",
                System.currentTimeMillis() + 3600,
                diamondCount = 300,
                price = "",
                status = 2
            )
        )

        binding.recyclerView.append<DslAdapterItem>(items) {
            itemLayoutId = R.layout.item_special_offer
            itemData = items
            itemBind = { itemHolder, _, _, _ ->
                val specialOffer = itemData as SpecialOfferBean
                itemHolder.tv(R.id.tv_title)?.text = specialOffer.title
                itemHolder.tv(R.id.tv_claim_time)?.text =
                    context.getString(R.string.claim_time, specialOffer.time.formatDate())
                itemHolder.tv(R.id.tv_diamonds)?.text = specialOffer.diamondCount.toString()

                itemHolder.tv(R.id.btn_claim)?.makeVisible(specialOffer.status != 0)
                itemHolder.v<View>(R.id.iv_claimed)?.makeVisible(specialOffer.status == 0)

                //未领取状态下，设置按钮点击事件
                when (specialOffer.status) {

                    // 0: 已领取
                    0 -> {
                        itemHolder.v<View>(R.id.container)
                            ?.setBackgroundResource(R.drawable.bg_special_offer_item_inactive)
                    }

                    // 1: 可领取
                    1 -> {
                        itemHolder.v<View>(R.id.container)
                            ?.setBackgroundResource(R.drawable.bg_special_offer_item_active)
                        itemHolder.tv(R.id.btn_claim)?.apply {
                            setBackgroundResource(R.drawable.bg_special_offer_claim_button)
                            click {

                            }
                        }
                    }

                    // 2: 锁定状态
                    2 -> {
                        itemHolder.v<View>(R.id.container)
                            ?.setBackgroundResource(R.drawable.bg_special_offer_item_active)
                        itemHolder.tv(R.id.btn_claim)?.apply {
                            setBackgroundResource(R.drawable.bg_special_offer_claim_button_disabled)
                            click {

                            }
                        }
                    }
                }
            }
        }
    }

    override fun getMaxWidth(): Int {
        return XPopupUtils.getAppWidth(context)
    }

    private fun initCheckinData() {
        // Day1
        updateDayStatus(1, CheckinStatus.CLAIMED)

        // Day2
        updateDayStatus(2, CheckinStatus.AVAILABLE)

        // Day3
        updateDayStatus(3, CheckinStatus.LOCKED)
    }

    private fun updateDayStatus(day: Int, status: CheckinStatus) {
        when (day) {
        }
    }

    private fun setupClickListeners() {
        // 关闭按钮
        binding.ivClose.click {
            dismiss()
        }
    }
}


/**
 * 签到状态枚举
 */
enum class CheckinStatus {
    LOCKED,     // 未解锁
    AVAILABLE,  // 可签到
    CLAIMED     // 已签到
}

/**
 * 显示三日签到弹框
 */
fun showSpecialOfferPopup(
    activity: ComponentActivity, rechargeBean: GoodsBean
) {
    XPopup.Builder(activity).dismissOnBackPressed(false).dismissOnTouchOutside(false).asCustom(
        SpecialOfferPopup(
            activity, rechargeBean
        )
    ).show()
}
