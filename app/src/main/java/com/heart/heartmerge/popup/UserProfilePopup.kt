package com.heart.heartmerge.popup

import androidx.activity.ComponentActivity
import androidx.activity.viewModels
import androidx.lifecycle.asLiveData
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.hideSoftKeyboard
import com.bdc.android.library.imageloader.ImageLoader
import com.bdc.android.library.utils.ToastUtil
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.PopupUserProfileBinding
import com.heart.heartmerge.extension.formatDate
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.viewmodes.UserViewModel
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BottomPopupView

class UserProfilePopup(private val activity: ComponentActivity, val block: () -> Unit) :
    BottomPopupView(activity) {

    private val userViewModel by activity.viewModels<UserViewModel>()

    private val params = JsonObject()

    override fun getImplLayoutId(): Int = R.layout.popup_user_profile

    override fun initPopupContent() {
        super.initPopupContent()
        val binding = PopupUserProfileBinding.bind(popupContentView)
        userViewModel.userBean.asLiveData().observe(this) {
            ImageLoader.with(activity).load(it.avatar).asAvatar().into(binding.ivHeader)
            binding.tvId.text = it.id
            binding.etNickname.setText(it.nickname)
            binding.tvAge.text = it.birthdayAt
            binding.tvCountry.text = it.userCountry?.title
            binding.tvLanguage.text = it.languages.map { it.languageName }.joinToString()
        }

        binding.ivHeader.click {
            hideSoftKeyboard()
            showAvatarPopup(activity) {
                ImageLoader.with(activity).load(it).asAvatar().into(binding.ivHeader)
                userViewModel.upload(it?:"").asLiveData()
                    .observe(this) {
                        params.addProperty("avatar", it.objectKey)
                    }
            }
        }
        binding.tvAge.click {
            hideSoftKeyboard()
            showBirthdayPopup(activity) {
                binding.tvAge.text = it.formatDate()
                params.addProperty("birthday", it.formatDate())
            }
        }

        //选择国家
        binding.tvCountry.click {
            hideSoftKeyboard()
            showCountryPopup(activity) {
                binding.tvCountry.text = it.title
                params.addProperty("country", it.title)
            }
        }

        //选择语言
        binding.tvLanguage.click {
            hideSoftKeyboard()
            showLanguagePopup(activity, MMKVDataRep.userInfo.languages) {
                binding.tvLanguage.text = it.map { it.languageName }.joinToString()
                params.add("languages", Gson().toJsonTree(it.map { it.languageCode }))
            }
        }

        binding.tvCancel.click {
            dismiss()
        }

        binding.tvSave.click {
            dismiss()
            userViewModel.updateProfile(params.apply {
                addProperty("nickName", binding.etNickname.text.toString())
                if (!has("headFileName")) {
                    addProperty("headFileName", MMKVDataRep.userInfo.avatar)
                }
                if (!has("birthday")) {
                    addProperty("birthday", MMKVDataRep.userInfo.birthdayAt)
                }
                if (!has("languages")) {
                    add(
                        "languages",
                        Gson().toJsonTree(MMKVDataRep.userInfo.languages.map { it.languageCode })
                    )
                }
                if (!has("country")) {
                    addProperty(
                        "country", MMKVDataRep.userInfo.userCountry?.title
                    )
                }
            }).asLiveData().observe(this) {
                ToastUtil.show(context.getString(R.string.modify_successful))
                block.invoke()
                dismiss()
            }
        }
    }
}

fun showUserProfilePopup(activity: ComponentActivity, block: () -> Unit) {
    XPopup.Builder(activity).autoFocusEditText(false).autoOpenSoftInput(false)
        .asCustom(UserProfilePopup(activity, block)).show()
}

