package com.heart.heartmerge.popup

import android.content.Context
import android.graphics.drawable.GradientDrawable
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.bdc.android.library.extension.click
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.RateLabelBean
import com.heart.heartmerge.beans.RateLabelList
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.databinding.PopupVideoRateBinding
import com.heart.heartmerge.extension.buildImageUrl
import com.heart.heartmerge.extension.loadAvatar
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView

/**
 * 作者：Lxf
 * 创建日期：2024/11/13 15:28
 * 描述：
 */
class VideoRatePopup(
    activity: AppCompatActivity,
    private val anchorInfo: UserBean,
    private val labels: MutableList<String>,
    private val block: (selRate: Int, selLabelIds: MutableList<Int>) -> Unit
) : CenterPopupView(activity) {
    override fun getImplLayoutId(): Int = R.layout.popup_video_rate
    private var binding: PopupVideoRateBinding? = null
    private var badLabels: MutableList<RateLabelBean> = mutableListOf()
    private var normalLabels: MutableList<RateLabelBean> = mutableListOf()
    private var goodLabels: MutableList<RateLabelBean> = mutableListOf()

    private var selRate = 3 // 默认选择一般
    private var selLabels: MutableList<String> = mutableListOf()
    override fun initPopupContent() {
        super.initPopupContent()
        binding = PopupVideoRateBinding.bind(popupImplView)
        val colors = arrayListOf(
            R.color.color_FC9898,
            R.color.color_89BFEB,
            R.color.color_8FDAC8,
            R.color.color_9297FF,
            R.color.color_9BDE92,
            R.color.color_BFCF75,
            R.color.color_EF91D3,
            R.color.color_FFB270,
            R.color.color_FFD86C
        )
        binding?.ivHeader?.loadAvatar(anchorInfo.avatar.buildImageUrl())
        binding?.labels?.setLabels(labels)
        binding?.labels?.setOnLabelSelectChangeListener { label, _, isSelect, _ ->
            val background = label.background as? GradientDrawable
            if (isSelect) {
                val randomColorIndex = (0..8).random()
                background?.setColor(ContextCompat.getColor(context, colors[randomColorIndex]))
                selLabels.add(label.text.toString())
                label.setTextColor(ContextCompat.getColor(context, R.color.color_white))
            } else {
                background?.setColor(ContextCompat.getColor(context, R.color.color_E6E6E6))
                selLabels.remove(label.text.toString())
                label.setTextColor(ContextCompat.getColor(context, R.color.color_A1A1A1))
            }
        }

        // 表情选择逻辑
        binding?.layoutBad?.click {
            selRate = 2
            updateEmotionSelection(1)
            showLabels()
        }

        binding?.layoutGeneral?.click {
            selRate = 3
            updateEmotionSelection(2)
            showLabels()
        }

        binding?.layoutGood?.click {
            selRate = 5
            updateEmotionSelection(3)
            showLabels()
        }

        // 关闭按钮
        binding?.ivClose?.click {
            dismissWith {
                activity.finish()
            }
        }

        binding?.btnSubmit?.click {
            val selLabelIds = mutableListOf<Int>()
            getSelectedLabels().forEach {
                if (it.title in selLabels) {
                    selLabelIds.add(it.id)
                }
            }
            block.invoke(selRate, selLabelIds)
            binding?.labels?.clearAllSelect()
        }

        // 默认选择Good
        updateEmotionSelection(2)
    }

    fun showLabels() {
        selLabels.clear()
        val labels = mutableListOf<String>()
        getSelectedLabels().forEach {
            labels.add(it.title)
        }
        binding?.labels?.setLabels(labels)
    }

    fun getSelectedLabels(): MutableList<RateLabelBean> {
        return when (selRate) {
            2 -> badLabels
            3 -> normalLabels
            5 -> goodLabels
            else -> mutableListOf()
        }
    }

    private fun updateEmotionSelection(selectedEmotion: Int) {
        binding?.apply {
            // 重置所有背景
            layoutBad.setBackgroundResource(R.drawable.shape_emotion_normal)
            layoutGeneral.setBackgroundResource(R.drawable.shape_emotion_normal)
            layoutGood.setBackgroundResource(R.drawable.shape_emotion_normal)

            // 设置选中状态
            when (selectedEmotion) {
                1 -> layoutBad.setBackgroundResource(R.drawable.shape_emotion_selected)
                2 -> layoutGeneral.setBackgroundResource(R.drawable.shape_emotion_selected)
                3 -> layoutGood.setBackgroundResource(R.drawable.shape_emotion_selected)
            }
        }
    }

    fun refreshLabels(rateLabels: MutableList<RateLabelList>) {
        rateLabels.forEach { label ->
            if (label.score <= 2) {
                label.list.forEach { labelBean ->
                    badLabels.add(labelBean)
                }
            } else if (label.score == 3) {
                label.list.forEach { labelBean ->
                    normalLabels.add(labelBean)
                }
            } else {
                label.list.forEach { labelBean ->
                    goodLabels.add(labelBean)
                }
            }
        }

        showLabels()
    }
}

fun showVideoRatePopup(
    activity: AppCompatActivity,
    anchorInfo: UserBean,
    labels: MutableList<String>,
    block: (selRate: Int, selLabelIds: MutableList<Int>) -> Unit
): VideoRatePopup {
    val popup = VideoRatePopup(activity, anchorInfo, labels, block)
    XPopup.Builder(activity).dismissOnTouchOutside(false).dismissOnBackPressed(false)
        .asCustom(popup).show()
    return popup
}