package com.heart.heartmerge.popup.raffle

import android.app.Dialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import com.heart.heartmerge.R

class RaffleWinDialog : DialogFragment() {

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_raffle_win, container, false)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return activity?.let {
            val view = LayoutInflater.from(it).inflate(R.layout.dialog_raffle_win, null)
            val index = arguments?.getInt("index")
            val dialog = Dialog(it)
            dialog.setContentView(view)

            val window = dialog.window
            val layoutParams = window?.attributes
            layoutParams?.width = (resources.displayMetrics.widthPixels * 0.8).toInt()
            window?.attributes = layoutParams
            window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            dialog
        } ?: throw IllegalStateException("Activity cannot be null")
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        view.findViewById<View>(R.id.iv_close).setOnClickListener { dialog?.dismiss() }
        view.findViewById<View>(R.id.tv_check).setOnClickListener {
            dismissNow()
        }
    }
}