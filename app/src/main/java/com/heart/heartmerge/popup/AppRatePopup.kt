package com.heart.heartmerge.popup

import android.content.Context
import androidx.appcompat.app.AppCompatActivity
import com.bdc.android.library.extension.click
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.PopupAppRateBinding
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/12 17:39
 * @description :权限申请提示弹框
 */
class AppRatePopup(context: Context) : CenterPopupView(context) {

    override fun getImplLayoutId(): Int = R.layout.popup_app_rate

    override fun initPopupContent() {
        super.initPopupContent()
        val binding = PopupAppRateBinding.bind(popupImplView)
        binding.ivClose.click { dismiss() }

        binding.btnSubmit.click {}
    }
}

fun showAppRatePopup(activity: AppCompatActivity) {
    XPopup.Builder(activity).asCustom(AppRatePopup(activity)).show()
}