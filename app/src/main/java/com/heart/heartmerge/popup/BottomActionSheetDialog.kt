package com.heart.heartmerge.popup

import android.app.Dialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.heart.heartmerge.R


data class BottomActionSheetItem(
    val name: String
)

class BottomActionSheetDialog(
    private val items: List<BottomActionSheetItem>,
    private val listener: BottomSheetAdapter.OnItemClickListener
) : BottomSheetDialogFragment() {

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_bottom_action_sheet, container, false)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return activity?.let {
            val view = LayoutInflater.from(it).inflate(R.layout.dialog_bottom_action_sheet, null)
            val dialog = BottomSheetDialog(it)
            dialog.setContentView(view)

            val window = dialog.window
            val layoutParams = window?.attributes
            layoutParams?.width = (resources.displayMetrics.widthPixels * 0.8).toInt()
            window?.attributes = layoutParams
            window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

            dialog
        } ?: throw IllegalStateException("Activity cannot be null")
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        view.findViewById<RecyclerView>(R.id.recyclerView).apply {
            adapter = BottomSheetAdapter(items, object : BottomSheetAdapter.OnItemClickListener {
                override fun onItemClick(position: Int) {
                    listener.onItemClick(position)
                    dialog?.dismiss()
                }
            })
        }
    }

    class BottomSheetAdapter(
        private val items: List<BottomActionSheetItem>, private val listener: OnItemClickListener
    ) : RecyclerView.Adapter<BottomSheetAdapter.ViewHolder>() {

        class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

        interface OnItemClickListener {
            fun onItemClick(position: Int)
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_bottom_action_sheet, parent, false)
            return ViewHolder(view)
        }

        override fun getItemCount(): Int {
            return items.size
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val item = items[position]
            holder.itemView.findViewById<TextView>(R.id.tv_option).text = item.name
            holder.itemView.setOnClickListener {
                listener.onItemClick(position)
            }
        }
    }
}