package com.heart.heartmerge.popup

import android.content.Context
import androidx.appcompat.app.AppCompatActivity
import com.angcyo.dsladapter.DslAdapterItem
import com.bdc.android.library.extension.click
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.PopupPermissionBinding
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/12 17:39
 * @description :权限申请提示弹框
 */
class PermissionPopup(context: Context) : CenterPopupView(context) {

    override fun getImplLayoutId(): Int = R.layout.popup_permission

    override fun initPopupContent() {
        super.initPopupContent()
        val binding = PopupPermissionBinding.bind(popupImplView)
        binding.ivClose.click { dismiss() }

        binding.recyclerView.append<DslAdapterItem>(
            listOf(
                Pair<Int, String>(R.mipmap.ic_permission_camera, "Camera permissions"),
                Pair<Int, String>(R.mipmap.ic_permission_voice, "Microphone permissions")
            )
        ) {
            itemLayoutId = R.layout.item_permission
            itemBindOverride = { itemHolder, _, _, _ ->
                val item = itemData as Pair<Int, String>
                itemHolder.img(R.id.iv_permission)?.setImageResource(item.first)
                itemHolder.tv(R.id.tv_permission)?.text = item.second
            }
        }

        binding.btnAllow.click {}
    }
}

fun showPermissionPopup(activity: AppCompatActivity) {
    XPopup.Builder(activity).asCustom(PermissionPopup(activity)).show()
}