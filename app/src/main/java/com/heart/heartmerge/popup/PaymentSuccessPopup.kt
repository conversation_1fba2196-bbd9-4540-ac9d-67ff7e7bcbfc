package com.heart.heartmerge.popup

import android.content.Context
import com.bdc.android.library.extension.click
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.PopupPaymentSuccessBinding
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025/1/4 18:31
 * @description :登录确认协议
 */
class PaymentSuccessPopup(context: Context, private val block: () -> Unit) :
    CenterPopupView(context) {

    override fun getImplLayoutId(): Int = R.layout.popup_payment_success

    override fun initPopupContent() {
        super.initPopupContent()
        val binding = PopupPaymentSuccessBinding.bind(popupImplView)

        binding.btnSure.click {
            block()
            dismiss()
        }

        binding.btnCancel.click {
            dismiss()
        }
    }
}

fun showPaymentSuccessPopup(context: Context, block: () -> Unit) {
    XPopup.Builder(context).asCustom(PaymentSuccessPopup(context, block)).show()
}