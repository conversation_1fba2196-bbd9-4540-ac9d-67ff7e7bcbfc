package com.heart.heartmerge.popup

import android.app.Activity
import com.bdc.android.library.extension.click
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.PopupWhoSeeMeBinding
import com.heart.heartmerge.utils.AppUtil
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.animator.PopupAnimator
import com.lxj.xpopup.animator.ScaleAlphaAnimator
import com.lxj.xpopup.enums.PopupAnimation
import com.lxj.xpopup.impl.FullScreenPopupView

/**
 * 作者：Lxf
 * 创建日期：2024/11/9 17:56
 * 描述：
 */
class WhoSeeMePopup(
    private val context: Activity,
    val block: () -> Unit = {},
) : FullScreenPopupView(context) {

    override fun getImplLayoutId(): Int = R.layout.popup_who_see_me

    override fun initPopupContent() {
        super.initPopupContent()
        val binding = PopupWhoSeeMeBinding.bind(popupImplView)
        binding.btnSure.click {
            dismissWith {
                block.invoke()
            }
        }
        AppUtil.applyBlur(context, binding.blurredBackground)
    }

    override fun getPopupAnimator(): PopupAnimator {
        return ScaleAlphaAnimator(popupContentView, animationDuration, PopupAnimation.ScaleAlphaFromCenter)
    }
}


fun showWhoSeeMePopup(
    context: Activity,
    block: () -> Unit = {},
) {
    XPopup.Builder(context)
        .asCustom(WhoSeeMePopup(context, block))
        .show()
}
