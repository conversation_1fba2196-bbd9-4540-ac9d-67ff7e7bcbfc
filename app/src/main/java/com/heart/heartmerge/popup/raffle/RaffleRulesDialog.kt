package com.heart.heartmerge.popup.raffle

import android.app.Dialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import androidx.fragment.app.DialogFragment
import com.heart.heartmerge.R

class RaffleRulesDialog :DialogFragment() {

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_raffle_rules,container,false)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return activity?.let {
            val view = LayoutInflater.from(it).inflate(R.layout.dialog_raffle_rules, null)

            val dialog = Dialog(it)
            dialog.setContentView(view)

            val window = dialog.window
            val layoutParams = window?.attributes
            layoutParams?.width = (resources.displayMetrics.widthPixels * 0.8).toInt()
            window?.attributes = layoutParams
            window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            dialog
        }?: throw IllegalStateException("Activity cannot be null")
    }

    val rules = "<div style=\"text-align:center;\">\n" +
            "    <h3 style=\"color:#FF6600;\">\uD83C\uDF89 幸运大转盘 —— 抽奖活动规则 \uD83C\uDF89</h3>\n" +
            "</div>\n" +
            "\n" +
            "<h4>\uD83C\uDFC6 活动时间</h4>\n" +
            "<ul>\n" +
            "    <li>开始时间：<span style=\"font-weight:bold;\">2023年5月1日 00:00</span></li>\n" +
            "    <li>结束时间：<span style=\"font-weight:bold;\">2023年5月31日 23:59</span></li>\n" +
            "</ul>\n" +
            "\n" +
            "<h4>\uD83C\uDF1F 参与资格</h4>\n" +
            "<ul>\n" +
            "    <li>所有注册用户均可免费参与抽奖。</li>\n" +
            "    <li>每位用户每日享有<span style=\"font-weight:bold;\">一次</span>抽奖机会。</li>\n" +
            "    <li>邀请好友注册并完成首次购买可额外获得<span style=\"font-weight:bold;\">一次</span>抽奖机会。</li>\n" +
            "</ul>\n" +
            "\n" +
            "<h4>\uD83C\uDF81 奖项设置</h4>\n" +
            "<ul>\n" +
            "    <li><span style=\"color:#800080;\">\uD83C\uDF08 特等奖：</span>苹果iPhone 14 Pro Max，共<span style=\"font-weight:bold;\">1名</span>。</li>\n" +
            "    <li><span style=\"color:#00FFFF;\">\uD83D\uDC8E 一等奖：</span>智能手表，共<span style=\"font-weight:bold;\">10名</span>。</li>\n" +
            "    <li><span style=\"color:#FFD700;\">\uD83C\uDF1F 二等奖：</span>蓝牙耳机，共<span style=\"font-weight:bold;\">50名</span>。</li>\n" +
            "    <li><span style=\"color:#008000;\">\uD83C\uDF81 三等奖：</span>50元购物券，<span style=\"font-weight:bold;\">不限量</span>。</li>\n" +
            "    <li><span style=\"color:#0000FF;\">\uD83C\uDF40 幸运奖：</span>10元优惠券，<span style=\"font-weight:bold;\">不限量</span>。</li>\n" +
            "</ul>\n" +
            "\n" +
            "<h4>\uD83D\uDE80 参与方式</h4>\n" +
            "<ol>\n" +
            "    <li>登录官网或APP，进入活动页面。</li>\n" +
            "    <li>点击“开始抽奖”，转动幸运大转盘。</li>\n" +
            "    <li>转盘停止后，指针所指奖项即为您的幸运奖励。</li>\n" +
            "</ol>\n" +
            "\n" +
            "<h4>\uD83D\uDCDD 特别说明</h4>\n" +
            "<ul>\n" +
            "    <li>奖品将在活动结束后15个工作日内发放。</li>\n" +
            "    <li>购物券和优惠券有效期为一个月，请在有效期内使用。</li>\n" +
            "    <li>活动最终解释权归本公司所有。</li>\n" +
            "</ul>\n" +
            "\n" +
            "<h4>\uD83D\uDD0D 注意事项</h4>\n" +
            "<ul>\n" +
            "    <li>严禁使用脚本或任何不正当手段参与，一经发现将取消资格。</li>\n" +
            "    <li>请确保账户信息准确无误，以便奖品发放。</li>\n" +
            "</ul>\n" +
            "\n" +
            "<p>祝您好运连连，期待与您共享惊喜时刻！</p>"

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        view.findViewById<WebView>(R.id.webView).apply {
            loadDataWithBaseURL(null,rules, "text/html", "utf-8",null)
        }

        view.findViewById<View>(R.id.iv_close).setOnClickListener { dialog?.dismiss() }
    }
}