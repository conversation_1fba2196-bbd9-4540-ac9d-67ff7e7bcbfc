package com.heart.heartmerge.popup

import android.content.Context
import android.os.Bundle
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.lifecycle.asLiveData
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.bdc.android.library.extension.makeInVisible
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.extension.setTextCompatColor
import com.bdc.android.library.utils.DateUtil
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.SignInItemBean
import com.heart.heartmerge.databinding.ItemUserSignBinding
import com.heart.heartmerge.databinding.PopupUserSignBinding
import com.heart.heartmerge.extension.formatAsDashedDate
import com.heart.heartmerge.extension.formatCountdown
import com.heart.heartmerge.extension.formatDate
import com.heart.heartmerge.manager.DiamondChangeManager
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond
import com.heart.heartmerge.manager.MainPopupPriority
import com.heart.heartmerge.manager.PriorityDialogManager
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.ui.activities.mine.MembershipCenterActivity
import com.heart.heartmerge.utils.FlexibleCountdownTimer
import com.heart.heartmerge.utils.PurchaseScene
import com.heart.heartmerge.viewmodes.UserViewModel
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.util.XPopupUtils

class UserSignPopup(val activity: AppCompatActivity, val block: () -> Unit = {}) :
    BasePriorityPopup(activity) {

    private val userViewModel by activity.viewModels<UserViewModel>()

    override fun getImplLayoutId(): Int = R.layout.popup_user_sign

    override fun beforeDismiss() {
        PriorityDialogManager.removeData(MainPopupPriority.USER_SIGN) //删除队列
    }

    override fun initPopupContent() {
        super.initPopupContent()
        val binding = PopupUserSignBinding.bind(contentView)
        binding.llLockContainer.apply {
            makeVisible(!MMKVDataRep.userInfo.isVIP)
            if (isVisible) {
                click {
                    jump(MembershipCenterActivity::class.java, Bundle().apply {
                        putParcelable(MembershipCenterActivity.SCENE, PurchaseScene.SignIn)
                    })
                }
            }
        }

        binding.tvSignDay.text = context.getString(R.string.checked_in_for_x_days, 0)
        var currentDayGift: SignInItemBean? = null
        MMKVDataRep.lastSignPopupShowDate = System.currentTimeMillis().formatDate()
        binding.tvTitle.setGradientColors(
            intArrayOf(
                ContextCompat.getColor(
                    context, R.color.color_EC12E2
                ), ContextCompat.getColor(
                    context, R.color.color_B323F2
                ), ContextCompat.getColor(context, R.color.color_8531FF)
            )
        )
        binding.icClose.click {
            dismiss()
        }
        userViewModel.fetchSignInList().asLiveData().observe(this) { result ->
            val currentItems = buildList {
                addAll(if (MMKVDataRep.userInfo.isVIP) result.list else result.default_list)
            }

            binding.btnSign.apply {
                isEnabled =
                    currentItems.find { item -> item.date_format == result.current_date_format }?.isAvailable == true && MMKVDataRep.userInfo.isVIP == true
                if (result.next_gain_time > 0) {
                    FlexibleCountdownTimer(result.next_gain_time, 1, onTick = { it ->
                        text = context.getString(R.string.claim_next_time, it.formatCountdown())
                    }, onFinish = {
                        text = context.getString(R.string.claim)
                        isEnabled = true
                    }).start()
                }

                click {
                    if (!MMKVDataRep.userInfo.isVIP) {
                        showMembershipSubscribePopup(activity, PurchaseScene.SignIn)
                    } else {
                        userViewModel.signIn(
                            currentItems.find { item -> item.date_format == result.current_date_format }?.record_id
                                ?: ""
                        ).asLiveData().observe(this@UserSignPopup) {
                            dismiss()
                            MMKVDataRep.lastSignDate = System.currentTimeMillis().formatDate()
                            currentDayGift?.let {
                                DiamondChangeManager.addDiamond(it.signInValue)
                                showUserSignRewardPopup(activity, currentDayGift) {
                                    block.invoke()
                                }
                            }
                        }
                    }
                }
            }

            val signItems = listOf(
                binding.itemSign1,
                binding.itemSign2,
                binding.itemSign3,
                binding.itemSign4,
                binding.itemSign5,
                binding.itemSign6,
                binding.itemSign7
            )

            currentItems.forEachIndexed { index, item ->
                if (index < signItems.size) {
                    signItems[index].setData(item)
                }
                if (item.isReceived) {
                    binding.tvSignDay.text = context.getString(
                        R.string.checked_in_for_x_days,
                        item.day_num.takeIf { it > 0 } ?: 0)
                    MMKVDataRep.lastSignDate = item.date_format.formatAsDashedDate()
                }
            }
            currentDayGift = currentItems?.find {
                result.current_date_format == it.date_format
            }?.apply {
                nextCoin = if (currentItems.indexOf(this) == currentItems.size - 1) {
                    coin
                } else {
                    currentItems[currentItems.indexOf(this) + 1].coin
                }
            }
        }
    }

    override fun getMaxWidth(): Int {
        return (XPopupUtils.getAppWidth(this.context) * 0.9f).toInt()
    }
}

fun showUserSignPopup(activity: AppCompatActivity, block: () -> Unit = {}) {
    XPopup.Builder(activity).asCustom(UserSignPopup(activity, block)).show()
}

open class UserSignItemView : FrameLayout {
    constructor(context: Context) : this(context, null)
    constructor(context: Context, attr: AttributeSet?) : super(context, attr)

    init {
        LayoutInflater.from(context).inflate(getLayoutId(), this)
    }

    open fun getLayoutId() = R.layout.item_user_sign

    fun setData(itemData: SignInItemBean) {
        val binding = ItemUserSignBinding.bind(this.getChildAt(0))
        binding.tvDiamond.text = context.getString(R.string.signin_day, itemData.day_num)
//        ImageLoader.with(this).load(itemData.userSignInGive.signInUrl).into(binding.ivItem)
        binding.tvTitle.text = "+${itemData.coin.toShowDiamond()}"

        //vip用户逻辑处理
        if (MMKVDataRep.userInfo.isVIP) {
            //  过去日期 签到状态逻辑处理
            if (DateUtil.convertToLong("${itemData.date_format.formatAsDashedDate()} 00:00:00") < DateUtil.convertToLong(
                    "${System.currentTimeMillis().formatDate()} 00:00:00"
                )
            ) {
                //已签到逻辑处理
                if (itemData.isReceived) {
                    binding.root.setBackgroundResource(R.drawable.bg_user_sign_signed_without_border)
                    binding.tvDiamond.setTextCompatColor(R.color.color_white)
                    binding.ivSignStatus.apply {
                        makeVisible()
                        setImageResource(R.mipmap.ic_sign_status_right)
                    }
                    binding.tvTitle.setTextCompatColor(R.color.color_white)
                    binding.ivItem.makeInVisible()
//                    binding.ivCover.makeVisible()
                }

                //未签到逻辑处理
                else {
                    binding.root.setBackgroundResource(R.drawable.bg_user_sign_signed_expired)
                    binding.tvDiamond.setTextCompatColor(R.color.color_979797)
//                    binding.ivSignStatus.apply {
//                        makeVisible()
//                        setImageResource(R.mipmap.ic_sign_status_x)
//                    }
                    binding.tvExpired.makeVisible()
                    binding.tvTitle.setTextCompatColor(R.color.color_979797)
//                    binding.ivCover.makeVisible()
                }
            }

            //今天及未来时间逻辑处理
            else {
                //今天
                if (TextUtils.equals(
                        itemData.date_format.formatAsDashedDate(),
                        System.currentTimeMillis().formatDate()
                    )
                ) {
                    //已签到
                    if (itemData.isReceived) {
                        binding.root.setBackgroundResource(R.drawable.bg_user_sign_signed_without_border)
                        binding.tvDiamond.setTextCompatColor(R.color.color_white)
                        binding.ivSignStatus.apply {
                            makeVisible()
                            setImageResource(R.mipmap.ic_sign_status_right)
                        }
                        binding.tvTitle.setTextCompatColor(R.color.color_white)
//                        binding.ivCover.makeVisible()
                        binding.ivItem.makeInVisible()
                    }

                    //可签到，但未签
                    else {
                        binding.root.setBackgroundResource(R.drawable.bg_user_sign_signed_available)
                        binding.tvDiamond.setTextCompatColor(R.color.color_white)
                        binding.ivSignStatus.apply {
                            makeVisible()
                        }
                        binding.tvTitle.setTextCompatColor(R.color.color_white)
                    }
                }

                //未签到
                else {
//                    binding.ivSignLock.makeVisible()
                    binding.root.setBackgroundResource(R.drawable.bg_user_sign_future)
                    binding.tvDiamond.setTextCompatColor(R.color.color_F36CEE)
                    binding.tvTitle.setTextCompatColor(R.color.color_F36CEE)
                }
            }
        }

        //非vip用户逻辑处理
        else {
//            binding.ivSignLock.makeVisible()
            binding.root.setBackgroundResource(R.drawable.bg_user_sign_future)
            binding.tvDiamond.setTextCompatColor(R.color.color_F36CEE)
            binding.tvTitle.setTextCompatColor(R.color.color_F36CEE)
//            binding.llLockContainer.makeVisible()
        }
    }
}

class UserSignSpecialItemView : UserSignItemView {
    constructor(context: Context) : super(context)
    constructor(context: Context, attr: AttributeSet?) : super(context, attr)

    override fun getLayoutId() = R.layout.item_special_user_sign
}