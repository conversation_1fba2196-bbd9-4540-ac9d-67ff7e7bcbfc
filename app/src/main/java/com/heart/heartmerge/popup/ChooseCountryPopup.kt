package com.heart.heartmerge.popup

import android.app.Activity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.http.request
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.CountryBean
import com.heart.heartmerge.databinding.PopupChoooseCountryBinding
import com.heart.heartmerge.http.RetrofitClient
import com.heart.heartmerge.ui.widget.SideBar.OnTouchingLetterChangedListener
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.PinyinUtil
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BottomPopupView
import java.util.Locale


class ChooseCountryPopup(
    activity: Activity, private val onClick: (CountryBean.CountryItemBean) -> Unit
) : BottomPopupView(activity) {

    override fun getImplLayoutId(): Int = R.layout.popup_chooose_country

    override fun initPopupContent() {
        super.initPopupContent()
        val binding = PopupChoooseCountryBinding.bind(popupContentView)

        val items = ArrayList<CountryBean.CountryItemBean>()

        binding.tvCancel.setOnClickListener {
            dismiss()
        }

        binding.ivClose.setOnClickListener {
            dismiss()
        }

        val adapter = CountryAdapter(items) {
            onClick.invoke(it)
            dismiss()
        }

        binding.progressBar.apply {
            visibility = VISIBLE
            isIndeterminate = true
        }
        request<CountryBean> {
            call { RetrofitClient.aggregatedService.getCountryList() }
            onSuccess {
                it?.let {
                    binding.progressBar.makeGone()
                    it.country_list?.forEach {
                        val pinyin = PinyinUtil.getPingYin(it.title)
                        val sortLetter = pinyin.substring(0, 1).uppercase(Locale.getDefault())
                        it.letter = if (sortLetter.matches("[A-Z]".toRegex())) sortLetter.uppercase(
                            Locale.getDefault()
                        ) else "#"
                    }
                    it
                }?.let {
                    it.country_list?.let {
                        items.addAll(it)
                        items.sortWith(Comparator { o1, o2 ->
                            if (o1.letter ?: "" == "@" || o2.letter ?: "" == "#") {
                                -1
                            } else if (o1.letter ?: "" == "#" || o2.letter ?: "" == "@") {
                                1
                            } else {
                                (o1.letter ?: "").compareTo(o2.letter ?: "")
                            }
                        })
                    }
                    adapter.notifyDataSetChanged()
                }
            }

            val manager = binding.recyclerView.layoutManager as LinearLayoutManager
            binding.sideBar.setOnTouchingLetterChangedListener(object :
                OnTouchingLetterChangedListener {
                override fun onTouchingLetterChanged(s: String) {
                    val position: Int = adapter.getPositionForSection(s[0].code)
                    if (position != -1) {
                        manager.scrollToPositionWithOffset(position, 0)
                    }
                }
            })

            binding.recyclerView.apply {
                this.adapter = adapter
            }
        }
    }

    class CountryAdapter(
        private var items: List<CountryBean.CountryItemBean>,
        private val onClick: (CountryBean.CountryItemBean) -> Unit
    ) : RecyclerView.Adapter<CountryAdapter.ViewHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_choose_country, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val item = items[position]
            val section = getSectionForPosition(position)
            if (position == getPositionForSection(section)) {
                holder.tvTag.makeVisible()
                holder.tvTag.text = item.letter
            } else {
                holder.tvTag.makeGone()
            }
            AppUtil.setLocalCountryImage(item.code, holder.ivFlag, radio = 8F)
            holder.tvName.text = item.title
            holder.itemView.setOnClickListener {
                onClick.invoke(item)
            }
        }

        override fun getItemCount(): Int = items.size

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val tvTag: TextView = itemView.findViewById(R.id.tag)
            val tvName: TextView = itemView.findViewById(R.id.name)
            val ivFlag: ImageView = itemView.findViewById(R.id.iv_flag)
        }

        private fun getSectionForPosition(position: Int): Int =
            items[position].letter?.get(0)?.code ?: 0

        fun getPositionForSection(section: Int): Int {
            for (i in 0 until itemCount) {
                val sortStr = items[i].letter
                val firstChar = sortStr?.uppercase()[0]
                if (firstChar?.toInt() == section) {
                    return i
                }
            }
            return -1
        }
    }
}

fun showCountryPopup(activity: Activity, block: (CountryBean.CountryItemBean) -> Unit) {
    XPopup.Builder(activity).asCustom(ChooseCountryPopup(activity, block)).show()
}
