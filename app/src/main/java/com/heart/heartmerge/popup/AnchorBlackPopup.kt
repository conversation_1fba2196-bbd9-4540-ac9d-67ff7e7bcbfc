package com.heart.heartmerge.popup

import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import coil.load
import coil.transform.CircleCropTransformation
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BottomPopupView
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.databinding.PopupAnchorBlockBinding
import com.heart.heartmerge.extension.buildImageUrl
import com.heart.heartmerge.viewmodes.AnchorViewModel


class AnchorBlackPopup(private val activity: AppCompatActivity, private val anchorInfo: UserBean?) : BottomPopupView(activity) {
    private val viewModel by activity.viewModels<AnchorViewModel>()
    override fun getImplLayoutId(): Int = R.layout.popup_anchor_block

    override fun initPopupContent() {
        super.initPopupContent()
        val binding = PopupAnchorBlockBinding.bind(popupImplView)
        binding.anchorHeader.load(anchorInfo?.avatar) {
            error(R.mipmap.ic_pic_default_oval)
            placeholder(R.mipmap.ic_pic_default_oval)
            transformations(CircleCropTransformation())
        }
        binding.btnBlack.setOnClickListener {
            var anchorId = anchorInfo?.anchorId
            if (anchorId.isNullOrEmpty()) {
                anchorId = anchorInfo?.id  //关注列表返回的是anchorId
            }
            anchorId?.let { it1 -> viewModel.blackAnchor(it1) }
            dismiss()
        }
        binding.flClose.setOnClickListener {
            dismiss()
        }
    }
}

fun showBlackPopup(activity: AppCompatActivity, anchorInfo: UserBean?) {
    XPopup.Builder(activity).asCustom(AnchorBlackPopup(activity, anchorInfo)).show()
}