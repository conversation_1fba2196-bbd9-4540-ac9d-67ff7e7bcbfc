package com.heart.heartmerge.popup

import android.os.Build
import android.widget.ImageView
import androidx.activity.ComponentActivity
import androidx.core.content.ContextCompat
import com.bdc.android.library.imageloader.ImageLoader
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.PopupChoooseAvatarBinding
import com.heart.heartmerge.utils.GlideImageEngine
import com.heart.heartmerge.utils.UCropImageCropEngine
import com.luck.lib.camerax.SimpleCameraX
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig
import com.luck.picture.lib.engine.CompressFileEngine
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.luck.picture.lib.style.PictureSelectorStyle
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BottomPopupView
import top.zibin.luban.Luban
import top.zibin.luban.OnNewCompressListener
import java.io.File


class ChooseAvatarPopup(
    private val activity: ComponentActivity, private val onClick: (String?) -> Unit
) : BottomPopupView(activity) {

    override fun getImplLayoutId(): Int = R.layout.popup_chooose_avatar

    private val compressFileEngine = CompressFileEngine { context, source, call ->
        Luban.with(activity).load(source).ignoreBy(100)
            .setCompressListener(object : OnNewCompressListener {
                override fun onStart() {
                }

                override fun onSuccess(
                    source: String?, compressFile: File?
                ) {
                    call?.onCallback(source, compressFile?.absolutePath)
                }

                override fun onError(
                    source: String?, e: Throwable?
                ) {
                    call?.onCallback(source, null)
                }
            }).launch()
    }

    override fun initPopupContent() {
        super.initPopupContent()

        val binding = PopupChoooseAvatarBinding.bind(popupContentView)
        binding.tvTakePicture.setOnClickListener {
            PictureSelector.create(activity).openCamera(SelectMimeType.ofImage())
                //是否开启原图功能 -- 这里不开启原图功能
                .isOriginalControl(false)
                .setCameraInterceptListener { fragment, cameraMode, requestCode ->
                    val camera: SimpleCameraX = SimpleCameraX.of()
                    camera.setCameraMode(cameraMode)

                    //视频帧率，越高视频体积越大
                    camera.setVideoFrameRate(25)

                    //bit率， 越大视频体积越大
                    camera.setVideoBitRate(3 * 1024 * 1024)

                    //是否显示录制时间  -- 设置为true
                    camera.isDisplayRecordChangeTime(true)

                    //是否支持手指点击对焦  设置true
                    camera.isManualFocusCameraPreview(true)

                    //支持手指缩放相机   设置true
                    camera.isZoomCameraPreview(true)

                    //拍照自定义输出路径
                    //                        camera.setOutputPathDir(com.oevhenmi.picture.selectgvimage.picture.PictureSelectorTools.getSandboxPath())
                    camera.setImageEngine { context, url, imageView ->
                        imageView.scaleType = ImageView.ScaleType.CENTER_CROP
                        ImageLoader.with(context).load(url).into(imageView)
                    }
                    camera.start(fragment.requireContext(), fragment, requestCode)
                }.setCropEngine(UCropImageCropEngine())
                .forResult(object : OnResultCallbackListener<LocalMedia?> {
                    override fun onResult(result: ArrayList<LocalMedia?>) {
                        val path = result.first()?.cutPath ?: result.first()?.path
                        onClick.invoke(path)
                        dismiss()
                    }

                    override fun onCancel() {
                        dismiss()
                    }
                })
        }
        binding.tvGallery.setOnClickListener {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                PictureSelector.create(activity).openSystemGallery(SelectMimeType.ofImage())
                    .setCompressEngine(compressFileEngine).setCropEngine(UCropImageCropEngine())
                    .setSelectionMode(SelectModeConfig.SINGLE)
                    .forSystemResult(object : OnResultCallbackListener<LocalMedia?> {
                        override fun onResult(result: java.util.ArrayList<LocalMedia?>?) {
                            val path = result?.first()?.cutPath ?: result?.first()?.realPath
                            onClick.invoke(path)
                            dismiss()
                        }

                        override fun onCancel() {
                            dismiss()
                        }
                    })
            } else {
                PictureSelector.create(activity).openGallery(SelectMimeType.ofImage())
                    .setMaxSelectNum(1).setImageEngine(GlideImageEngine()).isDisplayCamera(false)
                    .setCompressEngine(compressFileEngine).setCropEngine(UCropImageCropEngine())
                    .setSelectorUIStyle(PictureSelectorStyle().apply {
                        titleBarStyle = titleBarStyle.apply {
                            titleBackgroundColor =
                                ContextCompat.getColor(context, R.color.background)
                        }
                        selectMainStyle = selectMainStyle.apply {
                            selectTextColor = ContextCompat.getColor(context, R.color.colorAccent)
                        }
                        bottomBarStyle = bottomBarStyle.apply {
                            bottomNarBarBackgroundColor =
                                ContextCompat.getColor(context, R.color.background)
                            bottomPreviewSelectTextColor =
                                ContextCompat.getColor(context, R.color.color_B452FF)
                            bottomSelectNumResources = R.drawable.shape_9f2af8_50_20
                            bottomSelectNumTextColor = ContextCompat.getColor(
                                context, com.bdc.android.library.R.color.white
                            )
                        }
                    }).forResult(object : OnResultCallbackListener<LocalMedia?> {
                        override fun onResult(result: ArrayList<LocalMedia?>) {
                            val path = result.first()?.cutPath ?: result.first()?.realPath
                            onClick.invoke(path)
                            dismiss()
                        }

                        override fun onCancel() {
                            dismiss()
                        }
                    })
            }
        }

        binding.tvCancel.setOnClickListener { dismiss() }
    }
}

fun showAvatarPopup(activity: ComponentActivity, block: (String?) -> Unit) {
    XPopup.Builder(activity).asCustom(ChooseAvatarPopup(activity, block)).show()
}