package com.heart.heartmerge.popup

import android.content.Context
import com.bdc.android.library.extension.makeVisible
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.PopupConfirmBinding

class ConfirmPopup(
    activity: Context,
    private val title: String,
    val content: String,
    private val singleButton: Boolean,
    val cancelBlock: () -> Unit = {},
    val confirmBlock: () -> Unit = {}
) : CenterPopupView(activity) {
    override fun getImplLayoutId(): Int = R.layout.popup_confirm

    override fun initPopupContent() {
        super.initPopupContent()
        val binding = PopupConfirmBinding.bind(popupImplView)

        title.takeIf { it.isNotBlank() }?.let {
            binding.tvTitle.apply {
                makeVisible()
                text = it
            }
        }
        content.takeIf { it.isNotEmpty() }.let {
            binding.tvContent.apply {
                makeVisible()
                text = it
            }
        }
        binding.tvCancel.makeVisible(singleButton.not())
        binding.divider.makeVisible(singleButton.not())
        binding.tvCancel.setOnClickListener {
            cancelBlock()
            dismiss()
        }

        binding.tvConfirm.setOnClickListener {
            confirmBlock()
            dismiss()
        }
    }
}

fun showConfirmPopup(
    activity: Context,
    title: String = "",
    content: String = "",
    singleButton: Boolean = false,
    autoDismiss: Boolean = true,
    dismissOnBackPressed: Boolean = false,
    dismissOnTouchOutside: Boolean = false,
    cancelBlock: () -> Unit = {},
    confirmBlock: () -> Unit = { }
) {
    XPopup.Builder(activity).autoDismiss(autoDismiss).dismissOnBackPressed(dismissOnBackPressed)
        .dismissOnTouchOutside(dismissOnTouchOutside)
        .asCustom(ConfirmPopup(activity, title, content, singleButton, cancelBlock, confirmBlock))
        .show()
}