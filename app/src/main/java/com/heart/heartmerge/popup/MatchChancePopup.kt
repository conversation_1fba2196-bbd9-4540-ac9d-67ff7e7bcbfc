package com.heart.heartmerge.popup

import android.app.Activity
import com.bdc.android.library.utils.SpannableUtil
import com.heart.heartmerge.R
import com.heart.heartmerge.extension.formatDate
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.lxj.xpopup.XPopup

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2024/12/24 10:26
 * @description :匹配次数弹框
 */
class MatchChancePopup(context: Activity, val chance: Int, block: () -> Unit) : NormalNewPopup(
    context,
    icon = R.mipmap.ic_dialog_small_bell,
    title = context.getString(R.string.free_chance_title),
    content = context.getString(R.string.free_chance_content, chance.toString()),
    btnSure = context.getString(R.string.try_now),
    btnCancel = context.getString(R.string.cancel),
    mainColor = R.color.color_FF962D,
    titleColor = R.color.color_FF962D,
    block = block
) {

    override fun initPopupContent() {
        super.initPopupContent()
        val content = context.getString(R.string.free_chance_content, "$chance")
        val spannableString =
            SpannableUtil(context, content).first("$chance").textColor(R.color.color_FF962D)

        binding.tvContent.text = spannableString
        MMKVDataRep.freeRandomMatchPopupShowDate = System.currentTimeMillis().formatDate()
    }
}

fun showMatchChancePopup(context: Activity, chance: Int, block: () -> Unit) {
    XPopup.Builder(context).dismissOnBackPressed(false).dismissOnTouchOutside(false)
        .asCustom(MatchChancePopup(context, chance, block)).show()
}