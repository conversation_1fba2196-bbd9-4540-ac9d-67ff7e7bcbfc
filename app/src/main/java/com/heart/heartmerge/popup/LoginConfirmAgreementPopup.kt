package com.heart.heartmerge.popup

import android.content.Context
import android.text.TextPaint
import com.bdc.android.library.extension.click
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.PopupConfirmAgreementBinding
import com.heart.heartmerge.ui.activities.WebViewActivity
import com.heart.heartmerge.utils.Constants
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/1/4 18:31
 * @description :登录确认协议
 */
class LoginConfirmAgreementPopup(private val context: Context, private val block: () -> Unit) :
    CenterPopupView(context) {

    override fun getImplLayoutId(): Int = R.layout.popup_confirm_agreement

    override fun initPopupContent() {
        super.initPopupContent()
        val binding = PopupConfirmAgreementBinding.bind(popupImplView)

        binding.tvUserAgreement.paintFlags =
            binding.tvUserAgreement.paintFlags or TextPaint.UNDERLINE_TEXT_FLAG

        binding.tvPrivacyPolicy.paintFlags =
            binding.tvPrivacyPolicy.paintFlags or TextPaint.UNDERLINE_TEXT_FLAG

        binding.tvUserAgreement.click {
            WebViewActivity.jump(
                context,
                context.getString(R.string.user_agreement),
                Constants.Agreement.REGISTRATION_URL
            )
        }

        binding.tvPrivacyPolicy.click {
            WebViewActivity.jump(
                context,
                context.getString(R.string.privacy_policy),
                Constants.Agreement.PRIVACY_URL
            )
        }

        binding.btn.click {
            block()
            dismiss()
        }

        binding.tvLater.click {
            dismiss()
        }
    }
}

fun showLoginConfirmAgreementPopup(context: Context, block: () -> Unit) {
    XPopup.Builder(context).asCustom(LoginConfirmAgreementPopup(context, block)).show()
}