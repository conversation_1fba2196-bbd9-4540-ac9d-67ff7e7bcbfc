package com.heart.heartmerge.popup

import android.content.Context
import androidx.appcompat.app.AppCompatActivity
import com.bdc.android.library.extension.click
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.PopupSuggestionBinding
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/13 9:20
 * @description :意见反馈弹框
 */
class SuggestionPopup(context: Context) : CenterPopupView(context) {

    override fun getImplLayoutId(): Int = R.layout.popup_suggestion

    override fun initPopupContent() {
        super.initPopupContent()
        val binding = PopupSuggestionBinding.bind(popupImplView)
        binding.ivClose.click { dismiss() }
    }
}

fun showSuggestionPopup(activity: AppCompatActivity) {
    XPopup.Builder(activity).asCustom(SuggestionPopup(activity)).show()
}